package com.datalink.fdop.common.core.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 权限列扫描注解
 * 用于标记需要进行列权限控制的实体类
 *
 * <AUTHOR>
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface PermissionColumn {

    /**
     * 模块名称
     */
    String module() default "";

    /**
     * 实体描述
     */
    String description() default "";
}
