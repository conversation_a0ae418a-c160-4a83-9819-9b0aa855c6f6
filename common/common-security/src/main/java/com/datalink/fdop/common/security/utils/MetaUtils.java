package com.datalink.fdop.common.security.utils;

import com.alibaba.fastjson.JSONObject;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.datalink.fdop.common.core.domain.DynamicColumn;
import com.datalink.fdop.common.core.utils.SpringUtils;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 元数据工具类（用于获取实体的中文字段名和注释，并且返回高级查询字段）
 */
public class MetaUtils {

    public static JSONObject getMetadata(Class<?> entityClass, Object data) {
        return getMetadata(entityClass, data, true, true, null);
    }

    public static JSONObject getMetadata(Class<?> entityClass, Object data, List<DynamicColumn> dynamicColumns) {
        return getMetadata(entityClass, data, true, true, dynamicColumns);
    }

    public static JSONObject getMetadata(Class<?> entityClass, Object data, boolean isEntity, boolean isSearch) {
        return getMetadata(entityClass, data, isEntity, isSearch, null);
    }

    // 获取实体类的字段信息，并返回所需的JSON数据
    public static JSONObject getMetadata(Class<?> entityClass, Object data, boolean isEntity, boolean isSearch, List<DynamicColumn> dynamicColumns) {
        JSONObject response = new JSONObject();

        // 1. 获取用户的不可见字段
        Set<String> invisibleFields = getInvisibleFields(entityClass);

        // 获取columns和searchFields
        List<JSONObject> columns = new ArrayList<>();
        List<JSONObject> searchFields = new ArrayList<>();

        // 遍历实体类的所有字段
        for (Field field : entityClass.getDeclaredFields()) {
            String fieldCode = field.getName();

            // 2. 如果字段不可见，直接跳过
            if (invisibleFields.contains(fieldCode)) {
                continue;
            }
            // 读取@ApiModelProperty注解中的value（字段中文名）
            ApiModelProperty apiModelPropertyAnnotation = field.getAnnotation(ApiModelProperty.class);
            if (apiModelPropertyAnnotation == null) {
                continue; // 没有注解的字段跳过
            }
            // 列描述
            String filedName = apiModelPropertyAnnotation.value();
            // 判断是否过滤列
            boolean isFilter = true;
            // 动态列筛选
            if (CollectionUtils.isNotEmpty(dynamicColumns)) {
                // 如果fieldName包含attr/costStructure开头 则开始匹配dynamicColumns，如果存在与集合中的字段则加入searchFiled，column
                if (fieldCode.matches("attr\\d+") || fieldCode.contains("costStructure") || fieldCode.contains("osCostStructure")) {
                    for (DynamicColumn dynamicColumn : dynamicColumns) {
                        // 判断实体字段转为下划线命名后是否与动态列匹配
                        if (toUnderlineCase(fieldCode).equals(dynamicColumn.getColumnName())) {
                            filedName = dynamicColumn.getColumnDesc();
                            isFilter = false;
                        }
                    }
                } else {
                    isFilter = false;
                }
            } else {
                isFilter = false;
            }
            // 过滤当前列
            if (isFilter) {
                continue;
            }
            // 创建用于保存字段信息的JSONObject
            JSONObject column = new JSONObject();
            JSONObject searchField = new JSONObject();
            // 中文描述
            if (StringUtils.isNotBlank(filedName)) {
                // 表列描述
                column.put("filedName", filedName);
                // 高级查询名称
                searchField.put("filedName", filedName);
            }
            // 表列名
            column.put("filedCode", fieldCode);
            // 高级查询列名
            searchField.put("filedCode", toUnderlineCase(fieldCode));
            columns.add(column);
            searchFields.add(searchField);

            // 如果字段被@SearchField注解标注，则将该字段加入searchFiled
//            SearchField searchFieldAnnotation = field.getAnnotation(SearchField.class);
//            if (searchFieldAnnotation != null) {
//                String searchFieldDescription = searchFieldAnnotation.value();
//                if (StringUtils.isNotBlank(searchFieldDescription)) {
//                    searchField.put("filedName", searchFieldDescription);
//                }
//                searchFields.add(searchField);
//            }
        }

        // 将columns和searchFields放入最终返回的JSON中
        if (isEntity) {
            response.put("columns", columns);
        }
        if (isSearch) {
            response.put("searchFiled", searchFields);
        }

        // 应用字段权限过滤
        if (data != null) {
            data = filter(data);
        }

        response.put("data", data);

        return response;
    }

    // 将驼峰命名法转换为下划线命名法
    private static String toUnderlineCase(String fieldName) {
        if (StringUtils.isBlank(fieldName)) {
            return fieldName;
        }


        StringBuilder result = new StringBuilder();
        char[] chars = fieldName.toCharArray();
        for (int i = 0; i < chars.length; i++) {
            char c = chars[i];
            // 如果当前字符是数字
            if (Character.isDigit(c)) {
                // 检查前一个字符是否是非数字，如果是，则插入下划线
                if (i > 0 && !Character.isDigit(chars[i - 1])) {
                    result.append("_");
                }
                result.append(c);
            } else if (Character.isUpperCase(c)) {
                // 处理大写字母（驼峰转下划线）
                result.append("_").append(Character.toLowerCase(c));
            } else {
                result.append(c);
            }
        }
        return result.toString();
    }

    /**
     * 字段权限过滤
     */
    @SuppressWarnings("unchecked")
    public static <T> T filter(T data) {
        try {
            if (data == null) return data;

            // 检查是否需要权限控制
            Class<?> dataClass = data.getClass();
            if (data instanceof Collection) {
                Collection<?> collection = (Collection<?>) data;
                if (collection.isEmpty()) return data;
                dataClass = collection.iterator().next().getClass();
            }

            if (!needsPermissionControl(dataClass)) {
                return data;
            }

            // 获取不可见字段
            Set<String> invisibleFields = getInvisibleFields(dataClass);
            if (invisibleFields.isEmpty()) return data;

            ObjectMapper mapper = new ObjectMapper();
            JsonNode node = mapper.valueToTree(data);

            if (node.isArray()) {
                for (JsonNode item : node) {
                    // 移除不可见字段
                    invisibleFields.forEach(((ObjectNode) item)::remove);
                }
            } else {
                // 移除不可见字段
                invisibleFields.forEach(((ObjectNode) node)::remove);
            }

            return mapper.treeToValue(node, (Class<T>) data.getClass());
        } catch (Exception e) {
            return data;
        }
    }

    /**
     * 获取不可见字段
     */
    private static Set<String> getInvisibleFields(Class<?> entityClass) {
        try {
            // 检查实体是否需要权限控制
            if (!needsPermissionControl(entityClass)) {
                return Collections.emptySet();
            }

            // 直接获取当前用户的角色ID
            Long roleId = SecurityUtils.getLoginUser().getSysUser().getRoleId();
            if (roleId == null) {
                return Collections.emptySet();
            }

            String moduleName = getModuleName(entityClass);

            // 通过反射获取权限服务
            Object service = SpringUtils.getBean("sysRoleColumnServiceImpl");
            Method method = service.getClass().getMethod("getRoleColumnPermissions", Long.class, String.class);
            List<?> permissions = (List<?>) method.invoke(service, roleId, moduleName);

            return permissions.stream()
                .filter(permission -> {
                    try {
                        Method getIsVisible = permission.getClass().getMethod("getIsVisible");
                        return !(Boolean) getIsVisible.invoke(permission);  // 获取不可见字段
                    } catch (Exception e) {
                        return false;
                    }
                })
                .map(permission -> {
                    try {
                        Method getColumnName = permission.getClass().getMethod("getColumnName");
                        return (String) getColumnName.invoke(permission);
                    } catch (Exception e) {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        } catch (Exception e) {
            return Collections.emptySet();
        }
    }

    /**
     * 检查实体是否需要权限控制
     */
    private static boolean needsPermissionControl(Class<?> entityClass) {
        return entityClass.isAnnotationPresent(PermissionColumn.class);
    }

    /**
     * 获取模块名
     */
    private static String getModuleName(Class<?> entityClass) {
        PermissionColumn annotation = entityClass.getAnnotation(PermissionColumn.class);
        if (annotation != null && StringUtils.isNotEmpty(annotation.module())) {
            return annotation.module();
        }

        // 从包名推断
        String packageName = entityClass.getPackage().getName();
        if (packageName.contains("system")) return "system";
        if (packageName.contains("ccms")) return "ccms";

        return "system"; // 默认
    }


}
