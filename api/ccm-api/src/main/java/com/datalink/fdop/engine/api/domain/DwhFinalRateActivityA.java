package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName(schema = "dwh", value = "dwh_final_rate_activity_a")
@ApiModel("实际成本引擎-定版视图-作业费率")
@PermissionColumn(module = "ccms")
public class DwhFinalRateActivityA {

    @ApiModelProperty(value = "数据集")
    @Excel(name = "数据集")
    private String verId;

    @ApiModelProperty(value = "值类型")
    @Excel(name = "值类型")
    private String valueType;

    @ApiModelProperty(value = "管理范围")
    @Excel(name = "管理范围")
    private String controlAreaId;

    @ApiModelProperty(value = "年度月份")
    @Excel(name = "年度月份")
    private Long yearMonth;

    @ApiModelProperty(value = "年度")
    @Excel(name = "年度")
    private Long year;

    @ApiModelProperty(value = "月份")
    @Excel(name = "月份")
    private Long month;

    @ApiModelProperty(value = "成本中心")
    @Excel(name = "成本中心")
    private String costCenterId;

    @ApiModelProperty(value = "成本中心描述")
    @Excel(name = "成本中心描述")
    private String costCenterDesc;

    @ApiModelProperty(value = "成本中心类型")
    @Excel(name = "成本中心类型")
    private String costCenterType;

    @ApiModelProperty(value = "作业类型")
    @Excel(name = "作业类型")
    private String activityId;

    @ApiModelProperty(value = "作业类型描述")
    @Excel(name = "作业类型描述")
    private String activityDesc;

    @ApiModelProperty(value = "作业类型单位")
    @Excel(name = "作业类型单位")
    private String activityUnit;

    @ApiModelProperty(value = "货币值")
    @Excel(name = "货币值")
    private String amount;

    @ApiModelProperty(value = "有效作业量")
    @Excel(name = "有效作业量")
    private String activityQty;

    @ApiModelProperty(value = "作业费率")
    @Excel(name = "作业费率")
    private String unitPrice;

    @ApiModelProperty(value = "Primary_Key")
    @Excel(name = "Primary_Key")
    private String primaryKey;

    @ApiModelProperty(value = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Excel(name = "导入时间")
    private Date fdopImportTime;


}