package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName(schema = "dws", value = "dws_new_revalue_exp")
@ApiModel("逻辑视图-财务账记数据")
@PermissionColumn(module = "ccms")
public class DwsNewRevalueExp {

    @ApiModelProperty(value = "数据集")
    @Excel(name = "数据集")
    private String verId;

    @ApiModelProperty(value = "值类型")
    @Excel(name = "值类型")
    private String valueType;

    @ApiModelProperty(value = "管理范围")
    @Excel(name = "管理范围")
    private String controlAreaId;

    @ApiModelProperty(value = "公司代码")
    @Excel(name = "公司代码")
    private String companyId;

    @ApiModelProperty(value = "工厂代码")
    @Excel(name = "工厂代码")
    private String plantId;

    @ApiModelProperty(value = "年度月份")
    @Excel(name = "年度月份")
    private String yearMonth;

    @ApiModelProperty(value = "年度期间")
    @Excel(name = "年度期间")
    private String yearMonat;

    @ApiModelProperty(value = "年度")
    @Excel(name = "年度")
    private String year;

    @ApiModelProperty(value = "月份")
    @Excel(name = "月份")
    private String month;

    @ApiModelProperty(value = "期间")
    @Excel(name = "期间")
    private String monat;

    @ApiModelProperty(value = "过账日期")
    @Excel(name = "过账日期")
    private String postingDate;

    @ApiModelProperty(value = "会计凭证号")
    @Excel(name = "会计凭证号")
    private String finDoc;

    @ApiModelProperty(value = "会计凭证行")
    @Excel(name = "会计凭证行")
    private String finDocItem;

    @ApiModelProperty(value = "原科目")
    @Excel(name = "原科目")
    private String origCostElementId;

    @ApiModelProperty(value = "原科目描述")
    @Excel(name = "原科目描述")
    private String origCostElementDesc;

    @ApiModelProperty(value = "凭证状态")
    @Excel(name = "凭证状态")
    private String erpBstat;

    @ApiModelProperty(value = "凭证类型")
    @Excel(name = "凭证类型")
    private String documentType;

    @ApiModelProperty(value = "凭证货币")
    @Excel(name = "凭证货币")
    private String amountWsl;

    @ApiModelProperty(value = "公司货币")
    @Excel(name = "公司货币")
    private String amountHsl;

    @ApiModelProperty(value = "集团货币")
    @Excel(name = "集团货币")
    private String amountKsl;

    @ApiModelProperty(value = "数量")
    @Excel(name = "数量")
    private String qtyMsl;

    @ApiModelProperty(value = "物料编码")
    @Excel(name = "物料编码")
    private String materialId;

    @ApiModelProperty(value = "物料编码描述")
    @Excel(name = "物料编码描述")
    private String materialDesc;

    @ApiModelProperty(value = "设备ID")
    @Excel(name = "设备ID")
    private String equipId;

    @ApiModelProperty(value = "资产主号")
    @Excel(name = "资产主号")
    private String assets1;

    @ApiModelProperty(value = "资产子号")
    @Excel(name = "资产子号")
    private String assets2;

    @ApiModelProperty(value = "资产卡片描述")
    @Excel(name = "资产卡片描述")
    private String assetsDesc;

    @ApiModelProperty(value = "成本对象")
    @Excel(name = "成本对象")
    private String erpAccasty;

    @ApiModelProperty(value = "成本中心")
    @Excel(name = "成本中心")
    private String costCenterId;

    @ApiModelProperty(value = "成本中心描述")
    @Excel(name = "成本中心描述")
    private String costCenterDesc;

    @ApiModelProperty(value = "成本中心类型")
    @Excel(name = "成本中心类型")
    private String costCenterType;

    @ApiModelProperty(value = "费用属性类型")
    @Excel(name = "费用属性类型")
    private String expenseType;

    @ApiModelProperty(value = "订单")
    @Excel(name = "订单")
    private String orderId;

    @ApiModelProperty(value = "订单类型")
    @Excel(name = "订单类型")
    private String orderType;

    @ApiModelProperty(value = "订单类别")
    @Excel(name = "订单类别")
    private String orderCategory;

    @ApiModelProperty(value = "项目号码")
    @Excel(name = "项目号码")
    private String projectId;

    @ApiModelProperty(value = "项目号码描述")
    @Excel(name = "项目号码描述")
    private String projectDesc;

    @ApiModelProperty(value = "凭证头文本")
    @Excel(name = "凭证头文本")
    private String docHeadText;

    @ApiModelProperty(value = "凭证行文本")
    @Excel(name = "凭证行文本")
    private String docItemText;

    @ApiModelProperty(value = "原评估类")
    @Excel(name = "原评估类")
    private String origEvaluateSortId;

    @ApiModelProperty(value = "原评估类描述")
    @Excel(name = "原评估类描述")
    private String origEvaluateSortDesc;

    @ApiModelProperty(value = "物料组")
    @Excel(name = "物料组")
    private String materialGroup;

    @ApiModelProperty(value = "移动类型")
    @Excel(name = "移动类型")
    private String movementType;

    @ApiModelProperty(value = "评估细分")
    @Excel(name = "评估细分")
    private String evaluateSplit;

    @ApiModelProperty(value = "重评估类")
    @Excel(name = "重评估类")
    private String evaluateSortId;

    @ApiModelProperty(value = "重评估类描述")
    @Excel(name = "重评估类描述")
    private String evaluateSortDesc;

    @ApiModelProperty(value = "重估成本要素")
    @Excel(name = "重估成本要素")
    private String costElementId;

    @ApiModelProperty(value = "重估成本要素描述")
    @Excel(name = "重估成本要素描述")
    private String costElementDesc;

    @ApiModelProperty(value = "成本组件")
    @Excel(name = "成本组件")
    private String costStructureId;

    @ApiModelProperty(value = "成本组件描述")
    @Excel(name = "成本组件描述")
    private String costStructureDesc;

    @ApiModelProperty(value = "作业类型")
    @Excel(name = "作业类型")
    private String activityId;

    @ApiModelProperty(value = "作业类型描述")
    @Excel(name = "作业类型描述")
    private String activityDesc;

    @ApiModelProperty(value = "业务区分码[1]")
    @Excel(name = "业务区分码[1]")
    private String erpAwtyp;

    @ApiModelProperty(value = "业务区分码[2]")
    @Excel(name = "业务区分码[2]")
    private String erpBttype;

    @ApiModelProperty(value = "业务区分码[3]")
    @Excel(name = "业务区分码[3]")
    private String erpKtosl;

    @ApiModelProperty(value = "评估类型")
    @Excel(name = "评估类型")
    private String evaluateType;

    @ApiModelProperty(value = "评估范围")
    @Excel(name = "评估范围")
    private String evaluateAreaId;

    @ApiModelProperty(value = "Primary_key")
    @Excel(name = "Primary_key")
    private String primaryKey;

    @ApiModelProperty(value = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Excel(name = "导入时间")
    private Date fdopImportTime;
}
