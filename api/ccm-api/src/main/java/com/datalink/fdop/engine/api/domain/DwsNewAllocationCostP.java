package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName(schema = "dws", value = "dws_new_allocation_cost_p")
@ApiModel("逻辑视图-定向分摊")
@PermissionColumn(module = "ccms")
public class DwsNewAllocationCostP {

    @ApiModelProperty(value = "数据集")
    @Excel(name = "数据集")
    private String verId;

    @ApiModelProperty(value = "值类型")
    @Excel(name = "值类型")
    private String valueType;

    @ApiModelProperty(value = "管理范围")
    @Excel(name = "管理范围")
    private String controlAreaId;

    @ApiModelProperty(value = "年度月份")
    @Excel(name = "年度月份")
    private String yearMonth;

    @ApiModelProperty(value = "年度")
    @Excel(name = "年度")
    private Long year;

    @ApiModelProperty(value = "月份")
    @Excel(name = "月份")
    private Long month;

    @ApiModelProperty(value = "分摊类型")
    @Excel(name = "分摊类型")
    private String allocationType;

    @ApiModelProperty(value = "分摊方法")
    @Excel(name = "分摊方法")
    private String allocationMethodId;

    @ApiModelProperty(value = "KeyID")
    @Excel(name = "KeyID")
    private String keyid;

    @ApiModelProperty(value = "段编码")
    @Excel(name = "段编码")
    private String allocationSegmentId;

    @ApiModelProperty(value = "发送方规则")
    @Excel(name = "发送方规则")
    private String sendFactor;

    @ApiModelProperty(value = "发送者比例")
    @Excel(name = "发送者比例")
    private String sendScale;

    @ApiModelProperty(value = "接收方规则")
    @Excel(name = "接收方规则")
    private String acceptFactor;

    @ApiModelProperty(value = "接收方比例")
    @Excel(name = "接收方比例")
    private String acceptScale;

    @ApiModelProperty(value = "成本要素")
    @Excel(name = "成本要素")
    private String costElementId;

    @ApiModelProperty(value = "成本要素描述")
    @Excel(name = "成本要素描述")
    private String costElementDesc;

    @ApiModelProperty(value = "经由成本流要素")
    @Excel(name = "经由成本流要素")
    private String flowingElementId;

    @ApiModelProperty(value = "成本要素值")
    @Excel(name = "成本要素值")
    private String costElementAmount;

    @ApiModelProperty(value = "成本组件")
    @Excel(name = "成本组件")
    private String costStructureId;

    @ApiModelProperty(value = "成本组件描述")
    @Excel(name = "成本组件描述")
    private String costStructureDesc;

    @ApiModelProperty(value = "作业类型")
    @Excel(name = "作业类型")
    private String activityId;

    @ApiModelProperty(value = "作业类型描述")
    @Excel(name = "作业类型描述")
    private String activityDesc;

    @ApiModelProperty(value = "从对象类别")
    @Excel(name = "从对象类别")
    private String objectTypeFr;

    @ApiModelProperty(value = "从成本对象")
    @Excel(name = "从成本对象")
    private String costObjectFr;

    @ApiModelProperty(value = "从公司代码")
    @Excel(name = "从公司代码")
    private String costObjectFrCompanyId;

    @ApiModelProperty(value = "从工厂代码")
    @Excel(name = "从工厂代码")
    private String costObjectFrPlantId;

    @ApiModelProperty(value = "到对象类别")
    @Excel(name = "到对象类别")
    private String objectTypeTo;

    @ApiModelProperty(value = "到成本对象")
    @Excel(name = "到成本对象")
    private String costObjectTo;

    @ApiModelProperty(value = "到公司代码")
    @Excel(name = "到公司代码")
    private String costObjectToCompanyId;

    @ApiModelProperty(value = "到工厂代码")
    @Excel(name = "到工厂代码")
    private String costObjectToPlantId;

    @ApiModelProperty(value = "统计因子")
    @Excel(name = "统计因子")
    private String factorId;

    @ApiModelProperty(value = "统计因子值")
    @Excel(name = "统计因子值")
    private String factorQty;

    @Excel(name = "公式")
    @ApiModelProperty(value = "公式")
    private String formula;

    @ApiModelProperty(value = "货币值")
    @Excel(name = "货币值")
    private String amount;

    @ApiModelProperty(value = "是否记账")
    @Excel(name = "是否记账")
    private String posted;

    @ApiModelProperty(value = "是否冲销")
    @Excel(name = "是否冲销")
    private String writeOff;

    @ApiModelProperty(value = "执行识别码")
    @Excel(name = "执行识别码")
    private String transactionId;

    @ApiModelProperty(value = "Primary_Key")
    @Excel(name = "Primary_Key")
    private String primaryKey;

    @ApiModelProperty(value = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Excel(name = "导入时间")
    private Date fdopImportTime;

    @Excel(name = "分摊方法集")
    @ApiModelProperty(value = "分摊方法集")
    private String allocationMethodCollection;

    @Excel(name = "成本中心")
    @ApiModelProperty(value = "成本中心")
    private String costCenterId;

    @Excel(name = "设备ID")
    @ApiModelProperty(value = "设备ID")
    private String equipId;

    @Excel(name = "专用类型")
    @ApiModelProperty(value = "专用类型")
    private String specialType;

    @Excel(name = "专用物料")
    @ApiModelProperty(value = "专用物料")
    private String rawMaterialId;
}
