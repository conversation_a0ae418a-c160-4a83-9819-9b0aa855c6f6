package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.datalink.fdop.common.core.annotation.SearchField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
@TableName(schema = "dws", value = "dws_new_pbom")
@ApiModel("PBOM管理")
@PermissionColumn(module = "ccms")
public class DwsNewPbom {

    @ApiModelProperty(value = "CIM识别码")
    @Excel(name = "CIM识别码")
    private String pbomCimCode;

    @ApiModelProperty(value = "计数器")
    @Excel(name = "计数器")
    private String count;

    @ApiModelProperty(value = "数据集")
    @Excel(name = "数据集")
    private String verId;

    @ApiModelProperty(value = "工厂代码")
    @Excel(name = "工厂代码")
    @NotBlank(message = "工厂代码不能为空")
    @SearchField(value = "工厂代码")
    private String plantId;

    @ApiModelProperty(value = "制造工厂")
    @Excel(name = "制造工厂")
    private String factoryId;

    @ApiModelProperty(value = "产品编码")
    @Excel(name = "产品编码")
    @NotBlank(message = "产品编码不能为空")
    @SearchField(value = "产品编码")
    private String productId;

    @Excel(name = "产品描述")
    @ApiModelProperty(value = "产品描述")
    private String productDesc;

    @ApiModelProperty(value = "产品CIM编码")
    @Excel(name = "产品CIM编码")
    @NotBlank(message = "产品CIM编码不能为空")
    private String productCimId;

    @ApiModelProperty(value = "产品ERP编码")
    @Excel(name = "产品ERP编码")
    @NotBlank(message = "产品ERP编码不能为空")
    private String productErpId;

    @ApiModelProperty(value = "基础数量")
    @Excel(name = "基础数量")
    private String baseQty;

    @ApiModelProperty(value = "基础单位")
    @Excel(name = "基础单位")
    private String baseUnit;

    @ApiModelProperty(value = "原料编码")
    @Excel(name = "原料编码")
    @NotBlank(message = "原料编码不能为空")
    private String rawMaterialId;

    @Excel(name = "原料描述")
    @ApiModelProperty(value = "原料描述")
    private String rawMaterialDesc;

    @ApiModelProperty(value = "用量[使用单位]")
    @Excel(name = "用量[使用单位]")
    private String usageQty;

    @ApiModelProperty(value = "使用单位")
    @Excel(name = "使用单位")
    private String usageUnit;

    @ApiModelProperty(value = "用量[基本单位]")
    @Excel(name = "用量[基本单位]")
    private String basicQty;

    @ApiModelProperty(value = "基本单位")
    @Excel(name = "基本单位")
    private String basicUnit;

    @ApiModelProperty(value = "默认值")
    @Excel(name = "默认值")
    @TableField(value = "default")
    private String defaultValue;

    @ApiModelProperty(value = "Primary_Key")
    @Excel(name = "Primary_Key")
    private String primaryKey;

    @ApiModelProperty(value = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Excel(name = "导入时间")
    private Date fdopImportTime;


}
