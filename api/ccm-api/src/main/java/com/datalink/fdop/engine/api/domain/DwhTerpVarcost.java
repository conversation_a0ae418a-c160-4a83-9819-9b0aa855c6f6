package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName(schema = "dwh", value = "dwh_terp_varcost")
@ApiModel("交互视图-计算约当分配")
@PermissionColumn(module = "ccms")
public class DwhTerpVarcost {

    @ApiModelProperty(value = "数据集")
    @Excel(name = "数据集")
    private String verId;

    @ApiModelProperty(value = "工厂代码")
    @Excel(name = "工厂代码")
    private String plantId;

    @ApiModelProperty(value = "年度月份")
    @Excel(name = "年度月份")
    private String yearMonth;

    @ApiModelProperty(value = "年度")
    @Excel(name = "年度")
    private String year;

    @ApiModelProperty(value = "月份")
    @Excel(name = "月份")
    private String month;

    @ApiModelProperty(value = "母批工单")
    @Excel(name = "母批工单")
    private String baseOrder;

    @ApiModelProperty(value = "工作工单")
    @Excel(name = "工作工单")
    private String workOrder;

    @ApiModelProperty(value = "母批编码")
    @Excel(name = "母批编码")
    private String baseLotId;

    @ApiModelProperty(value = "约当项")
    @Excel(name = "约当项")
    private String costItem;

    @ApiModelProperty(value = "产品编码")
    @Excel(name = "产品编码")
    private String productId;

    @ApiModelProperty(value = "产品描述")
    @Excel(name = "产品描述")
    private String productDesc;

    @ApiModelProperty(value = "产品CIM编码")
    @Excel(name = "产品CIM编码")
    private String productCimId;

    @ApiModelProperty(value = "产品ERP编码")
    @Excel(name = "产品ERP编码")
    private String productErpId;

    @ApiModelProperty(value = "累计成本值")
    @Excel(name = "累计成本值")
    private String cumulativeCost;

    @ApiModelProperty(value = "组件001成本")
    @Excel(name = "组件001成本")
    private String osCostStructure001;

    @ApiModelProperty(value = "组件002成本")
    @Excel(name = "组件002成本")
    private String osCostStructure002;

    @ApiModelProperty(value = "组件003成本")
    @Excel(name = "组件003成本")
    private String osCostStructure003;

    @ApiModelProperty(value = "组件004成本")
    @Excel(name = "组件004成本")
    private String osCostStructure004;

    @ApiModelProperty(value = "组件005成本")
    @Excel(name = "组件005成本")
    private String osCostStructure005;

    @ApiModelProperty(value = "组件006成本")
    @Excel(name = "组件006成本")
    private String osCostStructure006;

    @ApiModelProperty(value = "组件007成本")
    @Excel(name = "组件007成本")
    private String osCostStructure007;

    @ApiModelProperty(value = "组件008成本")
    @Excel(name = "组件008成本")
    private String osCostStructure008;

    @ApiModelProperty(value = "组件009成本")
    @Excel(name = "组件009成本")
    private String osCostStructure009;

    @ApiModelProperty(value = "组件010成本")
    @Excel(name = "组件010成本")
    private String osCostStructure010;

    @ApiModelProperty(value = "组件011成本")
    @Excel(name = "组件011成本")
    private String osCostStructure011;

    @ApiModelProperty(value = "组件012成本")
    @Excel(name = "组件012成本")
    private String osCostStructure012;

    @ApiModelProperty(value = "组件013成本")
    @Excel(name = "组件013成本")
    private String osCostStructure013;

    @ApiModelProperty(value = "组件014成本")
    @Excel(name = "组件014成本")
    private String osCostStructure014;

    @ApiModelProperty(value = "组件015成本")
    @Excel(name = "组件015成本")
    private String osCostStructure015;

    @ApiModelProperty(value = "组件016成本")
    @Excel(name = "组件016成本")
    private String osCostStructure016;

    @ApiModelProperty(value = "组件017成本")
    @Excel(name = "组件017成本")
    private String osCostStructure017;

    @ApiModelProperty(value = "组件018成本")
    @Excel(name = "组件018成本")
    private String osCostStructure018;

    @ApiModelProperty(value = "组件019成本")
    @Excel(name = "组件019成本")
    private String osCostStructure019;

    @ApiModelProperty(value = "组件020成本")
    @Excel(name = "组件020成本")
    private String osCostStructure020;

    @ApiModelProperty(value = "组件021成本")
    @Excel(name = "组件021成本")
    private String osCostStructure021;

    @ApiModelProperty(value = "组件022成本")
    @Excel(name = "组件022成本")
    private String osCostStructure022;

    @ApiModelProperty(value = "组件023成本")
    @Excel(name = "组件023成本")
    private String osCostStructure023;

    @ApiModelProperty(value = "组件024成本")
    @Excel(name = "组件024成本")
    private String osCostStructure024;

    @ApiModelProperty(value = "组件025成本")
    @Excel(name = "组件025成本")
    private String osCostStructure025;

    @ApiModelProperty(value = "组件026成本")
    @Excel(name = "组件026成本")
    private String osCostStructure026;

    @ApiModelProperty(value = "组件027成本")
    @Excel(name = "组件027成本")
    private String osCostStructure027;

    @ApiModelProperty(value = "组件028成本")
    @Excel(name = "组件028成本")
    private String osCostStructure028;

    @ApiModelProperty(value = "组件029成本")
    @Excel(name = "组件029成本")
    private String osCostStructure029;

    @ApiModelProperty(value = "组件030成本")
    @Excel(name = "组件030成本")
    private String osCostStructure030;

    @ApiModelProperty(value = "组件031成本")
    @Excel(name = "组件031成本")
    private String osCostStructure031;

    @ApiModelProperty(value = "组件032成本")
    @Excel(name = "组件032成本")
    private String osCostStructure032;

    @ApiModelProperty(value = "组件033成本")
    @Excel(name = "组件033成本")
    private String osCostStructure033;

    @ApiModelProperty(value = "组件034成本")
    @Excel(name = "组件034成本")
    private String osCostStructure034;

    @ApiModelProperty(value = "组件035成本")
    @Excel(name = "组件035成本")
    private String osCostStructure035;

    @ApiModelProperty(value = "组件036成本")
    @Excel(name = "组件036成本")
    private String osCostStructure036;

    @ApiModelProperty(value = "组件037成本")
    @Excel(name = "组件037成本")
    private String osCostStructure037;

    @ApiModelProperty(value = "组件038成本")
    @Excel(name = "组件038成本")
    private String osCostStructure038;

    @ApiModelProperty(value = "组件039成本")
    @Excel(name = "组件039成本")
    private String osCostStructure039;

    @ApiModelProperty(value = "组件040成本")
    @Excel(name = "组件040成本")
    private String osCostStructure040;

    @ApiModelProperty(value = "组件041成本")
    @Excel(name = "组件041成本")
    private String osCostStructure041;

    @ApiModelProperty(value = "组件042成本")
    @Excel(name = "组件042成本")
    private String osCostStructure042;

    @ApiModelProperty(value = "组件043成本")
    @Excel(name = "组件043成本")
    private String osCostStructure043;

    @ApiModelProperty(value = "组件044成本")
    @Excel(name = "组件044成本")
    private String osCostStructure044;

    @ApiModelProperty(value = "组件045成本")
    @Excel(name = "组件045成本")
    private String osCostStructure045;

    @ApiModelProperty(value = "组件046成本")
    @Excel(name = "组件046成本")
    private String osCostStructure046;

    @ApiModelProperty(value = "组件047成本")
    @Excel(name = "组件047成本")
    private String osCostStructure047;

    @ApiModelProperty(value = "组件048成本")
    @Excel(name = "组件048成本")
    private String osCostStructure048;

    @ApiModelProperty(value = "组件049成本")
    @Excel(name = "组件049成本")
    private String osCostStructure049;

    @ApiModelProperty(value = "组件050成本")
    @Excel(name = "组件050成本")
    private String osCostStructure050;

    @ApiModelProperty(value = "组件051成本")
    @Excel(name = "组件051成本")
    private String osCostStructure051;

    @ApiModelProperty(value = "组件052成本")
    @Excel(name = "组件052成本")
    private String osCostStructure052;

    @ApiModelProperty(value = "组件053成本")
    @Excel(name = "组件053成本")
    private String osCostStructure053;

    @ApiModelProperty(value = "组件054成本")
    @Excel(name = "组件054成本")
    private String osCostStructure054;

    @ApiModelProperty(value = "组件055成本")
    @Excel(name = "组件055成本")
    private String osCostStructure055;

    @ApiModelProperty(value = "组件056成本")
    @Excel(name = "组件056成本")
    private String osCostStructure056;

    @ApiModelProperty(value = "组件057成本")
    @Excel(name = "组件057成本")
    private String osCostStructure057;

    @ApiModelProperty(value = "组件058成本")
    @Excel(name = "组件058成本")
    private String osCostStructure058;

    @ApiModelProperty(value = "组件059成本")
    @Excel(name = "组件059成本")
    private String osCostStructure059;

    @ApiModelProperty(value = "组件060成本")
    @Excel(name = "组件060成本")
    private String osCostStructure060;

    @ApiModelProperty(value = "Primary_Key")
    @Excel(name = "Primary_Key")
    private String primaryKey;

    @ApiModelProperty(value = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Excel(name = "导入时间")
    private Date fdopImportTime;
}
