package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName(schema = "dwh", value = "dwh_final_revalue_mat")
@ApiModel("定版视图-物料账记数据")
@PermissionColumn(module = "ccms")
public class DwhFinalRevalueMat {

    @ApiModelProperty(value = "数据集")
    @Excel(name = "数据集")
    private String verId;

    @ApiModelProperty(value = "值类型")
    @Excel(name = "值类型")
    private String valueType;

    @ApiModelProperty(value = "管理范围")
    @Excel(name = "管理范围")
    private String controlAreaId;

    @ApiModelProperty(value = "公司代码")
    @Excel(name = "公司代码")
    private String companyId;

    @ApiModelProperty(value = "工厂代码")
    @Excel(name = "工厂代码")
    private String plantId;

    @ApiModelProperty(value = "年度月份")
    @Excel(name = "年度月份")
    private String yearMonth;

    @ApiModelProperty(value = "年度")
    @Excel(name = "年度")
    private String year;

    @ApiModelProperty(value = "月份")
    @Excel(name = "月份")
    private String month;

    @ApiModelProperty(value = "过账日期")
    @Excel(name = "过账日期")
    private String postingDate;

    @ApiModelProperty(value = "物料凭证号")
    @Excel(name = "物料凭证号")
    private String matDoc;

    @ApiModelProperty(value = "物料凭证行")
    @Excel(name = "物料凭证行")
    private String matDocItem;

    @ApiModelProperty(value = "物料编码")
    @Excel(name = "物料编码")
    private String materialId;

    @ApiModelProperty(value = "物料编码描述")
    @Excel(name = "物料编码描述")
    private String materialDesc;

    @ApiModelProperty(value = "移动类型")
    @Excel(name = "移动类型")
    private String movementType;

    @ApiModelProperty(value = "移动标识")
    @Excel(name = "移动标识")
    private String movementFlag;

    @ApiModelProperty(value = "库存地点")
    @Excel(name = "库存地点")
    private String stockLocation;

    @ApiModelProperty(value = "用量[使用单位]")
    @Excel(name = "用量[使用单位]")
    private String usageQty;

    @ApiModelProperty(value = "使用单位")
    @Excel(name = "使用单位")
    private String usageUnit;

    @ApiModelProperty(value = "用量[基本单位]")
    @Excel(name = "用量[基本单位]")
    private String baseQty;

    @ApiModelProperty(value = "基本单位")
    @Excel(name = "基本单位")
    private String baseUnit;

    @ApiModelProperty(value = "凭证货币")
    @Excel(name = "凭证货币")
    private String amountWsl;

    @ApiModelProperty(value = "公司货币")
    @Excel(name = "公司货币")
    private String amountHsl;

    @ApiModelProperty(value = "集团货币")
    @Excel(name = "集团货币")
    private String amountKsl;

    @ApiModelProperty(value = "设备ID")
    @Excel(name = "设备ID")
    private String equipId;

    @ApiModelProperty(value = "成本中心")
    @Excel(name = "成本中心")
    private String costCenterId;

    @ApiModelProperty(value = "成本中心描述")
    @Excel(name = "成本中心描述")
    private String costCenterDesc;

    @ApiModelProperty(value = "订单")
    @Excel(name = "订单")
    private String orderId;

    @ApiModelProperty(value = "项目号码")
    @Excel(name = "项目号码")
    private String projectId;

    @ApiModelProperty(value = "项目号码描述")
    @Excel(name = "项目号码描述")
    private String projectDesc;

    @ApiModelProperty(value = "原评估类")
    @Excel(name = "原评估类")
    private String origEvaluateSortId;

    @ApiModelProperty(value = "原评估类描述")
    @Excel(name = "原评估类描述")
    private String origEvaluateSortDesc;

    @ApiModelProperty(value = "物料组")
    @Excel(name = "物料组")
    private String materialGroup;

    @ApiModelProperty(value = "评估细分")
    @Excel(name = "评估细分")
    private String evaluateSplit;

    @ApiModelProperty(value = "原科目")
    @Excel(name = "原科目")
    private String origCostElementId;

    @ApiModelProperty(value = "原科目描述")
    @Excel(name = "原科目描述")
    private String origCostElementDesc;

    @ApiModelProperty(value = "凭证头文本")
    @Excel(name = "凭证头文本")
    private String docHeadText;

    @ApiModelProperty(value = "凭证行文本")
    @Excel(name = "凭证行文本")
    private String docItemText;

    @ApiModelProperty(value = "重评估类")
    @Excel(name = "重评估类")
    private String evaluateSortId;

    @ApiModelProperty(value = "重评估类描述")
    @Excel(name = "重评估类描述")
    private String evaluateSortDesc;

    @ApiModelProperty(value = "重估成本要素")
    @Excel(name = "重估成本要素")
    private String costElementId;

    @ApiModelProperty(value = "重估成本要素描述")
    @Excel(name = "重估成本要素描述")
    private String costElementDesc;

    @ApiModelProperty(value = "成本组件")
    @Excel(name = "成本组件")
    private String costStructureId;

    @ApiModelProperty(value = "成本组件描述")
    @Excel(name = "成本组件描述")
    private String costStructureDesc;

    @ApiModelProperty(value = "作业类型")
    @Excel(name = "作业类型")
    private String activityId;

    @ApiModelProperty(value = "作业类型描述")
    @Excel(name = "作业类型描述")
    private String activityDesc;

    @ApiModelProperty(value = "评估类型")
    @Excel(name = "评估类型")
    private String evaluateType;

    @ApiModelProperty(value = "数量更新")
    @Excel(name = "数量更新")
    private String qtyUpdating;

    @ApiModelProperty(value = "价值更新")
    @Excel(name = "价值更新")
    private String valueUpdating;

    @ApiModelProperty(value = "Primary_Key")
    @Excel(name = "Primary_Key")
    private String primaryKey;

    @ApiModelProperty(value = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Excel(name = "导入时间")
    private Date fdopImportTime;

}