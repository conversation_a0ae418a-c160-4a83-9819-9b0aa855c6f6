package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName(schema = "dwh", value = "dwh_final_recover_ent")
@ApiModel("定版视图-计算事件成本")
@PermissionColumn(module = "ccms")
public class DwhFinalRecoverEnt {

    @ApiModelProperty(value = "数据集")
    @Excel(name = "数据集")
    private String verId;

    @ApiModelProperty(value = "工厂代码")
    @Excel(name = "工厂代码")
    private String plantId;

    @ApiModelProperty(value = "母批工单")
    @Excel(name = "母批工单")
    private String baseOrder;

    @ApiModelProperty(value = "工作工单")
    @Excel(name = "工作工单")
    private String workOrder;

    @ApiModelProperty(value = "母批编码")
    @Excel(name = "母批编码")
    private String baseLotId;

    @ApiModelProperty(value = "批次编码")
    @Excel(name = "批次编码")
    private String lotId;

    @ApiModelProperty(value = "事件代码")
    @Excel(name = "事件代码")
    private String eventCode;

    @ApiModelProperty(value = "日期")
    @Excel(name = "日期")
    private String date;

    @ApiModelProperty(value = "时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Excel(name = "时间戳")
    private Date timestamp;

    @ApiModelProperty(value = "历史序位")
    @Excel(name = "历史序位")
    private String historyStepNo;

    @ApiModelProperty(value = "sub_plan_seq")
    @Excel(name = "sub_plan_seq")
    private String subPlanSeq;

    @ApiModelProperty(value = "step_seq")
    @Excel(name = "step_seq")
    private String stepSeq;

    @ApiModelProperty(value = "step_no")
    @Excel(name = "step_no")
    private String stepNo;

    @ApiModelProperty(value = "返工前工序序位")
    @Excel(name = "返工前工序序位")
    private String stepNoNr;

    @ApiModelProperty(value = "数量（1）")
    @Excel(name = "数量（1）")
    private String qty1;

    @ApiModelProperty(value = "单位（1）")
    @Excel(name = "单位（1）")
    private String unit1;

    @ApiModelProperty(value = "数量（2）")
    @Excel(name = "数量（2）")
    private String qty2;

    @ApiModelProperty(value = "单位（2）")
    @Excel(name = "单位（2）")
    private String unit2;

    @ApiModelProperty(value = "旧批次类型")
    @Excel(name = "旧批次类型")
    private String oldLotType;

    @ApiModelProperty(value = "旧产品编码")
    @Excel(name = "旧产品编码")
    private String oldProductId;

    @ApiModelProperty(value = "旧产品CIM编码")
    @Excel(name = "旧产品CIM编码")
    private String oldProductCimId;

    @ApiModelProperty(value = "旧产品ERP编码")
    @Excel(name = "旧产品ERP编码")
    private String oldProductErpId;

    @ApiModelProperty(value = "批次类型")
    @Excel(name = "批次类型")
    private String lotType;

    @ApiModelProperty(value = "批次状态")
    @Excel(name = "批次状态")
    private String lotStatus;

    @ApiModelProperty(value = "产品编码")
    @Excel(name = "产品编码")
    private String productId;

    @ApiModelProperty(value = "产品CIM编码")
    @Excel(name = "产品CIM编码")
    private String productCimId;

    @ApiModelProperty(value = "产品ERP编码")
    @Excel(name = "产品ERP编码")
    private String productErpId;

    @ApiModelProperty(value = "product_ver")
    @Excel(name = "product_ver")
    private String productVer;

    @ApiModelProperty(value = "process_id")
    @Excel(name = "process_id")
    private String processId;

    @ApiModelProperty(value = "process_ver")
    @Excel(name = "process_ver")
    private String processVer;

    @ApiModelProperty(value = "sub_plan_id")
    @Excel(name = "sub_plan_id")
    private String subPlanId;

    @ApiModelProperty(value = "stage_id")
    @Excel(name = "stage_id")
    private String stageId;

    @ApiModelProperty(value = "step_id")
    @Excel(name = "step_id")
    private String stepId;

    @ApiModelProperty(value = "equip_id")
    @Excel(name = "equip_id")
    private String equipId;

    @ApiModelProperty(value = "chamber_id")
    @Excel(name = "chamber_id")
    private String chamberId;

    @ApiModelProperty(value = "equip_group_id")
    @Excel(name = "equip_group_id")
    private String equipGroupId;

    @ApiModelProperty(value = "工作单元")
    @Excel(name = "工作单元")
    private String workCenterId;

    @ApiModelProperty(value = "工作中心文本")
    @Excel(name = "工作中心文本")
    private String workCenterText;

    @ApiModelProperty(value = "成本中心")
    @Excel(name = "成本中心")
    private String costCenterId;

    @ApiModelProperty(value = "成本中心描述")
    @Excel(name = "成本中心描述")
    private String costCenterText;

    @ApiModelProperty(value = "累计成本值")
    @Excel(name = "累计成本值")
    private String cumulativeCost;

    @ApiModelProperty(value = "组件001成本")
    @Excel(name = "组件001成本")
    private String costStructure001;

    @ApiModelProperty(value = "组件002成本")
    @Excel(name = "组件002成本")
    private String costStructure002;

    @ApiModelProperty(value = "组件003成本")
    @Excel(name = "组件003成本")
    private String costStructure003;

    @ApiModelProperty(value = "组件004成本")
    @Excel(name = "组件004成本")
    private String costStructure004;

    @ApiModelProperty(value = "组件005成本")
    @Excel(name = "组件005成本")
    private String costStructure005;

    @ApiModelProperty(value = "组件006成本")
    @Excel(name = "组件006成本")
    private String costStructure006;

    @ApiModelProperty(value = "组件007成本")
    @Excel(name = "组件007成本")
    private String costStructure007;

    @ApiModelProperty(value = "组件008成本")
    @Excel(name = "组件008成本")
    private String costStructure008;

    @ApiModelProperty(value = "组件009成本")
    @Excel(name = "组件009成本")
    private String costStructure009;

    @ApiModelProperty(value = "组件010成本")
    @Excel(name = "组件010成本")
    private String costStructure010;

    @ApiModelProperty(value = "组件011成本")
    @Excel(name = "组件011成本")
    private String costStructure011;

    @ApiModelProperty(value = "组件012成本")
    @Excel(name = "组件012成本")
    private String costStructure012;

    @ApiModelProperty(value = "组件013成本")
    @Excel(name = "组件013成本")
    private String costStructure013;

    @ApiModelProperty(value = "组件014成本")
    @Excel(name = "组件014成本")
    private String costStructure014;

    @ApiModelProperty(value = "组件015成本")
    @Excel(name = "组件015成本")
    private String costStructure015;

    @ApiModelProperty(value = "组件016成本")
    @Excel(name = "组件016成本")
    private String costStructure016;

    @ApiModelProperty(value = "组件017成本")
    @Excel(name = "组件017成本")
    private String costStructure017;

    @ApiModelProperty(value = "组件018成本")
    @Excel(name = "组件018成本")
    private String costStructure018;

    @ApiModelProperty(value = "组件019成本")
    @Excel(name = "组件019成本")
    private String costStructure019;

    @ApiModelProperty(value = "组件020成本")
    @Excel(name = "组件020成本")
    private String costStructure020;

    @ApiModelProperty(value = "组件021成本")
    @Excel(name = "组件021成本")
    private String costStructure021;

    @ApiModelProperty(value = "组件022成本")
    @Excel(name = "组件022成本")
    private String costStructure022;

    @ApiModelProperty(value = "组件023成本")
    @Excel(name = "组件023成本")
    private String costStructure023;

    @ApiModelProperty(value = "组件024成本")
    @Excel(name = "组件024成本")
    private String costStructure024;

    @ApiModelProperty(value = "组件025成本")
    @Excel(name = "组件025成本")
    private String costStructure025;

    @ApiModelProperty(value = "组件026成本")
    @Excel(name = "组件026成本")
    private String costStructure026;

    @ApiModelProperty(value = "组件027成本")
    @Excel(name = "组件027成本")
    private String costStructure027;

    @ApiModelProperty(value = "组件028成本")
    @Excel(name = "组件028成本")
    private String costStructure028;

    @ApiModelProperty(value = "组件029成本")
    @Excel(name = "组件029成本")
    private String costStructure029;

    @ApiModelProperty(value = "组件030成本")
    @Excel(name = "组件030成本")
    private String costStructure030;

    @ApiModelProperty(value = "组件031成本")
    @Excel(name = "组件031成本")
    private String costStructure031;

    @ApiModelProperty(value = "组件032成本")
    @Excel(name = "组件032成本")
    private String costStructure032;

    @ApiModelProperty(value = "组件033成本")
    @Excel(name = "组件033成本")
    private String costStructure033;

    @ApiModelProperty(value = "组件034成本")
    @Excel(name = "组件034成本")
    private String costStructure034;

    @ApiModelProperty(value = "组件035成本")
    @Excel(name = "组件035成本")
    private String costStructure035;

    @ApiModelProperty(value = "组件036成本")
    @Excel(name = "组件036成本")
    private String costStructure036;

    @ApiModelProperty(value = "组件037成本")
    @Excel(name = "组件037成本")
    private String costStructure037;

    @ApiModelProperty(value = "组件038成本")
    @Excel(name = "组件038成本")
    private String costStructure038;

    @ApiModelProperty(value = "组件039成本")
    @Excel(name = "组件039成本")
    private String costStructure039;

    @ApiModelProperty(value = "组件040成本")
    @Excel(name = "组件040成本")
    private String costStructure040;

    @ApiModelProperty(value = "组件041成本")
    @Excel(name = "组件041成本")
    private String costStructure041;

    @ApiModelProperty(value = "组件042成本")
    @Excel(name = "组件042成本")
    private String costStructure042;

    @ApiModelProperty(value = "组件043成本")
    @Excel(name = "组件043成本")
    private String costStructure043;

    @ApiModelProperty(value = "组件044成本")
    @Excel(name = "组件044成本")
    private String costStructure044;

    @ApiModelProperty(value = "组件045成本")
    @Excel(name = "组件045成本")
    private String costStructure045;

    @ApiModelProperty(value = "组件046成本")
    @Excel(name = "组件046成本")
    private String costStructure046;

    @ApiModelProperty(value = "组件047成本")
    @Excel(name = "组件047成本")
    private String costStructure047;

    @ApiModelProperty(value = "组件048成本")
    @Excel(name = "组件048成本")
    private String costStructure048;

    @ApiModelProperty(value = "组件049成本")
    @Excel(name = "组件049成本")
    private String costStructure049;

    @ApiModelProperty(value = "组件050成本")
    @Excel(name = "组件050成本")
    private String costStructure050;

    @ApiModelProperty(value = "组件051成本")
    @Excel(name = "组件051成本")
    private String costStructure051;

    @ApiModelProperty(value = "组件052成本")
    @Excel(name = "组件052成本")
    private String costStructure052;

    @ApiModelProperty(value = "组件053成本")
    @Excel(name = "组件053成本")
    private String costStructure053;

    @ApiModelProperty(value = "组件054成本")
    @Excel(name = "组件054成本")
    private String costStructure054;

    @ApiModelProperty(value = "组件055成本")
    @Excel(name = "组件055成本")
    private String costStructure055;

    @ApiModelProperty(value = "组件056成本")
    @Excel(name = "组件056成本")
    private String costStructure056;

    @ApiModelProperty(value = "组件057成本")
    @Excel(name = "组件057成本")
    private String costStructure057;

    @ApiModelProperty(value = "组件058成本")
    @Excel(name = "组件058成本")
    private String costStructure058;

    @ApiModelProperty(value = "组件059成本")
    @Excel(name = "组件059成本")
    private String costStructure059;

    @ApiModelProperty(value = "组件060成本")
    @Excel(name = "组件060成本")
    private String costStructure060;

    @ApiModelProperty(value = "Primary_Key")
    @Excel(name = "Primary_Key")
    private String primaryKey;

    @ApiModelProperty(value = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Excel(name = "导入时间")
    private Date fdopImportTime;
    
}