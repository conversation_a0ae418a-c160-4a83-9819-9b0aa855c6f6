package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName(schema = "dwd", value = "dwd_plan_eid_exp")
@ApiModel("逻辑视图-设备编码费用计划")
@PermissionColumn(module = "ccms")
public class DwdPlanEidExp {

    @ApiModelProperty(value = "数据集")
    @Excel(name = "数据集")
    private String verId;

    @ApiModelProperty(value = "值类型")
    @Excel(name = "值类型")
    private String valueType;

    @ApiModelProperty(value = "管理范围")
    @Excel(name = "管理范围")
    private String controlAreaId;

    @ApiModelProperty(value = "公司代码")
    @Excel(name = "公司代码")
    private String companyId;

    @ApiModelProperty(value = "工厂代码")
    @Excel(name = "工厂代码")
    private String plantId;

    @ApiModelProperty(value = "币别")
    @Excel(name = "币别")
    private String currency;

    @ApiModelProperty(value = "年度月份")
    @Excel(name = "年度月份")
    private String yearMonth;

    @ApiModelProperty(value = "年度")
    @Excel(name = "年度")
    private String year;

    @ApiModelProperty(value = "月份")
    @Excel(name = "月份")
    private String month;

    @ApiModelProperty(value = "设备ID")
    @Excel(name = "设备ID")
    private String equipId;

    @ApiModelProperty(value = "设备ID描述")
    @Excel(name = "设备ID描述")
    private String equipDesc;

    @ApiModelProperty(value = "成本中心")
    @Excel(name = "成本中心")
    private String costCenterId;

    @ApiModelProperty(value = "成本中心描述")
    @Excel(name = "成本中心描述")
    private String costCenterDesc;

    @ApiModelProperty(value = "原科目")
    @Excel(name = "原科目")
    private String origCostElementId;

    @ApiModelProperty(value = "原科目描述")
    @Excel(name = "原科目描述")
    private String origCostElementDesc;

    @ApiModelProperty(value = "重估成本要素")
    @Excel(name = "重估成本要素")
    private String revalueElementId;

    @ApiModelProperty(value = "重估成本要素描述")
    @Excel(name = "重估成本要素描述")
    private String revalueElementDesc;

    @ApiModelProperty(value = "成本要素")
    @Excel(name = "成本要素")
    private String costElementId;

    @ApiModelProperty(value = "成本要素描述")
    @Excel(name = "成本要素描述")
    private String costElementDesc;

    @ApiModelProperty(value = "货币值")
    @Excel(name = "货币值")
    private String amount;

    @ApiModelProperty(value = "成本组件")
    @Excel(name = "成本组件")
    private String costStructureId;

    @ApiModelProperty(value = "成本组件描述")
    @Excel(name = "成本组件描述")
    private String costStructureDesc;

    @ApiModelProperty(value = "作业类型")
    @Excel(name = "作业类型")
    private String activityId;

    @ApiModelProperty(value = "作业类型描述")
    @Excel(name = "作业类型描述")
    private String activityDesc;

    @ApiModelProperty(value = "Primary_Key")
    @Excel(name = "Primary_Key")
    private String primaryKey;

    @ApiModelProperty(value = "导入时间")
    @Excel(name = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date fdopImportTime;










}
