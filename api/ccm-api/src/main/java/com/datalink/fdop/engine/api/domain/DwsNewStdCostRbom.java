package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@TableName(schema = "dws", value = "dws_new_std_cost_rbom")
@ApiModel("计划成本-STEP成本管理")
@PermissionColumn(module = "ccms")
public class DwsNewStdCostRbom {

    @ApiModelProperty(value = "STEP核算号")
    @Excel(name = "STEP核算号")
    private String costCode;

    @ApiModelProperty(value = "数据集")
    @NotBlank(message = "数据集不能为空")
    @Excel(name = "数据集")
    private String verId;

    @ApiModelProperty(value = "管理范围")
    @NotBlank(message = "管理范围不能为空")
    @Excel(name = "管理范围")
    private String controlAreaId;

    @ApiModelProperty(value = "工厂代码")
    @NotBlank(message = "工厂代码不能为空")
    @Excel(name = "工厂代码")
    private String plantId;

    @ApiModelProperty(value = "有效起始日期")
    @Excel(name = "有效起始日期")
    private String effectiveDate;

    @ApiModelProperty(value = "评估基于日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @Excel(name = "评估基于日期")
    private Date baseDate;

    @ApiModelProperty(value = "运算批量")
    @NotNull(message = "运算批量不能为空")
    @Excel(name = "运算批量")
    private Long batchQty;

    @ApiModelProperty(value = "运算时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Excel(name = "运算时间戳")
    private Date timestamp;

    @ApiModelProperty(value = "[主]产品编码")
    @NotBlank(message = "[主]产品编码不能为空")
    @Excel(name = "[主]产品编码")
    private String productId;

    @ApiModelProperty(value = "[主]产品CIM编码")
    @Excel(name = "[主]产品CIM编码")
    private String productCimId;

    @ApiModelProperty(value = "[主]产品ERP编码")
    @Excel(name = "[主]产品ERP编码")
    private String productErpId;

    @ApiModelProperty(value = "[主]产品描述")
    @Excel(name = "[主]产品描述")
    private String productDesc;

    @ApiModelProperty(value = "[主]基本数量")
    @Excel(name = "[主]基本数量")
    private String cbomBaseQty;

    @ApiModelProperty(value = "[主]基本单位")
    @Excel(name = "[主]基本单位")
    private String cbomBaseUnit;

    @ApiModelProperty(value = "组内层级")
    @Excel(name = "组内层级")
    private Long hierarchy;

    @ApiModelProperty(value = "层内序号")
    @Excel(name = "层内序号")
    private Long hierarchyInnerSeq;

    @ApiModelProperty(value = "[子]产品编码")
    @Excel(name = "[子]产品编码")
    private String productSn;

    @ApiModelProperty(value = "[子]产品CIM编码")
    @Excel(name = "[子]产品CIM编码")
    private String productCimSn;

    @ApiModelProperty(value = "[子]产品ERP编码")
    @Excel(name = "[子]产品ERP编码")
    private String productErpSn;

    @ApiModelProperty(value = "[子]产品描述")
    @Excel(name = "[子]产品描述")
    private String productSnDesc;

    @ApiModelProperty(value = "[子]基本数量")
    @Excel(name = "[子]基本数量")
    private String cbomBaseQtySn;

    @ApiModelProperty(value = "[子]基本单位")
    @Excel(name = "[子]基本单位")
    private String cbomBaseUnitSn;

    @ApiModelProperty(value = "Step_NO")
    @Excel(name = "Step_NO")
    private Long stepNo;

    @ApiModelProperty(value = "Step_Cost_Flag")
    @Excel(name = "Step_Cost_Flag")
    private String stepCostFlag;

    @ApiModelProperty(value = "原料编码")
    @NotBlank(message = "原料编码不能为空")
    @Excel(name = "原料编码")
    private String rawMaterialId;

    @ApiModelProperty(value = "原料描述")
    @Excel(name = "原料描述")
    private String rawMaterialDesc;

    @ApiModelProperty(value = "原料用量")
    @Excel(name = "原料用量")
    private String qty;

    @ApiModelProperty(value = "货币值")
    @Excel(name = "货币值")
    private String amount;

    @ApiModelProperty(value = "成本组件")
    @Excel(name = "成本组件")
    private String costStructureId;

    @ApiModelProperty(value = "组件描述")
    @Excel(name = "组件描述")
    private String costStructureDesc;

    @ApiModelProperty(value = "Product_ver")
    @Excel(name = "Product_ver")
    private Long productVer;

    @ApiModelProperty(value = "Process_id")
    @Excel(name = "Process_id")
    private String processId;

    @ApiModelProperty(value = "Process_ver")
    @Excel(name = "Process_ver")
    private Long processVer;

    @ApiModelProperty(value = "Sub_Plan_Seq")
    @Excel(name = "Sub_Plan_Seq")
    private String subPlanSeq;

    @ApiModelProperty(value = "Step_Seq")
    @Excel(name = "Step_Seq")
    private String stepSeq;

    @ApiModelProperty(value = "Sub_Plan_ID")
    @Excel(name = "Sub_Plan_ID")
    private String subPlanId;

    @ApiModelProperty(value = "Layer_ID")
    @Excel(name = "Layer_ID")
    private String layerId;

    @ApiModelProperty(value = "Stage_ID")
    @Excel(name = "Stage_ID")
    private String stageId;

    @ApiModelProperty(value = "Step_ID")
    @Excel(name = "Step_ID")
    private String stepId;

    @ApiModelProperty(value = "Step_Desc")
    @Excel(name = "Step_Desc")
    private String stepDesc;

    @ApiModelProperty(value = "Step_Type")
    @Excel(name = "Step_Type")
    private String stepType;

    @ApiModelProperty(value = "Recipe_ID")
    @Excel(name = "Recipe_ID")
    private String recipeId;

    @ApiModelProperty(value = "Equip_Group_ID")
    @Excel(name = "Equip_Group_ID")
    private String equipGroupId;

    @ApiModelProperty(value = "Work_Area")
    @Excel(name = "Work_Area")
    private String workArea;

    @ApiModelProperty(value = "Rwk_Sub_Plan_ID")
    @Excel(name = "Rwk_Sub_Plan_ID")
    private String rwkSubPlanId;

    @ApiModelProperty(value = "Equip_Group_Flag")
    @Excel(name = "Equip_Group_Flag")
    private String equipGroupFlag;

    @ApiModelProperty(value = "Outsourcing_Flag")
    @Excel(name = "Outsourcing_Flag")
    private String outsourcingFlag;

    @ApiModelProperty(value = "工作中心")
    @Excel(name = "工作中心")
    private String workCenterId;

    @ApiModelProperty(value = "工作中心文本")
    @Excel(name = "工作中心文本")
    private String workCenterText;

    @ApiModelProperty(value = "成本中心")
    @Excel(name = "成本中心")
    private String costCenterId;

    @ApiModelProperty(value = "成本中心描述")
    @Excel(name = "成本中心描述")
    private String costCenterText;

    @ApiModelProperty(value = "Flow基本数量")
    @Excel(name = "Flow基本数量")
    private String flowBaseQty;

    @ApiModelProperty(value = "Flow基本单位")
    @Excel(name = "Flow基本单位")
    private String flowBaseUnit;

    @ApiModelProperty(value = "工序数量[1]")
    @Excel(name = "工序数量[1]")
    private String operQty1;

    @ApiModelProperty(value = "工序单位[1]")
    @Excel(name = "工序单位[1]")
    private String operUnit1;

    @ApiModelProperty(value = "RBOM原料数量[使用单位]")
    @Excel(name = "RBOM原料数量[使用单位]")
    private String rbomUsageQtySn;

    @ApiModelProperty(value = "RBOM原料使用单位")
    @Excel(name = "RBOM原料使用单位")
    private String rbomUsageUnitSn;

    @ApiModelProperty(value = "RBOM原料数量[基本单位]")
    @Excel(name = "RBOM原料数量[基本单位]")
    private String rbomBaseQtySn;

    @ApiModelProperty(value = "RBOM原料基本单位")
    @Excel(name = "RBOM原料基本单位")
    private String rbomBaseUnitSn;

    @ApiModelProperty(value = "RBOM单价")
    @Excel(name = "RBOM单价")
    private String unitPrice;

    @ApiModelProperty(value = "Primary_Key")
    @Excel(name = "Primary_Key")
    private String primaryKey;

    @ApiModelProperty(value = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Excel(name = "导入时间")
    private Date fdopImportTime;
}