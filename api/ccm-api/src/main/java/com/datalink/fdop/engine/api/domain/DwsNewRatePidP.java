package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName(schema = "dws", value = "dws_new_rate_pid_p")
@ApiModel("计划成本分摊管理-逻辑视图-费率计算-专用物料")
@PermissionColumn(module = "ccms")
public class DwsNewRatePidP {

    @ApiModelProperty(value = "数据集")
    @Excel(name = "数据集")
    private String verId;

    @ApiModelProperty(value = "值类型")
    @Excel(name = "值类型")
    private String valueType;

    @ApiModelProperty(value = "管理范围")
    @Excel(name = "管理范围")
    private String controlAreaId;

    @ApiModelProperty(value = "年度月份")
    @Excel(name = "年度月份")
    private String yearMonth;

    @ApiModelProperty(value = "年度")
    @Excel(name = "年度")
    private String year;

    @ApiModelProperty(value = "月份")
    @Excel(name = "月份")
    private String month;

    @ApiModelProperty(value = "工厂代码")
    @Excel(name = "工厂代码")
    private String plantId;

    @ApiModelProperty(value = "专用类型")
    @Excel(name = "专用类型")
    private String specialType;

    @ApiModelProperty(value = "专用物料")
    @Excel(name = "专用物料")
    private String rawMaterialId;

    @ApiModelProperty(value = "专用物料描述")
    @Excel(name = "专用物料描述")
    private String rawMaterialDesc;

    @ApiModelProperty(value = "成本中心")
    @Excel(name = "成本中心")
    private String costCenterId;

    @ApiModelProperty(value = "成本中心描述")
    @Excel(name = "成本中心描述")
    private String costCenterDesc;

    @ApiModelProperty(value = "货币值")
    @Excel(name = "货币值")
    private String amount;

    @ApiModelProperty(value = "产品编码")
    @Excel(name = "产品编码")
    private String productId;

    @ApiModelProperty(value = "产品编码描述")
    @Excel(name = "产品编码描述")
    private String productDesc;

    @ApiModelProperty(value = "统计因子")
    @Excel(name = "统计因子")
    private String factorId;

    @ApiModelProperty(value = "统计因子描述")
    @Excel(name = "统计因子描述")
    private String factorDesc;

    @ApiModelProperty(value = "公式")
    @Excel(name = "公式")
    private String formula;

    @ApiModelProperty(value = "统计因子单位")
    @Excel(name = "统计因子单位")
    private String factorUnit;

    @ApiModelProperty(value = "统计因子量")
    @Excel(name = "统计因子量")
    private String factorQty;

    @ApiModelProperty(value = "因子费率")
    @Excel(name = "因子费率")
    private String unitPrice;

    @ApiModelProperty(value = "Primary_Key")
    @Excel(name = "Primary_Key")
    private String primaryKey;

    @ApiModelProperty(value = "导入时间")
    @Excel(name = "导入时间")
    private Date fdopImportTime;
    
}