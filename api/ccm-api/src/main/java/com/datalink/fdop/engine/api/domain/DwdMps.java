package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
@TableName(schema = "dwd", value = "dwd_mps")
@ApiModel("MPS整合管理-逻辑视图")
@PermissionColumn(module = "ccms")
public class DwdMps {

    @ApiModelProperty(value = "数据集")
    @NotBlank(message = "数据集不能为空")
    @Excel(name = "数据集")
    private String verId;

    @ApiModelProperty(value = "工厂代码")
    @NotBlank(message = "工厂代码不能为空")
    @Excel(name = "工厂代码")
    private String plantId;

    @ApiModelProperty(value = "年度")
    @NotBlank(message = "年度不能为空")
    @Excel(name = "年度")
    private String mpsYear;

    @ApiModelProperty(value = "月份")
    @NotBlank(message = "月份不能为空")
    @Excel(name = "月份")
    private String mpsMonth;

    @ApiModelProperty(value = "产品编码")
    @NotBlank(message = "产品编码不能为空")
    @Excel(name = "产品编码")
    private String productId;

    @ApiModelProperty(value = "产品描述")
    @Excel(name = "产品描述")
    private String productDesc;

    @ApiModelProperty(value = "产品CIM编码")
    @Excel(name = "产品CIM编码")
    private String productCimId;

    @ApiModelProperty(value = "产品ERP编码")
    @Excel(name = "产品ERP编码")
    private String productErpId;

    @ApiModelProperty(value = "MPS代表产品")
    @Excel(name = "MPS代表产品")
    private String productMpsId;

    @ApiModelProperty(value = "基本单位")
    @Excel(name = "基本单位")
    private String baseUnit;

    @ApiModelProperty(value = "计划类型")
    @NotBlank(message = "计划类型不能为空")
    @Excel(name = "计划类型")
    private String mpsType;

    @ApiModelProperty(value = "计划投入")
    @Excel(name = "计划投入")
    private String mpsIn;

    @ApiModelProperty(value = "计划产出")
    @Excel(name = "计划产出")
    private String mpsOut;

    @ApiModelProperty(value = "产品良率")
    @Excel(name = "产品良率")
    private String yieldIo;

    @ApiModelProperty(value = "代表产品良率")
    @Excel(name = "代表产品良率")
    private String yieldTy;

    @ApiModelProperty(value = "备注")
    @Excel(name = "备注")
    private String mpsRemark;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createTime;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date updateTime;

    @ApiModelProperty(value = "导入时间")
    @Excel(name = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date fdopImportTime;











}
