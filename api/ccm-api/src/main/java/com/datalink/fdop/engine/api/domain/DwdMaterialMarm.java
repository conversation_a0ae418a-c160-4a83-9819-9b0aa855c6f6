package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
@TableName(schema = "dwd", value = "dwd_material_marm")
@ApiModel("物料换算比例")
@PermissionColumn(module = "ccms")
public class DwdMaterialMarm {

    @ApiModelProperty(value = "物料编码")
    @NotBlank(message = "物料编码不能为空")
    @Excel(name = "物料编码")
    private String materialId;

    @ApiModelProperty(value = "物料描述")
    @Excel(name = "物料描述")
    private String materialDesc;

    @ApiModelProperty(value = "比率（使用单位）")
    @Excel(name = "比率（使用单位）")
    private String numerator;

    @ApiModelProperty(value = "基本单位")
    @Excel(name = "基本单位")
    private String baseUnit;

    @ApiModelProperty(value = "比率（基本单位）")
    @Excel(name = "比率（基本单位）")
    private String denominator;

    @ApiModelProperty(value = "使用单位")
    @NotBlank(message = "使用单位不能为空")
    @Excel(name = "使用单位")
    private String usageUnit;

    @ApiModelProperty(value = "物料类型")
    @Excel(name = "物料类型")
    private String materialType;

    @ApiModelProperty(value = "物料类型描述")
    @Excel(name = "物料类型描述")
    private String materialTypeDesc;

    @ApiModelProperty(value = "物料组")
    @Excel(name = "物料组")
    private String materialGroup;

    @ApiModelProperty(value = "物料组描述")
    @Excel(name = "物料组描述")
    private String materialGroupDesc;

    @ApiModelProperty(value = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Excel(name = "导入时间")
    private Date fdopImportTime;




}
