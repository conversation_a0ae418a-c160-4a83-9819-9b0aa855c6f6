package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.datalink.fdop.common.core.annotation.SearchField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@TableName(schema = "dws", value = "dws_chg_flow")
@ApiModel("FLOW比较视图")
@PermissionColumn(module = "ccms")
public class DwsChgFlow {

    @Excel(name = "标志")
    @ApiModelProperty(value = "对比")
    private String diffFlag;

    @Excel(name = "数据集")
    @ApiModelProperty(value = "数据集")
    @NotBlank(message = "数据集不能为空")
    private String verId;

    @Excel(name = "工厂代码")
    @ApiModelProperty(value = "工厂代码")
    @NotBlank(message = "工厂代码不能为空")
    @SearchField(value = "工厂代码")
    private String plantId;

    @Excel(name = "制造工厂")
    @ApiModelProperty(value = "制造工厂")
    private String factoryId;

    @Excel(name = "产品编码")
    @ApiModelProperty(value = "产品编码")
    @NotBlank(message = "产品编码不能为空")
    @SearchField(value = "产品编码")
    private String productId;

    @Excel(name = "产品描述")
    @ApiModelProperty(value = "产品描述")
    private String productDesc;

    @Excel(name = "产品CIM编码")
    @ApiModelProperty(value = "产品CIM编码")
    @NotBlank(message = "产品CIM编码不能为空")
    private String productCimId;

    @Excel(name = "产品ERP编码")
    @ApiModelProperty(value = "产品ERP编码")
    private String productErpId;

    @Excel(name = "CIM识别码")
    @ApiModelProperty(value = "CIM识别码")
    private String flowCimCode;

    @Excel(name = "计数器")
    @ApiModelProperty(value = "计数器")
    private String count;

    @Excel(name = "产品阶段")
    @ApiModelProperty(value = "产品阶段")
    private String techPhase;

    @Excel(name = "Step_NO")
    @ApiModelProperty(value = "Step_NO")
    @NotNull(message = "step_no不能为空")
    private String stepNo;

    @Excel(name = "Product_Ver")
    @ApiModelProperty(value = "Product_Ver")
    @NotNull(message = "product_ver不能为空")
    private String productVer;

    @Excel(name = "Process_ID")
    @ApiModelProperty(value = "Process_ID")
    @NotBlank(message = "process_id不能为空")
    private String processId;

    @Excel(name = "Process_Ver")
    @ApiModelProperty(value = "Process_Ver")
    @NotNull(message = "process_ver不能为空")
    private String processVer;

    @Excel(name = "Step_ID")
    @ApiModelProperty(value = "Step_ID")
    private String stepId;

    @Excel(name = "Step_Desc")
    @ApiModelProperty(value = "Step_Desc")
    private String stepDesc;

    @Excel(name = "Step_Type")
    @ApiModelProperty(value = "Step_Type")
    private String stepType;

    @Excel(name = "Recipe_ID")
    @ApiModelProperty(value = "Recipe_ID")
    private String recipeId;

    @Excel(name = "Equip_Group_ID")
    @ApiModelProperty(value = "Equip_Group_ID")
    private String equipGroupId;

    @Excel(name = "Work_Area")
    @ApiModelProperty(value = "Work_Area")
    private String workArea;

    @Excel(name = "Rwk_Sub_Plan_ID")
    @ApiModelProperty(value = "Rwk_Sub_Plan_ID")
    private String rwkSubPlanId;

    @Excel(name = "Oper_Spt")
    @ApiModelProperty(value = "Oper_Spt")
    private String operSpt;

    @Excel(name = "Spt_Unit")
    @ApiModelProperty(value = "Spt_Unit")
    private String sptUnit;

    @Excel(name = "Equip_Group_Flag")
    @ApiModelProperty(value = "Equip_Group_Flag")
    private String equipGroupFlag;

    @Excel(name = "Outsourcing_Flag")
    @ApiModelProperty(value = "Outsourcing_Flag")
    private String outsourcingFlag;

    @Excel(name = "CIM识别码")
    @ApiModelProperty(value = "CIM识别码")
    private String fFlowCimCode;

    @Excel(name = "计数器")
    @ApiModelProperty(value = "计数器")
    private String fcount;

    @Excel(name = "F_产品阶段")
    @ApiModelProperty(value = "F_产品阶段")
    private String ftechPhase;

    @Excel(name = "F_Step_NO")
    @ApiModelProperty(value = "F_Step_NO")
    private String fstepNo;

    @Excel(name = "F_Product_Ver")
    @ApiModelProperty(value = "F_Product_Ver")
    private String fproductVer;

    @Excel(name = "F_Process_ID")
    @ApiModelProperty(value = "F_Process_ID")
    private String fprocessId;

    @Excel(name = "F_Process_Ver")
    @ApiModelProperty(value = "F_Process_Ver")
    private String fprocessVer;

    @Excel(name = "F_Step_ID")
    @ApiModelProperty(value = "F_Step_ID")
    private String fstepId;

    @Excel(name = "F_Step_Desc")
    @ApiModelProperty(value = "F_Step_Desc")
    private String fstepDesc;

    @Excel(name = "F_Step_Type")
    @ApiModelProperty(value = "F_Step_Type")
    private String fstepType;

    @Excel(name = "F_Recipe_ID")
    @ApiModelProperty(value = "F_Recipe_ID")
    private String frecipeId;

    @Excel(name = "F_Equip_Group_ID")
    @ApiModelProperty(value = "F_Equip_Group_ID")
    private String fequipGroupId;

    @Excel(name = "F_Work_Area")
    @ApiModelProperty(value = "F_Work_Area")
    private String fworkArea;

    @Excel(name = "F_Rwk_Sub_Plan_ID")
    @ApiModelProperty(value = "F_Rwk_Sub_Plan_ID")
    private String frwkSubPlanId;

    @Excel(name = "F_Oper_Spt")
    @ApiModelProperty(value = "F_Oper_Spt")
    private String foperSpt;

    @Excel(name = "F_Spt_Unit")
    @ApiModelProperty(value = "F_Spt_Unit")
    private String fsptUnit;

    @Excel(name = "F_Equip_Group_Flag")
    @ApiModelProperty(value = "F_Equip_Group_Flag")
    private String fequipGroupFlag;

    @Excel(name = "F_OutSourcing_Flag")
    @ApiModelProperty(value = "F_OutSourcing_Flag")
    private String foutsourcingFlag;

    @Excel(name = "导入时间")
    @ApiModelProperty(value = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date fdopImportTime;

}
