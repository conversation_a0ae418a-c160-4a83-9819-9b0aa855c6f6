package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
@TableName(schema = "dwd", value = "dwd_purchase_price")
@ApiModel("采购价格信息")
@PermissionColumn(module = "ccms")
public class DwdPurchasePrice {

    @ApiModelProperty(value = "物料编码")
    @NotBlank(message = "物料编码不能为空")
    @Excel(name = "物料编码")
    private String materialId;

    @ApiModelProperty(value = "物料描述")
    @Excel(name = "物料描述")
    private String materialDesc;

    @ApiModelProperty(value = "物料文本")
    @Excel(name = "物料文本")
    private String materialText;

    @ApiModelProperty(value = "信息记录类型")
    @NotBlank(message = "信息记录类型不能为空")
    @Excel(name = "信息记录类型")
    private String priceType;

    @ApiModelProperty(value = "工厂代码")
    @NotBlank(message = "工厂代码不能为空")
    @Excel(name = "工厂代码")
    private String plantId;

    @ApiModelProperty(value = "采购组织")
    @NotBlank(message = "采购组织不能为空")
    @Excel(name = "采购组织")
    private String purchaseOrg;

    @ApiModelProperty(value = "供应商")
    @NotBlank(message = "供应商不能为空")
    @Excel(name = "供应商")
    private String vendor;

    @ApiModelProperty(value = "采购价格")
    @Excel(name = "采购价格")
    private String price;

    @ApiModelProperty(value = "货币")
    @Excel(name = "货币")
    private String currency;

    @ApiModelProperty(value = "定价数量")
    @Excel(name = "定价数量")
    private String priceBasis;

    @ApiModelProperty(value = "定价单位")
    @Excel(name = "定价单位")
    private String usageUnit;

    @ApiModelProperty(value = "分子")
    @Excel(name = "分子")
    private String numerator;

    @ApiModelProperty(value = "分母")
    @Excel(name = "分母")
    private String denominator;

    @ApiModelProperty(value = "基本单位")
    @Excel(name = "基本单位")
    private String baseUnit;

    @ApiModelProperty(value = "有效期从")
    @Excel(name = "有效期从")
    private String datefr;

    @ApiModelProperty(value = "有效期至")
    @NotBlank(message = "有效期至不能为空")
    @Excel(name = "有效期至")
    private String dateto;

    @ApiModelProperty(value = "物料类型")
    @Excel(name = "物料类型")
    private String materialType;

    @ApiModelProperty(value = "物料类型描述")
    @Excel(name = "物料类型描述")
    private String materialTypeDesc;

    @ApiModelProperty(value = "物料组")
    @Excel(name = "物料组")
    private String materialGroup;

    @ApiModelProperty(value = "物料组描述")
    @Excel(name = "物料组描述")
    private String materialGroupDesc;

    @ApiModelProperty(value = "条件记录编号")
    @Excel(name = "条件记录编号")
    private String purchaseInfo;

    @ApiModelProperty(value = "条件序号")
    @Excel(name = "条件序号")
    private String purchaseInfoSeq;

    @ApiModelProperty(value = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Excel(name = "导入时间")
    private Date fdopImportTime;









}
