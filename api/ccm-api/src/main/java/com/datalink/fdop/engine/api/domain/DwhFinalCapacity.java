package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
@TableName(schema = "dwh", value = "dwh_final_capacity")
@ApiModel("产能整合单元")
@PermissionColumn(module = "ccms")
public class DwhFinalCapacity {

    @ApiModelProperty(value = "数据集")
    @NotBlank(message = "数据集不能为空")
    @Excel(name = "数据集")
    private String verId;

    @ApiModelProperty(value = "工厂代码")
    @Excel(name = "工厂代码")
    private String plantId;

    @ApiModelProperty(value = "制造工厂")
    @Excel(name = "制造工厂")
    private String factoryId;

    @ApiModelProperty(value = "产能单元")
    @Excel(name = "产能单元")
    private String capacityId;

    @ApiModelProperty(value = "设备ID")
    @Excel(name = "设备ID")
    private String equipId;

    @ApiModelProperty(value = "腔体ID")
    @Excel(name = "腔体ID")
    private String chamberId;

    @ApiModelProperty(value = "有效期从")
    @Excel(name = "有效期从")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date dateFrom;

    @ApiModelProperty(value = "有效期至")
    @Excel(name = "有效期至")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date dateTo;

    @ApiModelProperty(value = "设备描述")
    @Excel(name = "设备描述")
    private String equipDesc;

    @ApiModelProperty(value = "设备组ID")
    @Excel(name = "设备组ID")
    private String equipGroupId;

    @ApiModelProperty(value = "设备组描述")
    @Excel(name = "设备组描述")
    private String equipGroupDesc;

    @ApiModelProperty(value = "工作中心")
    @Excel(name = "工作中心")
    private String workCenterId;

    @ApiModelProperty(value = "工作中心描述")
    @Excel(name = "工作中心描述")
    private String workCenterDesc;

    @ApiModelProperty(value = "成本中心")
    @Excel(name = "成本中心")
    private String costCenterId;

    @ApiModelProperty(value = "成本中心描述")
    @Excel(name = "成本中心描述")
    private String costCenterDesc;

    @ApiModelProperty(value = "Uptime")
    @Excel(name = "Uptime")
    private String attrUpt;

    @ApiModelProperty(value = "Effciency")
    @Excel(name = "Effciency")
    private String attrEff;

    @ApiModelProperty(value = "Utilization")
    @Excel(name = "Utilization")
    private String attrUtil;

    @ApiModelProperty(value = "OEE")
    @Excel(name = "OEE")
    private String attrOee;

    @ApiModelProperty(value = "TEEP")
    @Excel(name = "TEEP")
    private String attrTeep;

    @ApiModelProperty(value = "属性描述")
    @Excel(name = "属性描述")
    private String attr001;

    @ApiModelProperty(value = "属性描述")
    @Excel(name = "属性描述")
    private String attr002;

    @ApiModelProperty(value = "属性描述")
    @Excel(name = "属性描述")
    private String attr003;

    @ApiModelProperty(value = "属性描述")
    @Excel(name = "属性描述")
    private String attr004;

    @ApiModelProperty(value = "属性描述")
    @Excel(name = "属性描述")
    private String attr005;

    @ApiModelProperty(value = "属性描述")
    @Excel(name = "属性描述")
    private String attr006;

    @ApiModelProperty(value = "属性描述")
    @Excel(name = "属性描述")
    private String attr007;

    @ApiModelProperty(value = "属性描述")
    @Excel(name = "属性描述")
    private String attr008;

    @ApiModelProperty(value = "属性描述")
    @Excel(name = "属性描述")
    private String attr009;

    @ApiModelProperty(value = "属性描述")
    @Excel(name = "属性描述")
    private String attr010;

    @ApiModelProperty(value = "属性描述")
    @Excel(name = "属性描述")
    private String attr011;

    @ApiModelProperty(value = "属性描述")
    @Excel(name = "属性描述")
    private String attr012;

    @ApiModelProperty(value = "属性描述")
    @Excel(name = "属性描述")
    private String attr013;

    @ApiModelProperty(value = "属性描述")
    @Excel(name = "属性描述")
    private String attr014;

    @ApiModelProperty(value = "属性描述")
    @Excel(name = "属性描述")
    private String attr015;

    @ApiModelProperty(value = "属性描述")
    @Excel(name = "属性描述")
    private String attr016;

    @ApiModelProperty(value = "属性描述")
    @Excel(name = "属性描述")
    private String attr017;

    @ApiModelProperty(value = "属性描述")
    @Excel(name = "属性描述")
    private String attr018;

    @ApiModelProperty(value = "属性描述")
    @Excel(name = "属性描述")
    private String attr019;

    @ApiModelProperty(value = "属性描述")
    @Excel(name = "属性描述")
    private String attr020;

    @ApiModelProperty(value = "Primary_Key")
    @NotBlank(message = "主键不能为空")
    @Excel(name = "Primary_Key")
    private String primaryKey;

    @ApiModelProperty(value = "导入时间")
    @Excel(name = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date fdopImportTime;
}