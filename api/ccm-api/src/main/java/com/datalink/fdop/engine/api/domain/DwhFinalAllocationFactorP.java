package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName(schema = "dwh", value = "dwh_final_allocation_factor_p")
@ApiModel("定版视图-因子统计")
@PermissionColumn(module = "ccms")
public class DwhFinalAllocationFactorP {

    @ApiModelProperty(value = "数据集")
    @Excel(name = "数据集")
    private String verId;

    @ApiModelProperty(value = "值类型")
    @Excel(name = "值类型")
    private String valueType;

    @ApiModelProperty(value = "管理范围")
    @Excel(name = "管理范围")
    private String controlAreaId;

    @ApiModelProperty(value = "公司代码")
    @Excel(name = "公司代码")
    private String companyId;

    @ApiModelProperty(value = "工厂代码")
    @Excel(name = "工厂代码")
    private String plantId;

    @ApiModelProperty(value = "年度月份")
    @Excel(name = "年度月份")
    private String yearMonth;

    @ApiModelProperty(value = "年度")
    @Excel(name = "年度")
    private Long year;

    @ApiModelProperty(value = "月份")
    @Excel(name = "月份")
    private Long month;

    @ApiModelProperty(value = "分摊类型")
    @Excel(name = "分摊类型")
    private String allocationType;

    @ApiModelProperty(value = "设备组编码")
    @Excel(name = "设备组编码")
    private String equipGroupId;

    @ApiModelProperty(value = "设备组描述")
    @Excel(name = "设备组描述")
    private String equipGroupDesc;

    @ApiModelProperty(value = "产品编码")
    @Excel(name = "产品编码")
    private String productId;

    @ApiModelProperty(value = "产品描述")
    @Excel(name = "产品描述")
    private String productDesc;

    @ApiModelProperty(value = "成本中心")
    @Excel(name = "成本中心")
    private String costCenterId;

    @ApiModelProperty(value = "成本中心描述")
    @Excel(name = "成本中心描述")
    private String costCenterDesc;

    @ApiModelProperty(value = "成本中心类型")
    @Excel(name = "成本中心类型")
    private String costCenterType;

    @ApiModelProperty(value = "可用作业量[MOVE]")
    @Excel(name = "可用作业量[MOVE]")
    private String upmveFull;

    @ApiModelProperty(value = "可用作业量[MIN]")
    @Excel(name = "可用作业量[MIN]")
    private String activityFull;

    @ApiModelProperty(value = "ACT001")
    @Excel(name = "ACT001")
    private String act001;

    @ApiModelProperty(value = "ACT002")
    @Excel(name = "ACT002")
    private String act002;

    @ApiModelProperty(value = "ACT003")
    @Excel(name = "ACT003")
    private String act003;

    @ApiModelProperty(value = "ACT004")
    @Excel(name = "ACT004")
    private String act004;

    @ApiModelProperty(value = "ACT005")
    @Excel(name = "ACT005")
    private String act005;

    @ApiModelProperty(value = "ACT006")
    @Excel(name = "ACT006")
    private String act006;

    @ApiModelProperty(value = "统计因子")
    @Excel(name = "统计因子")
    private String factorId;

    @ApiModelProperty(value = "统计因子描述")
    @Excel(name = "统计因子描述")
    private String factorDesc;

    @ApiModelProperty(value = "公式")
    @Excel(name = "公式")
    private String formula;

    @ApiModelProperty(value = "因子单位")
    @Excel(name = "因子单位")
    private String factorUnit;

    @ApiModelProperty(value = "统计因子值")
    @Excel(name = "统计因子值")
    private String factorQty;

    @ApiModelProperty(value = "Primary_Key")
    @Excel(name = "Primary_Key")
    private String primaryKey;

    @ApiModelProperty(value = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Excel(name = "导入时间")
    private Date fdopImportTime;
}