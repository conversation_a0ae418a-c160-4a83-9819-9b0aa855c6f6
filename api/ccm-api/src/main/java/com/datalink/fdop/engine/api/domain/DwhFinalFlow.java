package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.datalink.fdop.common.core.annotation.SearchField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@TableName(schema = "dwh", value = "dwh_final_flow")
@ApiModel("FLOW管理")
@PermissionColumn(module = "ccms")
public class DwhFinalFlow {

    @Excel(name = "识别码")
    @ApiModelProperty(value = "识别码")
    private String flowCimCode;

    @Excel(name = "计数器")
    @ApiModelProperty(value = "计数器")
    private String count;

    @Excel(name = "数据集")
    @ApiModelProperty(value = "数据集")
    private String verId;

    @Excel(name = "工厂代码")
    @ApiModelProperty(value = "工厂代码")
    @NotBlank(message = "工厂代码不能为空")
    @SearchField(value = "工厂代码")
    private String plantId;

    @Excel(name = "制造工厂")
    @ApiModelProperty(value = "制造工厂")
    private String factoryId;

    @Excel(name = "产品编码")
    @ApiModelProperty(value = "产品编码")
    @NotBlank(message = "产品编码不能为空")
    @SearchField(value = "产品编码")
    private String productId;

    @Excel(name = "产品描述")
    @ApiModelProperty(value = "产品描述")
    private String productDesc;

    @Excel(name = "产品阶段")
    @ApiModelProperty(value = "产品阶段")
    private String techPhase;

    @Excel(name = "产品CIM编码")
    @ApiModelProperty(value = "产品CIM编码")
    private String productCimId;

    @Excel(name = "产品ERP编码")
    @ApiModelProperty(value = "产品ERP编码")
    private String productErpId;

    @Excel(name = "Product_Ver")
    @ApiModelProperty(value = "Product_Ver")
    @NotNull
    private String productVer;

    @Excel(name = "Process_ID")
    @ApiModelProperty(value = "Process_ID")
    @NotBlank(message = "Process_ID不能为空")
    private String processId;

    @Excel(name = "Process_Ver")
    @ApiModelProperty(value = "Process_Ver")
    @NotNull
    private String processVer;

    @Excel(name = "Step_NO")
    @ApiModelProperty(value = "Step_NO")
    @NotNull
    private String stepNo;

    @Excel(name = "Sub_Plan_Seq")
    @ApiModelProperty(value = "Sub_Plan_Seq")
    private String subPlanSeq;

    @Excel(name = "Step_Seq")
    @ApiModelProperty(value = "Step_Seq")
    private String stepSeq;

    @Excel(name = "Sub_plan_ID")
    @ApiModelProperty(value = "Sub_plan_ID")
    private String subPlanId;

    @Excel(name = "Layer_ID")
    @ApiModelProperty(value = "Layer_ID")
    private String layerId;

    @Excel(name = "Stage_ID")
    @ApiModelProperty(value = "Stage_ID")
    private String stageId;

    @Excel(name = "Step_ID")
    @ApiModelProperty(value = "Step_ID")
    private String stepId;

    @Excel(name = "Step_Desc")
    @ApiModelProperty(value = "Step_Desc")
    private String stepDesc;

    @Excel(name = "Step_Type")
    @ApiModelProperty(value = "Step_Type")
    private String stepType;

    @Excel(name = "Recipe_ID")
    @ApiModelProperty(value = "Recipe_ID")
    private String recipeId;

    @Excel(name = "Equip_Group_ID")
    @ApiModelProperty(value = "Equip_Group_ID")
    private String equipGroupId;

    @Excel(name = "work_area")
    @ApiModelProperty(value = "work_area")
    private String workArea;

    @Excel(name = "rwk_sub_plan_id")
    @ApiModelProperty(value = "rwk_sub_plan_id")
    private String rwkSubPlanId;

    @Excel(name = "Equip_Group_Flag")
    @ApiModelProperty(value = "Equip_Group_Flag")
    private String equipGroupFlag;

    @Excel(name = "Outsourcing_Flag")
    @ApiModelProperty(value = "Outsourcing_Flag")
    private String outsourcingFlag;

    @Excel(name = "工作中心")
    @ApiModelProperty(value = "工作中心")
    private String workCenterId;

    @Excel(name = "工作中心文本")
    @ApiModelProperty(value = "工作中心文本")
    private String workCenterText;

    @Excel(name = "成本中心")
    @ApiModelProperty(value = "成本中心")
    private String costCenterId;

    @Excel(name = "成本中心描述")
    @ApiModelProperty(value = "成本中心描述")
    private String costCenterText;

    @Excel(name = "工作成本单元")
    @ApiModelProperty(value = "工作成本单元")
    private String workCostCenter;

    @Excel(name = "基本数量")
    @ApiModelProperty(value = "基本数量")
    private Integer baseQty;

    @Excel(name = "基本单位")
    @ApiModelProperty(value = "基本单位")
    private String baseUnit;

    @Excel(name = "工序数量1")
    @ApiModelProperty(value = "工序数量1")
    private String operQty1;

    @Excel(name = "工序单位1")
    @ApiModelProperty(value = "工序单位1")
    private String operUnit1;

    @Excel(name = "工序数量2")
    @ApiModelProperty(value = "工序数量2")
    private String operQty2;

    @Excel(name = "工序单位2")
    @ApiModelProperty(value = "工序单位2")
    private String operUnit2;

    @Excel(name = "作业工时")
    @ApiModelProperty(value = "作业工时")
    private String operSpt;

    @Excel(name = "工时单位")
    @ApiModelProperty(value = "工时单位")
    private String sptUnit;

    @Excel(name = "权数")
    @ApiModelProperty(value = "权数")
    private String weight;

    @Excel(name = "act001")
    @ApiModelProperty(value = "act001")
    private String act001;

    @Excel(name = "act002")
    @ApiModelProperty(value = "act002")
    private String act002;

    @Excel(name = "act003")
    @ApiModelProperty(value = "act003")
    private String act003;

    @Excel(name = "act004")
    @ApiModelProperty(value = "act004")
    private String act004;

    @Excel(name = "act005")
    @ApiModelProperty(value = "act005")
    private String act005;

    @Excel(name = "act006")
    @ApiModelProperty(value = "act006")
    private String act006;

    @Excel(name = "Primary_Key")
    @ApiModelProperty(value = "Primary_Key")
    private String primaryKey;

    @Excel(name = "导入时间")
    @ApiModelProperty(value = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date fdopImportTime;








}
