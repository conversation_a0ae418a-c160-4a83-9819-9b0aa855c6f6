package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
@TableName(schema = "dwh", value = "dwh_terp_flow")
@ApiModel("TERP流程管理")
@PermissionColumn(module = "ccms")
public class DwhTerpFlow {

    @Excel(name = "识别码")
    @ApiModelProperty(value = "识别码")
    private String flowCimCode;

    @Excel(name = "计数器")
    @ApiModelProperty(value = "计数器")
    private String count;

    @Excel(name = "数据集")
    @ApiModelProperty(value = "数据集")
    @NotBlank(message = "数据集不能为空")
    private String verId;

    @Excel(name = "工厂代码")
    @ApiModelProperty(value = "工厂代码")
    private String plantId;

    @Excel(name = "制造工厂")
    @ApiModelProperty(value = "制造工厂")
    private String factoryId;

    @Excel(name = "产品编码")
    @ApiModelProperty(value = "产品编码")
    private String productId;

    @Excel(name = "产品阶段")
    @ApiModelProperty(value = "产品阶段")
    private String techPhase;

    @Excel(name = "产品CIM编码")
    @ApiModelProperty(value = "产品CIM编码")
    private String productCimId;

    @Excel(name = "产品ERP编码")
    @ApiModelProperty(value = "产品ERP编码")
    private String productErpId;

    @Excel(name = "Product_Ver")
    @ApiModelProperty(value = "Product_Ver")
    private String productVer;

    @Excel(name = "Process_ID")
    @ApiModelProperty(value = "Process_ID")
    private String processId;

    @Excel(name = "Process_Ver")
    @ApiModelProperty(value = "Process_Ver")
    private String processVer;

    @Excel(name = "Step_NO")
    @ApiModelProperty(value = "Step_NO")
    private String stepNo;

    @Excel(name = "Step_ID")
    @ApiModelProperty(value = "Step_ID")
    private String stepId;

    @Excel(name = "Step_Desc")
    @ApiModelProperty(value = "Step_Desc")
    private String stepDesc;

    @Excel(name = "Step_Type")
    @ApiModelProperty(value = "Step_Type")
    private String stepType;

    @Excel(name = "Recipe_ID")
    @ApiModelProperty(value = "Recipe_ID")
    private String recipeId;

    @Excel(name = "Equip_Group_ID")
    @ApiModelProperty(value = "Equip_Group_ID")
    private String equipGroupId;

    @Excel(name = "Equip_Group_Flag")
    @ApiModelProperty(value = "Equip_Group_Flag")
    private String equipGroupFlag;

    @Excel(name = "work_area")
    @ApiModelProperty(value = "work_area")
    private String workArea;

    @Excel(name = "rwk_sub_plan_id")
    @ApiModelProperty(value = "rwk_sub_plan_id")
    private String rwkSubPlanId;

    @Excel(name = "Outsourcing_Flag")
    @ApiModelProperty(value = "Outsourcing_Flag")
    private String outsourcingFlag;

    @Excel(name = "作业工时")
    @ApiModelProperty(value = "作业工时")
    private String operSpt;

    @Excel(name = "工时单位")
    @ApiModelProperty(value = "工时单位")
    private String sptUnit;

    @Excel(name = "Primary_Key")
    @ApiModelProperty(value = "Primary_Key")
    private String primaryKey;

    @Excel(name = "导入时间")
    @ApiModelProperty(value = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date fdopImportTime;



}
