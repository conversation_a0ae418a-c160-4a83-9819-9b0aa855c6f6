package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.datalink.fdop.common.core.annotation.SearchField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName(schema = "dwh", value = "dwh_terp_pbom")
@ApiModel("TERP PBOM管理")
@PermissionColumn(module = "ccms")
public class DwhTerpPbom {

    @Excel(name = "CIM识别码")
    @ApiModelProperty(value = "CIM识别码")
    private String pbomCimCode;

    @Excel(name = "计数器")
    @ApiModelProperty(value = "计数器")
    private String count;

    @Excel(name = "数据集")
    @ApiModelProperty(value = "数据集")
    @NotBlank(message = "数据集不能为空")
    private String verId;

    @Excel(name = "工厂代码")
    @ApiModelProperty(value = "工厂代码")
    private String plantId;

    @Excel(name = "制造工厂")
    @ApiModelProperty(value = "制造工厂")
    private String factoryId;

    @Excel(name = "产品编码")
    @ApiModelProperty(value = "产品编码")
    @NotBlank(message = "产品编码不能为空")
    @SearchField(value = "产品编码")
    private String productId;

    @Excel(name = "产品CIM编码")
    @ApiModelProperty(value = "产品CIM编码")
    private String productCimId;

    @Excel(name = "产品ERP编码")
    @ApiModelProperty(value = "产品ERP编码")
    private String productErpId;

    @Excel(name = "原料编码")
    @ApiModelProperty(value = "原料编码")
    private String rawMaterialId;

    @Excel(name = "默认值")
    @ApiModelProperty(value = "默认值")
    @TableField(value = "default")
    private String defaultValue;

    @Excel(name = "Primary_Key")
    @ApiModelProperty(value = "Primary_Key")
    private String primaryKey;

    @Excel(name = "导入时间")
    @ApiModelProperty(value = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date fdopImportTime;











}
