package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName(schema = "dws", value = "dws_new_varcost_lot")
@ApiModel("约当分配结算-逻辑视图-子批维")
@PermissionColumn(module = "ccms")
public class DwsNewVarcostLot {

    @ApiModelProperty(value = "数据集")
    @Excel(name = "数据集")
    private String verId;

    @ApiModelProperty(value = "工厂代码")
    @Excel(name = "工厂代码")
    private String plantId;

    @ApiModelProperty(value = "年度月份")
    @Excel(name = "年度月份")
    private String yearMonth;

    @ApiModelProperty(value = "母批工单")
    @Excel(name = "母批工单")
    private String baseOrder;

    @ApiModelProperty(value = "工作工单")
    @Excel(name = "工作工单")
    private String workOrder;

    @ApiModelProperty(value = "母批编码")
    @Excel(name = "母批编码")
    private String baseLotId;

    @ApiModelProperty(value = "约当项")
    @Excel(name = "约当项")
    private String costItem;

    @ApiModelProperty(value = "批次编码")
    @Excel(name = "批次编码")
    private String lotId;

    @ApiModelProperty(value = "批次类型")
    @Excel(name = "批次类型")
    private String lotType;

    @ApiModelProperty(value = "批次类别")
    @Excel(name = "批次类别")
    private String lotCategory;

    @ApiModelProperty(value = "产品编码")
    @Excel(name = "产品编码")
    private String productId;

    @ApiModelProperty(value = "产品描述")
    @Excel(name = "产品描述")
    private String productDesc;

    @ApiModelProperty(value = "产品CIM编码")
    @Excel(name = "产品CIM编码")
    private String productCimId;

    @ApiModelProperty(value = "产品ERP编码")
    @Excel(name = "产品ERP编码")
    private String productErpId;

    @ApiModelProperty(value = "step_no")
    @Excel(name = "step_no")
    private String stepNo;

    @ApiModelProperty(value = "step_no_nr")
    @Excel(name = "step_no_nr")
    private String stepNoNr;

    @ApiModelProperty(value = "数量")
    @Excel(name = "数量")
    private String qty;

    @ApiModelProperty(value = "单位")
    @Excel(name = "单位")
    private String baseUnit;

    @ApiModelProperty(value = "累计成本值")
    @Excel(name = "累计成本值")
    private String cumulativeCost;

    @ApiModelProperty(value = "组件001成本")
    @Excel(name = "组件001成本")
    private String costStructure001;

    @ApiModelProperty(value = "组件002成本")
    @Excel(name = "组件002成本")
    private String costStructure002;

    @ApiModelProperty(value = "组件003成本")
    @Excel(name = "组件003成本")
    private String costStructure003;

    @ApiModelProperty(value = "组件004成本")
    @Excel(name = "组件004成本")
    private String costStructure004;

    @ApiModelProperty(value = "组件005成本")
    @Excel(name = "组件005成本")
    private String costStructure005;

    @ApiModelProperty(value = "组件006成本")
    @Excel(name = "组件006成本")
    private String costStructure006;

    @ApiModelProperty(value = "组件007成本")
    @Excel(name = "组件007成本")
    private String costStructure007;

    @ApiModelProperty(value = "组件008成本")
    @Excel(name = "组件008成本")
    private String costStructure008;

    @ApiModelProperty(value = "组件009成本")
    @Excel(name = "组件009成本")
    private String costStructure009;

    @ApiModelProperty(value = "组件010成本")
    @Excel(name = "组件010成本")
    private String costStructure010;

    @ApiModelProperty(value = "组件011成本")
    @Excel(name = "组件011成本")
    private String costStructure011;

    @ApiModelProperty(value = "组件012成本")
    @Excel(name = "组件012成本")
    private String costStructure012;

    @ApiModelProperty(value = "组件013成本")
    @Excel(name = "组件013成本")
    private String costStructure013;

    @ApiModelProperty(value = "组件014成本")
    @Excel(name = "组件014成本")
    private String costStructure014;

    @ApiModelProperty(value = "组件015成本")
    @Excel(name = "组件015成本")
    private String costStructure015;

    @ApiModelProperty(value = "组件016成本")
    @Excel(name = "组件016成本")
    private String costStructure016;

    @ApiModelProperty(value = "组件017成本")
    @Excel(name = "组件017成本")
    private String costStructure017;

    @ApiModelProperty(value = "组件018成本")
    @Excel(name = "组件018成本")
    private String costStructure018;

    @ApiModelProperty(value = "组件019成本")
    @Excel(name = "组件019成本")
    private String costStructure019;

    @ApiModelProperty(value = "组件020成本")
    @Excel(name = "组件020成本")
    private String costStructure020;

    @ApiModelProperty(value = "组件021成本")
    @Excel(name = "组件021成本")
    private String costStructure021;

    @ApiModelProperty(value = "组件022成本")
    @Excel(name = "组件022成本")
    private String costStructure022;

    @ApiModelProperty(value = "组件023成本")
    @Excel(name = "组件023成本")
    private String costStructure023;

    @ApiModelProperty(value = "组件024成本")
    @Excel(name = "组件024成本")
    private String costStructure024;

    @ApiModelProperty(value = "组件025成本")
    @Excel(name = "组件025成本")
    private String costStructure025;

    @ApiModelProperty(value = "组件026成本")
    @Excel(name = "组件026成本")
    private String costStructure026;

    @ApiModelProperty(value = "组件027成本")
    @Excel(name = "组件027成本")
    private String costStructure027;

    @ApiModelProperty(value = "组件028成本")
    @Excel(name = "组件028成本")
    private String costStructure028;

    @ApiModelProperty(value = "组件029成本")
    @Excel(name = "组件029成本")
    private String costStructure029;

    @ApiModelProperty(value = "组件030成本")
    @Excel(name = "组件030成本")
    private String costStructure030;

    @ApiModelProperty(value = "组件031成本")
    @Excel(name = "组件031成本")
    private String costStructure031;

    @ApiModelProperty(value = "组件032成本")
    @Excel(name = "组件032成本")
    private String costStructure032;

    @ApiModelProperty(value = "组件033成本")
    @Excel(name = "组件033成本")
    private String costStructure033;

    @ApiModelProperty(value = "组件034成本")
    @Excel(name = "组件034成本")
    private String costStructure034;

    @ApiModelProperty(value = "组件035成本")
    @Excel(name = "组件035成本")
    private String costStructure035;

    @ApiModelProperty(value = "组件036成本")
    @Excel(name = "组件036成本")
    private String costStructure036;

    @ApiModelProperty(value = "组件037成本")
    @Excel(name = "组件037成本")
    private String costStructure037;

    @ApiModelProperty(value = "组件038成本")
    @Excel(name = "组件038成本")
    private String costStructure038;

    @ApiModelProperty(value = "组件039成本")
    @Excel(name = "组件039成本")
    private String costStructure039;

    @ApiModelProperty(value = "组件040成本")
    @Excel(name = "组件040成本")
    private String costStructure040;

    @ApiModelProperty(value = "组件041成本")
    @Excel(name = "组件041成本")
    private String costStructure041;

    @ApiModelProperty(value = "组件042成本")
    @Excel(name = "组件042成本")
    private String costStructure042;

    @ApiModelProperty(value = "组件043成本")
    @Excel(name = "组件043成本")
    private String costStructure043;

    @ApiModelProperty(value = "组件044成本")
    @Excel(name = "组件044成本")
    private String costStructure044;

    @ApiModelProperty(value = "组件045成本")
    @Excel(name = "组件045成本")
    private String costStructure045;

    @ApiModelProperty(value = "组件046成本")
    @Excel(name = "组件046成本")
    private String costStructure046;

    @ApiModelProperty(value = "组件047成本")
    @Excel(name = "组件047成本")
    private String costStructure047;

    @ApiModelProperty(value = "组件048成本")
    @Excel(name = "组件048成本")
    private String costStructure048;

    @ApiModelProperty(value = "组件049成本")
    @Excel(name = "组件049成本")
    private String costStructure049;

    @ApiModelProperty(value = "组件050成本")
    @Excel(name = "组件050成本")
    private String costStructure050;

    @ApiModelProperty(value = "组件051成本")
    @Excel(name = "组件051成本")
    private String costStructure051;

    @ApiModelProperty(value = "组件052成本")
    @Excel(name = "组件052成本")
    private String costStructure052;

    @ApiModelProperty(value = "组件053成本")
    @Excel(name = "组件053成本")
    private String costStructure053;

    @ApiModelProperty(value = "组件054成本")
    @Excel(name = "组件054成本")
    private String costStructure054;

    @ApiModelProperty(value = "组件055成本")
    @Excel(name = "组件055成本")
    private String costStructure055;

    @ApiModelProperty(value = "组件056成本")
    @Excel(name = "组件056成本")
    private String costStructure056;

    @ApiModelProperty(value = "组件057成本")
    @Excel(name = "组件057成本")
    private String costStructure057;

    @ApiModelProperty(value = "组件058成本")
    @Excel(name = "组件058成本")
    private String costStructure058;

    @ApiModelProperty(value = "组件059成本")
    @Excel(name = "组件059成本")
    private String costStructure059;

    @ApiModelProperty(value = "组件060成本")
    @Excel(name = "组件060成本")
    private String costStructure060;

    @Excel(name = "Primary_Key")
    @ApiModelProperty(value = "Primary_Key")
    private String primaryKey;

    @Excel(name = "导入时间")
    @ApiModelProperty(value = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date fdopImportTime;
}