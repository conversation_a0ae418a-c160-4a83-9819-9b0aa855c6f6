package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
@TableName(schema = "dwd", value = "dwd_material_mbew")
@ApiModel("物料评估信息")
@PermissionColumn(module = "ccms")
public class DwdMaterialMbew {

    @ApiModelProperty(value = "物料编码")
    @Excel(name = "物料编码")
    private String materialId;

    @ApiModelProperty(value = "物料描述")
    @Excel(name = "物料描述")
    private String materialDesc;

    @ApiModelProperty(value = "评估范围")
    @NotBlank(message = "评估范围不能为空")
    @Excel(name = "评估范围")
    private String evaluateAreaId;

    @ApiModelProperty(value = "年度")
    @Excel(name = "年度")
    private String year;

    @ApiModelProperty(value = "月份")
    @Excel(name = "月份")
    private String month;

    @ApiModelProperty(value = "原评估类")
    @Excel(name = "原评估类")
    private String origEvaluateSortId;

    @ApiModelProperty(value = "原评估类描述")
    @Excel(name = "原评估类描述")
    private String origEvaluateSortDesc;

    @ApiModelProperty(value = "物料组")
    @Excel(name = "物料组")
    private String materialGroup;

    @ApiModelProperty(value = "物料组描述")
    @Excel(name = "物料组描述")
    private String materialGroupDesc;

    @ApiModelProperty(value = "评估细分")
    @Excel(name = "评估细分")
    private String evaluateSplit;

    @ApiModelProperty(value = "重评估类")
    @Excel(name = "重评估类")
    private String evaluateSortId;

    @ApiModelProperty(value = "重评估类描述")
    @Excel(name = "重评估类描述")
    private String evaluateSortDesc;

    @ApiModelProperty(value = "价格控制")
    @Excel(name = "价格控制")
    private String priceModel;

    @ApiModelProperty(value = "价格单位")
    @Excel(name = "价格单位")
    private String priceBasis;

    @ApiModelProperty(value = "标准价格")
    @Excel(name = "标准价格")
    private String stdPrice;

    @ApiModelProperty(value = "移动价格")
    @Excel(name = "移动价格")
    private String actPrice;

    @ApiModelProperty(value = "计划价[1]")
    @Excel(name = "计划价[1]")
    private String plannedPrice1;

    @ApiModelProperty(value = "有效期从")
    @Excel(name = "有效期从")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private String planPr1Datefr;

    @ApiModelProperty(value = "计划价[2]")
    @Excel(name = "计划价[2]")
    private String plannedPrice2;

    @ApiModelProperty(value = "有效期从")
    @Excel(name = "有效期从")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private String planPr2Datefr;

    @ApiModelProperty(value = "计划价[3]")
    @Excel(name = "计划价[3]")
    private String plannedPrice3;

    @ApiModelProperty(value = "有效期从")
    @Excel(name = "有效期从")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private String planPr3Datefr;

    @ApiModelProperty(value = "物料类型")
    @Excel(name = "物料类型")
    private String materialType;

    @ApiModelProperty(value = "物料类型描述")
    @Excel(name = "物料类型描述")
    private String materialTypeDesc;

    @ApiModelProperty(value = "评估类型")
    @NotBlank(message = "评估类型不能为空")
    @Excel(name = "评估类型")
    private String evaluateType;

    @ApiModelProperty(value = "成本核算号[Y]")
    @Excel(name = "成本核算号[Y]")
    private String evaluateCodeY;

    @ApiModelProperty(value = "成本核算号[N]")
    @Excel(name = "成本核算号[N]")
    private String evaluateCodeN;

    @ApiModelProperty(value = "数量管理")
    @Excel(name = "数量管理")
    private String valueUpdating;

    @ApiModelProperty(value = "价值管理")
    @Excel(name = "价值管理")
    private String qtyUpdating;

    @ApiModelProperty(value = "导入时间")
    @Excel(name = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date fdopImportTime;

}
