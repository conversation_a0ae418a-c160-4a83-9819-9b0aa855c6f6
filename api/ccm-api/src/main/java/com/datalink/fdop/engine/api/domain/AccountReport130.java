package com.datalink.fdop.engine.api.domain;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023-02-17 17:00
 */
@Data
public class AccountReport130 {

    private String reportVer;
    private String reportItem;
    private String year;
    private String verId;
    private String controlAreaId;
    private String companyId;
    private String factoryId;
    private String reportItemL1Id;
    private String reportItemL1Desc;
    private String reportItemL2Id;
    private String reportItemL2Desc;
    private String costElementId;
    private String costElementDesc;
    private String costCenterGroupId;
    private String costCenterGroupDesc;
    private String costCenterId;
    private String costCenterDesc;
    private String sourceType;
    private String originCostCenterId;
    private String originCostCenterDesc;
    private BigDecimal value001;
    private BigDecimal value002;
    private BigDecimal value003;
    private BigDecimal value004;
    private BigDecimal value005;
    private BigDecimal value006;
    private BigDecimal value007;
    private BigDecimal value008;
    private BigDecimal value009;
    private BigDecimal value010;
    private BigDecimal value011;
    private BigDecimal value012;
    private BigDecimal value013;
    private BigDecimal value014;
    private BigDecimal value015;
    private BigDecimal value016;
    private BigDecimal valueQ1;
    private BigDecimal valueQ2;
    private BigDecimal valueQ3;
    private BigDecimal valueQ4;
    private BigDecimal valueTotal;
    
}
