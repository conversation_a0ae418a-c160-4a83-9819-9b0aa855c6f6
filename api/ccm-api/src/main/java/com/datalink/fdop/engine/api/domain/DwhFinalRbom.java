package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName(schema = "dwh", value = "dwh_final_rbom")
@ApiModel("RBOM管理")
@PermissionColumn(module = "ccms")
public class DwhFinalRbom {

    @ApiModelProperty(value = "识别码")
    @Excel(name = "识别码")
    private String flowCimCode;

    @ApiModelProperty(value = "计数器")
    @Excel(name = "计数器")
    private String count;

    @ApiModelProperty(value = "数据集")
    @Excel(name = "数据集")
    private String verId;

    @ApiModelProperty(value = "工厂代码")
    @Excel(name = "工厂代码")
    private String plantId;

    @ApiModelProperty(value = "制造工厂")
    @Excel(name = "制造工厂")
    private String factoryId;

    @ApiModelProperty(value = "产品编码")
    @Excel(name = "产品编码")
    private String productId;

    @Excel(name = "产品描述")
    @ApiModelProperty(value = "产品描述")
    private String productDesc;

    @ApiModelProperty(value = "产品阶段")
    @Excel(name = "产品阶段")
    private String techPhase;

    @ApiModelProperty(value = "产品CIM编码")
    @Excel(name = "产品CIM编码")
    private String productCimId;

    @ApiModelProperty(value = "产品ERP编码")
    @Excel(name = "产品ERP编码")
    private String productErpId;

    @ApiModelProperty(value = "Product_Ver")
    @Excel(name = "Product_Ver")
    private String productVer;

    @ApiModelProperty(value = "Process_ID")
    @Excel(name = "Process_ID")
    private String processId;

    @ApiModelProperty(value = "Process_Ver")
    @Excel(name = "Process_Ver")
    private String processVer;

    @ApiModelProperty(value = "Step_NO")
    @Excel(name = "Step_NO")
    private String stepNo;

    @ApiModelProperty(value = "Sub_Plan_Seq")
    @Excel(name = "Sub_Plan_Seq")
    private String subPlanSeq;

    @ApiModelProperty(value = "Step_Seq")
    @Excel(name = "Step_Seq")
    private String stepSeq;

    @ApiModelProperty(value = "Sub_Plan_ID")
    @Excel(name = "Sub_Plan_ID")
    private String subPlanId;

    @ApiModelProperty(value = "Layer_ID")
    @Excel(name = "Layer_ID")
    private String layerId;

    @ApiModelProperty(value = "Stage_ID")
    @Excel(name = "Stage_ID")
    private String stageId;

    @ApiModelProperty(value = "Step_ID")
    @Excel(name = "Step_ID")
    private String stepId;

    @ApiModelProperty(value = "Step_Desc")
    @Excel(name = "Step_Desc")
    private String stepDesc;

    @ApiModelProperty(value = "Step_Type")
    @Excel(name = "Step_Type")
    private String stepType;

    @ApiModelProperty(value = "Recipe_ID")
    @Excel(name = "Recipe_ID")
    private String recipeId;

    @ApiModelProperty(value = "Equip_Group_ID")
    @Excel(name = "Equip_Group_ID")
    private String equipGroupId;

    @ApiModelProperty(value = "Equip_Group_Flag")
    @Excel(name = "Equip_Group_Flag")
    private String equipGroupFlag;

    @ApiModelProperty(value = "Work_Area")
    @Excel(name = "Work_Area")
    private String workArea;

    @ApiModelProperty(value = "Rwk_Sub_Plan_ID")
    @Excel(name = "Rwk_Sub_Plan_ID")
    private String rwkSubPlanId;

    @ApiModelProperty(value = "工作中心")
    @Excel(name = "工作中心")
    private String workCenterId;

    @ApiModelProperty(value = "工作中心文本")
    @Excel(name = "工作中心文本")
    private String workCenterText;

    @ApiModelProperty(value = "成本中心")
    @Excel(name = "成本中心")
    private String costCenterId;

    @ApiModelProperty(value = "成本中心描述")
    @Excel(name = "成本中心描述")
    private String costCenterText;

    @ApiModelProperty(value = "Flow基础数量")
    @Excel(name = "Flow基础数量")
    private String baseQty;

    @ApiModelProperty(value = "Flow基础单位")
    @Excel(name = "Flow基础单位")
    private String baseUnit;

    @ApiModelProperty(value = "工序数量")
    @Excel(name = "工序数量")
    private String operQty1;

    @ApiModelProperty(value = "工序单位")
    @Excel(name = "工序单位")
    private String operUnit1;

    @ApiModelProperty(value = "原料编码")
    @Excel(name = "原料编码")
    private String rawMaterialId;

    @ApiModelProperty(value = "原料描述")
    @Excel(name = "原料描述")
    private String rawMaterialDesc;

    @ApiModelProperty(value = "用量[使用单位]")
    @Excel(name = "用量[使用单位]")
    private String usageQty;

    @ApiModelProperty(value = "使用单位")
    @Excel(name = "使用单位")
    private String usageUnit;

    @ApiModelProperty(value = "用量[基本单位]")
    @Excel(name = "用量[基本单位]")
    private String baseQtySn;

    @ApiModelProperty(value = "基本单位")
    @Excel(name = "基本单位")
    private String baseUnitSn;

    @ApiModelProperty(value = "Primary_Key")
    @Excel(name = "Primary_Key")
    private String primaryKey;

    @ApiModelProperty(value = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Excel(name = "导入时间")
    private Date fdopImportTime;

}
