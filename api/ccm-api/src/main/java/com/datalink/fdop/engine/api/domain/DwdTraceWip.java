package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName(schema = "dwd", value = "dwd_trace_wip")
@ApiModel("同步视图-时点在制状态")
@PermissionColumn(module = "ccms")
public class DwdTraceWip {

    @ApiModelProperty(value = "工厂代码")
    @Excel(name = "工厂代码")
    private String plantId;

    @ApiModelProperty(value = "制造工厂")
    @Excel(name = "制造工厂")
    private String factoryId;

    @ApiModelProperty(value = "日期")
    @Excel(name = "日期")
    private String date;

    @ApiModelProperty(value = "时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Excel(name = "时间戳")
    private Date timestamp;

    @ApiModelProperty(value = "母批编码")
    @Excel(name = "母批编码")
    private String baseLotId;

    @ApiModelProperty(value = "批次编码")
    @Excel(name = "批次编码")
    private String lotId;

    @ApiModelProperty(value = "历史序位")
    @Excel(name = "历史序位")
    private String historyStepNo;

    @ApiModelProperty(value = "Sub_Plan_Seq")
    @Excel(name = "Sub_Plan_Seq")
    private String subPlanSeq;

    @ApiModelProperty(value = "Step_Seq")
    @Excel(name = "Step_Seq")
    private String stepSeq;

    @ApiModelProperty(value = "Step_NO")
    @Excel(name = "Step_NO")
    private String stepNo;

    @ApiModelProperty(value = "返工前工序序位")
    @Excel(name = "返工前工序序位")
    private String stepNoNr;

    @ApiModelProperty(value = "数量（1）")
    @Excel(name = "数量（1）")
    private String qty1;

    @ApiModelProperty(value = "单位（1）")
    @Excel(name = "单位（1）")
    private String unit1;

    @ApiModelProperty(value = "数量（2）")
    @Excel(name = "数量（2）")
    private String qty2;

    @ApiModelProperty(value = "单位（2）")
    @Excel(name = "单位（2）")
    private String unit2;

    @ApiModelProperty(value = "批次类别")
    @Excel(name = "批次类别")
    private String lotCategory;

    @ApiModelProperty(value = "批次类型")
    @Excel(name = "批次类型")
    private String lotType;

    @ApiModelProperty(value = "批次状态")
    @Excel(name = "批次状态")
    private String lotStatus;

    @ApiModelProperty(value = "母批工单")
    @Excel(name = "母批工单")
    private String baseOrder;

    @ApiModelProperty(value = "工作工单")
    @Excel(name = "工作工单")
    private String workOrder;

    @ApiModelProperty(value = "产品编码")
    @Excel(name = "产品编码")
    private String productId;

    @ApiModelProperty(value = "产品描述")
    @Excel(name = "产品描述")
    private String productDesc;

    @ApiModelProperty(value = "产品CIM编码")
    @Excel(name = "产品CIM编码")
    private String productCimId;

    @ApiModelProperty(value = "产品ERP编码")
    @Excel(name = "产品ERP编码")
    private String productErpId;

    @ApiModelProperty(value = "Product_Ver")
    @Excel(name = "Product_Ver")
    private String productVer;

    @ApiModelProperty(value = "Process_ID")
    @Excel(name = "Process_ID")
    private String processId;

    @ApiModelProperty(value = "Process_Ver")
    @Excel(name = "Process_Ver")
    private String processVer;

    @ApiModelProperty(value = "Sub_Plan_ID")
    @Excel(name = "Sub_Plan_ID")
    private String subPlanId;

    @ApiModelProperty(value = "Layer_ID")
    @Excel(name = "Layer_ID")
    private String layerId;

    @ApiModelProperty(value = "stage_ID")
    @Excel(name = "stage_ID")
    private String stageId;

    @ApiModelProperty(value = "Step_ID")
    @Excel(name = "Step_ID")
    private String stepId;

    @ApiModelProperty(value = "Equip_ID")
    @Excel(name = "Equip_ID")
    private String equipId;

    @ApiModelProperty(value = "Chamber_ID")
    @Excel(name = "Chamber_ID")
    private String chamberId;

    @ApiModelProperty(value = "Equip_Group_ID")
    @Excel(name = "Equip_Group_ID")
    private String equipGroupId;

    @ApiModelProperty(value = "Primary_key")
    @Excel(name = "Primary_key")
    private String primaryKey;

    @ApiModelProperty(value = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Excel(name = "导入时间")
    private Date fdopImportTime;

}