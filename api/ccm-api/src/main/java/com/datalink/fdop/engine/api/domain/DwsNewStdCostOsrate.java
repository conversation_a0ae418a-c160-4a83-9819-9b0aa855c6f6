package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName(schema = "dws", value = "dws_new_std_cost_osrate")
@ApiModel("计划成本-外协费率管理")
@PermissionColumn(module = "ccms")
public class DwsNewStdCostOsrate {

    @ApiModelProperty(value = "STEP核算号")
    @Excel(name = "STEP核算号")
    private String costCode;

    @ApiModelProperty(value = "数据集")
    @Excel(name = "数据集")
    private String verId;

    @ApiModelProperty(value = "管理范围")
    @Excel(name = "管理范围")
    private String controlAreaId;

    @ApiModelProperty(value = "工厂代码")
    @Excel(name = "工厂代码")
    private String plantId;

    @ApiModelProperty(value = "有效起始日期")
    @Excel(name = "有效起始日期")
    private String effectiveDate;

    @ApiModelProperty(value = "评估基于日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @Excel(name = "评估基于日期")
    private Date baseDate;

    @ApiModelProperty(value = "运算批量")
    @Excel(name = "运算批量")
    private Long batchQty;

    @ApiModelProperty(value = "运算时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Excel(name = "运算时间戳")
    private Date timestamp;

    @ApiModelProperty(value = "[主]产品编码")
    @Excel(name = "[主]产品编码")
    private String productId;

    @ApiModelProperty(value = "[主]产品描述")
    @Excel(name = "[主]产品描述")
    private String productDesc;

    @ApiModelProperty(value = "[主]产品CIM编码")
    @Excel(name = "[主]产品CIM编码")
    private String productCimId;

    @ApiModelProperty(value = "[主]产品ERP编码")
    @Excel(name = "[主]产品ERP编码")
    private String productErpId;

    @ApiModelProperty(value = "[主]基本数量")
    @Excel(name = "[主]基本数量")
    private String cbomBaseQty;

    @ApiModelProperty(value = "[主]基本单位")
    @Excel(name = "[主]基本单位")
    private String cbomBaseUnit;

    @ApiModelProperty(value = "组内层级")
    @Excel(name = "组内层级")
    private Long hierarchy;

    @ApiModelProperty(value = "层内序号")
    @Excel(name = "层内序号")
    private Long hierarchyInnerSeq;

    @ApiModelProperty(value = "[子]产品编码")
    @Excel(name = "[子]产品编码")
    private String productSn;

    @ApiModelProperty(value = "[子]产品描述")
    @Excel(name = "[子]产品描述")
    private String productSnDesc;

    @ApiModelProperty(value = "[子]产品CIM编码")
    @Excel(name = "[子]产品CIM编码")
    private String productCimSn;

    @ApiModelProperty(value = "[子]产品ERP编码")
    @Excel(name = "[子]产品ERP编码")
    private String productErpSn;

    @ApiModelProperty(value = "[子]基本数量")
    @Excel(name = "[子]基本数量")
    private String cbomBaseQtySn;

    @ApiModelProperty(value = "[子]基本单位")
    @Excel(name = "[子]基本单位")
    private String cbomBaseUnitSn;

    @ApiModelProperty(value = "Step_NO")
    @Excel(name = "Step_NO")
    private Long stepNo;

    @ApiModelProperty(value = "Step_Cost_Flag")
    @Excel(name = "Step_Cost_Flag")
    private String stepCostFlag;

    @ApiModelProperty(value = "外协作业量")
    @Excel(name = "外协作业量")
    private String qty;

    @ApiModelProperty(value = "货币值")
    @Excel(name = "货币值")
    private String amount;

    @ApiModelProperty(value = "成本组件")
    @Excel(name = "成本组件")
    private String costStructureId;

    @ApiModelProperty(value = "组件描述")
    @Excel(name = "组件描述")
    private String costStructureDesc;

    @ApiModelProperty(value = "Product_Ver")
    @Excel(name = "Product_Ver")
    private Long productVer;

    @ApiModelProperty(value = "Process_ID")
    @Excel(name = "Process_ID")
    private String processId;

    @ApiModelProperty(value = "Process_Ver")
    @Excel(name = "Process_Ver")
    private Long processVer;

    @ApiModelProperty(value = "Sub_Plan_Seq")
    @Excel(name = "Sub_Plan_Seq")
    private String subPlanSeq;

    @ApiModelProperty(value = "Step_Seq")
    @Excel(name = "Step_Seq")
    private String stepSeq;

    @ApiModelProperty(value = "Sub_Plan_ID")
    @Excel(name = "Sub_Plan_ID")
    private String subPlanId;

    @ApiModelProperty(value = "Layer ID")
    @Excel(name = "Layer ID")
    private String layerId;

    @ApiModelProperty(value = "Stage ID")
    @Excel(name = "Stage ID")
    private String stageId;

    @ApiModelProperty(value = "Step ID")
    @Excel(name = "Step ID")
    private String stepId;

    @ApiModelProperty(value = "Step Desc")
    @Excel(name = "Step Desc")
    private String stepDesc;

    @ApiModelProperty(value = "Step Type")
    @Excel(name = "Step Type")
    private String stepType;

    @ApiModelProperty(value = "Recipe ID")
    @Excel(name = "Recipe ID")
    private String recipeId;

    @ApiModelProperty(value = "Equip Group ID")
    @Excel(name = "Equip Group ID")
    private String equipGroupId;

    @ApiModelProperty(value = "Equip Group Flag")
    @Excel(name = "Equip Group Flag")
    private String equipGroupFlag;

    @ApiModelProperty(value = "Work Area")
    @Excel(name = "Work Area")
    private String workArea;

    @ApiModelProperty(value = "Rwk Sub Plan ID")
    @Excel(name = "Rwk Sub Plan ID")
    private String rwkSubPlanId;

    @ApiModelProperty(value = "Outsourcing_Flag")
    @Excel(name = "Outsourcing_Flag")
    private String outsourcingFlag;

    @ApiModelProperty(value = "工作中心")
    @Excel(name = "工作中心")
    private String workCenterId;

    @ApiModelProperty(value = "工作中心文本")
    @Excel(name = "工作中心文本")
    private String workCenterText;

    @ApiModelProperty(value = "成本中心")
    @Excel(name = "成本中心")
    private String costCenterId;

    @ApiModelProperty(value = "成本中心描述")
    @Excel(name = "成本中心描述")
    private String costCenterText;

    @ApiModelProperty(value = "Flow基本数量")
    @Excel(name = "Flow基本数量")
    private String flowBaseQty;

    @ApiModelProperty(value = "Flow基本单位")
    @Excel(name = "Flow基本单位")
    private String flowBaseUnit;

    @ApiModelProperty(value = "工序数量[1]")
    @Excel(name = "工序数量[1]")
    private String operQty1;

    @ApiModelProperty(value = "工序单位[1]")
    @Excel(name = "工序单位[1]")
    private String operUnit1;

    @ApiModelProperty(value = "工序数量[2]")
    @Excel(name = "工序数量[2]")
    private String operQty2;

    @ApiModelProperty(value = "工序单位[2]")
    @Excel(name = "工序单位[2]")
    private String operUnit2;

    @ApiModelProperty(value = "作业工时")
    @Excel(name = "作业工时")
    private String operSpt;

    @ApiModelProperty(value = "工时单位")
    @Excel(name = "工时单位")
    private String sptUnit;

    @ApiModelProperty(value = "ACT001")
    @Excel(name = "ACT001")
    private String act001;

    @ApiModelProperty(value = "ACT002")
    @Excel(name = "ACT002")
    private String act002;

    @ApiModelProperty(value = "ACT003")
    @Excel(name = "ACT003")
    private String act003;

    @ApiModelProperty(value = "ACT004")
    @Excel(name = "ACT004")
    private String act004;

    @ApiModelProperty(value = "ACT005")
    @Excel(name = "ACT005")
    private String act005;

    @ApiModelProperty(value = "ACT006")
    @Excel(name = "ACT006")
    private String act006;

    @ApiModelProperty(value = "外协费率值")
    @Excel(name = "外协费率值")
    private String osrate;

    @ApiModelProperty(value = "Primary_Key")
    @Excel(name = "Primary_Key")
    private String primaryKey;

    @ApiModelProperty(value = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Excel(name = "导入时间")
    private Date fdopImportTime;





}
