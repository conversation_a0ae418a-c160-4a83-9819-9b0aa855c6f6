package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
@TableName(schema = "dwd", value = "dwd_material_mara")
@ApiModel("物料主数据")
@PermissionColumn(module = "ccms")
public class DwdMaterialMara {

    @ApiModelProperty(value = "物料编码")
    @NotBlank(message = "物料编码不能为空")
    @Excel(name = "物料编码")
    private String materialId;

    @ApiModelProperty(value = "物料描述")
    @Excel(name = "物料描述")
    private String materialDesc;

    @ApiModelProperty(value = "物料文本")
    @Excel(name = "物料文本")
    private String materialText;

    @ApiModelProperty(value = "产品编码")
    @Excel(name = "产品编码")
    private String productId;

    @ApiModelProperty(value = "产品CIM编码")
    @Excel(name = "产品CIM编码")
    private String productCimId;

    @ApiModelProperty(value = "产品ERP编码")
    @Excel(name = "产品ERP编码")
    private String productErpId;

    @ApiModelProperty(value = "基本单位")
    @Excel(name = "基本单位")
    private String baseUnit;

    @ApiModelProperty(value = "物料类型")
    @Excel(name = "物料类型")
    private String materialType;

    @ApiModelProperty(value = "物料类型描述")
    @Excel(name = "物料类型描述")
    private String materialTypeDesc;

    @ApiModelProperty(value = "物料组")
    @Excel(name = "物料组")
    private String materialGroup;

    @ApiModelProperty(value = "物料组描述")
    @Excel(name = "物料组描述")
    private String materialGroupDesc;

    @ApiModelProperty(value = "物料状态")
    @Excel(name = "物料状态")
    private String materialStatus;

    @ApiModelProperty(value = "物料状态描述")
    @Excel(name = "物料状态描述")
    private String materialStatusDesc;

    @ApiModelProperty(value = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Excel(name = "导入时间")
    private Date fdopImportTime;

}
