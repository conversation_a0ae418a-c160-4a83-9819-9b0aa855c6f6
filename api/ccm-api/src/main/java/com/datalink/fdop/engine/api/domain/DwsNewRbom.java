package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.datalink.fdop.common.core.annotation.SearchField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@TableName(schema = "dws", value = "dws_new_rbom")
@ApiModel("RBOM管理")
@PermissionColumn(module = "ccms")
public class DwsNewRbom {

    @Excel(name = "识别码")
    @ApiModelProperty(value = "识别码")
    private String flowCimCode;

    @Excel(name = "计数器")
    @ApiModelProperty(value = "计数器")
    private String count;

    @Excel(name = "数据集")
    @ApiModelProperty(value = "数据集")
    private String verId;

    @Excel(name = "工厂代码")
    @ApiModelProperty(value = "工厂代码")
    @NotBlank(message = "工厂代码不能为空")
    @SearchField(value = "工厂代码")
    private String plantId;

    @Excel(name = "制造工厂")
    @ApiModelProperty(value = "制造工厂")
    private String factoryId;

    @Excel(name = "产品编码")
    @ApiModelProperty(value = "产品编码")
    @NotBlank(message = "产品编码不能为空")
    @SearchField(value = "产品编码")
    private String productId;

    @Excel(name = "产品描述")
    @ApiModelProperty(value = "产品描述")
    private String productDesc;

    @Excel(name = "产品阶段")
    @ApiModelProperty(value = "产品阶段")
    private String techPhase;

    @Excel(name = "产品CIM编码")
    @ApiModelProperty(value = "产品CIM编码")
    private String productCimId;

    @Excel(name = "产品ERP编码")
    @ApiModelProperty(value = "产品ERP编码")
    private String productErpId;

    @Excel(name = "Product_Ver")
    @ApiModelProperty(value = "Product_Ver")
    @NotNull
    private String productVer;

    @Excel(name = "Process_ID")
    @ApiModelProperty(value = "Process_ID")
    @NotBlank(message = "Process_ID不能为空")
    private String processId;

    @Excel(name = "Process_Ver")
    @ApiModelProperty(value = "Process_Ver")
    @NotNull
    private String processVer;

    @Excel(name = "Step_NO")
    @ApiModelProperty(value = "Step_NO")
    @NotNull
    private String stepNo;

    @Excel(name = "Sub_Plan_Seq")
    @ApiModelProperty(value = "Sub_Plan_Seq")
    private String subPlanSeq;

    @Excel(name = "Step_Seq")
    @ApiModelProperty(value = "Step_Seq")
    private String stepSeq;

    @Excel(name = "Sub_plan_ID")
    @ApiModelProperty(value = "Sub_plan_ID")
    private String subPlanId;

    @Excel(name = "Layer_ID")
    @ApiModelProperty(value = "Layer_ID")
    private String layerId;

    @Excel(name = "Stage_ID")
    @ApiModelProperty(value = "Stage_ID")
    private String stageId;

    @Excel(name = "Step_ID")
    @ApiModelProperty(value = "Step_ID")
    private String stepId;

    @Excel(name = "Step_Desc")
    @ApiModelProperty(value = "Step_Desc")
    private String stepDesc;

    @Excel(name = "Step_Type")
    @ApiModelProperty(value = "Step_Type")
    private String stepType;

    @Excel(name = "Recipe_ID")
    @ApiModelProperty(value = "Recipe_ID")
    private String recipeId;

    @Excel(name = "Equip_Group_ID")
    @ApiModelProperty(value = "Equip_Group_ID")
    private String equipGroupId;

    @Excel(name = "Work_Area")
    @ApiModelProperty(value = "Work_Area")
    private String workArea;

    @Excel(name = "Rwk_Sub_Plan_ID")
    @ApiModelProperty(value = "Rwk_Sub_Plan_ID")
    private String rwkSubPlanId;

    @Excel(name = "Equip_Group_Flag")
    @ApiModelProperty(value = "Equip_Group_Flag")
    private String equipGroupFlag;

    @Excel(name = "工作中心")
    @ApiModelProperty(value = "工作中心")
    private String workCenterId;

    @Excel(name = "工作中心文本")
    @ApiModelProperty(value = "工作中心文本")
    private String workCenterText;

    @Excel(name = "成本中心")
    @ApiModelProperty(value = "成本中心")
    private String costCenterId;

    @Excel(name = "成本中心描述")
    @ApiModelProperty(value = "成本中心描述")
    private String costCenterText;

    @Excel(name = "Flow基础数量")
    @ApiModelProperty(value = "Flow基础数量")
    private Integer baseQty;

    @Excel(name = "Flow基础单位")
    @ApiModelProperty(value = "Flow基础单位")
    private String baseUnit;

    @Excel(name = "工序数量")
    @ApiModelProperty(value = "工序数量")
    private String operQty1;

    @Excel(name = "工序单位")
    @ApiModelProperty(value = "工序单位")
    private String operUnit1;

    @Excel(name = "原料编码")
    @ApiModelProperty(value = "原料编码")
    private String rawMaterialId;

    @Excel(name = "原料描述")
    @ApiModelProperty(value = "原料描述")
    private String rawMaterialDesc;

    @Excel(name = "用量[使用单位]")
    @ApiModelProperty(value = "用量[使用单位]")
    private String usageQty;

    @Excel(name = "使用单位")
    @ApiModelProperty(value = "使用单位")
    private String usageUnit;

    @Excel(name = "用量[基本单位]")
    @ApiModelProperty(value = "用量[基本单位]")
    private String baseQtySn;

    @Excel(name = "基本单位")
    @ApiModelProperty(value = "基本单位")
    private String baseUnitSn;

    @Excel(name = "Primary_Key")
    @ApiModelProperty(value = "Primary_Key")
    private String primaryKey;

    @Excel(name = "导入时间")
    @ApiModelProperty(value = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date fdopImportTime;
}