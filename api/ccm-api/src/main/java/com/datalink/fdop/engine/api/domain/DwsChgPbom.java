package com.datalink.fdop.engine.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.annotation.Excel;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.datalink.fdop.common.core.annotation.SearchField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
@TableName(schema = "dws", value = "dws_chg_pbom")
@ApiModel("PBOM管理")
@PermissionColumn(module = "ccms")
public class DwsChgPbom {

    @Excel(name = "标志: 对比")
    @ApiModelProperty(value = "标志: 对比")
    private String diffFlag;

    @Excel(name = "CIM识别码")
    @ApiModelProperty(value = "CIM识别码")
    private String pbomCimCode;

    @Excel(name = "计数器")
    @ApiModelProperty(value = "计数器")
    private String count;

    @Excel(name = "数据集")
    @ApiModelProperty(value = "数据集")
    @NotBlank(message = "数据集")
    private String verId;

    @Excel(name = "工厂代码")
    @ApiModelProperty(value = "工厂代码")
    @NotBlank(message = "工厂代码不能为空")
    @SearchField(value = "工厂代码")
    private String plantId;

    @Excel(name = "制造工厂")
    @ApiModelProperty(value = "制造工厂")
    private String factoryId;

    @Excel(name = "产品编码")
    @ApiModelProperty(value = "产品编码")
    @NotBlank(message = "产品编码不能为空")
    @SearchField(value = "产品编码")
    private String productId;

    @Excel(name = "产品描述")
    @ApiModelProperty(value = "产品描述")
    private String productDesc;

    @Excel(name = "产品ERP编码")
    @ApiModelProperty(value = "产品ERP编码")
    private String productErpId;

    @Excel(name = "产品CIM编码")
    @ApiModelProperty(value = "产品CIM编码")
    private String productCimId;

    @Excel(name = "原料编码")
    @ApiModelProperty(value = "原料编码")
    private String rawMaterialId;

    @Excel(name = "原料编码描述")
    @ApiModelProperty(value = "原料描述")
    private String rawMaterialDesc;

    @Excel(name = "默认值")
    @ApiModelProperty(value = "默认值")
    private String defaultValue;

    @Excel(name = "F_原料编码")
    @ApiModelProperty(value = "F_原料编码")
    private String frawMaterialId;

    @Excel(name = "F_原料描述")
    @ApiModelProperty(value = "F_原料描述")
    private String frawMaterialDesc;

    @Excel(name = "F_默认值")
    @ApiModelProperty(value = "F_默认值")
    private String fdefault;

    @ApiModelProperty(value = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @Excel(name = "导入时间")
    private Date fdopImportTime;

}
