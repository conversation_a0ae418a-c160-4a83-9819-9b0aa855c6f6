package com.datalink.fdop.element.model.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.datalink.fdop.common.core.enums.MenuType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/18 10:48
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("数据实体菜单/数据实体/数据实体关联表的结构")
public class DataEntityMenuTableVo {

    @ApiModelProperty(value = "数据实体菜单id/数据实体id/数据实体关联表id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "父级id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pid;

    @ApiModelProperty(value = "类型(MENU:菜单,NODE:实体,TABLE:关联表)")
    private MenuType menuType;

    @ApiModelProperty(value = "数据实体菜单编码、数据实体编码、数据实体关联表编码")
    private String code;

    @ApiModelProperty(value = "数据实体菜单名称、数据实体名称、数据实体关联表名称")
    private String name;

    @ApiModelProperty(value = "数据实体菜单描述、数据实体描述、数据实体关联表描述")
    private String description;

    @ApiModelProperty(value = "嵌套结构，菜单包含实体，实体包含关联表")
    private List<DataEntityMenuTableVo> children = new ArrayList<>();

}
