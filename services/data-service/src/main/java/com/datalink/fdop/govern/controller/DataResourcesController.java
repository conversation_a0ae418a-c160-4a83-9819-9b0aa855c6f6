package com.datalink.fdop.govern.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.govern.api.domain.DataResources;
import com.datalink.fdop.govern.api.domain.ImportDataLog;
import com.datalink.fdop.govern.service.IDataResourcesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/govern/dataResources")
@Api("数据资源")
public class DataResourcesController {

    @Autowired
    private IDataResourcesService dataResourcesService;

    @ApiOperation("创建")
    @Log(title = "数据资源")
    @PostMapping("/create")
    public R create(@RequestBody DataResources entity) {
        return R.ok(dataResourcesService.create(entity));
    }

    @ApiOperation("修改")
    @Log(title = "数据资源")
    @PostMapping("/update")
    public R update(@RequestBody DataResources entity) {
        return R.ok(dataResourcesService.update(entity));
    }

    @ApiOperation("删除")
    @Log(title = "数据资源")
    @DeleteMapping("/delete")
    public R delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.DATA_RESOURCES_TO_DELETE);
        }
        return R.ok(dataResourcesService.delete(ids));
    }

    @ApiOperation("根据ID查询")
    @Log(title = "数据资源")
    @GetMapping("/selectById/{id}")
    public R selectById(@PathVariable Long id) {
        return R.ok(dataResourcesService.selectById(id));
    }

    @ApiOperation("总览")
    @Log(title = "数据资源")
    @PostMapping(value = "/overview")
    public R<PageDataInfo<DataResources>> overview(@RequestParam(value = "sort", defaultValue = "ASC", required = false) String sort,
                                                          @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(dataResourcesService.overview(sort, searchVo));
    }

    @ApiOperation("查询报告类型的数据资源列表")
    @Log(title = "数据资源")
    @PostMapping(value = "/listByDataTypeIsReport")
    public R<List<DataResources>> listByDataTypeIsReport(@RequestBody(required = false) DataResources entity) {
        return R.ok(dataResourcesService.listByDataTypeIsReport(entity));
    }

    @ApiOperation("查询引用的数据资源列表")
    @Log(title = "数据资源")
    @PostMapping(value = "/listByNotReference")
    public R<List<DataResources>> listByNotReference(@RequestBody(required = false) DataResources entity) {
        return R.ok(dataResourcesService.listByNotReference(entity));
    }

    @ApiOperation("恢复")
    @Log(title = "数据资源")
    @PostMapping(value = "/resume")
    public R resume(@RequestBody List<DataResources> list) {
        return R.ok(dataResourcesService.resume(list));
    }

    @ApiOperation("废弃")
    @Log(title = "数据资源")
    @PostMapping(value = "/disuse")
    public R disuse(@RequestBody List<DataResources> list) {
        return R.ok(dataResourcesService.resume(list));
    }

    @ApiOperation(value = "导入")
    @Log(title = "数据资源")
    @PostMapping("/importData")
    public R importData(MultipartFile file) throws Exception {
        if (file == null || file.isEmpty()) {
            return R.fail("文件上传失败！");
        }
        if (!file.getOriginalFilename().endsWith(".xlsx") && !file.getOriginalFilename().endsWith(".xls")) {
            return R.fail("文件不是Excel文件，请上传.xlsx或.xls后缀文件");
        }
        ExcelUtil<DataResources> util = new ExcelUtil<>(DataResources.class);
        List<DataResources> list = util.importExcel(file.getInputStream());
        String operName = SecurityUtils.getUsername();
        dataResourcesService.importData(file.getOriginalFilename(), list, operName);
        return R.ok("导入中，请移至日志查看");
    }


    @ApiOperation(value = "导入模板")
    @Log(title = "数据资源")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DataResources> util = new ExcelUtil<>(DataResources.class);
        util.importTemplateExcel(response, "dataResources");
    }

    @ApiOperation("导出")
    @Log(title = "数据资源")
    @PostMapping("/export")
    public void export(HttpServletResponse response,
                       @RequestParam(value = "sort", defaultValue = "ASC", required = false) String sort,
                       @RequestBody(required = false) SearchVo searchVo) {
        List<DataResources> list = dataResourcesService.getList(sort, searchVo);
        ExcelUtil<DataResources> util = new ExcelUtil<>(DataResources.class);
        util.exportExcel(response, list, "数据资源数据");
    }

    @ApiOperation(value = "导入日志")
    @Log(title = "数据资源")
    @PostMapping(value = "/importLog")
    public R<PageDataInfo<ImportDataLog>> importLog(@RequestBody(required = false) ImportDataLog importDataLog,
                                                 @RequestParam(value = "sort", defaultValue = "DESC", required = false) String sort) {
        return R.ok(dataResourcesService.importLog(importDataLog, sort));
    }

    @ApiOperation(value = "日志详情")
    @Log(title = "数据资源")
    @GetMapping(value = "/importLog/selectById/{id}")
    public R<ImportDataLog> selectByIdLog(@PathVariable Long id) {
        return R.ok(dataResourcesService.selectByIdLog(id));
    }


}
