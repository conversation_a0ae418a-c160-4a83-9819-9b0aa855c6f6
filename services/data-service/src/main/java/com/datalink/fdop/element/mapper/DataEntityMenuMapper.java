package com.datalink.fdop.element.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.element.api.domain.DataEntityMenu;
import com.datalink.fdop.element.api.model.DataEntityTree;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface DataEntityMenuMapper extends BaseMapper<DataEntityMenu> {

    int createEntityMenuEdge(@Param("pid") Long pid, @Param("ids") List<Long> ids);

    int insertEntityMenu(@Param("dataEntityMenu") DataEntityMenu dataEntityMenu);

    int updateById(DataEntityMenu dataElementMenu);

    int bacthUpdatePidById(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteEntityMenuEdge(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteBatchIds(@Param("ids") List<Long> ids);

    DataEntityMenu selectById(Long id);

    List<DataEntityMenu> selectEntityListByL3Id(Long l3Id);

    DataEntityMenu selectByCode(String code);

    DataEntityMenu selectByPid(Long pid);

    DataEntityMenu checkCodeIsExists(@Param("id") Long id, @Param("code") String code);

    List<Long> selectIdsByPid(Long pid);

    List<DataEntityTree> selectMenuTree(@Param("sort") String sort, @Param("code") String code);

    List<String> selectAllTaskNameByProjectCode(Long projectCode);

    Long selectProjectCodeByTenantId(String tenantId);

    List<VlabelItem<DataEntityMenu>> selectByL3Id(@Param("l3Id") Long l3Id);

}
