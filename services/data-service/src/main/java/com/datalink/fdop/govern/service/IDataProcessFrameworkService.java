package com.datalink.fdop.govern.service;

import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.govern.api.domain.DataProcessFramework;
import com.datalink.fdop.govern.api.domain.DataProcessFrameworkTree;

import java.util.List;

public interface IDataProcessFrameworkService {

    int create(DataProcessFramework processFramework);

    int update(DataProcessFramework processFramework);

    int delete(List<Long> ids);

    DataProcessFramework selectById(Long id);

    DataProcessFramework selectByCode(String code);

    List<DataProcessFrameworkTree> treeList(String sort, String code, String name, String description);

    PageDataInfo<DataProcessFramework> overview(Long pid, String sort, SearchVo searchVo);

    List<SelectVo> selectVoList(DataProcessFramework processFramework);

    int copy(Long pid, List<DataProcessFramework> dataProcessFrameworkList);

}
