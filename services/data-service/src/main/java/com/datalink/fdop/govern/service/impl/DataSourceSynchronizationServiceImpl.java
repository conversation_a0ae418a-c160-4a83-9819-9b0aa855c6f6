package com.datalink.fdop.govern.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.DateUtils;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.age.SqlUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.drive.api.RemoteDriveService;
import com.datalink.fdop.drive.api.RemoteTaskService;
import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.drive.api.domain.dto.Field;
import com.datalink.fdop.govern.api.RemoteDolTaskService;
import com.datalink.fdop.govern.api.domain.*;
import com.datalink.fdop.govern.api.model.vo.*;
import com.datalink.fdop.govern.executor.FieldExecutor;
import com.datalink.fdop.govern.mapper.DataSourceSynchronizationMapper;
import com.datalink.fdop.govern.mapper.DataStandardMapper;
import com.datalink.fdop.govern.service.DataSourceSynchronizationService;
import com.datalink.fdop.quality.api.RemoteQualityService;
import com.datalink.fdop.quality.api.domain.DataQuality;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.dolphinscheduler.fdop.api.RemoteProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class DataSourceSynchronizationServiceImpl implements DataSourceSynchronizationService {


    @Autowired
    private DataSourceSynchronizationMapper dataSourceSynchronizationMapper;

    @Autowired
    private DataStandardMapper dataStandardMapper;

    @Autowired
    private RemoteProjectService remoteProjectService;

    @Autowired
    private RemoteTaskService remoteTaskService;

    @Autowired
    private RemoteDriveService remoteDriveService;

    @Autowired
    private RemoteQualityService remoteQualityService;

    @Autowired
    private RemoteDolTaskService remoteDolTaskService;

    @Override
    public int create(DataSourceSynchronization dataSourceSynchronization) {
        Long dataSourceId = dataSourceSynchronization.getDataSourceId();
        VlabelItem<DataSourceSynchronization> vlabelItem = dataSourceSynchronizationMapper.getSyncByDataSourceId(dataSourceId);
        if (vlabelItem == null) {
            long id = IdWorker.getId();
            dataSourceSynchronization.setId(id);

            //存表
            int insert = dataSourceSynchronizationMapper.create(dataSourceSynchronization);

            //创建与数据源的边
            int i = dataSourceSynchronizationMapper.createAadDataSourceRelation(id, dataSourceSynchronization.getDataSourceId());
            return insert;
        } else {
            dataSourceSynchronization.setId(vlabelItem.getProperties().getId());
            return update(dataSourceSynchronization);
        }
    }

    @Override
    public int update(DataSourceSynchronization dataSourceSynchronization) {
        if (StringUtils.isNotEmpty(dataSourceSynchronization.getSchemaJson())) {
            dataSourceSynchronization.setSchemaJson(dataSourceSynchronization.getSchemaJson().replace("\"", "\\\""));
        }
        int update = dataSourceSynchronizationMapper.updateById(dataSourceSynchronization);

        return update;
    }

    @Override
    public int del(List<Long> dataSourceIds) {
        int del = dataSourceSynchronizationMapper.del(dataSourceIds);
        return del;
    }

    @Override
    public DataSourceSynchronization getSyncByDataSourceId(Long dataSourceId) {
        VlabelItem<DataSourceSynchronization> vlabelItem = dataSourceSynchronizationMapper.getSyncByDataSourceId(dataSourceId);
        if (vlabelItem != null) {
            return vlabelItem.getProperties();
        }
        return new DataSourceSynchronization();
    }

    @Override
    public void sync(Long dataSourceId) {
        DataSourceSynchronization sourceSynchronization = this.getSyncByDataSourceId(dataSourceId);
        if (!sourceSynchronization.getEnabled()) {
            sourceSynchronization.setStartTime(null);
            sourceSynchronization.setEndTime(null);
        }
        if (sourceSynchronization == null) {
            throw new ServiceException("同步信息不存在");
        }
        R queryProjectByNameR = remoteProjectService.queryProjectByName(SecurityUtils
                .getTenantId().toString());
        if (queryProjectByNameR.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException(queryProjectByNameR.getMsg());
        }
        Map projectMap = (Map) queryProjectByNameR.getData();
        Long projectCode = MapUtils.getLong(projectMap, "code");

        R<DataSource> dataSourceR = remoteDriveService.queryDataSource(dataSourceId);
        if (dataSourceR.getCode() != 200) {
            throw new ServiceException("数据源不存在");
        }
        DataSource data = dataSourceR.getData();
        Map<String, Object> map = Maps.newHashMap();
        Map<String, Object> param = Maps.newHashMap();
        map.put("dataSourceId", sourceSynchronization.getDataSourceId());
        map.put("schemaJson", sourceSynchronization.getSchemaJson());
        map.put("type", sourceSynchronization.getType());
        map.put("projectCode", projectCode);
        map.put("rawScript", "echo 1");
        map.put("workerGroup", "default");
        map.put("failRetryTimes", 0);
        map.put("failRetryInterval", 1);
        map.put("timeoutFlag", "CLOSE");
        map.put("timeoutNotifyStrategy", "");
        map.put("timeout", 0);
        map.put("delayTime", 0);
        map.put("environmentCode", -1);
        map.put("cpuQuota", -1);
        map.put("memoryMax", -1);
        map.put("flag", "YES");
        param.put("taskParams", map);
        param.put("taskType", "SYNCHRONIZATION");
        param.put("dependence", new HashMap<>());
        String taskDefinitionJson = JSONObject.toJSONString(param);
        R r = remoteTaskService.runSyncTask(projectCode
                , data.getCode()
                , taskDefinitionJson
                , sourceSynchronization.getStartTime() != null ? DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, sourceSynchronization.getStartTime()) : ""
                , sourceSynchronization.getEndTime() != null ? DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, sourceSynchronization.getEndTime()) : ""
                , sourceSynchronization.getCrontab()
                , sourceSynchronization.getEnabled());
        System.out.println("执行完毕：" + r);
    }

    @Override
    public PageDataInfo<DataSource> overview(SearchVo searchVo) {

        Page<DataSource> page = PageUtils.getPage(DataSource.class);
        // 查询数据
        IPage<VlabelItem<DataSource>> dataSourceIPage = dataSourceSynchronizationMapper.selectAllDataSource(page, searchVo);
        List<DataSource> records = dataSourceIPage.getRecords().stream().map(VlabelItem::getProperties).collect(Collectors.toList());
        for (DataSource record : records) {
            VlabelItem<DataSourceSynchronization> vlabelItem = dataSourceSynchronizationMapper.getSyncByDataSourceId(record.getId());
            if (vlabelItem != null) {
                record.setIsSync(true);
            }
        }
        return PageUtils.getPageInfo(records, (int) dataSourceIPage.getTotal());
    }

    @Override
    @Transactional
    public R saveSyncData(List<DataSourceInfoVo> dataSourceInfoVos, Long dataSourceId,Long projectCode) {
        try {
            System.out.println("------保存数据开始------------------");
            //获取数据源下所有的字段
            List<VlabelItem<Field>> vlabelItems = dataSourceSynchronizationMapper.getFieldByDataSourceId(dataSourceId);
            List<Field> fieldList = vlabelItems.stream().map(VlabelItem::getProperties).collect(Collectors.toList());
            FieldExecutor fieldExecutor = new FieldExecutor( dataSourceSynchronizationMapper);


            R<DataSource> dataSourceR = remoteDriveService.queryDataSource(dataSourceId);
            if (dataSourceR.getCode() != 200) {
                throw new ServiceException(dataSourceR.getMsg());
            }
            DataSource dataSource = dataSourceR.getData();

            //记录id
            List<Long> databaseIdList=Lists.newArrayList();
            List<Long> scheamIdList=Lists.newArrayList();
            List<Long> tableIdList=Lists.newArrayList();
            List<Long> viewIdList=Lists.newArrayList();
            List<Long> fieldIdList=Lists.newArrayList();


            System.out.println("------保存数据："+dataSourceInfoVos);
            for (DataSourceInfoVo dataSourceInfoVo : dataSourceInfoVos) {

                List<SchemaVo> schemas = dataSourceInfoVo.getSchemas();
                List<DatabaseVo> databases = dataSourceInfoVo.getDatabases();

                if (CollectionUtils.isNotEmpty(databases)) {
                    //保存源到库
                    for (DatabaseVo database : databases) {
                        System.out.println("------保存数据库信息："+database);
                        DatabaseVo databaseVo= dataSourceSynchronizationMapper.findDatabaseByCode(database.getDatabaseName(),dataSource.getId());

                        Long databaseId =0l;
                        if (databaseVo==null) {
                            SynchronizationDatabase syncDatabase = new SynchronizationDatabase();
                            databaseId = IdWorker.getId();
                            syncDatabase.setId(databaseId);
                            syncDatabase.setDataSourceId(dataSourceId);
                            syncDatabase.setCode(database.getDatabaseName());
                            syncDatabase.setName(database.getDatabaseName());
                            syncDatabase.setMetadata(dataSource.getCode());
                            dataSourceSynchronizationMapper.createSyncDatabase(syncDatabase);
                            dataSourceSynchronizationMapper.createDataSourceSyncDatabase(dataSourceId, syncDatabase.getId());
                        }else {
                            databaseId=databaseVo.getId();
                        }
                        databaseIdList.add(databaseId);

                        List<SchemaVo> databaseSchemas = database.getSchemas();
                        System.out.println("------保存schemas："+databaseSchemas);
                        if (CollectionUtils.isNotEmpty(databaseSchemas)) {
                            for (SchemaVo databaseSchema : databaseSchemas) {
                                List<TableVo> tableVos = Lists.newArrayList();
                                List<ViewVo> viewVos=Lists.newArrayList();
                                SynchronizationSchema syncSchema= dataSourceSynchronizationMapper.findSchemaByCode(databaseSchema.getSchemaName(),dataSource.getId());

                                if (syncSchema==null) {
                                    syncSchema = new SynchronizationSchema();
                                    syncSchema.setId(IdWorker.getId());
                                    syncSchema.setCode(databaseSchema.getSchemaName());
                                    syncSchema.setName(databaseSchema.getSchemaName());
                                    syncSchema.setDataSourceId(dataSourceId);
                                    syncSchema.setMetadata(database.getDatabaseName());
                                    dataSourceSynchronizationMapper.createSyncSchema(syncSchema);
                                    dataSourceSynchronizationMapper.createSyncDatabaseSyncSchemaEdge(databaseId, syncSchema.getId());
                                }
                                scheamIdList.add(syncSchema.getId());
                                databaseSchema.setId(syncSchema.getId());
                                List<TableVo> tables = databaseSchema.getTables();
                                //保存表信息
                                saveTableInfo(syncSchema, tables, dataSourceId, fieldExecutor,tableIdList,fieldIdList);
                                tableVos.addAll(tables);
                                List<ViewVo> views = databaseSchema.getViews();
                                //保存视图信息
                                saveViewInfo(syncSchema, views, dataSourceId, fieldExecutor,viewIdList,fieldIdList);
                                viewVos.addAll(views);
                                System.out.println("------保存tables："+tables);
                                System.out.println("------保存views："+views);
                                if (CollectionUtils.isNotEmpty(tables)||CollectionUtils.isNotEmpty(views)) {
                                    SyncTableInfoVo syncTableInfoVo=new SyncTableInfoVo();
                                    syncTableInfoVo.setTableVoList(tableVos);
                                    syncTableInfoVo.setViewVos(viewVos);
                                    System.out.println("------syncTableInfoVo:"+JSONObject.toJSONString(syncTableInfoVo));
                                    R r = remoteDolTaskService.runSyncStandardCheckTask(projectCode, String.valueOf(dataSource.getId()), syncTableInfoVo);
                                    if (r.getCode()!=200) {
                                        throw new ServiceException(r.getMsg());
                                    }
                                }

                            }
                        }

                    }
                }

                if (CollectionUtils.isNotEmpty(schemas)) {
                    //保存源到schema
                    for (SchemaVo databaseSchema : schemas) {
                        List<TableVo> tableVos = Lists.newArrayList();
                        List<ViewVo> viewVos=Lists.newArrayList();

                        SynchronizationSchema syncSchema= dataSourceSynchronizationMapper.findSchemaByCode(databaseSchema.getSchemaName(), dataSource.getId());

                        if (syncSchema==null) {
                            syncSchema = new SynchronizationSchema();
                            long schemaId = IdWorker.getId();
                            syncSchema.setId(schemaId);
                            syncSchema.setCode(databaseSchema.getSchemaName());
                            syncSchema.setName(databaseSchema.getSchemaName());
                            syncSchema.setDataSourceId(dataSourceId);
                            syncSchema.setMetadata(dataSource.getCode());
                            dataSourceSynchronizationMapper.createSyncSchema(syncSchema);
                            dataSourceSynchronizationMapper.createDataSourceSyncSchemaEdge(dataSourceId, schemaId);
                        }
                        scheamIdList.add(syncSchema.getId());
                        databaseSchema.setId(syncSchema.getId());
                        List<TableVo> tables = databaseSchema.getTables();
                        saveTableInfo(syncSchema, tables, dataSourceId, fieldExecutor, tableIdList, fieldIdList);
                        tableVos.addAll(tables);
                        List<ViewVo> views = databaseSchema.getViews();
                        saveViewInfo(syncSchema, views, dataSourceId, fieldExecutor, viewIdList, fieldIdList);
                        viewVos.addAll(views);

                        if (CollectionUtils.isNotEmpty(tables)&&CollectionUtils.isNotEmpty(views)) {
                            SyncTableInfoVo syncTableInfoVo=new SyncTableInfoVo();
                            syncTableInfoVo.setTableVoList(tableVos);
                            syncTableInfoVo.setViewVos(viewVos);
                            System.out.println("------syncTableInfoVo:"+JSONObject.toJSONString(syncTableInfoVo));
                            R r = remoteDolTaskService.runSyncStandardCheckTask(projectCode, String.valueOf(dataSource.getId()), syncTableInfoVo);
                            if (r.getCode()!=200) {
                                throw new ServiceException(r.getMsg());
                            }
                        }
                    }

                }
            }




            if (CollectionUtils.isNotEmpty(fieldIdList)) {
                List<Field> fields = dataSourceSynchronizationMapper.findFieldByDataSourceId(dataSourceId);
                List<Long> list = fields.stream().map(Field::getId).filter(i -> !fieldIdList.contains(i)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(list)) {
                    List<List<Long>> inSql = SqlUtils.getInSql(list);
                    dataSourceSynchronizationMapper.delSyncField(inSql,dataSourceId);
                }
            }else {
                List<List<Long>> inSql = SqlUtils.getInSql(fieldIdList);
                dataSourceSynchronizationMapper.delSyncField(inSql,dataSourceId);
            }
            if (CollectionUtils.isNotEmpty(databaseIdList)) {
                List<DatabaseVo> databaseVos = dataSourceSynchronizationMapper.findDatabaseByDataSourceId(dataSourceId);
                List<Long> list = databaseVos.stream().map(DatabaseVo::getId).filter(i -> !databaseIdList.contains(i)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(list)) {
                    dataSourceSynchronizationMapper.delSyncDatabase(list,dataSourceId);
                }
            }else {
                dataSourceSynchronizationMapper.delSyncDatabase(databaseIdList,dataSourceId);
            }
            if (CollectionUtils.isNotEmpty(scheamIdList)) {
                List<SynchronizationSchema> schema = dataSourceSynchronizationMapper.findSchemaByDataSourceId(dataSourceId);
                List<Long> list = schema.stream().map(SynchronizationSchema::getId).filter(i -> !scheamIdList.contains(i)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(list)) {
                    dataSourceSynchronizationMapper.delSyncSchema(list,dataSourceId);
                }
            }else {
                dataSourceSynchronizationMapper.delSyncSchema(scheamIdList,dataSourceId);
            }
            if (CollectionUtils.isNotEmpty(tableIdList)) {
                List<SynchronizationTable> table = dataSourceSynchronizationMapper.findTableByDataSourceId(dataSourceId);
                List<Long> list = table.stream().map(SynchronizationTable::getId).filter(i -> !tableIdList.contains(i)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(list)) {
                    List<List<Long>> inSql = SqlUtils.getInSql(list);
                    dataSourceSynchronizationMapper.delSyncTable(inSql,dataSourceId);
                }
            }else {
                List<List<Long>> inSql = SqlUtils.getInSql(tableIdList);
                dataSourceSynchronizationMapper.delSyncTable(inSql,dataSourceId);
            }
            if (CollectionUtils.isNotEmpty(viewIdList)) {
                List<SynchronizationView> view = dataSourceSynchronizationMapper.findViewByDataSourceId(dataSourceId);
                List<Long> list = view.stream().map(SynchronizationView::getId).filter(i -> !viewIdList.contains(i)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(list)) {
                    List<List<Long>> inSql = SqlUtils.getInSql(list);
                    dataSourceSynchronizationMapper.delSyncView(inSql,dataSourceId);
                }
            }else {
                List<List<Long>> inSql = SqlUtils.getInSql(viewIdList);
                dataSourceSynchronizationMapper.delSyncView(inSql,dataSourceId);
            }





        } catch (Exception e) {
            e.printStackTrace();
            return R.fail(e.getMessage());
        }
        return R.ok();

    }

    @Override
    public List<DataSourceInfoVo> getMetadataTree(String tableName,String sort) {
        List<DataSourceInfoVo> result = Lists.newArrayList();
        //获取搜有的数据源
        R<List<DataSource>> listR = remoteDriveService.selectAll(sort);
        if (listR.getCode() != 200) {
            throw new ServiceException("获取数据源失败");
        }
        List<DataSource> dataSourceList = listR.getData();
        for (DataSource dataSource : dataSourceList) {
            DataSourceInfoVo dataSourceInfoVo = new DataSourceInfoVo();
            dataSourceInfoVo.setDataSource(dataSource);
            //获取数据源下的数据库
            List<DatabaseVo> databaseVoList = dataSourceSynchronizationMapper.getDatabaseByDataSourceId(dataSource.getId(), sort);
            if (CollectionUtils.isNotEmpty(databaseVoList)) {
                for (DatabaseVo databaseVo : databaseVoList) {
                    List<SchemaVo> schemaVoList = dataSourceSynchronizationMapper.getSchemaByDatabaseId(databaseVo.getId(),sort);
                    if (CollectionUtils.isNotEmpty(schemaVoList)) {
                        getSchemaInfo(schemaVoList, tableName,sort);
                        databaseVo.getSchemas().addAll(schemaVoList);
                    }
                }
                dataSourceInfoVo.getDatabases().addAll(databaseVoList);
            }
            List<SchemaVo> schemaVoList = dataSourceSynchronizationMapper.getSchemaByDataSourceId(dataSource.getId(),sort);
            if (CollectionUtils.isNotEmpty(schemaVoList)) {
                if (CollectionUtils.isNotEmpty(schemaVoList)) {
                    getSchemaInfo(schemaVoList, tableName,sort);
                    dataSourceInfoVo.getSchemas().addAll(schemaVoList);
                }
            }
            result.add(dataSourceInfoVo);
        }
        return result;
    }

    @Override
    public List<Field> getFieldByTableId(Long tableId) {
        if (tableId == null) {
            throw new ServiceException("参数错误");
        }
        List<Field> fields= dataSourceSynchronizationMapper.getFieldByTableId(tableId);
        if (CollectionUtils.isNotEmpty(fields)) {
            for (Field field : fields) {
                DataStandard standard = dataStandardMapper.getFieldStandard(field.getId());
                if (standard != null) {
                    field.setStandardId(standard.getId());
                    field.setStandardCode(standard.getCode());
                }
                VlabelItem<DataQuality> qualityV = dataSourceSynchronizationMapper.getQualityByField(field.getId());
                if (qualityV != null && qualityV.getProperties() != null) {
                    DataQuality quality = qualityV.getProperties();
                    field.setQualityId(quality.getId());
                    field.setQualityCode(quality.getCode());
                }
            }
            return fields;

        }

        return new ArrayList<Field>();
    }

    @Override
    public void bindingFieldId(Long fieldId, Long standardId, Long qualityId) {
        dataSourceSynchronizationMapper.delFieldStandardEdge(fieldId);
        if (standardId != null) {
            dataSourceSynchronizationMapper.createFieldStandardEdge(fieldId, standardId);
        }
        dataSourceSynchronizationMapper.delFieldQualityEdge(fieldId);
        if (qualityId != null) {
            dataSourceSynchronizationMapper.createFieldQualityEdge(fieldId, qualityId);
        }
    }

    @Override
    public PageDataInfo<DataSourceInfoShowVo> metadataOverview(SearchVo searchVo) {
        // 获取分页参数
        Page<DataSourceInfoShowVo> page = PageUtils.getPage(DataSourceInfoShowVo.class);
        // 查询数据
        IPage<DataSourceInfoShowVo> iPage = dataSourceSynchronizationMapper.selectAll(page, searchVo);
        return PageUtils.getPageInfo(iPage.getRecords(), (int) iPage.getTotal());
    }

    @Override
    public DataQuality getQualityByField(Long fieldId) {
        VlabelItem<DataQuality> vlabelItem = dataSourceSynchronizationMapper.getQualityByField(fieldId);
        if (vlabelItem == null) {
            return null;
        }
        return vlabelItem.getProperties();
    }

    private void getSchemaInfo(List<SchemaVo> schemaVoList, String tableName,String sort) {
        for (SchemaVo schemaVo : schemaVoList) {
            List<TableVo> tableVoList = dataSourceSynchronizationMapper.getTableBySchemaId(schemaVo.getId(), tableName,sort);
            if (CollectionUtils.isNotEmpty(tableVoList)) {
                schemaVo.getTables().addAll(tableVoList);
            }

            List<ViewVo> viewVos = dataSourceSynchronizationMapper.getViewBySchemaId(schemaVo.getId(), tableName,sort);
            if (CollectionUtils.isNotEmpty(viewVos)) {
                schemaVo.getViews().addAll(viewVos);
            }
        }
    }


    public void saveTableInfo(SynchronizationSchema schema, List<TableVo> tables, Long dataSourceId, FieldExecutor fieldExecutor, List<Long> tableIdList, List<Long> fieldIdList) throws Exception {
        if (CollectionUtils.isNotEmpty(tables)) {
            for (TableVo table : tables) {
                SynchronizationTable syncTable=dataSourceSynchronizationMapper.findTableByCode(table.getTableName(),schema.getId());
                if (syncTable==null) {
                    syncTable = new SynchronizationTable();
                    long tableId = IdWorker.getId();
                    syncTable.setId(tableId);
                    syncTable.setCode(table.getTableName());
                    syncTable.setName(table.getTableName());
                    syncTable.setDataSourceId(dataSourceId);
                    syncTable.setMetadata(schema.getCode());
                    dataSourceSynchronizationMapper.createSyncTable(syncTable);
                    dataSourceSynchronizationMapper.createSyncSchemaSyncTableEdge(schema.getId(), tableId);
                }
                tableIdList.add(syncTable.getId());
                table.setId(syncTable.getId());
                List<Field> fields = table.getFields();
                fieldExecutor.saveTableFieldInfo(fields, syncTable,fieldIdList);
            }
        }
    }

    public void saveViewInfo(SynchronizationSchema schema, List<ViewVo> views, Long dataSourceId, FieldExecutor fieldExecutor, List<Long> viewIdList, List<Long> fieldIdList) throws Exception {
        if (CollectionUtils.isNotEmpty(views)) {
            if (CollectionUtils.isNotEmpty(views)) {
                for (ViewVo viewVo : views) {
                    SynchronizationView syncView=dataSourceSynchronizationMapper.findViewByCode(viewVo.getViewName(),schema.getId());
                    if (syncView==null) {
                        syncView = new SynchronizationView();
                        long viewId = IdWorker.getId();
                        syncView.setId(viewId);
                        syncView.setCode(viewVo.getViewName());
                        syncView.setName(viewVo.getViewName());
                        syncView.setDataSourceId(dataSourceId);
                        syncView.setMetadata(schema.getCode());
                        dataSourceSynchronizationMapper.createSyncView(syncView);
                        dataSourceSynchronizationMapper.createSyncSchemaSyncViewEdge(schema.getId(), viewId);
                    }
                    viewIdList.add(syncView.getId());
                    viewVo.setId(syncView.getId());
                    List<Field> fields = viewVo.getFields();
                    fieldExecutor.saveViewFieldInfo(fields, syncView,fieldIdList);
                }
            }
        }
    }

    @Override
    public Boolean checkField(Long fId) {
        Field field = dataSourceSynchronizationMapper.selectFieldById(fId);

        // 检验标准
        DataStandard standard = dataStandardMapper.getFieldStandard(field.getId());
        if (standard != null) {
            if (standard.getFieldType() != field.getBaseFieldType()) {
                throw new ServiceException(MessageFormat.format(Status.CHECK_STANDARD_FIELD_TYPE.getMsg(),
                        field.getFieldName(), field.getBaseFieldType(), standard.getFieldType()));
            }
            if (standard.getLength() != field.getLength()) {
                throw new ServiceException(MessageFormat.format(Status.CHECK_STANDARD_FIELD_LENGTH.getMsg(),
                        field.getFieldName(), field.getLength(), standard.getLength()));
            }
            if (standard.getDecimalLength() != field.getDecimalLength()) {
                throw new ServiceException(MessageFormat.format(Status.CHECK_STANDARD_FIELD_DECIMALLENGTH.getMsg(),
                        field.getFieldName(), field.getDecimalLength(), standard.getDecimalLength()));
            }
        }
        // 校验规则
        VlabelItem<DataQuality> qualityV = dataSourceSynchronizationMapper.getQualityByField(field.getId());
        if (qualityV != null) {
            DataQuality quality = qualityV.getProperties();
            // 查询规则具体信息
            R<DataQuality> dataQualityR = remoteQualityService.selectById(quality.getId());
            if (dataQualityR.getCode() != Status.SUCCESS.getCode()) {
                throw new ServiceException(dataQualityR.getMsg());
            }
            DataQuality dataQuality = dataQualityR.getData();
            if (dataQuality.getFieldType() != field.getBaseFieldType()) {
                throw new ServiceException(MessageFormat.format(Status.CHECK_QUALITY_FIELD_TYPE.getMsg(),
                        field.getFieldName(), field.getBaseFieldType(), dataQuality.getFieldType()));
            }
            if (dataQuality.getLength() != field.getLength()) {
                throw new ServiceException(MessageFormat.format(Status.CHECK_QUALITY_FIELD_LENGTH.getMsg(),
                        field.getFieldName(), field.getLength(), dataQuality.getLength()));
            }
            if (dataQuality.getDecimalLength() != field.getDecimalLength()) {
                throw new ServiceException(MessageFormat.format(Status.CHECK_QUALITY_FIELD_DECIMALLENGTH.getMsg(),
                        field.getFieldName(), field.getDecimalLength(), dataQuality.getDecimalLength()));
            }
        }

        return true;
    }

    @Override
    public MonitorTableOrVIewInfoVo selectMonitorTableOrViewInfoList(Long monitorId) {
        List<SynchronizationTable> tableList = dataSourceSynchronizationMapper.selectMonitorTableInfoList(monitorId);

        List<SynchronizationView> viewList = dataSourceSynchronizationMapper.selectMonitorViewInfoList(monitorId);

        return new MonitorTableOrVIewInfoVo(tableList, viewList);
    }

    @Override
    public Field getFieldById(Long fieldId) {
        Field field = dataSourceSynchronizationMapper.getFieldByFieldId(fieldId);
        if (field==null) {
            return new Field();
        }

        return field;
    }
}
