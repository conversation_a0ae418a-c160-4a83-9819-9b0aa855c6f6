package com.datalink.fdop.govern.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.DateUtils;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.govern.api.domain.*;
import com.datalink.fdop.govern.api.enums.*;
import com.datalink.fdop.govern.mapper.DataResourcesMapper;
import com.datalink.fdop.govern.service.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.Date;
import java.util.List;

@Service
public class DataResourcesServiceImpl implements IDataResourcesService {

    @Autowired
    private DataResourcesMapper dataResourcesMapper;

    @Autowired
    private IDataProcessFrameworkService dataProcessFrameworkService;

    @Autowired
    private IDataSecurityLevelService dataSecurityLevelService;

    @Autowired
    private IDataSecurityClassificationService dataSecurityClassificationService;

    @Autowired
    private IDataApplicationFrameworkService dataApplicationFrameworkService;

    @Autowired
    private ImportDataLogService importDataLogService;
    /**
     * 版本号格式
     */
    private final String VERSION_FORMAT = "V%s-%s";

    private final String LOG_TABLE_NAME = "d_g_data_resources_import_log";

    @Override
    public int create(DataResources entity) {
        if (dataResourcesMapper.selectByCode(entity.getCode()) != null) {
            throw new ServiceException(Status.DATA_RESOURCES_EXIST);
        }
        // 校验字典值
        checkDictionaryValues(entity);
        entity.setId(IdWorker.getId());
        // ⾃动⽣成，每次修改升⼀个版本，保留历史版本，不可编辑。版本生成规则：V递增版本号-版本时间年⽉⽇
        entity.setVersion(String.format(VERSION_FORMAT, 1, DateUtils.dateTimeNow(DateUtils.YYYYMMDD)));
        // 检查所有表单项是否已填写，如果已填写，则状态为已完善，否则为新建
        boolean checked = checkAllFieldsNotNull(entity);
        if (checked) {
            entity.setDataStatus(DataStatusEnum.READ);
            entity.setDataStatusLabel(DataStatusEnum.READ.getDesc());
        } else {
            entity.setDataStatus(DataStatusEnum.NEW);
            entity.setDataStatusLabel(DataStatusEnum.NEW.getDesc());
        }
        int insert = dataResourcesMapper.insert(entity);
        if (insert > 0) {
            // 备份版本
            entity.setId(IdWorker.getId());
            dataResourcesMapper.insertVersion(entity);
        }
        return insert;
    }

    @Override
    public int update(DataResources entity) {
        VlabelItem<DataResources> vlabelItem = dataResourcesMapper.selectById(entity.getId());
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATA_RESOURCES_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(entity.getCode()) && dataResourcesMapper.checkCodeIsExists(entity.getId(), entity.getCode()) != null) {
            throw new ServiceException(Status.DATA_RESOURCES_EXIST);
        }
        // 校验字典值
        checkDictionaryValues(entity);
        // ⾃动⽣成，每次修改升⼀个版本，保留历史版本，不可编辑。版本生成规则：V递增版本号-版本时间年⽉⽇
        // 提取旧版本号
        String version = vlabelItem.getProperties().getVersion();
        if (StringUtils.isNotEmpty(version)) {
            // 提取版本号
            String[] split = version.split("-");
            if (split.length > 1) {
                // 提取版本号
                int versionNum = Integer.parseInt(split[0].substring(1));
                // 提取版本号
                entity.setVersion(String.format(VERSION_FORMAT, versionNum + 1, DateUtils.dateTimeNow(DateUtils.YYYYMMDD)));
            }
        }
        // 检查所有表单项是否已填写，如果已填写，则状态为已完善，否则为新建
        boolean checked = checkAllFieldsNotNull(entity);
        if (checked) {
            entity.setDataStatus(DataStatusEnum.READ);
            entity.setDataStatusLabel(DataStatusEnum.READ.getDesc());
        } else {
            entity.setDataStatus(DataStatusEnum.NEW);
            entity.setDataStatusLabel(DataStatusEnum.NEW.getDesc());
        }
        int update = dataResourcesMapper.updateById(entity);
        if (update > 0) {
            // 备份版本
            entity.setId(IdWorker.getId());
            dataResourcesMapper.insertVersion(entity);
        }
        return update;
    }

    private void checkDictionaryValues(DataResources entity) {
        if (entity.getAreasId() == null || entity.getAreasId() == -1L) {
            throw new ServiceException("领域L1" + Status.DATA_PROCESS_FRAMEWORK_NOT_EXIST);
        }
        // 校验领域L1
        DataProcessFramework l1 = dataProcessFrameworkService.selectById(entity.getAreasId());
        entity.setAreasId(l1.getId());
        entity.setAreasCode(l1.getCode());
        entity.setAreasName(l1.getName());
        if (entity.getThemeId() == null || entity.getThemeId() == -1L) {
            throw new ServiceException("主题L2" + Status.DATA_PROCESS_FRAMEWORK_NOT_EXIST);
        }
        // 校验主题L2
        DataProcessFramework l2 = dataProcessFrameworkService.selectById(entity.getThemeId());
        entity.setThemeId(l2.getId());
        entity.setThemeCode(l2.getCode());
        entity.setThemeName(l2.getName());
        if (entity.getSecurityLevelId() == null || entity.getSecurityLevelId() == -1L) {
            throw new ServiceException(Status.DATA_SECURITY_LEVEL_NOT_EXIST);
        }
        // 校验数据安全等级
        DataSecurityLevel dataSecurityLevel = dataSecurityLevelService.selectById(entity.getSecurityLevelId());
        entity.setSecurityLevelId(dataSecurityLevel.getId());
        entity.setSecurityLevelCode(dataSecurityLevel.getCode());
        entity.setSecurityLevelName(dataSecurityLevel.getName());
        if (entity.getSecuritySortId() == null || entity.getSecuritySortId() == -1L) {
            throw new ServiceException(Status.DATA_SECURITY_CLASS_NOT_EXIST);
        }
        // 校验数据安全分类
        DataSecurityClassification dataSecurityClassification = dataSecurityClassificationService.selectById(entity.getSecuritySortId());
        entity.setSecuritySortId(dataSecurityClassification.getId());
        entity.setSecuritySortCode(dataSecurityClassification.getCode());
        entity.setSecuritySortName(dataSecurityClassification.getName());
        if (entity.getSourceSystemId() == null || entity.getSourceSystemId() == -1L) {
            throw new ServiceException(Status.DATA_APP_NOT_EXIST);
        }
        // 校验数据来源系统
        DataApplicationFramework applicationFramework = dataApplicationFrameworkService.selectById(entity.getSourceSystemId());
        entity.setSourceSystemId(applicationFramework.getId());
        entity.setSourceSystemCode(applicationFramework.getCode());
        entity.setSourceSystemName(applicationFramework.getName());
    }

    @Override
    public int delete(List<Long> ids) {
        return dataResourcesMapper.deleteBatchIds(ids);
    }

    @Override
    public List<DataResources> listByDataTypeIsReport(DataResources dataResources) {
        return dataResourcesMapper.listByDataTypeIsReport(dataResources);
    }

    @Override
    public List<DataResources> listByNotReference(DataResources dataResources) {
        List<Long> ids = dataResourcesMapper.selectReferenceIds();
        return dataResourcesMapper.listByNotReference(dataResources, ids);
    }

    @Override
    public int resume(List<DataResources> list) {
        return 0;
    }

    @Override
    public List<DataResources> getList(String sort, SearchVo searchVo) {
        return dataResourcesMapper.getList(sort, searchVo);
    }

    @Async
    @Override
    public void importData(String fileName, List<DataResources> list, String operName) {
        ImportDataLog importDataLog = new ImportDataLog();
        importDataLog.setId(IdWorker.getId());
        importDataLog.setStatus("导入中");
        importDataLog.setImportBy(operName);
        importDataLog.setImportTime(new Date());
        importDataLog.setFileName(fileName);
        String log = "--INFO 数据正在导入中...\n";
        importDataLog.setLog(log);
        importDataLog.setTableName(LOG_TABLE_NAME);
        importDataLogService.insert(importDataLog);
        try {
            if (CollectionUtils.isEmpty(list)) {
                throw new ServiceException("导入的数据不能为空");
            }
            int successNum = 0;
            int failureNum = 0;
            StringBuilder successMsg = new StringBuilder();
            StringBuilder failureMsg = new StringBuilder();
            for (DataResources entity : list) {
                try {
                    if (StringUtils.isEmpty(entity.getCode())) {
                        throw new ServiceException("编码必填");
                    }
                    if (StringUtils.isEmpty(entity.getName())) {
                        throw new ServiceException("名称必填");
                    }
                    boolean updateFlag = false;
                    VlabelItem<DataResources> dataResourcesVlabelItem = dataResourcesMapper.selectByCode(entity.getCode());
                    if (dataResourcesVlabelItem != null) {
                        updateFlag = true;
                        ExcelUtil<DataResources> excelUtil = new ExcelUtil<>(DataResources.class);
                        DataResources dbEntity = dataResourcesVlabelItem.getProperties();
                        entity = excelUtil.copyImportDataToDbData(entity, dbEntity);
                        // ⾃动⽣成，每次修改升⼀个版本，保留历史版本，不可编辑。版本生成规则：V递增版本号-版本时间年⽉⽇
                        // 提取旧版本号
                        String version = entity.getVersion();
                        if (StringUtils.isNotEmpty(version)) {
                            // 提取版本号
                            String[] split = version.split("-");
                            if (split.length > 1) {
                                // 提取版本号
                                int versionNum = Integer.parseInt(split[0].substring(1));
                                // 提取版本号
                                entity.setVersion(String.format(VERSION_FORMAT, versionNum + 1, DateUtils.dateTimeNow(DateUtils.YYYYMMDD)));
                            }
                        }
                    } else {
                        entity.setId(IdWorker.getId());
                        // ⾃动⽣成，每次修改升⼀个版本，保留历史版本，不可编辑。版本生成规则：V递增版本号-版本时间年⽉⽇
                        entity.setVersion(String.format(VERSION_FORMAT, 1, DateUtils.dateTimeNow(DateUtils.YYYYMMDD)));
                    }
                    // 校验
                    if (StringUtils.isNotEmpty(entity.getAreasCode())) {
                        DataProcessFramework areas = dataProcessFrameworkService.selectByCode(entity.getAreasCode());
                        if (areas == null) {
                            throw new ServiceException("领域L1[" + entity.getAreasCode() + "]不存在");
                        }
                        entity.setAreasId(areas.getId());
                        entity.setAreasName(areas.getName());
                    }
                    if (StringUtils.isNotEmpty(entity.getThemeCode())) {
                        DataProcessFramework theme = dataProcessFrameworkService.selectByCode(entity.getThemeCode());
                        if (theme == null) {
                            throw new ServiceException("主题L2[" + entity.getThemeCode() + "]不存在");
                        }
                        entity.setThemeId(theme.getId());
                        entity.setThemeName(theme.getName());
                    }
                    if (StringUtils.isNotEmpty(entity.getSecuritySortCode())) {
                        DataSecurityClassification dataSecurityClassification = dataSecurityClassificationService.selectByCode(entity.getSecuritySortCode());
                        if (dataSecurityClassification == null) {
                            throw new ServiceException("数据安全分类[" + entity.getSecuritySortCode() + "]不存在");
                        }
                        entity.setSecuritySortId(dataSecurityClassification.getId());
                        entity.setSecuritySortName(dataSecurityClassification.getName());
                    }
                    if (StringUtils.isNotEmpty(entity.getSecurityLevelCode())) {
                        DataSecurityLevel dataSecurityLevel = dataSecurityLevelService.selectByCode(entity.getSecurityLevelCode());
                        if (dataSecurityLevel == null) {
                            throw new ServiceException("数据安全分级[" + entity.getSecurityLevelCode() + "]不存在");
                        }
                        entity.setSecurityLevelId(dataSecurityLevel.getId());
                        entity.setSecurityLevelName(dataSecurityLevel.getName());
                    }
                    if (StringUtils.isNotEmpty(entity.getSourceSystemCode())) {
                        DataApplicationFramework sourceSystem = dataApplicationFrameworkService.selectByCode(entity.getSourceSystemCode());
                        if (sourceSystem == null) {
                            throw new ServiceException("源系统[" + entity.getSourceSystemCode() + "]不存在");
                        }
                        entity.setSourceSystemId(sourceSystem.getId());
                        entity.setSourceSystemName(sourceSystem.getName());
                    }

                    // 检查所有表单项是否已填写，如果已填写，则状态为已完善，否则为新建
                    if (StringUtils.isNotEmpty(entity.getDataStatusLabel())) {
                        entity.setDataStatus(DataStatusEnum.valueOf(entity.getDataStatusLabel()));
                    }
                    if (StringUtils.isNotEmpty(entity.getInformationLabel())) {
                        entity.setIsInformation(InformationEnum.valueOf(entity.getInformationLabel()));
                    }
                    if (StringUtils.isNotEmpty(entity.getInnerLabel())) {
                        entity.setIsInner(InnerEnum.valueOf(entity.getInnerLabel()));
                    }
                    if (StringUtils.isNotEmpty(entity.getPriorityLabel())) {
                        entity.setPriority(PriorityEnum.valueOf(entity.getPriorityLabel()));
                    }
                    if (StringUtils.isNotEmpty(entity.getStructuredLabel())) {
                        entity.setIsStructured(StructuredEnum.valueOf(entity.getStructuredLabel()));
                    }
                    if (StringUtils.isNotEmpty(entity.getDataTypeLabel())) {
                        entity.setDataType(DataTypeEnum.valueOf(entity.getDataTypeLabel()));
                    }
                    boolean checked = checkAllFieldsNotNull(entity);
                    if (checked) {
                        entity.setDataStatus(DataStatusEnum.READ);
                        entity.setDataStatusLabel(DataStatusEnum.READ.getDesc());
                    } else {
                        entity.setDataStatus(DataStatusEnum.NEW);
                        entity.setDataStatusLabel(DataStatusEnum.NEW.getDesc());
                    }
                    int insert = dataResourcesMapper.insert(entity);
                    if (insert > 0) {
                        // 备份版本
                        entity.setId(IdWorker.getId());
                        dataResourcesMapper.insertVersion(entity);
                        successNum++;
                        successMsg.append("\n" + successNum + "、数据资源 " + entity.getCode() + " 导入成功");
                    }
                } catch (Exception e) {
                    failureNum++;
                    String msg = "\n" + failureNum + "、数据资源 " + entity.getCode() + " 导入失败：";
                    failureMsg.append(msg + e.getMessage());
                }
            }
            if (failureNum > 0) {
                failureMsg.insert(0, "很抱歉，导入失败！成功导入 " + successNum + " 条数据， 数据如下：" + successMsg + "， \n共 " + failureNum + " 条数据格式不正确，错误如下：");
                throw new ServiceException(failureMsg.toString());
            } else {
                successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
                importDataLog.setLog(log + "\n" + successMsg);
                importDataLog.setEndTime(new Date());
                importDataLog.setStatus("成功");
                importDataLogService.update(importDataLog);
            }
        } catch (Exception e) {
            String message = e.getMessage();
            importDataLog.setLog(log + "\n" + message);
            importDataLog.setEndTime(new Date());
            importDataLog.setStatus("失败");
            importDataLogService.update(importDataLog);
        }
    }

    @Override
    public DataResources selectByCode(String code) {
        VlabelItem<DataResources> vlabelItem = dataResourcesMapper.selectByCode(code);
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATA_RESOURCES_NOT_EXIST);
        }
        return vlabelItem.getProperties();
    }

    @Override
    public PageDataInfo<DataResources> selectAll(DataResources entity) {
        return null;
    }

    @Override
    public DataResources selectById(Long id) {
        VlabelItem<DataResources> vlabelItem = dataResourcesMapper.selectById(id);
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATA_RESOURCES_NOT_EXIST);
        }
        return vlabelItem.getProperties();
    }

    @Override
    public PageDataInfo<DataResources> overview(String sort, SearchVo searchVo) {
        Page<DataResources> page = PageUtils.getPage(DataResources.class);
        // 获取分页参数
        IPage<DataResources> pageInfoList = dataResourcesMapper.overview(page, sort, searchVo);
        List<DataResources> records = pageInfoList.getRecords();
        return PageUtils.getPageInfo(records, (int) pageInfoList.getTotal());
    }

    @Override
    public PageDataInfo<ImportDataLog> importLog(ImportDataLog importDataLog, String sort) {
        if (importDataLog == null) {
            importDataLog = new ImportDataLog();
        }
        importDataLog.setTableName(LOG_TABLE_NAME);
        return importDataLogService.list(importDataLog, sort);
    }

    @Override
    public ImportDataLog selectByIdLog(Long id) {
        return importDataLogService.selectById(LOG_TABLE_NAME,id);
    }

    /**
     * 检查实体类中的所有属性是否都不为空
     *
     * @param entity 当前实体类的对象
     * @return 如果所有属性都不为空则返回true，否则返回false
     */
    private boolean checkAllFieldsNotNull(DataResources entity) {
        for (Field field : entity.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            try {
                if (!"sourceSystemId".equals(field.getName()) && !"sourceSystemCode".equals(field.getName())
                        && !"sourceSystemName".equals(field.getName()) && !"disuseStatus".equals(field.getName())) {
                    if (field.get(entity) == null) {
                        return false;
                    }
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
                return false;
            }
        }
        return true;
    }

}
