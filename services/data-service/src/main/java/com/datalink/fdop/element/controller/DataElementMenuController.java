package com.datalink.fdop.element.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.idempotent.annotation.RepeatSubmit;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.element.api.domain.DataElement;
import com.datalink.fdop.element.api.domain.DataElementMenu;
import com.datalink.fdop.element.api.model.DataElementTree;
import com.datalink.fdop.element.service.IDataElementMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
@RequestMapping(value = "/element/menu")
@RestController
@Api(tags = "数据元素菜单api")
public class DataElementMenuController extends BaseController {

    @Autowired
    private IDataElementMenuService dataElementMenuService;

    @ApiOperation("创建数据元素菜单")
    @Log(title = "数据元素", businessType = BusinessType.INSERT)
    @RepeatSubmit(interval = 1000)
    @PostMapping(value = "/create")
    public R create(@Validated @RequestBody DataElementMenu dataElementMenu) {
        return R.toResult(dataElementMenuService.create(dataElementMenu));
    }

    @ApiOperation("修改数据元素菜单")
    @Log(title = "数据元素", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public R update(@Validated @RequestBody DataElementMenu dataElementMenu) {
        return R.toResult(dataElementMenuService.update(dataElementMenu));
    }

    @ApiOperation("删除数据元素菜单")
    @Log(title = "数据元素", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.PLEASE_SPECIFY_THE_ELEMENT_MENU_THAT_NEEDS_TO_BE_REMOVED);
        }
        return R.toResult(dataElementMenuService.delete(ids));
    }

    @ApiOperation("数据元素树结构")
    @Log(title = "数据元素")
    @GetMapping(value = "/tree")
    public R<List<DataElementTree>> tree(
            @RequestParam(value = "sort", required = false, defaultValue = "ASC") String sort,
            @RequestParam(value = "code", required = false) String code,
            @RequestParam(value = "isQueryNode", required = false, defaultValue = "true") Boolean isQueryNode) {
        return R.ok(dataElementMenuService.tree(sort, code, isQueryNode));
    }

    @ApiOperation("数据元素总览")
    @Log(title = "数据元素")
    @PostMapping(value = "/overview")
    public R<PageDataInfo<DataElement>> overview(@RequestParam(value = "pid", defaultValue = "-1", required = false) Long pid,
                                                 @RequestParam(value = "sort", defaultValue = "ASC", required = false) String sort,
                                                 @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(dataElementMenuService.overview(pid, sort, searchVo));
    }

}
