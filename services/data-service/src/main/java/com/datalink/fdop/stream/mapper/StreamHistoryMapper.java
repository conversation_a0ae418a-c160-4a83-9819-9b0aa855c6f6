package com.datalink.fdop.stream.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.stream.api.domain.StreamHistory;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface StreamHistoryMapper extends BaseMapper<StreamHistory> {

    int createStreamAndStreamHistoryEdge(@Param("streamId") Long streamId, @Param("ids") List<Long> ids);

    int insertStreamHistory(@Param("streamHistory") StreamHistory stream);

    int updateById(StreamHistory stream);

    int batchUpdatePidById(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteStreamAndStreamHistoryEdge(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteBatchIds(@Param("ids") List<Long> ids);

    VlabelItem<StreamHistory> selectById(Long id);

    IPage<StreamHistory> selectHistoryListByStreamId(@Param("page") Page<StreamHistory> page, @Param("streamId") Long streamId, @Param("sort") String sort, @Param("searchVo") SearchVo searchVo);

    Integer querySerialNumber();

}
