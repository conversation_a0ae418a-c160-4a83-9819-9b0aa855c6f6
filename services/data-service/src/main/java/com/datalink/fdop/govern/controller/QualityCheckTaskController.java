package com.datalink.fdop.govern.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.element.api.RemoteEntityStrureService;
import com.datalink.fdop.element.api.model.vo.DataEntityStructureVo;
import com.datalink.fdop.govern.api.domain.QualityCheck;
import com.datalink.fdop.govern.mapper.QualityCheckMapper;
import com.datalink.fdop.govern.service.QualityCheckService;
import io.swagger.annotations.ApiOperation;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = "/govern/qualityCheckTask")
public class QualityCheckTaskController {


    @Autowired
    private QualityCheckMapper qualityCheckMapper;

    @Autowired
    private RemoteEntityStrureService remoteEntityStrureService;

    @Autowired
    private QualityCheckService qualityCheckService;


    @ApiOperation(value = "生成检查数据总数的SQL")
    @PostMapping("/generateCheckDataTotalSql")
    public R<String> generateCheckDataTotalSql(@RequestBody QualityCheck qualityCheck) {
        return R.ok(qualityCheckService.generateCheckDataTotalSql(qualityCheck));
    }

    @ApiOperation(value = "生成检查数据总数的SQL")
    @PostMapping("/generateCheckErrorDataSql")
    public R<String> generateCheckErrorDataSql(@RequestBody QualityCheck qualityCheck) {
        return R.ok(qualityCheckService.generateCheckErrorDataSql(qualityCheck));
    }


    @PostMapping("/updateTask")
    public R<String> updateTask(@RequestBody QualityCheck qualityCheck) {
        if (qualityCheckService.updateTask(qualityCheck) > 0) {
            return R.ok("更新成功");
        }
        return R.fail();
    }

    @PostMapping("/getTaskById")
    public R<QualityCheck> getTaskById(Long id) {
        VlabelItem<QualityCheck> createQualityCheckVlabelItem = qualityCheckMapper.selectById(id);
        return R.ok(createQualityCheckVlabelItem.getProperties());
    }


    @GetMapping("/parseSql")
    public R<List<DataEntityStructureVo>> parseSql(@RequestParam String sql, @RequestParam Long id) {
        try {
            Statement statement = CCJSqlParserUtil.parse(sql);
            if (statement instanceof Select) {
                PlainSelect selectBody = (PlainSelect) ((Select) statement).getSelectBody();
                // 获取查询的列
                List<String> collect = selectBody.getSelectItems().stream().map(Object::toString).collect(Collectors.toList());
                R<List<DataEntityStructureVo>> entityStructureById = remoteEntityStrureService.selectStructureById(id);
                List<DataEntityStructureVo> result = entityStructureById.getData().stream().filter(dataEntityStructureVo -> collect.contains(dataEntityStructureVo.getCode())).collect(Collectors.toList());
                return R.ok(result);
            }
        } catch (Exception ignored) {
            return R.fail();
        }
        return R.fail();
    }
}
