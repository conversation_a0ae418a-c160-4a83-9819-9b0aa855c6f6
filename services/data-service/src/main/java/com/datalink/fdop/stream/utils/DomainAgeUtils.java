package com.datalink.fdop.stream.utils;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.datalink.fdop.common.core.utils.DateUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.stream.api.domain.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/8/18 9:31
 */
public class DomainAgeUtils {

    public static String getStreamMenuAgeStr(StreamMenu streamMenu) {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{")
                .append(" id: " + (streamMenu.getId() != null ? streamMenu.getId() : IdWorker.getId()))
                .append(", pid: " + (streamMenu.getPid() != null ? streamMenu.getPid() : -1))
                .append((StringUtils.isNotEmpty(streamMenu.getCode()) ? ", code: '" + streamMenu.getCode() + "'" : ""))
                .append((StringUtils.isNotEmpty(streamMenu.getName()) ? ", name: '" + streamMenu.getName() + "'" : ""))
                .append((StringUtils.isNotEmpty(streamMenu.getDescription()) ? ", description: '" + streamMenu.getDescription() + "'" : ""))
                .append(", createBy: '" + (StringUtils.isNotEmpty(streamMenu.getCreateBy()) ? streamMenu.getCreateBy() : SecurityUtils.getUsername()) + "'")
                .append(", createTime: '" + (streamMenu.getCreateTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(streamMenu.getCreateTime())) + "'")
                .append(" }");
        return ageString.toString();
    }

    public static String getStreamCdcMenuAgeStr(StreamCdcMenu streamCdcMenu) {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{")
                .append(" id: " + (streamCdcMenu.getId() != null ? streamCdcMenu.getId() : IdWorker.getId()))
                .append(", pid: " + (streamCdcMenu.getPid() != null ? streamCdcMenu.getPid() : -1))
                .append((StringUtils.isNotEmpty(streamCdcMenu.getCode()) ? ", code: '" + streamCdcMenu.getCode() + "'" : ""))
                .append((StringUtils.isNotEmpty(streamCdcMenu.getName()) ? ", name: '" + streamCdcMenu.getName() + "'" : ""))
                .append((StringUtils.isNotEmpty(streamCdcMenu.getDescription()) ? ", description: '" + streamCdcMenu.getDescription() + "'" : ""))
                .append(", serialNumber: " + (streamCdcMenu.getSerialNumber() != null ? streamCdcMenu.getSerialNumber() : 0))
                .append(", createBy: '" + (StringUtils.isNotEmpty(streamCdcMenu.getCreateBy()) ? streamCdcMenu.getCreateBy() : SecurityUtils.getUsername()) + "'")
                .append(", createTime: '" + (streamCdcMenu.getCreateTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(streamCdcMenu.getCreateTime())) + "'")
                .append(" }");
        return ageString.toString();
    }

    public static String getStreamAgeStr(Stream stream) {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{")
                .append(" id: " + (stream.getId() != null ? stream.getId() : IdWorker.getId()))
                .append(", pid: " + (stream.getPid() != null ? stream.getPid() : -1))
                .append((StringUtils.isNotEmpty(stream.getCode()) ? ", code: '" + stream.getCode() + "'" : ""))
                .append((StringUtils.isNotEmpty(stream.getName()) ? ", name: '" + stream.getName() + "'" : ""))
                .append((StringUtils.isNotEmpty(stream.getDescription()) ? ", description: '" + stream.getDescription() + "'" : ""))
                .append((StringUtils.isNotEmpty(stream.getTaskInfo()) ? ", taskInfo: '" + stream.getTaskInfo() + "'" : ""))
                .append(", createBy: '" + (StringUtils.isNotEmpty(stream.getCreateBy()) ? stream.getCreateBy() : SecurityUtils.getUsername()) + "'")
                .append(", createTime: '" + (stream.getCreateTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(stream.getCreateTime())) + "'")
                .append(" }");
        return ageString.toString();
    }

    public static String getStreamSavePointAgeStr(StreamSavePoint savePoint) {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{")
                .append(" id: " + (savePoint.getId() != null ? savePoint.getId() : IdWorker.getId()))
                .append(", streamId: " + (savePoint.getStreamId() != null ? savePoint.getStreamId() : -1))
                .append((StringUtils.isNotEmpty(savePoint.getCode()) ? ", code: '" + savePoint.getCode() + "'" : ""))
                .append((StringUtils.isNotEmpty(savePoint.getName()) ? ", name: '" + savePoint.getName() + "'" : ""))
                .append((StringUtils.isNotEmpty(savePoint.getDescription()) ? ", description: '" + savePoint.getDescription() + "'" : ""))
                .append((StringUtils.isNotEmpty(savePoint.getSavePointPath()) ? ", savePointPath: '" + savePoint.getSavePointPath() + "'" : ""))
                .append(", createBy: '" + (StringUtils.isNotEmpty(savePoint.getCreateBy()) ? savePoint.getCreateBy() : SecurityUtils.getUsername()) + "'")
                .append(", createTime: '" + (savePoint.getCreateTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(savePoint.getCreateTime())) + "'")
                .append(" }");
        return ageString.toString();
    }

    public static String getStreamHistoryAgeStr(StreamHistory streamHistory) {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{")
                .append(" id: " + (streamHistory.getId() != null ? streamHistory.getId() : IdWorker.getId()))
                .append(", streamId: " + (streamHistory.getStreamId() != null ? streamHistory.getStreamId() : -1L))
                .append(", sort: " + (streamHistory.getSort() != null ? streamHistory.getSort() : 1))
                .append((StringUtils.isNotEmpty(streamHistory.getJobId()) ? ", jobId: '" + streamHistory.getJobId() + "'" : ""))
                .append((StringUtils.isNotEmpty(streamHistory.getStatus()) ? ", status: '" + streamHistory.getStatus() + "'" : ""))
                .append((StringUtils.isNotEmpty(streamHistory.getErrorInfo()) ? ", errorInfo: '" + streamHistory.getErrorInfo() + "'" : ""))
                .append((StringUtils.isNotEmpty(streamHistory.getStream()) ? ", stream: '" + streamHistory.getStream() + "'" : ""))
                .append((StringUtils.isNotEmpty(streamHistory.getTaskInfo()) ? ", taskInfo: '" + streamHistory.getTaskInfo() + "'" : ""))
                .append(", createBy: '" + (StringUtils.isNotEmpty(streamHistory.getCreateBy()) ? streamHistory.getCreateBy() : SecurityUtils.getUsername()) + "'")
                .append(", createTime: '" + (streamHistory.getCreateTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(streamHistory.getCreateTime())) + "'")
                .append(", endTime: '" + (streamHistory.getEndTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(streamHistory.getEndTime() )) + "'")
                .append(" }");
        return ageString.toString();
    }


    public static String getStreamCdcAgeStr(StreamCdc streamCdc) {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{")
                .append(" id: " + (streamCdc.getId() != null ? streamCdc.getId() : IdWorker.getId()))
                .append(", pid: " + (streamCdc.getPid() != null ? streamCdc.getPid() : -1))
                .append((StringUtils.isNotEmpty(streamCdc.getCode()) ? ", code: '" + streamCdc.getCode() + "'" : ""))
                .append((StringUtils.isNotEmpty(streamCdc.getName()) ? ", name: '" + streamCdc.getName() + "'" : ""))
                .append((StringUtils.isNotEmpty(streamCdc.getDescription()) ? ", description: '" + streamCdc.getDescription() + "'" : ""))
                .append((StringUtils.isNotEmpty(streamCdc.getType()) ? ", type: '" + streamCdc.getType() + "'" : ""))
                .append((StringUtils.isNotEmpty(streamCdc.getBaseTaskInfo()) ? ", baseTaskInfo: '" + streamCdc.getBaseTaskInfo() + "'" : ""))
                .append((StringUtils.isNotEmpty(streamCdc.getTaskInfo()) ? ", taskInfo: '" + streamCdc.getTaskInfo() + "'" : ""))
                .append((StringUtils.isNotEmpty(streamCdc.getStatus()) ? ", status: '" + streamCdc.getStatus() + "'" : ""))
                .append(", serialNumber: " + (streamCdc.getSerialNumber() != null ? streamCdc.getSerialNumber() : 0))
                .append(", createBy: '" + (StringUtils.isNotEmpty(streamCdc.getCreateBy()) ? streamCdc.getCreateBy() : SecurityUtils.getUsername()) + "'")
                .append(", createTime: '" + (streamCdc.getCreateTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(streamCdc.getCreateTime())) + "'")
                .append(" }");
        return ageString.toString();
    }

}
