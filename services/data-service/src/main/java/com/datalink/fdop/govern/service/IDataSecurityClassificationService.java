package com.datalink.fdop.govern.service;

import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.govern.api.domain.DataSecurityClassification;
import com.datalink.fdop.govern.api.domain.DataSecurityClassificationTree;

import java.util.List;

public interface IDataSecurityClassificationService {

    int create(DataSecurityClassification dataSecurityClassification);

    int update(DataSecurityClassification dataSecurityClassification);

    int delete(List<Long> ids);

    DataSecurityClassification selectById(Long id);

    DataSecurityClassification selectByCode(String code);

    PageDataInfo<DataSecurityClassification> list(DataSecurityClassification dataSecurityClassification);

    List<SelectVo> selectVoList(DataSecurityClassification dataSecurityClassification);

    int copy(Long pid, List<DataSecurityClassification> DataSecurityClassificationList);

    List<DataSecurityClassificationTree> treeList(String sort, String code, String description);

}
