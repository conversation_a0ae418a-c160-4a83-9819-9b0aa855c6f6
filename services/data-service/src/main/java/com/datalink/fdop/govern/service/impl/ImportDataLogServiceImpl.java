package com.datalink.fdop.govern.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.govern.api.domain.ImportDataLog;
import com.datalink.fdop.govern.mapper.ImportDataLogMapper;
import com.datalink.fdop.govern.service.ImportDataLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.List;

@Service
public class ImportDataLogServiceImpl implements ImportDataLogService {

    @Autowired
    private ImportDataLogMapper importDataLogMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insert(ImportDataLog entity) {
        if (StringUtils.isNotEmpty(entity.getLog())) {
            entity.setLog(Base64.getEncoder().encodeToString(entity.getLog().getBytes()));
        }
        return importDataLogMapper.insert(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(ImportDataLog entity) {
        if (StringUtils.isNotEmpty(entity.getLog())) {
            entity.setLog(Base64.getEncoder().encodeToString(entity.getLog().getBytes()));
        }
        return importDataLogMapper.updateById(entity);
    }

    @Override
    public ImportDataLog selectById(String tableName, Long id) {
        ImportDataLog entity = importDataLogMapper.selectById(tableName, id);
        if (StringUtils.isNotEmpty(entity.getLog())) {
            entity.setLog(new String(Base64.getDecoder().decode(entity.getLog())));
        }
        return entity;
    }

    @Override
    public PageDataInfo<ImportDataLog> list(ImportDataLog entity, String sort) {
        Page<ImportDataLog> page = PageUtils.getPage(ImportDataLog.class);
        IPage<ImportDataLog> iPage = importDataLogMapper.selectList(page, entity, sort);
        List<ImportDataLog> records = iPage.getRecords();
        records.forEach(record -> {
            if (StringUtils.isNotEmpty(record.getLog())) {
                record.setLog(new String(Base64.getDecoder().decode(record.getLog())));
            }
        });
        return PageUtils.getPageInfo(records, (int) iPage.getTotal());
    }

}
