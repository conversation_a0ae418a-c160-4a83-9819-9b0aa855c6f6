package com.datalink.fdop.element.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datalink.fdop.element.api.domain.DataElementMenu;
import com.datalink.fdop.element.api.model.DataElementTree;
import com.datalink.fdop.element.api.model.vo.DataElementStructureVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface DataElementMenuMapper extends BaseMapper<DataElementMenu> {

    int createElementMenuEdge(@Param("pid") Long pid, @Param("ids") List<Long> ids);

    int insertElementMenu(@Param("dataElementMenu") DataElementMenu dataElementMenu);

    int updateById(DataElementMenu dataElementMenu);

    int bacthUpdatePidById(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteElementMenuEdge(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteBatchIds(@Param("ids") List<Long> ids);

    DataElementMenu selectById(Long id);

    DataElementMenu selectByCode(String code);

    DataElementMenu selectByPid(Long pid);

    DataElementMenu checkCodeIsExists(@Param("id") Long id, @Param("code") String code);

    List<Long> selectIdsByPid(Long pid);

    List<DataElementTree> selectMenuTree(@Param("sort") String sort, @Param("code") String code);

    List<DataElementStructureVo> selectCiteMenuTree(@Param("code") String code, @Param("name") String name);
    List<String> getAllTaskName();
}
