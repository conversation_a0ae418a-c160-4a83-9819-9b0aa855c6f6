package com.datalink.fdop.stream.controller;

import com.datalink.fdop.common.core.domain.BaseDataModel;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.graph.api.dto.TaskInfo;
import com.datalink.fdop.graph.api.flink.SelectResult;
import com.datalink.fdop.stream.api.RemoteStreamService;
import com.datalink.fdop.stream.api.domain.Stream;
import com.datalink.fdop.stream.api.dto.ReturnVo;
import com.datalink.fdop.stream.config.KubernetesProperties;
import com.datalink.fdop.stream.service.IStreamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
@RequestMapping("/stream")
@RestController
@Api(tags = "Flink流任务api")
public class StreamController extends BaseController {

    @Autowired
    private IStreamService streamService;

    @Autowired
    private RemoteStreamService remoteStreamService;

    @ApiOperation("创建Flink流任务")
    @Log(title = "Flink流任务", businessType = BusinessType.INSERT)
    @PostMapping(value = "/create")
    public R create(@Validated @RequestBody Stream stream) {
        return R.toResult(streamService.create(stream));
    }

    @ApiOperation("修改Flink流任务")
    @Log(title = "Flink流任务", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public R update(@RequestBody Stream stream) {
        return R.toResult(streamService.update(stream));
    }

    @ApiOperation("删除Flink流任务")
    @Log(title = "Flink流任务", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.LEASE_SPECIFY_THE_FLOW_TO_DELETE);
        }
        return R.toResult(streamService.delete(ids));
    }

    @ApiOperation("根据id查询Flink流任务信息")
    @Log(title = "Flink流任务")
    @GetMapping(value = "/selectById/{id}")
    public R<Stream> selectById(@PathVariable("id") Long id) {
        return R.ok(streamService.selectById(id));
    }

    @ApiOperation("检查任务状态")
    @Log(title = "Flink流任务")
    @GetMapping(value = "/checkStatus/{id}")
    public R checkStatus(@PathVariable("id") Long id) {
        return R.ok(streamService.checkStatus(id));
    }

    @ApiOperation("判断任务是否存在(只针对批任务)")
    @Log(title = "Flink流任务")
    @GetMapping(value = "/checkTaskIsExists")
    public R<Boolean> checkTaskIsExists(@RequestParam(value = "taskName") String taskName) {
        return R.ok(streamService.checkTaskIsExists(taskName));
    }

    @ApiOperation("根据任务名称获取任务日志")
    @Log(title = "Flink流任务")
    @GetMapping(value = "/getLog")
    public R<String> getLog(@RequestParam(value = "taskName") String taskName) {
        return R.ok(streamService.getLog(taskName), Status.SUCCESS.getMsg());
    }

    @ApiOperation("测试任务日志")
    @Log(title = "Flink流任务")
    @GetMapping(value = "/testGetLog")
    public R testGetLog(@RequestParam(value = "taskName") String taskName) {
        int index = 0;
        // 校验任务执行状态
        Boolean flag = true;
        while (flag) {
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
            }

            // 获取日志，并切分
            R<String> logR = remoteStreamService.getLog(taskName);
            if (logR.getCode() != 200) {
                throw new RuntimeException(logR.getMsg());
            }
            // 获取日志，并切分
            String logJson = logR.getData();
            if (StringUtils.isEmpty(logJson)) {
                break;
            }
            List<String> logList = Arrays.asList(logJson.split("\\n"));
            // 日志行数
            int logLine = logList.size();
            if (logLine == index) {
                continue;
            }
            logList = logList.subList(index, logLine);
            for (String log : logList) {
                System.out.println(log.replace("\\r", "").replace("\\n", ""));
            }
            // 重新赋值
            index = logLine;
        }
        return R.ok();
    }

    @ApiOperation("Flink流任务上线")
    @Log(title = "Flink流任务")
    @GetMapping(value = "/online/{id}")
    public R online(@PathVariable("id") Long id){
        return R.ok(streamService.online(id));
    }

    @ApiOperation("Flink流任务下线")
    @Log(title = "Flink流任务")
    @GetMapping(value = "/offline/{id}")
    public R offline(@PathVariable("id") Long id){
        return R.ok(streamService.offline(id));
    }

    @ApiOperation("启动任务")
    @Log(title = "Flink流任务")
    @GetMapping(value = "/start/{id}")
    public R start(@PathVariable("id") Long id) {
        streamService.start(id);
        return R.ok();
    }

    @ApiOperation("停止任务")
    @Log(title = "Flink流任务")
    @GetMapping(value = "/stop/{id}")
    public R stop(@PathVariable("id") Long id) {
        streamService.stop(id);
        return R.ok();
    }

    @ApiOperation("根据任务名称停止任务")
    @Log(title = "Flink流任务")
    @GetMapping(value = "/stop")
    public R stop(@RequestParam(value = "taskName") String taskName) {
        streamService.stop(taskName);
        return R.ok();
    }

    @ApiOperation("获取k8s的url和token")
    @Log(title = "Flink流任务")
    @GetMapping(value = "/getK8sUrlAndToken")
    public R<KubernetesProperties> getK8sUrlAndToken() {
        return R.ok(streamService.getK8sUrlAndToken());
    }

    @ApiOperation("提交flink任务到k8s上")
    @Log(title = "Flink流任务")
    @PostMapping(value = "/submitFlink2K8s")
    public R<ReturnVo> submitFlink2K8s(@RequestBody TaskInfo taskInfo) {
        return R.ok(streamService.submitFlink2K8s(taskInfo));
    }

    @ApiOperation("根据实体/数据表生成FlinkCreateSql")
    @Log(title = "Flink流任务")
    @PostMapping(value = "/generateFlinkCreateTableStructure")
    public R<String> generateFlinkCreateTableStructure(@RequestBody BaseDataModel dataModel){
        return R.ok(streamService.generateFlinkCreateTableStructure(dataModel));
    }

    @ApiOperation("根据JobId获取异常日志信息")
    @Log(title = "Flink流任务")
    @GetMapping(value = "/getExceptionByJobId/{jobId}")
    public R<String> getExceptionByJobId(@PathVariable(value = "jobId") String jobId){
        return R.ok(streamService.getExceptionByJobId(jobId), Status.SUCCESS.getMsg());
    }

    @ApiOperation("获取缓存数据")
    @GetMapping("/getJobData/{jobId}")
    @ApiImplicitParam(name = "jobId", required = true, dataType = "String", paramType = "query")
    public R<SelectResult> getJobData(@PathVariable(value = "jobId") String jobId) {
        return R.ok(streamService.getJobData(jobId));
    }

    @ApiOperation("解析校验SQL")
    @Log(title = "Flink流任务")
    @PostMapping(value = "/explainSql")
    public R<String> explainSql(@RequestBody Stream stream){
        return R.ok("FlinkSQL无异常", Status.SUCCESS.getMsg());
    }


}
