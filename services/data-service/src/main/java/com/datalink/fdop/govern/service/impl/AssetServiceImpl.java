package com.datalink.fdop.govern.service.impl;



import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.govern.api.enums.AssetType;
import com.datalink.fdop.govern.api.model.vo.AssetVo;
import com.datalink.fdop.govern.mapper.AssetMapper;
import com.datalink.fdop.govern.service.AssetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class AssetServiceImpl implements AssetService {

    @Autowired
    private AssetMapper assetMapper;


    @Override
    public PageDataInfo search(List<AssetType> assetTypes, String searchValue) {
        Page<AssetVo> page = PageUtils.getPage(AssetVo.class);
        Page <AssetVo>  typePage= assetMapper.search(page, assetTypes,searchValue);
        return PageUtils.getPageInfo(typePage.getRecords(),(int) typePage.getTotal());
    }
}
