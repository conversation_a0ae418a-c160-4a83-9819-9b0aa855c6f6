package com.datalink.fdop.stream.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.tree.TreeUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.graph.api.flink.FlinkAPI;
import com.datalink.fdop.stream.api.domain.Stream;
import com.datalink.fdop.stream.api.domain.StreamMenu;
import com.datalink.fdop.stream.api.domain.StreamTree;
import com.datalink.fdop.stream.mapper.StreamMapper;
import com.datalink.fdop.stream.mapper.StreamMenuMapper;
import com.datalink.fdop.stream.service.IStreamMenuService;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
@Service
@Slf4j
public class StreamMenuService implements IStreamMenuService {

    @Autowired
    private StreamMapper streamMapper;

    @Autowired
    private StreamMenuMapper streamMenuMapper;

    @Value("${flink.host}")
    private String flinkHost;

    @Value("${flink.port}")
    private String flinkPort;

    /**
     * 全局序号间隔参数 当前数领 * 序号间隔数量
     */
    @Value("${dwms.serial-interval}")
    private Integer serialIntervalNumber;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int create(StreamMenu streamMenu) {
        if (streamMenuMapper.selectByCode(streamMenu.getCode()) != null) {
            throw new ServiceException(Status.STREAM_MENU_ALREADY_EXISTS);
        }
        streamMenu.setId(IdWorker.getId());
        int insert = streamMenuMapper.insertStreamtMenu(streamMenu);
        // 创建菜单边关系
        if (insert > 0 && streamMenu.getPid() != -1L) {
            // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
            streamMenuMapper.createStreamMenuEdge(streamMenu.getPid(), Arrays.asList(streamMenu.getId()));
        }
        return insert;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(StreamMenu streamMenu) {
        VlabelItem<StreamMenu> vlabelItem = streamMenuMapper.selectById(streamMenu.getId());
        if (vlabelItem == null) {
            throw new ServiceException(Status.STREAM_MENU_DOES_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(streamMenu.getCode()) && streamMenuMapper.checkCodeIsExists(streamMenu.getId(), streamMenu.getCode()) != null) {
            throw new ServiceException(Status.STREAM_MENU_ALREADY_EXISTS);
        }
        int update = streamMenuMapper.updateById(streamMenu);
        if (update > 0) {
            // 获取修改前的菜单pid,并删除修改前的边关系
            streamMenuMapper.deleteStreamMenuEdge(Arrays.asList(streamMenu.getId()), vlabelItem.getProperties().getPid());
            // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
            if (streamMenu.getPid() != -1L) {
                streamMenuMapper.createStreamMenuEdge(streamMenu.getPid(), Arrays.asList(streamMenu.getId()));
            }
        }
        return update;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int delete(List<Long> ids) {
        for (Long id : ids) {
            VlabelItem<StreamMenu> vlabelItem = streamMenuMapper.selectById(id);
            if (vlabelItem == null) {
                continue;
            }
            StreamMenu streamMenu = vlabelItem.getProperties();
            // 获取当前被删除菜单的所有子级菜单
            List<Long> menuIdList = streamMenuMapper.selectIdsByPid(id);
            // 如果存在子级菜单，则将所有子级菜单的pid修改为被删除菜单的pid
            if (CollectionUtils.isNotEmpty(menuIdList)) {
                int update = streamMenuMapper.bacthUpdatePidById(menuIdList, streamMenu.getPid());
                if (update > 0 && streamMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    streamMenuMapper.createStreamMenuEdge(streamMenu.getPid(), menuIdList);
                }
            }

            // 获取当前被删除菜单的所有子级
            List<Long> elementIdList = streamMapper.selectIdsByPid(id);
            // 如果存在子级，则将所有子级的pid修改为被删除菜单的pid
            if (CollectionUtils.isNotEmpty(elementIdList)) {
                int update = streamMapper.bacthUpdatePidById(elementIdList, streamMenu.getPid());
                if (update > 0 && streamMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    streamMapper.createStreamAndMenuEdge(streamMenu.getPid(), elementIdList);
                }
            }
        }
        // 删除菜单并且删除菜单的边关系
        return streamMenuMapper.deleteBatchIds(ids);
    }

    @Override
    public List<StreamTree> tree(String sort, String code, Boolean isQueryNode) {
        List<StreamTree> trees = new ArrayList<>();
        // 添加Flink流任务树
        if (isQueryNode) {
            trees.addAll(streamMapper.selectTree(sort, code));
        }
        // 添加Flink流任务菜单树
        trees.addAll(streamMenuMapper.selectMenuTree(sort, null));
        // 递归成树结构
        List<StreamTree> treeList = (List<StreamTree>) TreeUtils.getTree(trees);
        // 条件查找删除空集合的菜单
        if (StringUtils.isNotEmpty(code) && isQueryNode) {
            TreeUtils.removeEmptyChilderAndMenu(treeList);
        }
        return treeList;
    }

    @Override
    public PageDataInfo<Stream> overview(Long pid, String sort, SearchVo searchVo) {
        // 获取分页参数
        Page<Stream> page = PageUtils.getPage(Stream.class);
        IPage<Stream> streamIpage = streamMenuMapper.overview(page, pid, sort, searchVo);
        List<Stream> records = streamIpage.getRecords();
        for (Stream stream : records){
            try {
                if (StringUtils.isNotEmpty(stream.getJobId())) {
                    FlinkAPI flinkAPI = FlinkAPI.build(flinkHost + ":" + flinkPort);
                    JsonNode jobInfo = flinkAPI.getJobInfo(stream.getJobId());
                    if (jobInfo != null) {
                        //"IITIALIZING"，"CREATED"，"RUMWING"，"FAILING"，"FAILED"，"CANCELLING"，"CANCELED"，"FINISHED"，"RESTARTING"，"SUSPENDED"，"RECONCILING'
                        //“初始化"、"已创建"、“正在运行"、"失败"、“未成功”、“取消”、“被取消"、"已完成"、"重新启动"、"已挂起"、"正在协调
                        stream.setStatus(jobInfo.get("state").asText());
                    }
                }
            } catch (Exception e){
                log.error(e.getLocalizedMessage());
            }
        }
        return PageUtils.getPageInfo(records, (int) streamIpage.getTotal());
    }

    @Override
    public Integer querySerialNumber(Boolean menuFlag) {
        if (menuFlag) {
            return streamMenuMapper.querySerialNumber() * serialIntervalNumber;
        }else {
            return streamMapper.querySerialNumber() * serialIntervalNumber;
        }

    }

}
