package com.datalink.fdop.element.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.element.api.domain.DataElement;
import com.datalink.fdop.element.api.domain.DataEntityRelation;
import com.datalink.fdop.element.api.domain.DataEntityStructure;
import com.datalink.fdop.element.api.model.dto.DataEntityStructureDto;
import com.datalink.fdop.element.api.model.dto.TaskEtlDto;
import com.datalink.fdop.element.api.model.vo.DataElementStructureVo;
import com.datalink.fdop.element.api.model.vo.DataEntityStructureVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface DataEntityStructureMapper extends BaseMapper<DataEntityStructure> {

    DataEntityStructure selectById(Long id);

    DataEntityStructure selectByCode(@Param("dataEntityId") Long dataEntityId, @Param("code") String code);

    List<DataEntityStructure> selectByIdAndCodes(@Param("dataEntityId") Long dataEntityId, @Param("codes") List<String> codes);

    DataEntityStructure checkCodeIsExists(@Param("id") Long id, @Param("code") String code, @Param("dataEntityId") Long dataEntityId);

    int insertStructure(@Param("dataEntityStructure") DataEntityStructure dataEntityStructure);

    int batchInsertStructure(@Param("dataEntityStructureList") List<DataEntityStructure> dataEntityStructureList);

    int batchCreateStructureEdge(@Param("dataEntityId") Long dataEntityId, @Param("dataEntityStructureList") List<DataEntityStructure> dataEntityStructureList);

    int updateStructure(DataEntityStructure dataEntityStructure);

    int deleteCustomize(@Param("dataEntityId") Long dataEntityId, @Param("structureIds") List<Long> structureIds);

    int deleteCustomizeMapping(@Param("dataEntityId") Long dataEntityId, @Param("structureIds") List<Long> structureIds);

    int deletePredefinedMainAndField(@Param("dataEntityId") Long dataEntityId, @Param("structureIds") List<Long> structureIds);

    int deletePredefinedMainAndFieldMapping(@Param("dataEntityId") Long dataEntityId, @Param("structureIds") List<Long> structureIds);

    int deletePredefinedInput(@Param("dataEntityId") Long dataEntityId, @Param("structureIds") List<Long> structureIds);

    int deletePredefinedInputMapping(@Param("dataEntityId") Long dataEntityId, @Param("structureIds") List<Long> structureIds);

    int deleteAll(@Param("dataEntityId") Long dataEntityId);

    int batchDeleteAll(@Param("dataEntityIdList") List<Long> dataEntityIdList);

    int batchDeleteEntityStructureByEntityIds(@Param("dataEntityIds") List<Long> dataEntityIds);

    boolean checkEntityElementEdge(@Param("dataEntityId") Long dataEntityId, @Param("dataElementId") Long dataElementId);

    int insertEntityElementEdge(@Param("dataEntityId") Long dataEntityId, @Param("dataElementStructureVo") DataElementStructureVo dataElementStructureVo);

    boolean checkEntityInputEdge(@Param("dataEntityId") Long dataEntityId, @Param("dataInputId") Long dataInputId);

    int insertEntityInputEdge(@Param("dataEntityId") Long dataEntityId, @Param("dataElementStructureVo") DataElementStructureVo dataElementStructureVo);

    List<DataEntityStructureVo> selectEntityPkStructure(@Param("dataEntityId") Long dataEntityId);

    IPage<DataEntityStructureVo> selectStructureByIdPaging(IPage<DataEntityStructureVo> page,
                                                           @Param("dataEntityId") Long dataEntityId,
                                                           @Param("sort") String sort,
                                                           @Param("dataEntityStructureDto") DataEntityStructureDto dataEntityStructureDto);

    List<DataEntityStructureVo> selectEntityNoPkStructure(@Param("dataEntityId") Long dataEntityId);

    List<Integer> selectEntityAllMaxSeq(@Param("dataEntityId") Long dataEntityId, @Param("isPk") Boolean isPk);

    List<DataEntityStructureVo> selectEntityStructureByElementFieldId(@Param("dataElementStructureIds") List<Long> dataElementStructureIds);

    DataEntityStructureVo selectElementStructureByElementFieldId(@Param("dataElementStructureId") Long dataElementStructureId, @Param("dataEntityId") Long dataEntityId);

    int deletePredefinedEdge(@Param("dataEntityStructureVoList") List<DataEntityStructureVo> dataEntityStructureVoList);

    List<TaskEtlDto> selectTaskAndFieldEdge(@Param("dataEntityId") Long dataEntityId, @Param("dataEntityStructureId") Long dataEntityStructureId);

    List<TaskEtlDto> selectTaskAndFieldEdgeByDataEntityIds(@Param("dataEntityIds") List<Long> dataEntityIds);

    List<TaskEtlDto> selectTaskAndFieldEdgeList(@Param("dataEntityId") Long dataEntityId,
                                                @Param("dataEntityStructureIdss") List<List<Long>> dataEntityStructureIdss);

    int deleteL5Id(@Param("ids") List<Long> ids);

    int createEntityRelation(@Param("sourceDataEntityStructureId") Long sourceDataEntityStructureId,
                             @Param("targetDataEntityStructureId") Long targetDataEntityStructureId,
                             @Param("id") Long id);

    int deleteEntityRelation(@Param("ids") List<Long> ids);

    List<DataEntityRelation> queryEntityRelation(@Param("ids") List<Long> ids,@Param("queryType") String queryType);

}
