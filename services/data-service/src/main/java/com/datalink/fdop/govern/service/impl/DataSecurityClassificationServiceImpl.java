package com.datalink.fdop.govern.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.tree.TreeUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.govern.api.domain.DataSecurityClassification;
import com.datalink.fdop.govern.api.domain.DataSecurityClassificationTree;
import com.datalink.fdop.govern.mapper.DataSecurityClassificationMapper;
import com.datalink.fdop.govern.service.IDataSecurityClassificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DataSecurityClassificationServiceImpl implements IDataSecurityClassificationService {

    @Autowired
    private DataSecurityClassificationMapper dataSecurityClassificationMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int create(DataSecurityClassification dataSecurityClassification) {
        VlabelItem<DataSecurityClassification> vlabelItem = dataSecurityClassificationMapper.selectByCode(dataSecurityClassification.getCode());
        if (vlabelItem != null) {
            throw new ServiceException(Status.DATA_SECURITY_CLASS_EXIST);
        }
        dataSecurityClassification.setId(IdWorker.getId());
        if (dataSecurityClassification.getPid() == null){
            dataSecurityClassification.setPid(-1L);
        }
        return dataSecurityClassificationMapper.insert(dataSecurityClassification);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(DataSecurityClassification dataSecurityClassification) {
        VlabelItem<DataSecurityClassification> vlabelItem = dataSecurityClassificationMapper.selectById(dataSecurityClassification.getId());
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATA_SECURITY_CLASS_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(dataSecurityClassification.getCode()) && dataSecurityClassificationMapper.checkCodeIsExists(dataSecurityClassification.getId(), dataSecurityClassification.getCode()) != null) {
            throw new ServiceException(Status.DATA_SECURITY_CLASS_EXIST);
        }
        return dataSecurityClassificationMapper.updateById(dataSecurityClassification);
    }

    @Override
    public int delete(List<Long> ids) {
        return dataSecurityClassificationMapper.deleteBatchIds(ids);
    }

    @Override
    public DataSecurityClassification selectById(Long id) {
        VlabelItem<DataSecurityClassification> vlabelItem = dataSecurityClassificationMapper.selectById(id);
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATA_SECURITY_CLASS_NOT_EXIST);
        }
        return vlabelItem.getProperties();
    }

    @Override
    public DataSecurityClassification selectByCode(String code) {
        VlabelItem<DataSecurityClassification> vlabelItem = dataSecurityClassificationMapper.selectByCode(code);
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATA_SECURITY_CLASS_NOT_EXIST);
        }
        return vlabelItem.getProperties();
    }

    @Override
    public PageDataInfo<DataSecurityClassification> list(DataSecurityClassification dataSecurityClassification) {
        // 获取分页参数
        Page<VlabelItem> page = PageUtils.getPage(VlabelItem.class);
        IPage<VlabelItem<DataSecurityClassification>> vlabelItemIPage = dataSecurityClassificationMapper.selectList(page, dataSecurityClassification);
        return PageUtils.getPageInfo(vlabelItemIPage.getRecords().stream().map(VlabelItem::getProperties).collect(Collectors.toList()), (int) vlabelItemIPage.getTotal());
    }

    @Override
    public List<SelectVo> selectVoList(DataSecurityClassification dataSecurityClassification) {
        return dataSecurityClassificationMapper.selectListAll(dataSecurityClassification);
    }

    @Override
    public int copy(Long pid, List<DataSecurityClassification> dataSecurityClassificationList) {
        return 0;
    }

    @Override
    public List<DataSecurityClassificationTree> treeList(String sort, String code, String description) {
        // 所有的数据集合
        List<DataSecurityClassificationTree> trees = new ArrayList<>();
        List<DataSecurityClassificationTree> dataSecurityClassificationTrees = dataSecurityClassificationMapper.selectDataSecurityClassificationTree(sort, code, description);
        trees.addAll(dataSecurityClassificationTrees);
        // 递归获取子节点
        List<DataSecurityClassificationTree> treeList = (List<DataSecurityClassificationTree>) TreeUtils.getTree(trees);
        // 条件查找删除空集合的菜单
        //if (StringUtils.isNotEmpty(code)) {
        //   TreeUtils.removeEmptyChilderAndMenu(treeList);
        //}
        return treeList;
    }
}
