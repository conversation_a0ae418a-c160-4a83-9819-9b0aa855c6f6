package com.datalink.fdop.govern.controller;


import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.govern.api.domain.DataSecurityLevel;
import com.datalink.fdop.govern.service.IDataSecurityLevelService;
import io.swagger.annotations.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping(value = "/govern/security/level")
@RestController
@Api(tags = "安全分级api")
public class DataSecurityLevelController {

    @Autowired
    private IDataSecurityLevelService dataSecurityLevelService;

    @ApiOperation("创建安全分级")
    @Log(title = "数据治理")
    @PostMapping(value = "/create")
    public R create(@Validated @RequestBody DataSecurityLevel dataSecurityLevel) {
        return R.toResult(dataSecurityLevelService.create(dataSecurityLevel));
    }

    @ApiOperation("修改安全分级")
    @Log(title = "数据治理")
    @PostMapping(value = "/update")
    public R update(@RequestBody DataSecurityLevel dataSecurityLevel) {
        if (dataSecurityLevel.getId() == null) {
            throw new ServiceException(Status.PARAM_ERROR);
        }
        return R.toResult(dataSecurityLevelService.update(dataSecurityLevel));
    }

    @ApiOperation("删除安全分级")
    @Log(title = "数据治理")
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.DATA_SECURITY_LEVEL_TO_DELETE);
        }
        return R.toResult(dataSecurityLevelService.delete(ids));
    }

    @ApiOperation("根据id查询查询数据安全等级信息")
    @Log(title = "数据治理")
    @GetMapping(value = "/selectById")
    public R<DataSecurityLevel> selectById(@RequestParam Long id) {
        return R.ok(dataSecurityLevelService.selectById(id));
    }

    @ApiOperation(value = "根据code查询数据安全等级信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "安全等级编码", required = true, dataType = "String", paramType = "query"),
    })
    @GetMapping(value = "/selectByCode")
    @Log(title = "数据治理")
    public R<DataSecurityLevel> selectByCode(@RequestParam(value = "code") String code) {
        return R.ok(dataSecurityLevelService.selectByCode(code));
    }

    @ApiOperation(value = "查询数据安全等级信息列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功", response = PageDataInfo.class)
    })
    @PostMapping(value = "/list")
    @Log(title = "数据治理")
    public R<PageDataInfo<DataSecurityLevel>> list(@RequestBody(required = false) DataSecurityLevel dataSecurityLevel) {
        return R.ok(dataSecurityLevelService.list(dataSecurityLevel));
    }

    @ApiOperation("查询数据安全等级（下拉框）")
    @Log(title = "数据治理")
    @PostMapping(value = "/selectVoList")
    public R<List<SelectVo>> selectVoList(@RequestBody(required = false) DataSecurityLevel dataSecurityLevel) {
        return R.ok(dataSecurityLevelService.selectVoList(dataSecurityLevel));
    }

    @ApiOperation("复制")
    @Log(title = "数据治理")
    @PostMapping(value = "/copy")
    public R copy(
            @RequestParam(value = "pid", defaultValue = "-1") Long pid,
            @RequestBody List<DataSecurityLevel> dataSecurityLevelList) {
        return R.ok(dataSecurityLevelService.copy(pid, dataSecurityLevelList));
    }

}
