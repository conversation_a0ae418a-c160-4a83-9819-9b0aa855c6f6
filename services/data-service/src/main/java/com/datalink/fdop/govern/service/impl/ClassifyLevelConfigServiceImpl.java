package com.datalink.fdop.govern.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.govern.api.domain.*;
import com.datalink.fdop.govern.mapper.ClassifyLevelConfigMapper;
import com.datalink.fdop.govern.service.IClassifyLevelConfigService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class ClassifyLevelConfigServiceImpl implements IClassifyLevelConfigService {

    @Autowired
    private ClassifyLevelConfigMapper classifyLevelConfigMapper;

    @Override
    public int create(ClassifyLevelConfig classifyLevelConfig) {
        classifyLevelConfig.setId(IdWorker.getId());
        classifyLevelConfig.setCreateBy(SecurityUtils.getUsername());
        classifyLevelConfig.setCreateTime(new Date());
        return classifyLevelConfigMapper.insert(classifyLevelConfig);
    }

    @Override
    public int update(ClassifyLevelConfig classifyLevelConfig) {
        return classifyLevelConfigMapper.updateById(classifyLevelConfig);
    }

    @Override
    public int delete(List<Long> ids) {
        return classifyLevelConfigMapper.deleteBatchIds(ids);
    }

    @Override
    public PageDataInfo<ClassifyLevelConfig> overview(String sort, String fieldName, String category, String level) {
        Page<ClassifyLevelConfig> page = PageUtils.getPage(ClassifyLevelConfig.class);
        IPage<ClassifyLevelConfig> dataSourceIPage = classifyLevelConfigMapper.overview(page, sort,fieldName,category,level);

        return PageUtils.getPageInfo(dataSourceIPage.getRecords(), (int) dataSourceIPage.getTotal());
    }

    @Override
    public void importData(List<ClassifyLevelConfig> list) {
         if (CollectionUtils.isEmpty(list)) {
                throw new ServiceException("导入的数据不能为空");
            }
            for (ClassifyLevelConfig classifyLevelConfig : list) {
                classifyLevelConfig.setId(IdWorker.getId());
                classifyLevelConfig.setCreateBy(SecurityUtils.getUsername());
                classifyLevelConfig.setCreateTime(new Date());
            }
            classifyLevelConfigMapper.batchInsert(list);
        }

    @Override
    public List<ClassifyLevelConfig> list(String sort, String fieldName, String category, String level) {
        return classifyLevelConfigMapper.overview(sort,fieldName,category,level);
    }
}
