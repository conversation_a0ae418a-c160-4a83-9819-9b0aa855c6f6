package com.datalink.fdop.govern.service;

import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.govern.api.domain.DirectoryStructure;
import com.datalink.fdop.govern.api.domain.DirectoryStructureTree;

import java.util.List;

public interface IDirectoryStructureService {

    int create(DirectoryStructure entity);

    int update(DirectoryStructure entity);

    int delete(List<Long> ids);

    DirectoryStructure selectById(Long id);

    PageDataInfo<DirectoryStructure> list(DirectoryStructure entity, String sort);

    List<DirectoryStructureTree> tree(String sort, String code, String name, String description);

    List<DirectoryStructureTree> treeApplication(String sort, String code, String name, String description);

}
