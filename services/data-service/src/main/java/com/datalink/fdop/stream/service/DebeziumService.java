package com.datalink.fdop.stream.service;

import com.alibaba.fastjson.JSONObject;
import com.datalink.fdop.drive.api.factory.RemoteDriveFallbackFactory;
import com.datalink.fdop.stream.service.factory.DebeziumFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */

@FeignClient(fallbackFactory = DebeziumFallbackFactory.class, name = "connectors",url = "${k8s.url}")
@Service
public interface DebeziumService {

    @GetMapping("/connectors")
    List getConnectorList();

    @PostMapping("/connectors")
    JSONObject createConnector(@RequestBody JSONObject jsonObject);

    @PutMapping("/connectors/{name}/pause")
    void pauseConnector(@PathVariable(value = "name") String name);

    @PutMapping("/connectors/{name}/resume")
    void resumeConnector(@PathVariable(value = "name") String name);

    @PostMapping("/connectors/{name}/restart")
    void restartConnector(@PathVariable(value = "name") String name);

    @DeleteMapping("/connectors/{name}")
    void deleteConnector(@PathVariable(value = "name") String name);

    @GetMapping("/connectors/{name}")
    JSONObject getConnector(@PathVariable(value = "name") String name);

    @GetMapping("/connectors/{name}/config")
    JSONObject getConfig(@PathVariable(value = "name") String name);

    @PutMapping("/connectors/{name}/config")
    JSONObject updateConfig(@PathVariable(value = "name") String name);

    @GetMapping("/connectors/{name}/status")
    JSONObject getConnectorStatus(@PathVariable(value = "name") String name);

    @PutMapping("/connector-plugins/{type}/config/validate")
    JSONObject validateJson(@PathVariable(value = "type") String type, @RequestBody JSONObject jsonObject);

}


//public interface DebeziumService {
//
//    List getConnectorList(Integer port);
//
//    JSONObject createConnector(Integer port, JSONObject jsonObject);
//
//    void pauseConnector(Integer port, String name);
//
//    void resumeConnector(Integer port, String name);
//
//    void restartConnector(Integer port, String name);
//
//    void deleteConnector(Integer port, String name);
//
//    JSONObject getConnector(Integer port, String name);
//
//    JSONObject getConfig(Integer port, String name);
//
//    JSONObject updateConfig(Integer port, String name);
//
//    JSONObject getConnectorStatus(Integer port, String name);
//
//    JSONObject validateJson(Integer port, String type, JSONObject jsonObject);
//
//}
