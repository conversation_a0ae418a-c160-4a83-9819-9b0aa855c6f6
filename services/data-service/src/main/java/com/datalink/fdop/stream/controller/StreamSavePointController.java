package com.datalink.fdop.stream.controller;


import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.stream.api.domain.StreamSavePoint;
import com.datalink.fdop.stream.service.IStreamSavePointService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping(value = "/stream/savepoint")
@RestController
@Api(tags = "流任务保存点api")
public class StreamSavePointController {

    @Autowired
    private IStreamSavePointService streamSavePointService;

    @ApiOperation("查询流任务保存点")
    @Log(title = "流任务保存点")
    @PostMapping(value = "/getHistoryListByStreamId")
    public R<PageDataInfo<StreamSavePoint>> getHistoryListByStreamId(@RequestParam(value = "streamId") Long streamId,
                                                                     @RequestParam(value = "sort", defaultValue = "ASC", required = false) String sort,
                                                                     @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(streamSavePointService.selectSavePointByStreamId(streamId, sort, searchVo));
    }

    @ApiOperation("创建流任务保存点")
    @Log(title = "流任务保存点", businessType = BusinessType.INSERT)
    @PostMapping(value = "/create")
    public R create(@Validated @RequestBody StreamSavePoint savePoint) {
        return R.toResult(streamSavePointService.create(savePoint));
    }

    @ApiOperation("修改流任务保存点")
    @Log(title = "流任务保存点", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public R update(@RequestBody StreamSavePoint savePoint) {
        return R.toResult(streamSavePointService.update(savePoint));
    }


    @ApiOperation("删除流任务保存点")
    @Log(title = "流任务保存点", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/delete/{streamId}")
    public R delete(@PathVariable(value = "streamId") Long streamId, @RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.PLEASE_SPECIFY_THE_FLOW_MENU_TO_DELETE);
        }
        return R.toResult(streamSavePointService.delete(ids,streamId));
    }

}
