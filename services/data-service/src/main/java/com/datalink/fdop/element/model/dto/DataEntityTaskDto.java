package com.datalink.fdop.element.model.dto;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/9 17:09
 */
@Data
public class DataEntityTaskDto {

    private String pid;

    private long code;

    private int etlStatus;

    private String name;

    private String nameDesc;

    private String description;

    private String taskType;

    private String updateBeforeTaskParams;

    private Map<String, Object> taskParams;

    private String flag;

    private String taskPriority;

    private String workerGroup;

    private Integer environmentCode;

    private Integer failRetryTimes;

    private Integer failRetryInterval;

    private String timeoutFlag;

    private String timeoutNotifyStrategy;

    private Integer timeout;

    private Integer delayTime;

    private String resourceIds;

}
