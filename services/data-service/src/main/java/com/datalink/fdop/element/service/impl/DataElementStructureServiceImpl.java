package com.datalink.fdop.element.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.datalink.fdop.auth.api.RemoteQueryAuthService;
import com.datalink.fdop.common.core.enums.FieldType;
import com.datalink.fdop.common.core.enums.MenuType;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.excel.ExcelDto;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.tree.TreeUtils;
import com.datalink.fdop.element.api.domain.DataElement;
import com.datalink.fdop.element.api.domain.DataElementStructure;
import com.datalink.fdop.element.api.enums.DataElementType;
import com.datalink.fdop.element.api.model.vo.DataElementStructureVo;
import com.datalink.fdop.element.mapper.DataElementMapper;
import com.datalink.fdop.element.mapper.DataElementMenuMapper;
import com.datalink.fdop.element.mapper.DataElementStructureMapper;
import com.datalink.fdop.element.service.IDataElementStructureService;
import com.datalink.fdop.element.service.IDataEntityStructureService;
import com.datalink.fdop.element.utils.GraphTableUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @date 2022/4/12 13:58
 */
@Service
public class DataElementStructureServiceImpl implements IDataElementStructureService {

    @Autowired
    private DataElementMapper dataElementMapper;

    @Autowired
    private DataElementStructureMapper dataElementStructureMapper;

    @Autowired
    private DataElementMenuMapper dataElementMenuMapper;

    @Autowired
    private IDataEntityStructureService dataEntityStructureService;

    @Autowired
    private RemoteQueryAuthService remoteQueryAuthService;

    private DataElement checkDataElement(Long dataElementId) {
        DataElement dataElement = dataElementMapper.selectById(dataElementId);
        if (dataElement == null) {
            throw new ServiceException(Status.DATA_ELEMENT_NOT_EXIST);
        }
        if (dataElement.getDataElementType() != DataElementType.MAIN) {
            throw new ServiceException(Status.NOT_MAIN_OPERATION_NOT_ALLOWED);
        }
        return dataElement;
    }

    // 获取最大小seq
    private Integer getMinSeq(Long dataElementId, Boolean isPk) {
        List<Integer> seqs = dataElementStructureMapper.selectElementAllMinSeq(dataElementId, isPk);
        return seqs.stream().min(Integer::compare).get();
    }

    // 获取最大的seq
    private Integer getMaxSeq(Long dataElementId, Boolean isPk) {
        List<Integer> seqs = dataElementStructureMapper.selectElementAllMaxSeq(dataElementId, isPk);
        return seqs.stream().max(Integer::compare).get();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int create(Long dataElementId, DataElementStructure dataElementStructure) {
        // 验证数据元素
        checkDataElement(dataElementId);

        // 录入类型不能设置为主键
        if (dataElementStructure.getIsPk()) {
            throw new ServiceException(Status.THE_ENTRY_TYPE_FIELD_CANNOT_BE_SET_AS_THE_PRIMARY_KEY);
        }

        // 验证长度和类型
        if (dataElementStructure.getFieldType() != null) {
            GraphTableUtils.initLength(dataElementStructure);
        }

        // 校验字段名，不能重复
        List<DataElementStructure> dataElementStructureList = selectStructureByDataElementId(dataElementId);
        if (dataElementStructureList.stream().anyMatch(structure -> dataElementStructure.getCode().equalsIgnoreCase(structure.getCode()))) {
            throw new ServiceException(Status.A_FIELD_WITH_THE_SAME_NAME_CANNOT_EXIST);
        }

        // 设置seq
        dataElementStructure.setSeq(getMaxSeq(dataElementId, dataElementStructure.getIsPk()) + 1);

        // 添加的列只有可能是录入类型，并且只需要建立 d_e_data_element -[d_e_element_input_edge] -> d_e_data_element_input的单向关系
        return dataElementStructureMapper.insertStructure(dataElementId, Arrays.asList(dataElementStructure));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(Long dataElementId, DataElementStructure dataElementStructure) {
        // 验证数据元素
        checkDataElement(dataElementId);

        // 主数据类型和字段类型不能修改
        if (dataElementStructure.getDataElementType() == DataElementType.MAIN ||
                dataElementStructure.getDataElementType() == DataElementType.FIELD) {
            throw new ServiceException(Status.MASTER_DATA_AND_FIELD_TYPES_CANNOT_BE_MODIFIED);
        }

        // 录入类型不能设置为主键
        if (dataElementStructure.getDataElementType() == DataElementType.INPUT && dataElementStructure.getIsPk()) {
            throw new ServiceException(Status.THE_ENTRY_TYPE_FIELD_CANNOT_BE_SET_AS_THE_PRIMARY_KEY);
        }

        // 验证长度和类型
        if (dataElementStructure.getFieldType() != null) {
            GraphTableUtils.initLength(dataElementStructure);
        }

        // 校验字段名，不能重复
        List<DataElementStructure> dataElementStructureList = selectStructureByDataElementId(dataElementId);
        if (dataElementStructureList.stream().anyMatch(structure -> !dataElementStructure.getId().equals(structure.getId()) && dataElementStructure.getCode().equalsIgnoreCase(structure.getCode()))) {
            throw new ServiceException(Status.A_FIELD_WITH_THE_SAME_NAME_CANNOT_EXIST);
        }

        // 修改的列中三种类型，MAIN、FIELD、INPUT
        // MAIN、FIELD类型只能修改字段类型、长度、精度，并且只有MAIN类型可以修改主键，还要建立 d_e_data_element <- [d_e_element_element_edge] -> d_e_data_element的双向关系
        // INPUT需要修改所有字段（除了主键字段），还要建立 d_e_data_element -[d_e_element_input_edge] -> d_e_data_element_input的单向关系
        dataElementStructureMapper.updateStructure(dataElementId, Arrays.asList(dataElementStructure));
        return 1;
    }

    @Override
    public int updateSequence(Long dataElementId, List<DataElementStructure> dataElementStructureList) {
        // 验证数据元素
        checkDataElement(dataElementId);

        // 主键列必须在非主键列的前面
        // 所有主键列
        List<DataElementStructure> pkDataElementStructureList = dataElementStructureList.stream()
                .filter(dataElementStructure -> dataElementStructure.getIsPk())
                .collect(Collectors.toList());

        // 所有非主键列
        List<DataElementStructure> notPkDataElementStructureList = dataElementStructureList.stream()
                .filter(dataElementStructure -> !dataElementStructure.getIsPk())
                .collect(Collectors.toList());

        // 判断主键的序列号最大值和非主键的序列号最小值
        Integer pkSeq = pkDataElementStructureList.stream().max(Comparator.comparing(DataElementStructure::getSeq)).get().getSeq();
        Integer notPkSeq = notPkDataElementStructureList.stream().min(Comparator.comparing(DataElementStructure::getSeq)).get().getSeq();
        if (pkSeq > notPkSeq) {
            throw new ServiceException(Status.THE_PRIMARY_KEY_COLUMN_MUST_PRECEDE_THE_NON_PRIMARY_KEY_COLUMN);
        }

        // 自身的元素字段必须是所有主键列的最后面
        // 获取不包括自身的其他主键列字段
        List<DataElementStructure> otherPkDataElementStructureList = pkDataElementStructureList.stream()
                .filter(dataElementStructure -> !dataElementId.equals(dataElementStructure.getId()))
                .collect(Collectors.toList());

        // 获取其他主键列字段的最大序列号
        Integer otherPkSeq = otherPkDataElementStructureList.stream().max(Comparator.comparing(DataElementStructure::getSeq)).get().getSeq();

        // 自己的序列号
        DataElementStructure selfDataElementStructure = pkDataElementStructureList.stream()
                .filter(dataElementStructure -> dataElementId.equals(dataElementStructure.getId()))
                .collect(Collectors.toList()).get(0);
        Integer selfSeq = selfDataElementStructure.getSeq();

        // 判断自己的序列号和其他主键列字段的序列号
        if (selfSeq <= otherPkSeq) {
            throw new ServiceException(Status.ITS_OWN_ELEMENT_FIELD_MUST_BE_THE_MOST_RECENT_OF_ALL_PRIMARY_KEY_COLUMNS);
        }

        // 每批主键列的顺序不能更改

        // 主键列需要过滤出间接节点
        List<DataElementStructure> fatherStructureList = dataElementStructureMapper.selectMainFatherById(dataElementId,
                null, null, null);
        List<DataElementStructure> updateDataElementStructureList = dataElementStructureList.stream()
                .filter(pkDataElementStructure -> {
                    for (DataElementStructure fatherStructure : fatherStructureList) {
                        if (pkDataElementStructure.getId().equals(fatherStructure.getId())) {
                            return true;
                        }
                    }
                    return false;
                }).collect(Collectors.toList());

        // 添加自身
        updateDataElementStructureList.add(selfDataElementStructure);
        // 添加非主键列
        updateDataElementStructureList.addAll(notPkDataElementStructureList);

        return dataElementStructureMapper.updateStructure(dataElementId, updateDataElementStructureList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int delete(Long dataElementId, List<DataElementStructure> dataElementStructureList) {
        // 验证数据元素
        checkDataElement(dataElementId);

        // 不能删除主键列
        if (dataElementStructureList.stream().anyMatch(dataElementStructure -> dataElementStructure.getDataElementType() == DataElementType.MAIN && dataElementId.equals(dataElementStructure.getId()))) {
            throw new ServiceException(Status.CANNOT_DELETE_PRIMARY_KEY_COLUMN);
        }

        // 实体需要添加引用了数据元素的所有字段,转换为实体的自定义类型字段
        dataEntityStructureService.updateEntityElementEdge(dataElementStructureList);

        // 间接引用的主数据字段不能删除
        List<DataElementStructure> mainStructureList = dataElementStructureList.stream()
                .filter(dataElementStructure -> dataElementStructure.getDataElementType() == DataElementType.MAIN)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(mainStructureList)) {
            //
            for (DataElementStructure dataElementStructure : mainStructureList) {
                // 如果不存在，说明是间接引用的主数据字段
                if (!dataElementStructureMapper.isMainStructureExist(dataElementId, dataElementStructure.getId())) {
                    throw new ServiceException(Status.PRIMARY_DATA_FIELDS_THAT_ARE_INDIRECTLY_REFERENCED_CANNOT_BE_DELETED);
                }
            }
        }

        return dataElementStructureMapper.deleteStructure(dataElementId, dataElementStructureList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int referenceAdd(Long dataElementId, List<DataElementStructure> dataElementStructureList) {
        // 验证数据元素
        DataElement dataElement = checkDataElement(dataElementId);

        return this.referenceAdd(dataElement, dataElementStructureList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int referenceAdd(DataElement dataElement, List<DataElementStructure> dataElementStructureList) {

        Long dataElementId = dataElement.getId();

        // 校验字段名，不能重复
        List<DataElementStructure> structureList = this.selectStructureByDataElementId(dataElement, true,
                null, null, null);
        if (dataElementStructureList.stream().anyMatch(structure -> {
            for (DataElementStructure dataElementStructure : structureList) {
                if (dataElementStructure.getCode().equalsIgnoreCase(structure.getCode())) {
                    return true;
                }
            }
            return false;
        })) {
            throw new ServiceException(Status.A_FIELD_WITH_THE_SAME_NAME_CANNOT_EXIST);
        }

        List<DataElementStructure> insertStructureList = new ArrayList<>();
        insertStructureList.addAll(dataElementStructureList);

        List<DataElementStructure> allFatherList = dataElementStructureList.stream()
                // 过滤出主数据类型
                .filter(dataElementStructure -> dataElementStructure.getDataElementType() == DataElementType.MAIN)
                // 获取所有主数据类型的父级
                .map(dataElementStructure -> {
                    return getIterateStructure(dataElementStructure.getId(), null, null, null);
                }).flatMap(Collection::stream).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(allFatherList)) {
            // 按id去重
            allFatherList = allFatherList.stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(
                            Comparator.comparing(DataElementStructure::getId))), ArrayList::new));

            for (DataElementStructure dataElementStructure : allFatherList) {
                for (DataElementStructure elementStructure : dataElementStructureList) {
                    // 如果所有的父级的主数据中包含当前主数据则过滤
                    if (dataElementStructure.getId().equals(elementStructure.getId())) {
                        insertStructureList.remove(elementStructure);
                        break;
                    }
                }
            }
        }

        Integer minSeq = getMinSeq(dataElementId, true);
        Integer maxSeq = getMaxSeq(dataElementId, false);

        for (DataElementStructure dataElementStructure : insertStructureList) {
            // 如果是录入类型，则需要重新生成雪花id
            if (dataElementStructure.getDataElementType() == DataElementType.INPUT) {
                dataElementStructure.setId(IdWorker.getId());
            }
            Boolean isPk = dataElementStructure.getIsPk();
            if (isPk) {
                dataElementStructure.setSeq(--minSeq);
            } else if (!isPk) {
                dataElementStructure.setSeq(++maxSeq);
            }
        }

        return dataElementStructureMapper.insertStructure(dataElementId, insertStructureList);
    }

    // TODO:展示主数据结构时需要先过滤主数据的字段权限
    @Override
    public List<DataElementStructure> selectStructureByDataElementId(Long dataElementId, Boolean isQueryFather) {
        // 验证数据元素
        DataElement dataElement = checkDataElement(dataElementId);

        return this.selectStructureByDataElementId(dataElement, isQueryFather, null, null, null);
    }

    @Override
    public List<DataElementStructure> selectStructureByDataElementId(DataElement dataElement, Boolean isQueryFather,
                                                                     FieldType fieldType, Long length, Long decimalLength) {

        Long dataElementId = dataElement.getId();

        List<DataElementStructure> finallyStructureList = new ArrayList<>();

        // 当前数据元素的所有列
        List<DataElementStructure> dataElementStructureList = dataElementStructureMapper.selectByElementId(dataElementId,
                fieldType, length, decimalLength);

        // 获取主数据，如果存在则需要继续迭代寻找是否还有主数据的主数据
        List<DataElementStructure> mainStructureList = dataElementStructureList.stream()
                .filter(structure -> DataElementType.MAIN == structure.getDataElementType())
                // .map(structure -> {
                //     structure.setIsNested(false);
                //     return structure;
                // })
                .collect(Collectors.toList());

        if (isQueryFather) {
            for (DataElementStructure mainStructure : mainStructureList) {
                // 添加嵌套的主键列
                finallyStructureList.addAll(getIterateStructure(mainStructure.getId(), fieldType, length, decimalLength));
                // 添加主键列
                mainStructure.setBatch(Integer.MAX_VALUE);
                finallyStructureList.add(mainStructure);
            }
        } else {
            finallyStructureList.addAll(mainStructureList);
        }

        // 添加自身
        // finallyStructureList.addAll(dataElementStructureList.stream()
        //         .filter(structure -> dataElementId.equals(structure.getId()))
        //         .collect(Collectors.toList())
        // );

        // 添加非主键列
        finallyStructureList.addAll(dataElementStructureList.stream()
                .filter(structure -> DataElementType.MAIN != structure.getDataElementType())
                .sorted(Comparator.comparing(DataElementStructure::getSeq))
                .collect(Collectors.toList())
        );

        // 赋予顺序
        IntStream.range(0, finallyStructureList.size()).forEach(i -> {
            finallyStructureList.get(i).setSeq(i + 1);
        });

        return finallyStructureList;
    }

    @Override
    public List<DataElementStructure> selectStructureByDataElementId(Long dataElementId) {
        return this.selectStructureByDataElementId(dataElementId, true);
    }

    @Override
    public List<DataElementStructure> selectStructureByDataElementId(Long dataElementId, FieldType fieldType, Long length, Long decimalLength) {
        // 验证数据元素
        DataElement dataElement = checkDataElement(dataElementId);

        return this.selectStructureByDataElementId(dataElement, true, fieldType, length, decimalLength);
    }

    /**
     * 根据元素id迭代查找元素的父级
     *
     * @param dataElementId 元素id
     * @return
     */
    @Override
    public List<DataElementStructure> getIterateStructure(Long dataElementId, FieldType fieldType, Long length, Long decimalLength) {
        List<DataElementStructure> iterateStructureList = new ArrayList<>();
        List<DataElementStructure> fatchMainStructureList = dataElementStructureMapper.selectMainFatherById(dataElementId,
                fieldType, length, decimalLength);
        if (CollectionUtils.isEmpty(fatchMainStructureList)) {
            return iterateStructureList;
        }
        for (int i = 0; i < fatchMainStructureList.size(); i++) {
            DataElementStructure fatchMainStructure = fatchMainStructureList.get(i);

            // 每批都需要一个序号
            List<DataElementStructure> batchMainStructureList = getIterateStructure(fatchMainStructure.getId(), fieldType, length, decimalLength);
            for (DataElementStructure dataElementStructure : batchMainStructureList) {
                dataElementStructure.setBatch(i + 1);
            }
            iterateStructureList.addAll(batchMainStructureList);

            fatchMainStructure.setBatch(i + 1);
            iterateStructureList.add(fatchMainStructure);
        }
        return iterateStructureList;
    }

    @Override
    public List<DataElementStructureVo> selectMenuAndElementAndFieldList(Long dataElementId, String code, String name,
                                                                         Boolean isFilerInput) {

        // 查询所有菜单
        // List<DataElementStructureVo> menuList = new ArrayList<>();
        List<DataElementStructureVo> menuList = dataElementMenuMapper.selectCiteMenuTree(code, name);

        // 查询所有数据元素
        // List<DataElementStructureVo> elementList = new ArrayList<>();
        List<DataElementStructureVo> elementList = dataElementMapper.selectCiteElementTree(code, name);

        // 过滤当前数据元素已有的主数据列或字段列
        if (dataElementId != null) {
            // 验证数据元素
            checkDataElement(dataElementId);

            List<DataElementStructure> currentDataElementStructureList = selectStructureByDataElementId(dataElementId);
            elementList = elementList.stream()
                    .filter(element -> {
                        for (DataElementStructure dataElementStructure : currentDataElementStructureList) {
                            if (element.getId().equals(dataElementStructure.getId())) {
                                return false;
                            }
                        }
                        return true;
                    }).collect(Collectors.toList());
        }

        for (DataElementStructureVo elementStructure : elementList) {
            // 主数据类型需要获取主数据的表结构
            if (elementStructure.getDataElementType() == DataElementType.MAIN) {
                List<DataElementStructure> dataElementStructureList = selectStructureByDataElementId(elementStructure.getId());
                // 判断是否过滤录入类型数据
                if (isFilerInput) {
                    dataElementStructureList = dataElementStructureList.stream()
                            .filter(dataElementStructure -> dataElementStructure.getDataElementType() != DataElementType.INPUT)
                            .collect(Collectors.toList());
                }
                elementStructure.setChildren(JSONObject.parseArray(JSONObject.toJSONString(dataElementStructureList), DataElementStructureVo.class));
            }
        }

        // 遍历出树结构
        // 所有的数据集合
        List<DataElementStructureVo> trees = new ArrayList<>();
        // 添加数据元素菜单树
        trees.addAll(menuList);
        // 添加数据元素树
        trees.addAll(elementList);

        // 一级菜单集合
        List<DataElementStructureVo> oneMenuList = new ArrayList<>();

        // 获取第一级节点
        for (DataElementStructureVo tree : trees) {
            if (tree.getPid().equals(-1L)) {
                oneMenuList.add(tree);
            }
        }
        // 递归获取子节点
        for (DataElementStructureVo parent : oneMenuList) {
            recursiveTree(parent, trees);
        }

        return oneMenuList;
    }

    /**
     * 递归获取子节点
     */
    private DataElementStructureVo recursiveTree(DataElementStructureVo parent, List<DataElementStructureVo> menuList) {
        for (DataElementStructureVo menu : menuList) {
            if (parent.getMenuType() == MenuType.MENU && parent.getId().equals(menu.getPid())) {
                // 如果是菜单就继续递归查询
                if (menu.getMenuType() == MenuType.MENU) {
                    menu = recursiveTree(menu, menuList);
                }
                parent.getChildren().add(menu);
            }
        }
        return parent;
    }

    @Override
    public List<DataElementStructureVo> selectQualityMenuAndElementAndFieldList(Long dataElementId, String code, String name, Boolean isFilerInput, FieldType fieldType, Long length, Long decimalLength) {

        // 查询所有菜单
        // List<DataElementStructureVo> menuList = new ArrayList<>();
        List<DataElementStructureVo> menuList = dataElementMenuMapper.selectCiteMenuTree(code, name);
        // 过滤空节点
        menuList = menuList.stream()
                .filter(menu -> {
                    if (menu.getMenuType() == MenuType.NODE && menu.getDataElementType() == null) {
                        return false;
                    }
                    return true;
                }).collect(Collectors.toList());

        // 查询所有数据元素
        // List<DataElementStructureVo> elementList = new ArrayList<>();
        List<DataElementStructureVo> elementList = dataElementMapper.selectCiteElementTree(code, name);

        // 过滤不满足条件的字段
        elementList = elementList.stream()
                .filter(element -> {
                    DataElementType dataElementType = element.getDataElementType();
                    FieldType fieldType1 = element.getFieldType();
                    Long length1 = element.getLength();
                    Long decimalLength1 = element.getDecimalLength();
                    if (dataElementType == null) {
                        return false;
                    }
                    if (dataElementType == DataElementType.FIELD &&
                            (fieldType1 != fieldType || length1 != length || decimalLength1 != decimalLength)) {
                        return false;
                    }
                    return true;
                }).collect(Collectors.toList());

        // 过滤当前数据元素已有的主数据列或字段列
        if (dataElementId != null) {
            // 验证数据元素
            checkDataElement(dataElementId);

            List<DataElementStructure> currentDataElementStructureList = selectStructureByDataElementId(dataElementId, fieldType, length, decimalLength);
            elementList = elementList.stream()
                    .filter(element -> {
                        for (DataElementStructure dataElementStructure : currentDataElementStructureList) {
                            if (element.getId().equals(dataElementStructure.getId())) {
                                return false;
                            }
                        }
                        return true;
                    }).collect(Collectors.toList());
        }

        // 过滤不满足条件的主数据
        elementList = elementList.stream()
                .filter(element -> {
                    // 主数据类型需要获取主数据的表结构
                    if (element.getDataElementType() == DataElementType.MAIN) {
                        List<DataElementStructure> dataElementStructureList = selectStructureByDataElementId(element.getId(), fieldType, length, decimalLength);
                        if (CollectionUtils.isEmpty(dataElementStructureList)) {
                            return false;
                        }
                        // 判断是否过滤录入类型数据
                        if (isFilerInput) {
                            dataElementStructureList = dataElementStructureList.stream()
                                    .filter(dataElementStructure -> dataElementStructure.getDataElementType() != DataElementType.INPUT)
                                    .collect(Collectors.toList());
                            if (CollectionUtils.isEmpty(dataElementStructureList)) {
                                return false;
                            }
                        }
                        element.setChildren(JSONObject.parseArray(JSONObject.toJSONString(dataElementStructureList), DataElementStructureVo.class));
                    }
                    return true;
                }).collect(Collectors.toList());

        // for (DataElementStructureVo elementStructure : elementList) {
        //     // 主数据类型需要获取主数据的表结构
        //     if (elementStructure.getDataElementType() == DataElementType.MAIN) {
        //         List<DataElementStructure> dataElementStructureList = selectStructureByDataElementId(elementStructure.getId(), fieldType, length, decimalLength);
        //         // 判断是否过滤录入类型数据
        //         if (isFilerInput) {
        //             dataElementStructureList = dataElementStructureList.stream()
        //                     .filter(dataElementStructure -> dataElementStructure.getDataElementType() != DataElementType.INPUT)
        //                     .collect(Collectors.toList());
        //         }
        //         elementStructure.setChildren(JSONObject.parseArray(JSONObject.toJSONString(dataElementStructureList), DataElementStructureVo.class));
        //     }
        // }

        // 遍历出树结构
        // 所有的数据集合
        List<DataElementStructureVo> trees = new ArrayList<>();
        // 添加数据元素菜单树
        trees.addAll(menuList);
        // 添加数据元素树
        trees.addAll(elementList);

        // 递归成树结构
        List<DataElementStructureVo> treeList = (List<DataElementStructureVo>) TreeUtils.getTree(trees);
        // 删除空集合的菜单
        TreeUtils.removeEmptyChilderAndMenu(treeList);

        return treeList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int importElement(MultipartFile file, Long dataElementId) {
        checkDataElement(dataElementId);

        int insert = 0;
        try {
            List<Map<String, Object>> dataList = ExcelUtils.importFromExcel(file);
            if (CollectionUtils.isEmpty(dataList)) {
                return 1;
            }
            List<DataElementStructure> dataElementStructureList = dataList.stream().map(dataMap -> {
                Set<String> keys = dataMap.keySet();
                // 判断是否存在该列
                if (!keys.contains("编码") &&
                        !keys.contains("字段类型") &&
                        !keys.contains("长度") &&
                        !keys.contains("精度") &&
                        !keys.contains("主键")) {
                    throw new ServiceException("缺少必要{编码/字段类型/长度/精度/主键}信息");
                }

                DataElementStructure dataElementStructure = new DataElementStructure();
                for (String key : dataMap.keySet()) {
                    switch (key) {
                        case "编码":
                            if (StringUtils.isEmpty(MapUtils.getString(dataMap, key))) {
                                throw new ServiceException(Status.THE_DATA_ELEMENT_FIELD_ENCODING_CANNOT_BE_EMPTY);
                            }
                            dataElementStructure.setCode(MapUtils.getString(dataMap, key));
                            break;
                        case "名称":
                            dataElementStructure.setName(MapUtils.getString(dataMap, key, null));
                            break;
                        case "描述":
                            dataElementStructure.setDescription(MapUtils.getString(dataMap, key, null));
                            break;
                        case "字段类型":
                            if (StringUtils.isEmpty(MapUtils.getString(dataMap, key))) {
                                throw new ServiceException(Status.THE_DATA_ELEMENT_FIELD_TYPE_CANNOT_BE_EMPTY);
                            }
                            dataElementStructure.setFieldType(FieldType.valueOf(MapUtils.getString(dataMap, key)));
                            break;
                        case "长度":
                            if (MapUtils.getLong(dataMap, key) == null) {
                                throw new ServiceException(Status.THE_DATA_ELEMENT_FIELD_LENGTH_CANNOT_BE_EMPTY);
                            }
                            dataElementStructure.setLength(MapUtils.getLong(dataMap, key));
                            break;
                        case "精度":
                            if (MapUtils.getLong(dataMap, key) == null) {
                                throw new ServiceException(Status.THE_DATA_ELEMENT_FIELD_DECIMAL_LENGTH_CANNOT_BE_EMPTY);
                            }
                            dataElementStructure.setDecimalLength(MapUtils.getLong(dataMap, key));
                            break;
                        case "主键":
                            if (MapUtils.getBoolean(dataMap, key) == null) {
                                throw new ServiceException(Status.THE_DATA_ELEMENT_FIELD_PRIMARY_KEY_CANNOT_BE_EMPTY);
                            }
                            dataElementStructure.setIsPk(MapUtils.getBoolean(dataMap, key));
                            break;
                    }
                }
                return dataElementStructure;
            }).collect(Collectors.toList());

            for (DataElementStructure dataElementStructure : dataElementStructureList) {
                dataElementStructure.setDataElementType(DataElementType.INPUT);
                insert += create(dataElementId, dataElementStructure);
            }

        } catch (Exception e) {
            throw new ServiceException("导入失败:" + e.getMessage());
        }
        return insert;
    }

    @Override
    public void exportElement(HttpServletResponse response, Long dataElementId) {

        // 获取元素对象
        DataElement dataElement = checkDataElement(dataElementId);

        // 获取表结构
        List<DataElementStructure> dataElementStructureList = selectStructureByDataElementId(dataElementId);

        // 获取头信息
        List<ExcelDto> headList = new ArrayList<>();
        ExcelDto excelDto1 = new ExcelDto();
        excelDto1.setDisplayCode("code");
        excelDto1.setDisplayName("编码");
        headList.add(excelDto1);
        ExcelDto excelDto2 = new ExcelDto();
        excelDto2.setDisplayCode("name");
        excelDto2.setDisplayName("名称");
        headList.add(excelDto2);
        ExcelDto excelDto3 = new ExcelDto();
        excelDto3.setDisplayCode("description");
        excelDto3.setDisplayName("描述");
        headList.add(excelDto3);
        ExcelDto excelDto4 = new ExcelDto();
        excelDto4.setDisplayCode("fieldType");
        excelDto4.setDisplayName("字段类型");
        headList.add(excelDto4);
        ExcelDto excelDto5 = new ExcelDto();
        excelDto5.setDisplayCode("length");
        excelDto5.setDisplayName("长度");
        headList.add(excelDto5);
        ExcelDto excelDto6 = new ExcelDto();
        excelDto6.setDisplayCode("decimalLength");
        excelDto6.setDisplayName("精度");
        headList.add(excelDto6);
        ExcelDto excelDto7 = new ExcelDto();
        excelDto7.setDisplayCode("isPk");
        excelDto7.setDisplayName("主键");
        headList.add(excelDto7);

        // 获取数据信息
        List<Map<String, Object>> dataList = dataElementStructureList.stream().map(dataElementStructure -> {
            Map<String, Object> dataMap = (Map<String, Object>) JSONObject.parseObject(JSONObject.toJSONString(dataElementStructure), Map.class);
            return dataMap;
        }).collect(Collectors.toList());

        // 导出成excel文件
        ExcelUtils.export2Excel(response, dataElement.getCode(), headList, dataList);
    }

}
