package com.datalink.fdop.element.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.ExportJSONFileUtil;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.split.SplitUtils;
import com.datalink.fdop.common.core.utils.tree.TreeUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.datasource.annotation.SwitchDataSource;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.drive.api.RemoteDriveService;
import com.datalink.fdop.drive.api.RemoteJdbcService;
import com.datalink.fdop.drive.api.RemoteTaskService;
import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.drive.api.domain.dto.Field;
import com.datalink.fdop.element.api.domain.*;
import com.datalink.fdop.element.api.enums.EntityInsertType;
import com.datalink.fdop.element.api.enums.EntityType;
import com.datalink.fdop.element.api.model.DataEntityTree;
import com.datalink.fdop.element.api.model.dto.TaskEtlDto;
import com.datalink.fdop.element.api.model.vo.*;
import com.datalink.fdop.element.mapper.*;
import com.datalink.fdop.element.service.IDataEntityService;
import com.datalink.fdop.element.service.IDataEntityStructureService;
import com.datalink.fdop.element.service.IDataEntitySynLogService;
import com.datalink.fdop.element.service.IDataEntityTableService;
import com.datalink.fdop.graph.api.RemoteEtlService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @date 2022/4/12 13:58
 */
@Service
@Slf4j
public class DataEntityServiceImpl implements IDataEntityService {

    @Autowired
    private DataEntityMapper dataEntityMapper;

    @Autowired
    private DataEntityMenuMapper dataEntityMenuMapper;

    @Autowired
    private IDataEntityTableService dataEntityTableService;

    @Autowired
    private DataEntityTableMapper dataEntityTableMapper;

    @Autowired
    private DataEntityStructureMapper dataEntityStructureMapper;

    @Autowired
    private DataEntitytSynLogMapper dataEntitytSynLogMapper;

    @Autowired
    private IDataEntityStructureService dataEntityStructureService;

    @Autowired
    private RemoteTaskService remoteTaskService;

    @Autowired
    private RemoteEtlService remoteEtlService;

    @Autowired
    private RemoteDriveService remoteDriveService;

    @Autowired
    private RemoteJdbcService remoteJdbcService;

    @Autowired
    private IDataEntitySynLogService dataEntitySynLogService;

    @Autowired
    private DataEntityMenuServiceImpl dataEntityMenuService;

    private DataEntity checkDataEntity(Long id) {
        DataEntity dataEntity = dataEntityMapper.selectById(id);
        if (dataEntity == null) {
            throw new ServiceException(Status.DATA_ENTITY_NOT_EXIST);
        }
        return dataEntity;
    }

    private DataEntity checkDataEntity(String code) {
        DataEntity dataEntity = dataEntityMapper.selectByCode(code);
        if (dataEntity == null) {
            throw new ServiceException(Status.DATA_ENTITY_NOT_EXIST);
        }
        return dataEntity;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DataEntity create(DataEntity dataEntity) {
        if (dataEntity.getPid() != -1L && dataEntityMenuMapper.selectById(dataEntity.getPid()) == null) {
            throw new ServiceException(Status.DATA_ENTITY_MENU_NOT_EXIST);
        }
        if (dataEntityMapper.selectByCode(dataEntity.getCode()) != null) {
            throw new ServiceException(Status.DATA_ENTITY_EXIST);
        }
        if (dataEntity.getId() == null || dataEntity.getId() == 0L) {
            dataEntity.setId(IdWorker.getId());
        }
        int insert = dataEntityMapper.insertEntity(dataEntity);
        // 创建元素边关系
        if (insert > 0 && dataEntity.getPid() != -1L) {
            // 如果修改了元素层级，并且不是置为顶级菜单，则需要添加点和菜单的边关系
            dataEntityMapper.createEntityAndMenuEdge(dataEntity.getPid(), Arrays.asList(dataEntity.getId()));
        }
        return dataEntity;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createBySql(DataEntity dataEntity) {
        String sql = dataEntity.getSql();
        if (StringUtils.isEmpty(sql)) {
            throw new ServiceException("sql不能为空");
        }
        String regex = "^\\s*CREATE\\s+(TABLE|VIEW)\\s";
        Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(sql);
        if (!matcher.find()) {
            throw new ServiceException("不是创表语句不予执行");
        }
        if (dataEntity.getPid() != -1L && dataEntityMenuMapper.selectById(dataEntity.getPid()) == null) {
            throw new ServiceException(Status.DATA_ENTITY_MENU_NOT_EXIST);
        }
        DataEntitySynVo dataEntitySynVo = new DataEntitySynVo();
        dataEntitySynVo.setDataSourceId(dataEntity.getDataSourceId());
        dataEntitySynVo.setPid(dataEntity.getPid());
        dataEntitySynVo.setIsView(false);
        String regex1 = "CREATE\\s+TABLE\\s+(?:IF\\s+NOT\\s+EXISTS\\s+)?(?:`?([^`\\.]+)`?\\.)?(?:`?([^`\\.]+)`?\\.)?`?([^`]+)`?\\s*\\(";
        Pattern pattern1 = Pattern.compile(regex1, Pattern.CASE_INSENSITIVE);
        Matcher matcher1 = pattern1.matcher(sql);
        if (matcher1.find()) {
            String database = matcher1.group(1);
            String schema = matcher1.group(2);
            String table = matcher1.group(3);
            if (StringUtils.isNotEmpty(schema)) {
                dataEntitySynVo.setDatabaseName(schema);
            } else {
                dataEntitySynVo.setDatabaseName(database);
            }
            if (StringUtils.isEmpty(dataEntity.getCode())) {
                dataEntity.setCode(table);
            }
            DataEntityTableNameSynVo dataEntityTableNameSynVo = new DataEntityTableNameSynVo();
            dataEntityTableNameSynVo.setTableName(table);
            dataEntityTableNameSynVo.setEntityCode(table);
            dataEntitySynVo.setTableNameList(Collections.singletonList(dataEntityTableNameSynVo));
            if (dataEntityMapper.selectByCode(dataEntity.getCode()) != null) {
                throw new ServiceException(Status.DATA_ENTITY_EXIST);
            }
            R r = remoteJdbcService.execDdlSql(dataEntity.getDataSourceId(), sql);
            if (r.getCode() != 200) {
                throw new ServiceException("sql执行失败：" + r.getMsg());
            }
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            dataEntitySynLogService.synchronize(dataEntitySynVo);
        } else {
            throw new ServiceException("sql匹配失败");
        }

    }

    /**
     * 未使用
     *
     * @param createSql
     * @return
     */
    public static String parseTableIdentifier(String createSql) {
        // 简化正则表达式，仅用于示例
        // 注意：这个正则表达式可能无法处理所有情况，特别是包含注释、多行SQL等
        // 假设CREATE TABLE语句遵循CREATE TABLE [IF NOT EXISTS] [database.]?[schema.]?table (...)
        String regex = "CREATE\\s+TABLE\\s+(?:IF\\s+NOT\\s+EXISTS\\s+)?(?:`?([^`\\.]+)`?\\.)?(?:`?([^`\\.]+)`?\\.)?`?([^`]+)`?\\s*\\(";
        Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(createSql);

        if (matcher.find()) {
            String database = matcher.group(1);
            String schema = matcher.group(2);
            String table = matcher.group(3);
            if (schema != null && !schema.isEmpty()) {
                return String.format("%s.%s.%s", database == null ? "" : database, schema, table);
            } else {
                return String.format("%s.%s", database == null ? "" : database, table);
            }
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(DataEntity dataEntity) {
        // 校验实体
        DataEntity checkDataEntity = checkDataEntity(dataEntity.getId());

        if (StringUtils.isNotEmpty(dataEntity.getCode()) && dataEntityMapper.checkCodeIsExists(dataEntity.getId(), dataEntity.getCode()) != null) {
            throw new ServiceException(Status.DATA_ENTITY_EXIST);
        }
        // 不能拖拽到节点里面
        if (dataEntity.getPid() != null && dataEntity.getPid() != -1L && dataEntityMenuMapper.selectById(dataEntity.getPid()) == null) {
            throw new ServiceException(Status.UNKNOWN_ENTITY_MENU);
        }

        int update = dataEntityMapper.updateById(dataEntity);
        if (update > 0) {
            // 获取修改前的菜单pid,并删除修改前的点和菜单的边关系
            dataEntityMapper.deleteEntityAndMenuEdge(Arrays.asList(dataEntity.getId()), checkDataEntity.getPid());
            if (dataEntity.getPid() != -1L) {
                // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                dataEntityMapper.createEntityAndMenuEdge(dataEntity.getPid(), Arrays.asList(dataEntity.getId()));
            }
        }
        return update;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void copy(Long pid, List<DataEntityCopyVo> dataEntityCopyList) {
        // 检查菜单
        DataEntityMenu dataEntityMenu = dataEntityMenuMapper.selectById(pid);
        if (dataEntityMenu == null) {
            throw new ServiceException(Status.DATA_ENTITY_MENU_NOT_EXIST);
        }

        if (CollectionUtils.isEmpty(dataEntityCopyList)) {
            throw new ServiceException(Status.THE_COPIED_ENTITY_INFORMATION_CANNOT_BE_EMPTY);
        }

        for (DataEntityCopyVo dataEntityCopyVo : dataEntityCopyList) {
            // 检查实体
            DataEntity dataEntity = checkDataEntity(dataEntityCopyVo.getCodeNodeId());
            // 设置修改后的pid
            dataEntity.setPid(pid);

            // 修改基本信息
            dataEntity.setCode(dataEntityCopyVo.getCode());
            dataEntity.setName(dataEntityCopyVo.getName());
            dataEntity.setDescription(dataEntityCopyVo.getDescription());
            // 创建节点
            DataEntity copyEntity = this.create(dataEntity);

            // 查询被复制的实体的字段
            List<DataEntityStructureVo> dataEntityStructureVos = dataEntityStructureService.selectStructureById(dataEntityCopyVo.getCodeNodeId(), true);
            dataEntityStructureVos = dataEntityStructureVos.stream().map(dataEntityStructureVo -> {
                dataEntityStructureVo.setDataEntityId(copyEntity.getId());
                return dataEntityStructureVo;
            }).collect(Collectors.toList());

            // 实体字段不为空则创建
            if (CollectionUtils.isNotEmpty(dataEntityStructureVos)) {
                for (DataEntityStructureVo dataEntityStructureVo : dataEntityStructureVos) {
                    if (dataEntityStructureVo.getEntityInsertType() == EntityInsertType.PREDEFINED) {
                        dataEntityStructureService.addPredefined(copyEntity.getId(), Arrays.asList(new DataElementStructureVo(dataEntityStructureVo)), false);
                    } else {
                        dataEntityStructureService.create(new DataEntityStructure(dataEntityStructureVo), false);
                    }
                }
            }
        }
    }

    // TODO:目前只展示了实体和任务的关系
    @Override
    public Map<String, Object> checkDeleteEntity(List<Long> ids) {
        Map<String, Object> result = new HashMap<>();
        List<TaskEtlDto> taskEtlDtos = dataEntityStructureMapper.selectTaskAndFieldEdgeByDataEntityIds(ids);
        if (CollectionUtils.isNotEmpty(taskEtlDtos)) {
            // 获取实体被引用的任务
            //List<String> taskNameList = taskEtlDtos.stream().map(TaskEtlDto::getTaskName).collect(Collectors.toList());
            List<String> entityCodeList = taskEtlDtos.stream().distinct().map(TaskEtlDto::getEntityCode).collect(Collectors.toList());
            for (String entityCode : entityCodeList) {
                List<String> taskNameList = taskEtlDtos.stream().filter(taskEtlDto -> entityCode.equals(taskEtlDto.getEntityCode())).map(TaskEtlDto::getTaskName).collect(Collectors.toList());
                Map<String, Object> map = new HashMap<>();
                map.put("TASK", taskNameList);
                result.put(entityCode, map);
            }
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int delete(Boolean isDeleteTable, List<Long> ids) {
        for (Long id : ids) {
            List<DataEntityTable> dataEntityTableList = dataEntityMapper.selectEntityTableById(id);
            if (isDeleteTable && CollectionUtils.isNotEmpty(dataEntityTableList)) {
                // 删除实体判断是否删除关联表已经创建的表
                dataEntityTableService.delete(id, isDeleteTable, dataEntityTableList.stream().map(DataEntityTable::getId).collect(Collectors.toList()));
            }

//            // 获取实体字段id集合
//            List<DataEntityStructureVo> dataEntityStructureVoList = dataEntityStructureService.selectStructureById(id, true);
//
//            // 删除成功需要修改被etl任务引用的任务json
//            // 获取实体被引用的任务
//            List<TaskEtlDto> taskEtlDtoList = dataEntityStructureMapper.selectTaskAndFieldEdgeList(id,
//                    SqlUtils.getInSql(dataEntityStructureVoList
//                            .stream().map(DataEntityStructureVo::getId).collect(Collectors.toList())));
//            if (CollectionUtils.isNotEmpty(taskEtlDtoList)) {
//                Map<String, List<TaskEtlDto>> taskEtlNodeMap = taskEtlDtoList.stream()
//                        .collect(Collectors.groupingBy(taskEtlDto -> taskEtlDto.getTaskCode() + "_" + taskEtlDto.getNodeId()));
//                taskEtlNodeMap.forEach((key, value) -> {
//                    TaskEtlDto taskEtlDto = value.get(0);
//                    // 获取节点信息
//                    R<EtlNode> etlNodeR = remoteEtlService.selectEtlNode(taskEtlDto.getTaskCode(), taskEtlDto.getNodeId());
//                    if (etlNodeR.getCode() != Status.SUCCESS.getCode()) {
//                        throw new ServiceException(etlNodeR.getMsg());
//                    }
//                    EtlNode etlNode = etlNodeR.getData();
//                    if (etlNode.getNodeType() == EtlNodeType.SOURCE) {
//                        // 修改节点字段
//                        List<EtlField> fieldList = etlNode.getFieldList();
//                        etlNode.setFieldList(fieldList.stream()
//                                .filter(field -> {
//                                    if (dataEntityStructureVoList.stream()
//                                            .anyMatch(dataEntityStructureVo -> field.getCiteFieldId().equals(dataEntityStructureVo.getId().toString()))) {
//                                        return true;
//                                    }
//                                    return false;
//                                })
//                                .map(field -> {
//                                    for (DataEntityStructureVo dataEntityStructureVo : dataEntityStructureVoList) {
//                                        if (field.getCiteFieldId().equals(dataEntityStructureVo.getId().toString())) {
//                                            field.setOperType(OperType.DELETE);
//                                            field.setWarnType(WarnType.ERROR);
//                                            field.setIsSyn(false);
//                                        }
//                                    }
//                                    return field;
//                                }).collect(Collectors.toList()));
//                        R<SourceNode> sourceNodeR = remoteEtlService.updateSourceNode(taskEtlDto.getTaskCode(), JSONObject.parseObject(JSONObject.toJSONString(etlNode), SourceNode.class));
//                        if (sourceNodeR.getCode() != Status.SUCCESS.getCode()) {
//                            throw new ServiceException(sourceNodeR.getMsg());
//                        }
//                    }
//
//                    // 0:失败,1:激活,2:警告,3:未激活
//                    int etlStatus = taskEtlDto.getEtlStatus();
//                    if (etlStatus != 0) {
//                        // 修改任务json
//                        R updateTaskDefinitionR = remoteTaskService.updateTaskDefinitionEtlStatus(0l, taskEtlDto.getTaskCode(), 0);
//                        if (updateTaskDefinitionR.getCode() != Status.SUCCESS.getCode()) {
//                            throw new ServiceException(updateTaskDefinitionR.getMsg());
//                        }
//                    }
//                });
//
//            }
        }
        // 删除实体关联的字段
        dataEntityStructureMapper.batchDeleteEntityStructureByEntityIds(ids);
        // 删除实体
        return dataEntityMapper.deleteBatchIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchDelete(Boolean isDeleteTable, List<DataEntity> dataEntities) {
        for (DataEntity dataEntity : dataEntities) {
            if (dataEntity.getEntityType() == EntityType.DIM || dataEntity.getEntityType() == EntityType.DWD) {
                List<DataEntity> list = dataEntityMapper.list(dataEntity.getId());
                if (CollectionUtils.isNotEmpty(list)) {
                    this.delete(isDeleteTable, list.stream().map(DataEntity::getId).collect(Collectors.toList()));
                }
                dataEntityMenuMapper.deleteBatchIds(Collections.singletonList(dataEntity.getId()));
            } else {
                this.delete(isDeleteTable, Collections.singletonList(dataEntity.getId()));
            }
        }
        return dataEntities.size();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int deleteSynLog(List<Long> ids) {
        return dataEntitytSynLogMapper.deleteBatchIds(ids);
    }

    @Override
    public PageDataInfo<DataEntity> list(DataEntity dataEntity) {
        // 获取分页参数
        Page<DataEntity> page = PageUtils.getPage(DataEntity.class);
        IPage<DataEntity> dataEntityIPage = dataEntityMapper.selectList(page, dataEntity);

        return PageUtils.getPageInfo(dataEntityIPage.getRecords(), (int) dataEntityIPage.getTotal());
    }

    @Override
    public List<DataEntity> dwdOrDimList(DataEntity dataEntity) {
        return dataEntityMapper.dwdOrDimList(dataEntity);
    }

    @Override
    public DataEntity selectById(Long id) {

        DataEntity dataEntity = checkDataEntity(id);
        if (dataEntity.getTableId() != null) {
            DataEntityTable dataEntityTable = dataEntityTableService.checkDataEntityTable(dataEntity.getTableId());
            if (dataEntityTable != null) {
                Long sourceId = dataEntityTable.getDataSourceId();
                // 查询数据源信息
                R<DataSource> dataSourceR = remoteDriveService.queryDataSource(sourceId);
                if (dataSourceR.getCode() == Status.SUCCESS.getCode()) {
                    DataSource dataSource = dataSourceR.getData();
                    dataEntity.setIsRead(dataSource.getIsRead());
                }

            }
        }
        return dataEntity;
    }

    @Override
    public List<DataEntityTree> getEntityMenuTableTree(String code, Boolean isRead, Long dataSourceId) {

        // 所有的数据集合
        List<DataEntityTree> trees = new ArrayList<>();
        // 添加数据元素树 (过滤空实体)
        if (StringUtils.isNotEmpty(code)) {
            code = code.toLowerCase();
        }
        List<DataEntityTree> treeList = dataEntityMapper.selectEntityTableTree("ASC", code, isRead);
        //过滤出不是同一数据源下的实体
        if (dataSourceId != null) {
            treeList = treeList.stream()
                    .filter(tree -> {
                        if (tree.getTableId() != null) {
                            DataEntityTable dataEntityTable = dataEntityTableMapper.selectById(tree.getTableId());
                            if (dataEntityTable != null && dataSourceId.equals(dataEntityTable.getDataSourceId())) {
                                tree.setDataSourceId(dataEntityTable.getDataSourceId());
                                return true;
                            } else {
                                return false;
                            }
                        } else {
                            return false;
                        }
                    })
                    .collect(Collectors.toList());
        }
        trees.addAll(treeList);

        // 添加数据元素菜单树
        trees.addAll(dataEntityMenuMapper.selectMenuTree("ASC", code));

        // 递归成树结构
        return (List<DataEntityTree>) TreeUtils.getTree(trees);
    }

    @Override
    public DataEntity selectByCode(String code) {
        return checkDataEntity(code);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DataEntity generateEntity(Long pid, String code, String name, String description, List<Field> fieldList) {
        // 创建实体
        DataEntity dataEntity = this.create(new DataEntity(pid, code, name, description));

        // 添加字段
        if (CollectionUtils.isEmpty(fieldList)) {
            throw new ServiceException(Status.AT_LEAST_ONE_ENTITY_FIELD);
        }

        for (int i = 0; i < fieldList.size(); i++) {
            Field field = fieldList.get(i);
            Integer seq = i + 1;
            DataEntityStructure dataEntityStructure = new DataEntityStructure(dataEntity.getId(), EntityInsertType.CUSTOMIZE, field.getFieldName(), field.getFieldDesc(), field.getBaseFieldType(), field.getLength(), field.getDecimalLength(), field.getIsPk() != null ? field.getIsPk() : false, seq);
            // 自定义添加
            dataEntityStructureMapper.insertStructure(dataEntityStructure);
        }

        return dataEntity;
    }

    @Override
    public void batchExportDataEntityByIds(List<Long> ids, HttpServletResponse response) {
        List<DataEntity> dataEntities = dataEntityMapper.selectDataEntityList(ids);
        if (CollectionUtils.isNotEmpty(dataEntities)) {
            DataEntityExport dataEntityExport = new DataEntityExport();
            // 封装需要导出的实体、实体列结构、实体表信息、实体表映射
            List<DataEntityCollect> dataEntityCollectList = new ArrayList<>();
            dataEntities.forEach(dataEntity -> {
                DataEntityCollect dataEntityCollect = new DataEntityCollect();
                // 实体
                dataEntityCollect.setDataEntity(dataEntity);
                // 实体结构
                dataEntityCollect.setDataEntityStructureList(dataEntityStructureService.selectStructureById(dataEntity.getId(), true));
                // 实体表与实体表映射关系
                DataEntityTable dataEntityTable = dataEntityTableService.checkDataEntityTable(dataEntity.getTableId());
                dataEntityTable.setDataEntityTableMappingList(dataEntityTableService.selectEntityTableMapping(dataEntity.getId()));
                // 实体表
                dataEntityCollect.setDataEntityTable(dataEntityTable);
                // 实体列结构
                dataEntityCollectList.add(dataEntityCollect);
            });
            // 获取上级菜单ID
            Set<Long> pidSet = dataEntities.stream().map(DataEntity::getPid).collect(Collectors.toSet());
            List<DataEntityTree> taskTree = dataEntityMenuService.getTaskTree(pidSet);
            // 封装需要导出的任务树菜单
            dataEntityExport.setDataEntityCollectList(dataEntityCollectList);
            // 获取指定实体的对应任务树菜单
            dataEntityExport.setDataEntityTreeList(taskTree);
            // 导出JSON文件
            ExportJSONFileUtil.exportJson(response, dataEntityExport);
        }
    }

    @Override
    public R importDataEntity(MultipartFile file) {
        // 读取文件解析json字符串
        String explainJson = ExportJSONFileUtil.file2String(file);
        if (StringUtils.isEmpty(explainJson)) {
            return R.fail("导入实体为空");
        }
        // 总数
        int total = 0;
        // 导入成功数量
        int success = 0;
        // 导入失败数量
        int fail = 0;
        DataEntityExport dataEntityExport = JSONObject.parseObject(explainJson, DataEntityExport.class);
        if (dataEntityExport != null) {
            // 导入实体菜单信息
            List<DataEntityTree> dataEntityTreeList = dataEntityExport.getDataEntityTreeList();
            if (CollectionUtils.isNotEmpty(dataEntityTreeList)) {
                for (DataEntityTree dataEntityTree : dataEntityTreeList) {
                    DataEntityMenu dataEntityMenu = new DataEntityMenu(dataEntityTree.getCode(), dataEntityTree.getName(), dataEntityTree.getDescription(), dataEntityTree.getId(), dataEntityTree.getPid());
                    if (dataEntityMenuMapper.selectById(dataEntityMenu.getId()) == null) {
                        // 新增实体菜单
                        dataEntityMenuService.create(dataEntityMenu);
                    } else {
                        // 更新实体菜单
                        dataEntityMenuService.update(dataEntityMenu);
                    }
                }
            }
            // 实体汇总信息
            List<DataEntityCollect> dataEntityCollectList = dataEntityExport.getDataEntityCollectList();
            total = dataEntityCollectList.size();
            if (CollectionUtils.isNotEmpty(dataEntityCollectList)) {
                for (DataEntityCollect dataEntityCollect : dataEntityCollectList) {
                    // 实体
                    DataEntity dataEntity = dataEntityCollect.getDataEntity();
                    try {
                        // 新增/更新实体
                        if (selectById(dataEntity.getId()) == null) {
                            // 新增实体
                            create(dataEntity);
                        } else {
                            // 更新实体
                            update(dataEntity);
                        }
                        // 实体列结构
                        List<DataEntityStructureVo> dataEntityStructureList = dataEntityCollect.getDataEntityStructureList();
                        for (DataEntityStructureVo dataEntityStructureVo : dataEntityStructureList) {
                            DataEntityStructure dataEntityStructure = new DataEntityStructure(dataEntityStructureVo);
                            if (dataEntityStructureMapper.selectById(dataEntityStructure.getId()) == null) {
                                // 新增实体结构
                                dataEntityStructureService.create(dataEntityStructure);
                            } else {
                                // 更新实体结构
                                dataEntityStructureService.update(dataEntityStructure);
                            }
                        }
                        // 实体表
                        DataEntityTable dataEntityTable = dataEntityCollect.getDataEntityTable();
                        // 新增/更新实体表以及保存表与字段映射关系（默认字段同名匹配）
                        dataEntityTableService.createOnUpdate(dataEntityTable);
                        // 实体表映射关系 目前会自动更新映射关系，暂时不用
                        // List<DataEntityTableMapping> dataEntityTableMappingList = dataEntityTable.getDataEntityTableMappingList();
                        success++;
                    } catch (Exception e) {
                        fail++;
                        log.info("实体[{}]导入新增/更新失败，详情：{}", dataEntity.getCode(), e.getMessage());
                    }
                }

            }
        }
        if (success < total) {
            return R.ok(String.format("实体部分导入成功，成功数量：%s，失败数量：%s", success, fail));
        } else {
            return R.ok("实体导入成功");
        }
    }

    @Override
    @SwitchDataSource(value = "current")
    public List<DataEntityTableVo> selectDataEntityTableVoByTenantIdAndIds(Long tenantId, List<Long> dataEntityIds) {
        // 创建实体表和mapping的关系，mapping和实体字段的关系
        List<List<Long>> splitList = SplitUtils.splitList(dataEntityIds, 50);
        List<DataEntityTableVo> result = new ArrayList<>();
        for (List<Long> splitIds : splitList) {
            List<DataEntityTableVo> dataEntityTableVos = dataEntityTableMapper.selectDataEntityTableVoByDataEntityIds(splitIds);
            result.addAll(dataEntityTableVos);
        }
        return result;
    }

    @Override
    public List<DataJsonEntityVo> exportEntityJson(List<Long> ids) {
        List<DataEntity> dataEntities = dataEntityMapper.selectDataEntityList(ids);

        List<DataJsonEntityVo> result = new ArrayList<>();
        for (DataEntity dataEntity : dataEntities) {

            // 创建返回对象
            DataJsonEntityVo dataJsonEntityVo = new DataJsonEntityVo();

            // 设置实体信息
            dataJsonEntityVo.setDataEntity(dataEntity);

            // 查询并设置菜单信息
            if (dataEntity.getPid() != null) {
                DataEntityMenu menuInfo = dataEntityMenuMapper.selectById(dataEntity.getPid());
                dataJsonEntityVo.setMenuInfo(menuInfo);
            }

            // 查询实体结构信息
            List<DataEntityStructure> dataEntityStructureList = new ArrayList<>();
            List<DataEntityStructureVo> structureVoList = dataEntityStructureService.selectStructureById(dataEntity.getId(), true);
            if (CollectionUtils.isNotEmpty(structureVoList)) {
                for (DataEntityStructureVo structureVo : structureVoList) {
                    DataEntityStructure structure = new DataEntityStructure();
                    structure.setId(structureVo.getId());
                    structure.setDataEntityId(structureVo.getDataEntityId());
                    structure.setCode(structureVo.getCode());
                    structure.setName(structureVo.getName());
                    structure.setDescription(structureVo.getDescription());
                    structure.setFieldType(structureVo.getFieldType());
                    structure.setLength(structureVo.getLength());
                    structure.setDecimalLength(structureVo.getDecimalLength());
                    structure.setIsPk(structureVo.getIsPk());
                    structure.setEntityInsertType(structureVo.getEntityInsertType());
                    structure.setSeq(structureVo.getSeq());
                    dataEntityStructureList.add(structure);
                }
            }
            dataJsonEntityVo.setDataEntityStructureList(dataEntityStructureList);

            // 查询实体表信息
            if (dataEntity.getTableId() != null) {
                DataEntityTable dataEntityTable = dataEntityTableService.checkDataEntityTable(dataEntity.getTableId());
                if (dataEntityTable != null) {
                    // 获取表映射关系
                    List<DataEntityTableMapping> mappingList = dataEntityTableService.selectEntityTableMapping(dataEntity.getId());
                    dataEntityTable.setDataEntityTableMappingList(mappingList);

                    // 构建表名列表（用于同步模式）
                    List<DataEntityTableNameSynVo> tableNameList = new ArrayList<>();
                    DataEntityTableNameSynVo tableNameSynVo = new DataEntityTableNameSynVo();
                    tableNameSynVo.setTableName(dataEntityTable.getTableName());
                    tableNameSynVo.setEntityCode(dataEntity.getCode());
                    tableNameSynVo.setEntityType(dataEntity.getEntityType());
                    tableNameSynVo.setTableDesc(dataEntity.getDescription());
                    tableNameList.add(tableNameSynVo);
                    dataEntityTable.setTableNameList(tableNameList);

                    dataJsonEntityVo.setDataEntityTable(dataEntityTable);
                }
            }

            result.add(dataJsonEntityVo);
        }

        return result;
    }

    /**
     * 递归收集菜单信息 - 不再使用此方法，改为只收集直接上级菜单
     */
    private void collectMenuInfo(Long menuId, Map<Long, DataEntityMenu> menuInfoMap) {
        if (menuId == null || menuId == -1L || menuInfoMap.containsKey(menuId)) {
            return;
        }

        try {
            // 查询菜单信息
            DataEntityMenu menu = dataEntityMenuMapper.selectById(menuId);
            if (menu != null) {
                // 添加到映射
                menuInfoMap.put(menuId, menu);

                // 递归处理父菜单
                if (menu.getPid() != null && menu.getPid() != -1L) {
                    collectMenuInfo(menu.getPid(), menuInfoMap);
                }
            }
        } catch (Exception e) {
            log.error("查询菜单[{}]信息时出错: {}", menuId, e.getMessage());
        }
    }

}
