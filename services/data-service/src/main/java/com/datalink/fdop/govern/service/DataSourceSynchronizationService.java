package com.datalink.fdop.govern.service;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.drive.api.domain.dto.Field;
import com.datalink.fdop.govern.api.domain.DataSourceSynchronization;
import com.datalink.fdop.govern.api.model.vo.DataSourceInfoShowVo;
import com.datalink.fdop.govern.api.model.vo.DataSourceInfoVo;
import com.datalink.fdop.govern.api.model.vo.MonitorTableOrVIewInfoVo;
import com.datalink.fdop.quality.api.domain.DataQuality;

import java.util.List;

public interface DataSourceSynchronizationService {
    int create(DataSourceSynchronization dataSourceSynchronization);

    int update(DataSourceSynchronization dataSourceSynchronization);

    int del(List<Long> dataSourceIds);

    DataSourceSynchronization getSyncByDataSourceId(Long dataSourceId);

    void sync(Long dataSourceId);

    PageDataInfo<DataSource> overview(SearchVo searchVo);

    R saveSyncData(List<DataSourceInfoVo> dataSourceInfoVos, Long dataSourceId,Long projectCode);

    List<DataSourceInfoVo> getMetadataTree(String tableName,String sort);

    List<Field> getFieldByTableId(Long tableId);

    void bindingFieldId(Long fieldId, Long standardId, Long qualityId);

    PageDataInfo<DataSourceInfoShowVo> metadataOverview(SearchVo searchVo);

    DataQuality getQualityByField(Long fId);

    Boolean checkField(Long fId);

    MonitorTableOrVIewInfoVo selectMonitorTableOrViewInfoList(Long monitorId);

    Field getFieldById(Long fieldId);
}
