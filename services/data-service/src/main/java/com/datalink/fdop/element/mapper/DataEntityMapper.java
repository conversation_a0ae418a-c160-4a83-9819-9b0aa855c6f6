package com.datalink.fdop.element.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.element.api.domain.DataEntity;
import com.datalink.fdop.element.api.domain.DataEntityTable;
import com.datalink.fdop.element.api.model.DataEntityTree;
import com.datalink.fdop.element.model.dto.DataEntityTaskDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface DataEntityMapper extends BaseMapper<DataEntity> {

    int createEntityAndMenuEdge(@Param("pid") Long pid, @Param("ids") List<Long> ids);

    int insertEntity(@Param("dataEntity") DataEntity dataEntity);

    int updateById(DataEntity dataEntity);

    int batchUpdatePidById(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteEntityAndMenuEdge(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteBatchIds(@Param("ids") List<Long> ids);

    DataEntity selectById(Long id);

    List<DataEntity> selectEntityListByTagId(Long tagId);

    DataEntity selectByCode(String code);

    DataEntity checkCodeIsExists(@Param("id") Long id, @Param("code") String code);

    IPage<DataEntity> selectList(IPage<DataEntity> page, @Param("dataEntity") DataEntity dataEntity);

    List<DataEntity> dwdOrDimList(@Param("dataEntity") DataEntity dataEntity);

    int selectTotal();

    List<DataEntityTree> selectNodeTree(@Param("sort") String sort, @Param("code") String code,@Param("dataSourceId") Long dataSourceId);

    List<DataEntityTree> selectEntityTableTree(@Param("sort") String sort, @Param("code") String code, @Param("isRead") Boolean isRead);

    IPage<DataEntity> overview(IPage<DataEntity> page,
                               @Param("pid") Long pid,
                               @Param("sort") String sort,
                               @Param("searchVo") SearchVo searchVo);

    List<DataEntity> list(@Param("dataEntityId") Long dataEntityId);

    List<DataEntityTable> selectEntityTableById(@Param("id") Long id);

    List<Long> selectIdsByPid(Long pid);

    // 查询元素被哪些实体引用
    List<DataEntity> selectEntityAndElementRef(@Param("dataElementId") Long dataElementId);

    List<DataEntityTaskDto> selectEntityAndTaskEdge(@Param("dataEntityId") Long dataEntityId);

    List<DataEntity> selectDataEntityList(@Param("ids") List<Long> ids);

    List<DataEntity> overviewNoPage( @Param("pid") Long pid,
                                     @Param("sort") String sort,
                                     @Param("searchVo") SearchVo searchVo);

}
