package com.datalink.fdop.govern.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.govern.api.domain.ImportDataLog;
import com.datalink.fdop.govern.api.domain.LogicalObject;
import com.datalink.fdop.govern.service.ILogicalObjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/govern/logicalObject")
@Api(tags = "L4")
public class LogicalObjectController {

    @Autowired
    private ILogicalObjectService logicalObjectService;

    @ApiOperation(value = "新增")
    @Log(title = "逻辑对象L4新增", businessType = BusinessType.INSERT)
    @PostMapping
    public R create(@RequestBody LogicalObject entity) {
        return R.ok(logicalObjectService.create(entity));
    }

    @ApiOperation(value = "修改")
    @Log(title = "逻辑对象L4修改", businessType = BusinessType.UPDATE)
    @PutMapping
    public R update(@RequestBody LogicalObject entity) {
        return R.ok(logicalObjectService.update(entity));
    }

    @ApiOperation(value = "删除")
    @Log(title = "逻辑对象L4删除", businessType = BusinessType.DELETE)
    @DeleteMapping
    public R delete(@RequestBody List<Long> ids) {
        return R.toResult(logicalObjectService.delete(ids));
    }

    @ApiOperation(value = "selectById")
    @Log(title = "逻辑对象L4根据id查询", businessType = BusinessType.DELETE)
    @GetMapping("/selectById/{id}")
    public R<LogicalObject> selectById(@PathVariable Long id) {
        return R.ok(logicalObjectService.selectById(id));
    }

    @ApiOperation(value = "查询列表")
    @Log(title = "逻辑对象L4查询列表")
    @PostMapping("/list")
    public R<PageDataInfo> list(@RequestBody(required = false) LogicalObject entity,
                                @RequestParam(required = false, value = "sort", defaultValue = "asc") String sort) {
        return R.ok(logicalObjectService.list(entity, sort));
    }

    @ApiOperation(value = "锁定")
    @Log(title = "逻辑对象L4锁定", businessType = BusinessType.UPDATE)
    @PostMapping("/lock")
    public R lock(@RequestBody List<Long> ids) {
        return R.ok(logicalObjectService.lock(ids));
    }

    @ApiOperation(value = "反锁定")
    @Log(title = "逻辑对象L4反锁定", businessType = BusinessType.UPDATE)
    @PostMapping("/unlock")
    public R unlock(@RequestBody List<Long> ids) {
        return R.ok(logicalObjectService.unlock(ids));
    }

    @ApiOperation(value = "导入")
    @Log(title = "逻辑对象L4-导入")
    @PostMapping("/importData")
    public R importData(MultipartFile file) throws Exception {
        if (file == null || file.isEmpty()) {
            return R.fail("文件上传失败！");
        }
        if (!file.getOriginalFilename().endsWith(".xlsx") && !file.getOriginalFilename().endsWith(".xls")) {
            return R.fail("文件不是Excel文件，请上传.xlsx或.xls后缀文件");
        }
        ExcelUtil<LogicalObject> util = new ExcelUtil<>(LogicalObject.class);
        List<LogicalObject> list = util.importExcel(file.getInputStream());
        String operatorName = SecurityUtils.getUsername();
        logicalObjectService.importData(file.getOriginalFilename(), list, operatorName);
        return R.ok("导入中，请移至日志查看");
    }


    @ApiOperation(value = "导入模板")
    @Log(title = "逻辑对象L4-导入模板")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<LogicalObject> util = new ExcelUtil<>(LogicalObject.class);
        util.importTemplateExcel(response, "logicalObject");
    }

    @ApiOperation("导出数据")
    @Log(title = "逻辑对象L4-导出数据")
    @PostMapping("/export")
    public void export(HttpServletResponse response,
                       @RequestBody(required = false) LogicalObject entity,
                       @RequestParam(required = false, value = "sort", defaultValue = "asc") String sort) {
        List<LogicalObject> list = logicalObjectService.getExportList(entity, sort);
        ExcelUtil<LogicalObject> util = new ExcelUtil<>(LogicalObject.class);
        util.exportExcel(response, list, "逻辑对象L4数据");
    }

    @ApiOperation(value = "导入日志")
    @Log(title = "逻辑对象L4-导入日志")
    @PostMapping(value = "/importLog")
    public R<PageDataInfo<ImportDataLog>> importLog(@RequestBody(required = false) ImportDataLog importDataLog,
                                                    @RequestParam(value = "sort", defaultValue = "DESC", required = false) String sort) {
        return R.ok(logicalObjectService.importLog(importDataLog, sort));
    }

    @ApiOperation(value = "日志详情")
    @Log(title = "逻辑对象L4-日志详情")
    @GetMapping(value = "/importLog/selectById/{id}")
    public R<ImportDataLog> selectByIdLog(@PathVariable Long id) {
        return R.ok(logicalObjectService.selectByIdLog(id));
    }
    
}
