package com.datalink.fdop.govern.service;
import com.datalink.fdop.govern.api.domain.QualityCheck;

public interface QualityCheckService {


    /**
     * 生成检查数据总数的SQL
     * @param qualityCheck 质量检查任务配置信息
     * @return 生成的SQL
     */
    String generateCheckDataTotalSql(QualityCheck qualityCheck);


    /**
     * 获取检查错误数据SQL
     * @param qualityCheck 质量检查任务配置信息
     * @return 生成的SQL
     */
    String generateCheckErrorDataSql(QualityCheck qualityCheck);

    int create(QualityCheck seaTunnelCmd);

    QualityCheck selectById(Long id);

    Integer updateTask(QualityCheck qualityCheck);


}
