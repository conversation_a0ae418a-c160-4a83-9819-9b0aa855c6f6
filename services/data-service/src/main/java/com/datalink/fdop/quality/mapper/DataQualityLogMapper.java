package com.datalink.fdop.quality.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.quality.api.domain.DataQualityLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface DataQualityLogMapper extends BaseMapper<DataQualityLog> {

    int insertQualityLog(@Param("dataQualityLog") DataQualityLog dataQualityLog);

    int creareQualityLogAndEntityEdge(@Param("id") Long id, @Param("entityId") Long entityId);

    int updateById(DataQualityLog dataQualityLog);

    int deleteBatchIds(@Param("ids") List<Long> ids);

    VlabelItem<DataQualityLog> selectById(Long id);

    IPage<DataQualityLog> selectList(IPage<DataQualityLog> page,@Param("dataQualityLog") DataQualityLog dataQualityLog);

    List<DataQualityLog> selectStorageNameByIds(@Param("dataQualityLogIds") List<Long> dataQualityLogIds);

}
