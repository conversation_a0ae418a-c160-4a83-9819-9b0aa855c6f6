package com.datalink.fdop.govern.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.constant.Constants;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.govern.api.domain.BusinessObject;
import com.datalink.fdop.govern.api.domain.ImportDataLog;
import com.datalink.fdop.govern.api.domain.LogicalObject;
import com.datalink.fdop.govern.mapper.BusinessObjectMapper;
import com.datalink.fdop.govern.mapper.LogicalObjectMapper;
import com.datalink.fdop.govern.service.ILogicalObjectService;
import com.datalink.fdop.govern.service.ImportDataLogService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class LogicalObjectServiceImpl implements ILogicalObjectService {

    @Autowired
    private LogicalObjectMapper logicalObjectMapper;

    @Autowired
    private BusinessObjectMapper businessObjectMapper;

    @Autowired
    private ImportDataLogService importDataLogService;

    /**
     * 导入日志表
     */
    private final String LOG_TABLE_NAME = "d_g_logical_object_import_log";

    @Override
    public int create(LogicalObject entity) {
        if (StringUtils.isEmpty(entity.getCode())) {
            throw new ServiceException("编码不能为空");
        }
        if (entity.getPid() == null) {
            throw new ServiceException("请选择上级");
        }
        if (businessObjectMapper.selectById(entity.getPid()) == null) {
            throw new ServiceException("上级不存在");
        }
        if (logicalObjectMapper.selectByCode(entity.getCode()) != null) {
            throw new ServiceException("已存在编码为[" + entity.getCode() + "]的数据");
        }
        entity.setVersion(Constants.VERSION_INITIALIZE);
        return logicalObjectMapper.insert(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(LogicalObject entity) {
        if (entity.getId() == null) {
            throw new ServiceException("id不能为空");
        }
        LogicalObject logicalObject = selectById(entity.getId());
        if (logicalObject.getLockStatus()) {
            throw new ServiceException("锁定状态不能修改");
        }
        if (StringUtils.isEmpty(entity.getCode())) {
            throw new ServiceException("编码不能为空");
        }
        if (logicalObjectMapper.codeIsOnly(entity.getId(), entity.getCode()) > 0) {
            throw new ServiceException("已存在编码为[" + entity.getCode() + "]的数据");
        }
        return logicalObjectMapper.updateById(entity);
    }

    @Override
    public int delete(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException("请指定要删除的内容");
        }
        List<String> codes = logicalObjectMapper.queryExistChildrenNodes(ids);
        if (CollectionUtils.isNotEmpty(codes)) {
            throw new ServiceException("存在下级，不允许删除");
        }
        return logicalObjectMapper.delete(ids);
    }

    @Override
    public LogicalObject selectById(Long id) {
        VlabelItem<LogicalObject> vlabelItem = logicalObjectMapper.selectById(id);
        if (vlabelItem == null) {
            throw new ServiceException("不存在");
        }
        return vlabelItem.getProperties();
    }

    @Override
    public PageDataInfo<LogicalObject> list(LogicalObject entity, String sort) {
        Page<VlabelItem> page = PageUtils.getPage(VlabelItem.class);
        IPage<VlabelItem<LogicalObject>> vlabelItemIPage = logicalObjectMapper.selectList(page, entity, sort);
        return PageUtils.getPageInfo(vlabelItemIPage.getRecords().stream().map(VlabelItem::getProperties).collect(Collectors.toList()), (int) vlabelItemIPage.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int lock(List<Long> ids) {
        List<VlabelItem<LogicalObject>> vlabelItems = logicalObjectMapper.selectByIds(ids);
        List<String> collect = vlabelItems.stream().map(VlabelItem::getProperties).filter(LogicalObject::getLockStatus).map(LogicalObject::getCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            throw new ServiceException("存在以下编码的逻辑对象已锁定：" + JSONObject.toJSONString(collect));
        }
        List<String> codes = logicalObjectMapper.queryExistUnLockChildrenNodes(ids);
        if (CollectionUtils.isNotEmpty(codes)) {
            throw new ServiceException("存在未锁定的下级");
        }
        return logicalObjectMapper.lock(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int unlock(List<Long> ids) {
        List<VlabelItem<LogicalObject>> vlabelItems = logicalObjectMapper.selectByIds(ids);
        List<String> collect = vlabelItems.stream().map(VlabelItem::getProperties).filter(e -> !e.getLockStatus()).map(LogicalObject::getCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            throw new ServiceException("存在以下编码的逻辑对象未锁定：" + JSONObject.toJSONString(collect));
        }
        List<Long> pids = vlabelItems.stream().map(VlabelItem::getProperties).filter(e -> !e.getLockStatus()).map(LogicalObject::getPid).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(pids)) {
            businessObjectMapper.unregister(pids);
        }
        return logicalObjectMapper.unlock(ids);
    }

    @Override
    public List<LogicalObject> selectByPid(Long id) {
        List<VlabelItem<LogicalObject>> vlabelItems = logicalObjectMapper.selectByPid(id);
        return vlabelItems.stream().map(VlabelItem::getProperties).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateVersion(List<Long> ids, String version) {
        List<VlabelItem<LogicalObject>> vlabelItems = logicalObjectMapper.selectByIds(ids);
        if (CollectionUtils.isNotEmpty(vlabelItems)) {
            List<LogicalObject> logicalObjects = vlabelItems.stream().map(VlabelItem::getProperties).collect(Collectors.toList());
            logicalObjectMapper.updateActiveStateByIds(ids);
            for (LogicalObject logicalObject : logicalObjects) {
                logicalObject.setVersion(version);
                logicalObject.setUpdateBy(SecurityUtils.getUsername());
                logicalObject.setUpdateTime(new Date());
                logicalObject.setActiveState(true);
                logicalObjectMapper.insert(logicalObject);
            }
            return logicalObjects.size();
        }
        return 0;
    }

    @Async
    @Override
    public void importData(String fileName, List<LogicalObject> list, String operatorName) {
        ImportDataLog importDataLog = new ImportDataLog();
        importDataLog.setId(IdWorker.getId());
        importDataLog.setStatus("导入中");
        importDataLog.setImportBy(operatorName);
        importDataLog.setImportTime(new Date());
        importDataLog.setFileName(fileName);
        String log = "--INFO 数据正在导入中...\n";
        importDataLog.setLog(log);
        importDataLog.setTableName(LOG_TABLE_NAME);
        importDataLogService.insert(importDataLog);
        try {
            if (CollectionUtils.isEmpty(list)) {
                throw new ServiceException("导入的数据不能为空");
            }
            int successNum = 0;
            int failureNum = 0;
            StringBuilder successMsg = new StringBuilder();
            StringBuilder failureMsg = new StringBuilder();
            for (LogicalObject entity : list) {
                try {
                    if (StringUtils.isEmpty(entity.getPCode())) {
                        throw new ServiceException("业务对象编码必填");
                    }
                    VlabelItem<BusinessObject> businessObjectVlabelItem = businessObjectMapper.selectByCode(entity.getPCode());
                    if (businessObjectVlabelItem == null) {
                        throw new ServiceException("业务对象不存在");
                    }
                    // 设置上级ID
                    entity.setPid(businessObjectVlabelItem.getProperties().getId());
                    if (StringUtils.isEmpty(entity.getCode())) {
                        throw new ServiceException("编码必填");
                    }
                    if (StringUtils.isEmpty(entity.getName())) {
                        throw new ServiceException("名称必填");
                    }
                    boolean updateFlag = false;
                    VlabelItem<LogicalObject> logicalObjectVlabelItem = logicalObjectMapper.selectByCode(entity.getCode());
                    if (logicalObjectVlabelItem != null) {
                        updateFlag = true;
                        ExcelUtil<LogicalObject> excelUtil = new ExcelUtil<>(LogicalObject.class);
                        LogicalObject dbEntity = logicalObjectVlabelItem.getProperties();
                        entity = excelUtil.copyImportDataToDbData(entity, dbEntity);
                    } else {
                        entity.setId(IdWorker.getId());
                    }
                    if (updateFlag) {
                        int update = logicalObjectMapper.updateById(entity);
                        if (update > 0) {
                            successNum++;
                            successMsg.append("\n" + successNum + "、逻辑对象 " + entity.getCode() + " 导入更新成功");
                        }
                    } else {
                        // 版本号
                        entity.setVersion(Constants.VERSION_INITIALIZE);
                        int insert = logicalObjectMapper.insert(entity);
                        if (insert > 0) {
                            successNum++;
                            successMsg.append("\n" + successNum + "、逻辑对象 " + entity.getCode() + " 导入成功");
                        }
                    }

                } catch (Exception e) {
                    failureNum++;
                    String msg = "\n" + failureNum + "、逻辑对象 " + entity.getCode() + " 导入失败：";
                    failureMsg.append(msg + e.getMessage());
                }
            }
            if (failureNum > 0) {
                failureMsg.insert(0, "很抱歉，导入失败！成功导入 " + successNum + " 条数据， 数据如下：" + successMsg + "， \n共 " + failureNum + " 条数据格式不正确，错误如下：");
                throw new ServiceException(failureMsg.toString());
            } else {
                successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
                importDataLog.setLog(log + "\n" + successMsg.toString());
                importDataLog.setEndTime(new Date());
                importDataLog.setStatus("成功");
                importDataLogService.update(importDataLog);
            }
        } catch (Exception e) {
            String message = e.getMessage();
            importDataLog.setLog(log + "\n" + message);
            importDataLog.setEndTime(new Date());
            importDataLog.setStatus("失败");
            importDataLogService.update(importDataLog);
        }
    }

    @Override
    public List<LogicalObject> getExportList(LogicalObject entity, String sort) {
        List<VlabelItem<LogicalObject>> exportVLabelItem = logicalObjectMapper.getExportList(entity, sort);
        return exportVLabelItem.stream().map(vlabelItem -> vlabelItem.getProperties()).collect(Collectors.toList());
    }

    @Override
    public PageDataInfo<ImportDataLog> importLog(ImportDataLog importDataLog, String sort) {
        if (importDataLog == null) {
            importDataLog = new ImportDataLog();
        }
        importDataLog.setTableName(LOG_TABLE_NAME);
        return importDataLogService.list(importDataLog, sort);
    }

    @Override
    public ImportDataLog selectByIdLog(Long id) {
        return importDataLogService.selectById(LOG_TABLE_NAME, id);
    }
}
