package com.datalink.fdop.element.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.FieldType;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.excel.ExcelDto;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.age.SqlUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.split.SplitUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.datasource.annotation.SwitchDataSource;
import com.datalink.fdop.drive.api.RemoteDriveService;
import com.datalink.fdop.drive.api.RemoteJdbcService;
import com.datalink.fdop.drive.api.RemoteTaskService;
import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.drive.api.domain.dto.Field;
import com.datalink.fdop.element.api.domain.*;
import com.datalink.fdop.element.api.enums.BuiltinField;
import com.datalink.fdop.element.api.enums.DataElementType;
import com.datalink.fdop.element.api.enums.EntityInsertType;
import com.datalink.fdop.element.api.enums.EntityType;
import com.datalink.fdop.element.api.model.dto.DataEntityStructureDto;
import com.datalink.fdop.element.api.model.dto.TaskEtlDto;
import com.datalink.fdop.element.api.model.vo.DataElementStructureVo;
import com.datalink.fdop.element.api.model.vo.DataEntityStructureVo;
import com.datalink.fdop.element.mapper.DataEntityMapper;
import com.datalink.fdop.element.mapper.DataEntityStructureMapper;
import com.datalink.fdop.element.mapper.DataEntityTableMapper;
import com.datalink.fdop.element.service.IDataEntityStructureService;
import com.datalink.fdop.element.service.IDataEntityTableService;
import com.datalink.fdop.element.utils.GraphTableUtils;
import com.datalink.fdop.govern.api.RemoteObjectPropertiesService;
import com.datalink.fdop.govern.api.domain.ObjectProperties;
import com.datalink.fdop.graph.api.RemoteEtlService;
import com.datalink.fdop.graph.api.domain.etl.EtlNode;
import com.datalink.fdop.graph.api.domain.etl.SourceNode;
import com.datalink.fdop.graph.api.enums.OperType;
import com.datalink.fdop.graph.api.enums.WarnType;
import com.datalink.fdop.graph.api.graph.etl.EtlField;
import com.datalink.fdop.graph.api.graph.etl.enums.EtlNodeType;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @date 2022/4/12 13:58
 */
@Service
public class DataEntityStructureServiceImpl implements IDataEntityStructureService {

    @Autowired
    private DataEntityMapper dataEntityMapper;

    @Autowired
    private DataEntityTableMapper dataEntityTableMapper;

    @Autowired
    private DataEntityStructureMapper dataEntityStructureMapper;

    @Autowired
    private IDataEntityTableService iDataEntityTableService;

    @Autowired
    private RemoteTaskService remoteTaskService;

    @Autowired
    private RemoteEtlService remoteEtlService;

    @Autowired
    private RemoteJdbcService remoteJdbcService;

    @Autowired
    private RemoteDriveService remoteDriveService;

    @Autowired
    private RemoteObjectPropertiesService remoteObjectPropertiesService;

    @Autowired
    private IDataEntityTableService dataEntityTableService;

    private DataEntity checkDataEntity(Long dataEntityId) {
        DataEntity dataEntity = dataEntityMapper.selectById(dataEntityId);
        if (dataEntity == null) {
            throw new ServiceException(Status.DATA_ENTITY_NOT_EXIST);
        }
        return dataEntity;
    }

    /**
     * 获取实体的主键/非主键的最大的排序序号
     *
     * @param dataEntityId
     * @param isPk
     * @return
     */
    private Integer getMaxSeq(Long dataEntityId, Boolean isPk) {
        List<Integer> seqs = dataEntityStructureMapper.selectEntityAllMaxSeq(dataEntityId, isPk);
        return seqs.stream().max(Integer::compare).get() + 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int create(DataEntityStructure dataEntityStructure) {
        return this.create(dataEntityStructure, true);
    }

    @Override
    public int batchCreate(Long dataEntityId, List<DataEntityStructure> dataEntityStructureList) {
        // 每50个提交一次
        List<List<DataEntityStructure>> dataEntityStructureSplitList = SplitUtils.splitList(dataEntityStructureList, 50);
        for (List<DataEntityStructure> dataEntityStructureSplit : dataEntityStructureSplitList) {
            // 自定义添加字段
            dataEntityStructureMapper.batchInsertStructure(dataEntityStructureSplit);
            // 创建关系
            dataEntityStructureMapper.batchCreateStructureEdge(dataEntityId, dataEntityStructureSplit);
        }
        return 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int create(DataEntityStructure dataEntityStructure, Boolean isCheckLength) {
        Long dataEntityId = dataEntityStructure.getDataEntityId();
        // 验证数据实体
        checkDataEntity(dataEntityId);

        dataEntityStructure.setId(IdWorker.getId());

        // 验证长度和类型
        if (isCheckLength && dataEntityStructure.getFieldType() != null) {
            GraphTableUtils.initLength(dataEntityStructure);
        }

        if (dataEntityStructureMapper.selectByCode(dataEntityId, dataEntityStructure.getCode()) != null) {
            throw new ServiceException(Status.DATA_ENTITY_STRUCTURE_EXIST);
        }

        int insert = 0;
        if (dataEntityStructure.getEntityInsertType() == EntityInsertType.CUSTOMIZE || dataEntityStructure.getEntityInsertType() == EntityInsertType.BUILTIN) {
            // 获取最大序号
            if (dataEntityStructure.getSeq() == null) {
                dataEntityStructure.setSeq(this.getMaxSeq(dataEntityId, dataEntityStructure.getIsPk()));
            }
            // 自定义/内置列添加
            insert += dataEntityStructureMapper.insertStructure(dataEntityStructure);
        }

        return insert;
    }

    @Override
    public Map<String, Object> checkEntityStructure(Long dataEntityId, List<DataEntityStructureVo> dataEntityStructureVoList) {
        Map<String, Object> result = new HashMap<>();

        // 验证数据实体
        DataEntity dataEntity = checkDataEntity(dataEntityId);

        // 查询实体字段被哪些任务引用
        Map<String, Object> map = new HashMap<>();

        for (DataEntityStructureVo dataEntityStructureVo : dataEntityStructureVoList) {
            // 实体字段id
            Long id = dataEntityStructureVo.getId();
            // 获取实体被引用的任务
            List<String> taskNameList = dataEntityStructureMapper.selectTaskAndFieldEdge(dataEntityId, id)
                    .stream().map(TaskEtlDto::getTaskName).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(taskNameList)) {
                map.put("TASK", taskNameList);
                if (dataEntityStructureVo.getEntityInsertType() == EntityInsertType.PREDEFINED) {
                    DataEntityStructureVo dataElementStructure = dataEntityStructureMapper.selectElementStructureByElementFieldId(id, dataEntityId);
                    result.put(dataElementStructure.getCode(), map);
                } else {
                    DataEntityStructure dataEntityStructure = dataEntityStructureMapper.selectById(id);
                    result.put(dataEntityStructure.getCode(), map);
                }
            }
        }

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(DataEntityStructure dataEntityStructure) {
        Long dataEntityId = dataEntityStructure.getDataEntityId();
        // 验证数据实体
        checkDataEntity(dataEntityId);


//        修改内置列主键属性,此处该注销掉
//        if (dataEntityStructure.getEntityInsertType() == EntityInsertType.BUILTIN) {
//            throw new ServiceException(Status.BUILT_IN_COLUMNS_CANNOT_BE_MODIFIED);
//        }

        // 验证长度和类型
        if (dataEntityStructure.getFieldType() != null) {
            GraphTableUtils.initLength(dataEntityStructure);
        }

        if (StringUtils.isNotEmpty(dataEntityStructure.getCode()) &&
                dataEntityStructureMapper.checkCodeIsExists(dataEntityStructure.getId(), dataEntityStructure.getCode(), dataEntityStructure.getDataEntityId()) != null) {
            throw new ServiceException(Status.DATA_ENTITY_STRUCTURE_EXIST);
        }

        int update = 0;
        // 内置列允许定义为主键，只是不允许修改列名字段长度等信息这些前端会做校验
        if (dataEntityStructure.getEntityInsertType() == EntityInsertType.CUSTOMIZE || dataEntityStructure.getEntityInsertType() == EntityInsertType.BUILTIN) {
            // 自定义添加
            update += dataEntityStructureMapper.updateStructure(dataEntityStructure);
        }

        if (update > 0) {
            // 判断是否修改了类型、长度、精度
            DataEntityStructure updatePreDataEntityStructure = dataEntityStructureMapper.selectById(dataEntityStructure.getId());
            if (updatePreDataEntityStructure.getFieldType() != dataEntityStructure.getFieldType()
                    || updatePreDataEntityStructure.getLength() != dataEntityStructure.getLength()
                    || updatePreDataEntityStructure.getDecimalLength() != dataEntityStructure.getDecimalLength()) {
                // 修改了长度需要修改被引用任务的状态
                // 获取实体被引用的任务
                List<TaskEtlDto> taskEtlDtoList = dataEntityStructureMapper.selectTaskAndFieldEdge(dataEntityId, dataEntityStructure.getId());
                if (CollectionUtils.isNotEmpty(taskEtlDtoList)) {
                    for (TaskEtlDto taskEtlDto : taskEtlDtoList) {
                        // 获取节点信息
                        R<EtlNode> etlNodeR = remoteEtlService.selectEtlNode(taskEtlDto.getTaskCode(), taskEtlDto.getNodeId());
                        if (etlNodeR.getCode() != Status.SUCCESS.getCode()) {
                            throw new ServiceException(etlNodeR.getMsg());
                        }
                        EtlNode etlNode = etlNodeR.getData();
                        if (etlNode.getNodeType() == EtlNodeType.SOURCE) {
                            // 修改节点字段
                            List<EtlField> fieldList = etlNode.getFieldList();
                            etlNode.setFieldList(fieldList.stream()
                                    .filter(field -> {
                                        if (field.getCiteFieldId().equals(dataEntityStructure.getId().toString())) {
                                            return true;
                                        }
                                        return false;
                                    })
                                    .map(field -> {
                                        field.setFieldName(dataEntityStructure.getCode());
                                        field.setFieldDesc(dataEntityStructure.getName());
                                        field.setFieldType(dataEntityStructure.getFieldType());
                                        field.setLength(dataEntityStructure.getLength());
                                        field.setDecimalLength(dataEntityStructure.getDecimalLength());
                                        field.setIsPk(dataEntityStructure.getIsPk());
                                        field.setOperType(OperType.REVISE);
                                        field.setWarnType(WarnType.WARN);
                                        field.setIsSyn(false);
                                        return field;
                                    }).collect(Collectors.toList()));

                            R<SourceNode> sourceNodeR = remoteEtlService.updateSourceNode(taskEtlDto.getTaskCode(), JSONObject.parseObject(JSONObject.toJSONString(etlNode), SourceNode.class));
                            if (sourceNodeR.getCode() != Status.SUCCESS.getCode()) {
                                throw new ServiceException(sourceNodeR.getMsg());
                            }
                        }

                        // 0:失败,1:激活,2:警告,3:未激活
                        int etlStatus = taskEtlDto.getEtlStatus();
                        if (etlStatus == 1 || etlStatus == 3) {
                            // 修改任务json
                            R updateTaskDefinitionR = remoteTaskService.updateTaskDefinitionEtlStatus(0l, taskEtlDto.getTaskCode(), 2);
                            if (updateTaskDefinitionR.getCode() != Status.SUCCESS.getCode()) {
                                throw new ServiceException(updateTaskDefinitionR.getMsg());
                            }
                        }
                    }
                }
            }
        }

        return update;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int delete(Long dataEntityId, List<DataEntityStructureVo> dataEntityStructureVoList) {
        // 验证数据实体
        checkDataEntity(dataEntityId);

        // 获取实体被引用的任务
        List<TaskEtlDto> taskEtlDtoList = dataEntityStructureMapper.selectTaskAndFieldEdgeList(dataEntityId,
                SqlUtils.getInSql(dataEntityStructureVoList
                        .stream().map(DataEntityStructureVo::getId).collect(Collectors.toList())));
        if (CollectionUtils.isNotEmpty(taskEtlDtoList)) {
            Map<String, List<TaskEtlDto>> taskEtlNodeMap = taskEtlDtoList.stream()
                    .collect(Collectors.groupingBy(taskEtlDto -> taskEtlDto.getTaskCode() + "_" + taskEtlDto.getNodeId()));
            taskEtlNodeMap.forEach((key, value) -> {
                TaskEtlDto taskEtlDto = value.get(0);
                // 获取节点信息
                R<EtlNode> etlNodeR = remoteEtlService.selectEtlNode(taskEtlDto.getTaskCode(), taskEtlDto.getNodeId());
                if (etlNodeR.getCode() != Status.SUCCESS.getCode()) {
                    throw new ServiceException(etlNodeR.getMsg());
                }
                EtlNode etlNode = etlNodeR.getData();
                if (etlNode.getNodeType() == EtlNodeType.SOURCE) {
                    // 修改节点字段
                    List<EtlField> fieldList = etlNode.getFieldList();
                    etlNode.setFieldList(fieldList.stream()
                            .filter(field -> {
                                if (dataEntityStructureVoList.stream()
                                        .anyMatch(dataEntityStructureVo -> field.getCiteFieldId().equals(dataEntityStructureVo.getId().toString()))) {
                                    return true;
                                }
                                return false;
                            })
                            .map(field -> {
                                for (DataEntityStructureVo dataEntityStructureVo : dataEntityStructureVoList) {
                                    if (field.getCiteFieldId().equals(dataEntityStructureVo.getId().toString())) {
                                        field.setOperType(OperType.DELETE);
                                        field.setWarnType(WarnType.ERROR);
                                        field.setIsSyn(false);
                                    }
                                }
                                return field;
                            }).collect(Collectors.toList()));
                    R<SourceNode> sourceNodeR = remoteEtlService.updateSourceNode(taskEtlDto.getTaskCode(), JSONObject.parseObject(JSONObject.toJSONString(etlNode), SourceNode.class));
                    if (sourceNodeR.getCode() != Status.SUCCESS.getCode()) {
                        throw new ServiceException(sourceNodeR.getMsg());
                    }
                }

                // 0:失败,1:激活,2:警告,3:未激活
                int etlStatus = taskEtlDto.getEtlStatus();
                if (etlStatus != 0) {
                    // 修改任务json
                    R updateTaskDefinitionR = remoteTaskService.updateTaskDefinitionEtlStatus(0l, taskEtlDto.getTaskCode(), 0);
                    if (updateTaskDefinitionR.getCode() != Status.SUCCESS.getCode()) {
                        throw new ServiceException(updateTaskDefinitionR.getMsg());
                    }
                }
            });

        }

        int delete = 0;
        // 所有新增类型是自定义或内置列的字段id
        List<Long> customizeStructureIds = dataEntityStructureVoList.stream()
                .filter(dataEntityStructureVo -> dataEntityStructureVo.getEntityInsertType() == EntityInsertType.CUSTOMIZE || dataEntityStructureVo.getEntityInsertType() == EntityInsertType.BUILTIN)
                .map(DataEntityStructureVo::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(customizeStructureIds)) {
            // 先删除mapping字段
            dataEntityStructureMapper.deleteCustomizeMapping(dataEntityId, customizeStructureIds);
            delete += dataEntityStructureMapper.deleteCustomize(dataEntityId, customizeStructureIds);
        }

        // 所有新增类型是预定义的字段id
        List<DataEntityStructureVo> predefinedStructure = dataEntityStructureVoList.stream()
                .filter(dataEntityStructureVo -> dataEntityStructureVo.getEntityInsertType() == EntityInsertType.PREDEFINED)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(predefinedStructure)) {
            // 主数据和字段类型id集合
            List<Long> mainAndFeildIds = predefinedStructure.stream()
                    .filter(dataEntityStructureVo -> dataEntityStructureVo.getDataElementType() == DataElementType.MAIN || dataEntityStructureVo.getDataElementType() == DataElementType.FIELD)
                    .map(DataEntityStructureVo::getId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(mainAndFeildIds)) {
                // 先删除mapping字段
                dataEntityStructureMapper.deletePredefinedMainAndFieldMapping(dataEntityId, mainAndFeildIds);
                delete += dataEntityStructureMapper.deletePredefinedMainAndField(dataEntityId, mainAndFeildIds);
            }

            // 录入类型id集合
            List<Long> inputIds = predefinedStructure.stream()
                    .filter(dataEntityStructureVo -> dataEntityStructureVo.getDataElementType() == DataElementType.INPUT)
                    .map(DataEntityStructureVo::getId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(inputIds)) {
                // 先删除mapping字段
                dataEntityStructureMapper.deletePredefinedInputMapping(dataEntityId, inputIds);
                delete += dataEntityStructureMapper.deletePredefinedInput(dataEntityId, inputIds);
            }

        }

        return delete;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteL5Id(List<Long> ids) {
        return dataEntityStructureMapper.deleteL5Id(ids);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int addPredefined(Long dataEntityId, List<DataElementStructureVo> dataElementStructureVoList) {
        return this.addPredefined(dataEntityId, dataElementStructureVoList, true);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int addPredefined(Long dataEntityId, List<DataElementStructureVo> dataElementStructureVoList, Boolean coverSeq) {
        // 验证数据实体
        checkDataEntity(dataEntityId);

        // 校验是否重复
        int distinct = dataElementStructureVoList.stream().map(DataElementStructureVo::getId).distinct().collect(Collectors.toList()).size();
        if (distinct != dataElementStructureVoList.size()) {
            throw new ServiceException(Status.THE_SAME_FIELD_EXISTS_IN_THE_ENTITY);
        }

        // 校验是否重复
        int distinct1 = dataElementStructureVoList.stream().map(DataElementStructureVo::getCode).distinct().collect(Collectors.toList()).size();
        if (distinct1 != dataElementStructureVoList.size()) {
            throw new ServiceException(Status.THE_SAME_FIELD_EXISTS_IN_THE_ENTITY);
        }

        // 校验是否重复
        List<DataEntityStructureVo> dataEntityStructureVos = selectStructureById(dataEntityId, true);
        for (DataElementStructureVo dataElementStructureVo : dataElementStructureVoList) {
            for (DataEntityStructureVo dataEntityStructureVo : dataEntityStructureVos) {
                if (dataElementStructureVo.getCode().equalsIgnoreCase(dataEntityStructureVo.getCode())) {
                    throw new ServiceException(Status.THE_SAME_FIELD_EXISTS_IN_THE_ENTITY);
                }
            }
        }

        // 获取最大主键的seq
        Integer pkMaxSeq = getMaxSeq(dataEntityId, true);
        // 获取最大非主键的seq
        Integer npkMaxSeq = getMaxSeq(dataEntityId, false);

        int insert = 0;
        for (DataElementStructureVo dataElementStructureVo : dataElementStructureVoList) {
            DataElementType dataElementType = dataElementStructureVo.getDataElementType();
            // 主数据类型
            switch (dataElementType) {
                // 建立 (d_e_data_entity)-[d_e_data_entity_element_edge]-(d_e_data_element)的双边关系
                case MAIN:
                case FIELD:
                    // 检验元素中是否存在这个关系
                    if (!dataEntityStructureMapper.checkEntityElementEdge(dataEntityId, dataElementStructureVo.getId())) {
                        throw new ServiceException(Status.THE_SAME_FIELD_EXISTS_IN_THE_ENTITY);
                    }
                    // 是否覆盖序号
                    if (coverSeq) {
                        dataElementStructureVo.setSeq(dataElementStructureVo.getIsPk() ? pkMaxSeq++ : npkMaxSeq++);
                    }
                    insert += dataEntityStructureMapper.insertEntityElementEdge(dataEntityId, dataElementStructureVo);
                    break;
                // 建立 (d_e_data_entity)-[d_e_data_entity_input_edge]-(d_e_data_element_input)的双边关系
                case INPUT:
                    // 检验录入表中是否存在这个关系
                    if (!dataEntityStructureMapper.checkEntityInputEdge(dataEntityId, dataElementStructureVo.getId())) {
                        throw new ServiceException(Status.THE_SAME_FIELD_EXISTS_IN_THE_ENTITY);
                    }
                    // 是否覆盖序号
                    if (coverSeq) {
                        dataElementStructureVo.setSeq(dataElementStructureVo.getIsPk() ? pkMaxSeq++ : npkMaxSeq++);
                    }
                    insert += dataEntityStructureMapper.insertEntityInputEdge(dataEntityId, dataElementStructureVo);
                    break;
                default:
                    break;
            }
        }

        return insert;
    }

    @Override
    public List<DataEntityStructureVo> selectStructureById(Long dataEntityId, Boolean isSort) {
        // 验证数据实体
        checkDataEntity(dataEntityId);

        List<DataEntityStructureVo> allStructureList = new ArrayList<>();

        // 自定义和预定义中实体的所有字段的主键列
        List<DataEntityStructureVo> pkStructureList = dataEntityStructureMapper.selectEntityPkStructure(dataEntityId);
        // 自定义和预定义中实体的所有字段的非主键列
        List<DataEntityStructureVo> noPkStructureList = dataEntityStructureMapper.selectEntityNoPkStructure(dataEntityId);

        allStructureList.addAll(pkStructureList);
        allStructureList.addAll(noPkStructureList);
        // 重新赋值seq
        IntStream.range(0, allStructureList.size()).forEach(i -> {
            allStructureList.get(i).setSeq(i + 1);
        });
        // 排序处理
        if (isSort) {
            allStructureList.sort(Comparator.comparing(DataEntityStructureVo::getSeq));
        } else {
            allStructureList.sort(Comparator.comparing(DataEntityStructureVo::getSeq).reversed());
        }
        return allStructureList;
    }

    @Override
    @SwitchDataSource(value = "current")
    public List<DataEntityStructureVo> selectStructureByIdAndTenantId(Long tenantId, Long dataEntityId, Boolean isSort) {
        // 验证数据实体
        checkDataEntity(dataEntityId);

        List<DataEntityStructureVo> allStructureList = new ArrayList<>();

        // 自定义和预定义中实体的所有字段的主键列
        List<DataEntityStructureVo> pkStructureList = dataEntityStructureMapper.selectEntityPkStructure(dataEntityId);
        // 自定义和预定义中实体的所有字段的非主键列
        List<DataEntityStructureVo> noPkStructureList = dataEntityStructureMapper.selectEntityNoPkStructure(dataEntityId);

        // 主键列在最上面
        allStructureList.addAll(pkStructureList);
        allStructureList.addAll(noPkStructureList);

        // 重新赋值seq
        IntStream.range(0, allStructureList.size()).forEach(i -> {
            allStructureList.get(i).setSeq(i + 1);
        });

        // 排序处理
        if (isSort) {
            allStructureList.sort(Comparator.comparing(DataEntityStructureVo::getSeq));
        } else {
            allStructureList.sort(Comparator.comparing(DataEntityStructureVo::getSeq).reversed());
        }
        return allStructureList;
    }

    @Override
    public PageDataInfo<DataEntityStructureVo> selectStructureByIdPaging(Long dataEntityId, String sort, DataEntityStructureDto dataEntityStructureDto) {
        // 获取分页参数
        Page<DataEntityStructureVo> page = PageUtils.getPage(DataEntityStructureVo.class);
        IPage<DataEntityStructureVo> dataEntityStructures = dataEntityStructureMapper.selectStructureByIdPaging(page, dataEntityId, sort, dataEntityStructureDto);
        return PageUtils.getPageInfo(dataEntityStructures.getRecords(), (int) dataEntityStructures.getTotal());
    }

    @Override
    public int updateEntityElementEdge(List<DataElementStructure> dataElementStructureList) {
        int update = 0;
        // 根据数据元素字段的id查询字段是否被其他数据实体引用
        List<Long> dataElementStructureIds = dataElementStructureList.stream().map(DataElementStructure::getId).collect(Collectors.toList());
        List<DataEntityStructureVo> dataEntityStructureList = dataEntityStructureMapper.selectEntityStructureByElementFieldId(dataElementStructureIds);
        if (CollectionUtils.isEmpty(dataEntityStructureList)) {
            return 1;
        }
        // 删除实体和元素的字段关系，并添加成实体的自定义的字段
        for (DataEntityStructureVo dataEntityStructureVo : dataEntityStructureList) {
            DataElementType dataElementType = dataEntityStructureVo.getDataElementType();
            switch (dataElementType) {
                case MAIN:
                case FIELD:
                    dataEntityStructureMapper.deletePredefinedMainAndField(dataEntityStructureVo.getDataEntityId(), Arrays.asList(dataEntityStructureVo.getId()));
                    break;
                case INPUT:
                    dataEntityStructureMapper.deletePredefinedInput(dataEntityStructureVo.getDataEntityId(), Arrays.asList(dataEntityStructureVo.getId()));
                    break;
            }
            DataEntityStructure dataEntityStructure = new DataEntityStructure(
                    dataEntityStructureVo.getId(),
                    dataEntityStructureVo.getDataEntityId(),
                    EntityInsertType.CUSTOMIZE,
                    dataEntityStructureVo.getCode(),
                    dataEntityStructureVo.getName(),
                    dataEntityStructureVo.getDescription(),
                    dataEntityStructureVo.getFieldType(),
                    dataEntityStructureVo.getLength(),
                    dataEntityStructureVo.getDecimalLength(),
                    dataEntityStructureVo.getIsPk(),
                    getMaxSeq(dataEntityStructureVo.getDataEntityId(), dataEntityStructureVo.getIsPk())
            );
            update += dataEntityStructureMapper.insertStructure(dataEntityStructure);
        }

        return update;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int importEntity(MultipartFile file, Long dataEntityId) {
        checkDataEntity(dataEntityId);

        int insert = 0;
        try {
            List<Map<String, Object>> dataList = ExcelUtils.importFromExcel(file);
            if (CollectionUtils.isEmpty(dataList)) {
                return 1;
            }
            List<DataEntityStructure> dataEntityStructureList = dataList.stream().map(dataMap -> {
                Set<String> keys = dataMap.keySet();
                // 判断是否存在该列
                if (!keys.contains("编码") &&
                        !keys.contains("字段类型") &&
                        !keys.contains("长度") &&
                        !keys.contains("精度") &&
                        !keys.contains("主键")) {
                    throw new ServiceException("缺少必要{编码/字段类型/长度/精度/主键}信息");
                }

                DataEntityStructure dataEntityStructure = new DataEntityStructure();
                for (String key : dataMap.keySet()) {
                    switch (key) {
                        case "编码":
                            if (StringUtils.isEmpty(MapUtils.getString(dataMap, key))) {
                                throw new ServiceException(Status.THE_DATA_ENTITY_FIELD_ENCODING_CANNOT_BE_EMPTY);
                            }
                            dataEntityStructure.setCode(MapUtils.getString(dataMap, key));
                            break;
                        case "名称":
                            dataEntityStructure.setName(MapUtils.getString(dataMap, key, null));
                            break;
                        case "描述":
                            dataEntityStructure.setDescription(MapUtils.getString(dataMap, key, null));
                            break;
                        case "字段类型":
                            if (StringUtils.isEmpty(MapUtils.getString(dataMap, key))) {
                                throw new ServiceException(Status.THE_DATA_ENTITY_FIELD_TYPE_CANNOT_BE_EMPTY);
                            }
                            dataEntityStructure.setFieldType(FieldType.valueOf(MapUtils.getString(dataMap, key)));
                            break;
                        case "长度":
                            if (MapUtils.getLong(dataMap, key) == null) {
                                throw new ServiceException(Status.THE_DATA_ENTITY_FIELD_LENGTH_CANNOT_BE_EMPTY);
                            }
                            dataEntityStructure.setLength(MapUtils.getLong(dataMap, key));
                            break;
                        case "精度":
                            if (MapUtils.getLong(dataMap, key) == null) {
                                throw new ServiceException(Status.THE_DATA_ENTITY_FIELD_DECIMAL_LENGTH_CANNOT_BE_EMPTY);
                            }
                            dataEntityStructure.setDecimalLength(MapUtils.getLong(dataMap, key));
                            break;
                        case "主键":
                            if (MapUtils.getBoolean(dataMap, key) == null) {
                                throw new ServiceException(Status.THE_DATA_ENTITY_FIELD_PRIMARY_KEY_CANNOT_BE_EMPTY);
                            }
                            dataEntityStructure.setIsPk(MapUtils.getBoolean(dataMap, key));
                            break;
                    }
                }
                return dataEntityStructure;
            }).collect(Collectors.toList());

            // 获取排序序号
            Integer maxSeq = getMaxSeq(dataEntityId, false);
            for (DataEntityStructure dataEntityStructure : dataEntityStructureList) {
                dataEntityStructure.setDataEntityId(dataEntityId);
                if (dataEntityStructure.getCode().equalsIgnoreCase(BuiltinField._BATCH_ID.getDesc()) || dataEntityStructure.getCode().equalsIgnoreCase(BuiltinField._IMPORT_TIME.getDesc())) {
                    dataEntityStructure.setEntityInsertType(EntityInsertType.BUILTIN);
                } else {
                    dataEntityStructure.setEntityInsertType(EntityInsertType.CUSTOMIZE);
                }
                dataEntityStructure.setSeq(maxSeq++);
                insert += create(dataEntityStructure);
            }

        } catch (Exception e) {
            throw new ServiceException("导入失败:" + e.getMessage());
        }
        return insert;
    }

    @Override
    public void exportEntity(HttpServletResponse response, List<Long> dataEntityIds, Boolean isExportData) {
        // 设置表头信息
        List<ExcelDto> headList = new ArrayList<>();
        // 获取数据信息
        List<Map<String, Object>> dataList = new ArrayList<>();
        // 导出实体数据  false 为导出模板列
        if (isExportData) {
            ExcelDto excelDto1 = new ExcelDto();
            excelDto1.setDisplayCode("entityCode");
            excelDto1.setDisplayName("实体编码");
            headList.add(excelDto1);
            ExcelDto excelDto2 = new ExcelDto();
            excelDto2.setDisplayCode("dataSource");
            excelDto2.setDisplayName("数据源");
            headList.add(excelDto2);
            ExcelDto excelDto3 = new ExcelDto();
            excelDto3.setDisplayCode("schema");
            excelDto3.setDisplayName("库");
            headList.add(excelDto3);
            ExcelDto excelDto4 = new ExcelDto();
            excelDto4.setDisplayCode("tableName");
            excelDto4.setDisplayName("表名");
            headList.add(excelDto4);
            ExcelDto excelDto5 = new ExcelDto();
            excelDto5.setDisplayCode("code");
            excelDto5.setDisplayName("编码");
            headList.add(excelDto5);
            ExcelDto excelDto6 = new ExcelDto();
            excelDto6.setDisplayCode("name");
            excelDto6.setDisplayName("名称");
            headList.add(excelDto6);
            ExcelDto excelDto7 = new ExcelDto();
           /* excelDto7.setDisplayCode("description");
            excelDto7.setDisplayName("描述");
            headList.add(excelDto7);*/
            ExcelDto excelDto8 = new ExcelDto();
            excelDto8.setDisplayCode("fieldType");
            excelDto8.setDisplayName("字段类型");
            headList.add(excelDto8);
            ExcelDto excelDto9 = new ExcelDto();
            excelDto9.setDisplayCode("length");
            excelDto9.setDisplayName("长度");
            headList.add(excelDto9);
            ExcelDto excelDto10 = new ExcelDto();
            excelDto10.setDisplayCode("decimalLength");
            excelDto10.setDisplayName("精度");
            headList.add(excelDto10);
            ExcelDto excelDto11 = new ExcelDto();
            excelDto11.setDisplayCode("isPk");
            excelDto11.setDisplayName("主键");
            headList.add(excelDto11);
            for (Long dataEntityId : dataEntityIds) {
                // 获取实体对象
                DataEntity dataEntity = dataEntityMapper.selectById(dataEntityId);
                boolean falg = false;
                if (dataEntity.getTableId() != null) {
                    DataEntityTable dataEntityTable = dataEntityTableService.checkDataEntityTable(dataEntity.getTableId());
                    if (dataEntityTable.getDataSourceId() != null) {
                        R<DataSource> dataSourceR = remoteDriveService.queryDataSource(dataEntityTable.getDataSourceId());
                        DataSource data = new DataSource();
                        if (dataSourceR.getCode() == 200) {
                            data = dataSourceR.getData();
                        }
                        if (dataEntityTable != null) {

                            R<List<Field>> fieldsR = remoteJdbcService.getFields(dataEntityTable.getDataSourceId(), dataEntityTable.getDatabaseName(), dataEntityTable.getTableName());
                            if (fieldsR.getCode() == 200) {
                                List<Field> fieldList = fieldsR.getData();
                                for (Field field : fieldList) {
                                    Map<String, Object> dataMap = Maps.newHashMap();
                                    dataMap.put("entityCode", dataEntity.getCode());
                                    dataMap.put("schema", dataEntityTable.getDatabaseName());
                                    dataMap.put("tableName", dataEntityTable.getTableName());
                                    dataMap.put("dataSource", data.getCode());
                                    dataMap.put("code", field.getFieldName());
                                    dataMap.put("name", field.getFieldDesc());
                                    dataMap.put("fieldType", field.getFieldType());
                                    dataMap.put("length", field.getLength());
                                    dataMap.put("decimalLength", field.getDecimalLength());
                                    dataMap.put("isPk", field.getIsPk());
                                    dataList.add(dataMap);
                                    falg = true;
                                }

                            }
                        }

                    }
                    // dataMap.putAll(JSONObject.parseObject(JSONObject.toJSONString(dataEntityStructureVo), Map.class));
                }
                if (!falg) {
                    Map<String, Object> dataMap = Maps.newHashMap();
                    dataMap.put("entityCode", dataEntity.getCode());
                    dataList.add(dataMap);
                }
            }
        } else {
            ExcelDto excelDto1 = new ExcelDto();
            excelDto1.setDisplayCode("entityCode");
            excelDto1.setDisplayName("编码");
            headList.add(excelDto1);
            ExcelDto excelDto2 = new ExcelDto();
            excelDto2.setDisplayCode("fieldType");
            excelDto2.setDisplayName("字段类型");
            headList.add(excelDto2);
            ExcelDto excelDto3 = new ExcelDto();
            excelDto3.setDisplayCode("name");
            excelDto3.setDisplayName("名称");
            headList.add(excelDto3);
            ExcelDto excelDto4 = new ExcelDto();
            excelDto4.setDisplayCode("description");
            excelDto4.setDisplayName("描述");
            headList.add(excelDto4);
            ExcelDto excelDto5 = new ExcelDto();
            excelDto5.setDisplayCode("length");
            excelDto5.setDisplayName("长度");
            headList.add(excelDto5);
            ExcelDto excelDto6 = new ExcelDto();
            excelDto6.setDisplayCode("decimalLength");
            excelDto6.setDisplayName("精度");
            headList.add(excelDto6);
            ExcelDto excelDto7 = new ExcelDto();
            excelDto7.setDisplayCode("isPk");
            excelDto7.setDisplayName("主键");
            headList.add(excelDto7);
            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put("entityCode", "code");
            dataMap.put("fieldType", "字符类型");
            dataMap.put("name", "");
            dataMap.put("description", "");
            dataMap.put("length", 50);
            dataMap.put("decimalLength", 0);
            dataMap.put("isPk", false);
            dataList.add(dataMap);
        }
        // 导出成excel文件
        ExcelUtils.export2Excel(response, "实体结构", headList, dataList);
    }

    @Override
    public List<DataEntityStructureVo> selectEntityMappingField(Long dataEntityId) {
        // 验证数据实体
        DataEntity dataEntity = checkDataEntity(dataEntityId);

        // 验证数据实体关联表
        DataEntityTable dataEntityTable = iDataEntityTableService.checkDataEntityTable(dataEntity.getTableId());

        // 获取实体字段
        List<DataEntityStructureVo> dataEntityStructureVos = this.selectStructureById(dataEntityId, true);
        // 获取映射字段
        List<DataEntityTableMapping> dataEntityTableMappingList = dataEntityTableMapper.selectTableMapping(dataEntityId, dataEntity.getTableId());
        for (DataEntityStructureVo dataEntityStructureVo : dataEntityStructureVos) {
            dataEntityStructureVo.setDataSourceId(dataEntityTable.getDataSourceId());
            dataEntityStructureVo.setTableName(dataEntityTable.getTableName());
            List<DataEntityTableMapping> dataEntityTableMappings = dataEntityTableMappingList.stream().filter(dataEntityTableMapping -> dataEntityStructureVo.getId().equals(dataEntityTableMapping.getFieldId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(dataEntityTableMappings)) {
                dataEntityStructureVo.setFieldName(dataEntityTableMappings.get(0).getFieldName());
            } else {
                dataEntityStructureVo.setFieldName(dataEntityStructureVo.getCode());
            }

        }
        return dataEntityStructureVos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createByL5(Long dataEntityId, List<Long> ids) {
        R<List<ObjectProperties>> listR = remoteObjectPropertiesService.selectByIds(ids);
        if (listR.getCode() != 200) {
            throw new ServiceException("查询L5失败：" + listR.getMsg());
        }
        List<ObjectProperties> objectPropertiesList = listR.getData();
        StringBuffer sb = new StringBuffer();
        List<DataEntityStructure> dataEntityStructureList = new ArrayList<>();
        for (int i = 0; i < objectPropertiesList.size(); i++) {
            ObjectProperties objectProperties = objectPropertiesList.get(i);
            DataEntityStructure dataEntityStructure = new DataEntityStructure();
            dataEntityStructure.setSeq(i + 1);
            dataEntityStructure.setId(IdWorker.getId());
            dataEntityStructure.setDataEntityId(dataEntityId);
            dataEntityStructure.setCode(objectProperties.getFieldName());
            dataEntityStructure.setName(objectProperties.getName());
            dataEntityStructure.setEntityInsertType(EntityInsertType.ASSET);
            dataEntityStructure.setDescription(objectProperties.getFieldDescription());
            dataEntityStructure.setIsPk(objectProperties.getPrimaryKey());
            if (dataEntityStructure.getIsPk() == null) {
                dataEntityStructure.setIsPk(false);
            }
            dataEntityStructure.setLength(objectProperties.getLength());
            dataEntityStructure.setDecimalLength(objectProperties.getDecimalLength());
            dataEntityStructure.setL5Id(objectProperties.getId());
            try {
                dataEntityStructure.setFieldType(FieldType.valueOf(objectProperties.getFieldType()));
            } catch (Exception e) {
                sb.append("资产：[" + objectProperties.getName() + "]的[" + objectProperties.getFieldType() +
                        "]无法正确转换类型，对于结构字段编码[" + dataEntityStructure.getCode() + "]\n");
            }
            dataEntityStructureList.add(dataEntityStructure);
        }
        if (CollectionUtils.isNotEmpty(dataEntityStructureList)) {
            List<String> codes = dataEntityStructureList.stream().map(DataEntityStructure::getCode).filter(Objects::nonNull).collect(Collectors.toList());
            List<String> duplicateCodes = codes.stream().filter(s -> codes.stream().filter(s2 -> s2.equals(s)).count() > 1).distinct().collect(Collectors.toList());
            List<DataEntityStructure> existList = dataEntityStructureMapper.selectByIdAndCodes(dataEntityId, codes.stream().distinct().collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(existList)) {
                duplicateCodes.addAll(existList.stream().map(DataEntityStructure::getCode).distinct().collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(duplicateCodes)) {
                throw new ServiceException("存在以下编码重复：" + JSONObject.toJSONString(duplicateCodes));
            }
        }
        // 每50个提交一次
        List<List<DataEntityStructure>> dataEntityStructureSplitList = SplitUtils.splitList(dataEntityStructureList, 50);
        for (List<DataEntityStructure> dataEntityStructureSplit : dataEntityStructureSplitList) {
            // 自定义添加字段
            dataEntityStructureMapper.batchInsertStructure(dataEntityStructureSplit);
            // 创建关系
            dataEntityStructureMapper.batchCreateStructureEdge(dataEntityId, dataEntityStructureSplit);
        }
        if (StringUtils.isNotEmpty(sb.toString())) {
            return "创建过程中存在以下问题：\n" + sb.toString();
        }
        return "创建成功";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int createEntityRelation(List<DataEntityRelation> dataEntityRelations) {
        for (DataEntityRelation dataEntityRelation : dataEntityRelations) {
            if (dataEntityRelation.getSourceDataEntityStructureId() == null) {
                throw new ServiceException("来源结构id不能为空");
            }
            if (dataEntityRelation.getTargetDataEntityStructureId() == null) {
                throw new ServiceException("目标结构id不能为空");
            }
            dataEntityStructureMapper.createEntityRelation(dataEntityRelation.getSourceDataEntityStructureId(), dataEntityRelation.getTargetDataEntityStructureId(), IdWorker.getId());
        }
        return dataEntityRelations.size();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateEntityRelation(List<DataEntityRelation> dataEntityRelations) {
        if (CollectionUtils.isEmpty(dataEntityRelations)) {
            throw new ServiceException("请指定要修改的内容");
        }
        List<Long> ids = dataEntityRelations.stream().map(DataEntityRelation::getId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(ids)) {
            dataEntityStructureMapper.deleteEntityRelation(ids);
        }
        for (DataEntityRelation dataEntityRelation : dataEntityRelations) {
            if (dataEntityRelation.getSourceDataEntityStructureId() == null) {
                throw new ServiceException("来源结构id不能为空");
            }
            if (dataEntityRelation.getTargetDataEntityStructureId() == null) {
                throw new ServiceException("目标结构id不能为空");
            }
            dataEntityStructureMapper.createEntityRelation(dataEntityRelation.getSourceDataEntityStructureId(), dataEntityRelation.getTargetDataEntityStructureId(), IdWorker.getId());
        }
        return dataEntityRelations.size();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteEntityRelation(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException("请指定要删除的模型关系");
        }
        return dataEntityStructureMapper.deleteEntityRelation(ids);
    }

    @Override
    public PageDataInfo<DataEntityRelation> queryEntityRelation(Long dataEntityId, EntityType entityType, DataEntityRelation dataEntityRelation) {
        List<DataEntityRelation> dataEntityRelationList = queryEntityRelationByList(dataEntityId, entityType, dataEntityRelation, false);
        return PageUtils.getPageInfo(dataEntityRelationList, dataEntityRelationList.size());
    }

    @Override
    public List<DataEntityRelation> queryEntityRelationByList(Long dataEntityId, EntityType entityType, DataEntityRelation dataEntityRelation, boolean useGovern) {
        List<DataEntity> dataEntities = new ArrayList<>();
        if (!useGovern && (entityType == EntityType.DWD || entityType == EntityType.DIM)) {
            // 通过DIM、DWD的菜单ID带出所有子模型
            List<DataEntity> entities = dataEntityMapper.list(dataEntityId);
            dataEntities.addAll(entities);
        } else {
            dataEntities.add(dataEntityMapper.selectById(dataEntityId));
        }
        return queryEntityRelationByTarget(dataEntities, entityType);
    }

    @Override
    public List<DataEntityRelation> queryEntityRelationByTarget(List<DataEntity> dataEntities, EntityType entityType) {
        List<DataEntityStructureVo> dataEntityStructureVos = new ArrayList<>();
        for (DataEntity dataEntity : dataEntities) {
            dataEntityStructureVos.addAll(this.selectStructureById(dataEntity.getId(), true));
        }
        if (CollectionUtils.isEmpty(dataEntityStructureVos)) {
            return new ArrayList<>();
        }
        List<Long> ids = dataEntityStructureVos.stream().map(DataEntityStructureVo::getId).collect(Collectors.toList());
        List<DataEntityRelation> dataEntityRelations = dataEntityStructureMapper.queryEntityRelation(ids, "target");
        List<DataEntityRelation> dataEntityRelationList = new ArrayList<>();
        Map<Long, DataEntity> map = dataEntities.stream().collect(Collectors.toMap(DataEntity::getId, Function.identity()));
        for (DataEntityStructureVo dataEntityStructureVo : dataEntityStructureVos) {
            List<DataEntityRelation> collect = dataEntityRelations.stream().filter(e -> e.getTargetDataEntityStructureId().equals(dataEntityStructureVo.getId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) {
                DataEntityRelation dataEntityRelation1 = new DataEntityRelation();
                dataEntityRelation1.setTargetDataEntityId(dataEntityStructureVo.getDataEntityId());
                DataEntity dataEntity = map.get(dataEntityStructureVo.getDataEntityId());
                if (dataEntity != null) {
                    dataEntityRelation1.setTargetDataEntityCode(dataEntity.getCode());
                    dataEntityRelation1.setTargetDataEntityName(dataEntity.getName());
                    dataEntityRelation1.setTargetDataEntityType(dataEntity.getEntityType());
                }
                dataEntityRelation1.setTargetDataEntityStructureId(dataEntityStructureVo.getId());
                dataEntityRelation1.setTargetDataEntityStructureCode(dataEntityStructureVo.getCode());
                dataEntityRelation1.setTargetDataEntityStructureName(dataEntityStructureVo.getName());
                dataEntityRelation1.setTargetDataEntityStructureDescription(dataEntityStructureVo.getDescription());
                dataEntityRelation1.setTargetEntityInsertType(dataEntityStructureVo.getEntityInsertType());
                dataEntityRelation1.setTargetDataEntityStructureFieldType(dataEntityStructureVo.getFieldType());
                dataEntityRelation1.setTargetDataEntityStructureLength(dataEntityStructureVo.getLength());
                dataEntityRelation1.setTargetDataEntityStructureDecimalLength(dataEntityStructureVo.getDecimalLength());
                dataEntityRelation1.setTargetDataEntityStructureIsPk(dataEntityStructureVo.getIsPk());
                dataEntityRelationList.add(dataEntityRelation1);
            } else {
                dataEntityRelationList.addAll(collect);
            }
        }
        return dataEntityRelationList;
    }

    @Override
    public List<DataEntityRelation> queryEntityRelationBySource(List<DataEntity> dataEntities, EntityType entityType) {
        List<DataEntityStructureVo> dataEntityStructureVos = new ArrayList<>();
        for (DataEntity dataEntity : dataEntities) {
            dataEntityStructureVos.addAll(this.selectStructureById(dataEntity.getId(), true));
        }
        if (CollectionUtils.isEmpty(dataEntityStructureVos)) {
            return new ArrayList<>();
        }
        List<Long> ids = dataEntityStructureVos.stream().map(DataEntityStructureVo::getId).collect(Collectors.toList());
        List<DataEntityRelation> dataEntityRelations = dataEntityStructureMapper.queryEntityRelation(ids, "source");
        List<DataEntityRelation> dataEntityRelationList = new ArrayList<>();
        Map<Long, DataEntity> map = dataEntities.stream().collect(Collectors.toMap(DataEntity::getId, Function.identity()));
        for (DataEntityStructureVo dataEntityStructureVo : dataEntityStructureVos) {
            List<DataEntityRelation> collect = dataEntityRelations.stream().filter(e -> e.getSourceDataEntityStructureId().equals(dataEntityStructureVo.getId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) {
                DataEntityRelation dataEntityRelation1 = new DataEntityRelation();
                dataEntityRelation1.setSourceDataEntityId(dataEntityStructureVo.getDataEntityId());
                DataEntity dataEntity = map.get(dataEntityStructureVo.getDataEntityId());
                if (dataEntity != null) {
                    dataEntityRelation1.setSourceDataEntityCode(dataEntity.getCode());
                    dataEntityRelation1.setSourceDataEntityName(dataEntity.getName());
                    dataEntityRelation1.setSourceDataEntityType(dataEntity.getEntityType());
                }
                dataEntityRelation1.setSourceDataEntityStructureId(dataEntityStructureVo.getId());
                dataEntityRelation1.setSourceDataEntityStructureCode(dataEntityStructureVo.getCode());
                dataEntityRelation1.setSourceDataEntityStructureName(dataEntityStructureVo.getName());
                dataEntityRelation1.setSourceDataEntityStructureDescription(dataEntityStructureVo.getDescription());
                dataEntityRelation1.setSourceEntityInsertType(dataEntityStructureVo.getEntityInsertType());
                dataEntityRelation1.setSourceDataEntityStructureFieldType(dataEntityStructureVo.getFieldType());
                dataEntityRelation1.setSourceDataEntityStructureLength(dataEntityStructureVo.getLength());
                dataEntityRelation1.setSourceDataEntityStructureDecimalLength(dataEntityStructureVo.getDecimalLength());
                dataEntityRelation1.setSourceDataEntityStructureIsPk(dataEntityStructureVo.getIsPk());
                dataEntityRelationList.add(dataEntityRelation1);
            } else {
                dataEntityRelationList.addAll(collect);
            }
        }
        return dataEntityRelationList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByEntityIdAndInsertType(Long dataEntityId) {
        // 查询该实体指定类型的字段结构
        List<DataEntityStructureVo> structureList = selectStructureById(dataEntityId, false);
        if (CollectionUtils.isEmpty(structureList)) {
            return 0;
        }
        // 调用现有的删除方法
        return delete(dataEntityId, structureList);
    }

}
