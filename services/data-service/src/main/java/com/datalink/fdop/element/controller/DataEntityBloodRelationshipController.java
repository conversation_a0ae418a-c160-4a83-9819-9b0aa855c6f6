package com.datalink.fdop.element.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.element.api.domain.DataEntityBloodExport;
import com.datalink.fdop.element.api.domain.DataEntityBloodVo;
import com.datalink.fdop.element.service.IDataEntityBloodRelationshipService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping(value = "/element/entity/blood/relationship")
@Api(tags = "数据实体血缘（数据治理血缘）")
public class DataEntityBloodRelationshipController {

    @Autowired
    private IDataEntityBloodRelationshipService dataEntityBloodRelationshipService;

    @ApiOperation("根据指标ID获取实体血缘关系")
    @GetMapping(value = "/getBloodByTagId")
    public R<DataEntityBloodVo> getBloodByTagId(@RequestParam(value = "tagId") Long tagId) {
        return R.ok(dataEntityBloodRelationshipService.getBloodByTagId(tagId));
    }

    @ApiOperation("根据资产ID获取实体血缘关系")
    @GetMapping(value = "/getBloodByAssetId")
    public R<DataEntityBloodVo> getBloodByL3AssetId(@RequestParam(value = "assetId") Long assetId) {
        return R.ok(dataEntityBloodRelationshipService.getBloodByL3AssetId(assetId));
    }

    @ApiOperation("血缘导出")
    @PostMapping(value = "/exportExcel")
    public void exportExcel(HttpServletResponse response, @RequestBody List<DataEntityBloodExport> dataEntityBloodExportList) {
        if (CollectionUtils.isEmpty(dataEntityBloodExportList)){
            throw new RuntimeException("导出数据为空");
        }
        ExcelUtil<DataEntityBloodExport> util = new ExcelUtil<>(DataEntityBloodExport.class);
        util.exportExcel(response, dataEntityBloodExportList, "血缘分析数据");
    }

}
