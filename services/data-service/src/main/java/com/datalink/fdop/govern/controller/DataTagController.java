package com.datalink.fdop.govern.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.govern.api.domain.DataProcessFrameworkTree;
import com.datalink.fdop.govern.api.domain.DataTag;
import com.datalink.fdop.govern.api.domain.DataTagExport;
import com.datalink.fdop.govern.api.domain.ImportDataLog;
import com.datalink.fdop.govern.service.IDataTagService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/govern/dataTag")
@Api("数据指标")
public class DataTagController {

    @Autowired
    private IDataTagService dataTagService;

    @ApiOperation("创建")
    @Log(title = "数据指标")
    @PostMapping("/create")
    public R create(@RequestBody DataTag entity) {
        return R.ok(dataTagService.create(entity));
    }

    @ApiOperation("修改")
    @Log(title = "数据指标")
    @PostMapping("/update")
    public R update(@RequestBody DataTag entity) {
        return R.ok(dataTagService.update(entity));
    }

    @ApiOperation("删除")
    @Log(title = "数据指标")
    @DeleteMapping("/delete")
    public R delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.DATA_RESOURCES_TO_DELETE);
        }
        return R.ok(dataTagService.delete(ids));
    }

    @ApiOperation("根据ID查询")
    @Log(title = "数据指标")
    @GetMapping("/selectById/{id}")
    public R selectById(@PathVariable Long id) {
        return R.ok(dataTagService.selectById(id));
    }

    @ApiOperation("总览")
    @Log(title = "数据指标")
    @PostMapping(value = "/overview")
    public R<PageDataInfo<DataTag>> overview(@RequestParam(value = "pid", defaultValue = "-1") Long pid,
                                             @RequestParam(value = "sort", defaultValue = "ASC", required = false) String sort,
                                             @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(dataTagService.overview(pid, sort, searchVo));
    }

    @ApiOperation("流程框架L1、L2、指标目录L3树结构")
    @Log(title = "流程框架")
    @GetMapping(value = "/tree")
    public R<List<DataProcessFrameworkTree>> treeList(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestParam(required = false) String code,
            @RequestParam(required = false) String name) {
        return R.ok(dataTagService.treeList(sort, code, name));
    }

    @ApiOperation("注册")
    @Log(title = "数据指标")
    @PostMapping(value = "/register")
    public R register(@RequestBody List<Long> ids) {
        return R.ok(dataTagService.register(ids));
    }

    @ApiOperation("取消注册")
    @Log(title = "数据指标")
    @PostMapping(value = "/unregister")
    public R unregister(@RequestBody List<Long> ids) {
        return R.ok(dataTagService.unregister(ids));
    }

    @ApiOperation(value = "导入")
    @Log(title = "数据指标-导入")
    @PostMapping("/importData")
    public R importData(MultipartFile file) throws Exception {
        if (file == null || file.isEmpty()) {
            return R.fail("文件上传失败！");
        }
        if (!file.getOriginalFilename().endsWith(".xlsx") && !file.getOriginalFilename().endsWith(".xls")) {
            return R.fail("文件不是Excel文件，请上传.xlsx或.xls后缀文件");
        }
        ExcelUtil<DataTag> util = new ExcelUtil<>(DataTag.class);
        List<DataTag> list = util.importExcel(file.getInputStream());
        String operatorName = SecurityUtils.getUsername();
        dataTagService.importData(file.getOriginalFilename(), list, operatorName);
        return R.ok("导入中，请移至日志查看");
    }

    @ApiOperation(value = "导入模板")
    @Log(title = "数据指标-导入模板")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DataTag> util = new ExcelUtil<>(DataTag.class);
        util.importTemplateExcel(response, "dataTag");
    }

    @ApiOperation("导出数据")
    @Log(title = "数据指标-导出数据")
    @PostMapping("/export")
    public void export(HttpServletResponse response,
                       @RequestParam(value = "pid", defaultValue = "-1") Long pid,
                       @RequestParam(value = "sort", defaultValue = "ASC", required = false) String sort,
                       @RequestBody(required = false) SearchVo searchVo) {
        List<DataTagExport> list = dataTagService.getExportList(pid, sort, searchVo);
        ExcelUtil<DataTagExport> util = new ExcelUtil<>(DataTagExport.class);
        util.exportExcel(response, list, "数据指标数据");
    }

    @ApiOperation(value = "导入日志")
    @Log(title = "数据指标-导入日志")
    @PostMapping(value = "/importLog")
    public R<PageDataInfo<ImportDataLog>> importLog(@RequestBody(required = false) ImportDataLog importDataLog,
                                                    @RequestParam(value = "sort", defaultValue = "DESC", required = false) String sort) {
        return R.ok(dataTagService.importLog(importDataLog, sort));
    }

    @ApiOperation(value = "日志详情")
    @Log(title = "数据指标-日志详情")
    @GetMapping(value = "/importLog/selectById/{id}")
    public R<ImportDataLog> selectByIdLog(@PathVariable Long id) {
        return R.ok(dataTagService.selectByIdLog(id));
    }

}
