package com.datalink.fdop.govern.service;

import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.govern.api.domain.DataProcessFrameworkTree;
import com.datalink.fdop.govern.api.domain.DataTag;
import com.datalink.fdop.govern.api.domain.DataTagExport;
import com.datalink.fdop.govern.api.domain.ImportDataLog;

import java.util.List;

public interface IDataTagService {

    int create(DataTag entity);

    int update(DataTag entity);

    int delete(List<Long> ids);

    int register(List<Long> ids);

    int unregister(List<Long> ids);

    List<DataTag> getList(DataTag dataResources, Boolean disuseStatus);

    DataTag selectByCode(String code);

    PageDataInfo<DataTag> selectAll(DataTag entity);

    DataTag selectById(Long id);

    PageDataInfo<DataTag> overview(Long pid, String sort, SearchVo searchVo);

    List<DataProcessFrameworkTree> treeList(String sort, String code, String name);

    /**
     * 导入数据
     * @param fileName 文件名称
     * @param list excel数据
     * @param operatorName 操作员（日志记录用）
     */
    void importData(String fileName, List<DataTag> list, String operatorName);

    /**
     * 导出数据
     * @param pid 父级ID
     * @param sort 排序
     * @param searchVo 高级查询
     * @return
     */
    List<DataTagExport> getExportList(Long pid, String sort, SearchVo searchVo);


    /**
     * 导入日志
     * @param importDataLog 查询条件
     * @param sort 排序
     * @return
     */
    PageDataInfo<ImportDataLog> importLog(ImportDataLog importDataLog, String sort);

    /**
     * 日志详情
     * @param id
     * @return
     */
    ImportDataLog selectByIdLog(Long id);
}