package com.datalink.fdop.govern.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.TreeVo;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.govern.api.domain.DataProcessFramework;
import com.datalink.fdop.govern.api.domain.DataProcessFrameworkMenu;
import com.datalink.fdop.govern.service.IDataProcessFrameworkMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RequestMapping(value = "/govern/processFramework/menu")
@RestController
@Api(tags = "流程框架菜单api")
public class DataProcessFrameworkMenuController extends BaseController {

    @Autowired
    private IDataProcessFrameworkMenuService processFrameworkMenuService;

    @ApiOperation("创建流程框架菜单")
    @Log(title = "流程框架菜单", businessType = BusinessType.INSERT)
    @PostMapping(value = "/create")
    public R create(@Validated @RequestBody DataProcessFrameworkMenu processFrameworkMenu) {
        return R.toResult(processFrameworkMenuService.create(processFrameworkMenu));
    }

    @ApiOperation("修改流程框架菜单")
    @Log(title = "流程框架菜单", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public R update(@RequestBody DataProcessFrameworkMenu processFrameworkMenu) {
        return R.toResult(processFrameworkMenuService.update(processFrameworkMenu));
    }

    @ApiOperation("删除流程框架菜单")
    @Log(title = "流程框架菜单", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.DATA_PROCESS_FRAMEWORK_MENU_TO_DELETE);
        }
        return R.toResult(processFrameworkMenuService.delete(ids));
    }

    @ApiOperation("根据id查询流程框架菜单信息")
    @Log(title = "流程框架菜单")
    @GetMapping(value = "/selectById/{id}")
    public R<DataProcessFrameworkMenu> selectById(@PathVariable("id") Long id) {
        return R.ok(processFrameworkMenuService.selectById(id));
    }


    @ApiOperation("流程框架树菜单")
    @Log(title = "流程框架菜单")
    @GetMapping(value = "/tree")
    public R<List<TreeVo>> tree(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestParam(required = false) String code,
            @RequestParam(required = false, defaultValue = "true") Boolean isQueryNode) {
        List<TreeVo> list = processFrameworkMenuService.tree(sort, code, isQueryNode);
        return R.ok(list);
    }


    @ApiOperation("总览")
    @Log(title = "流程框架菜单")
    @PostMapping(value = "/overview")
    public R<PageDataInfo<DataProcessFramework>> overview(@RequestParam(value = "pid", defaultValue = "-1", required = false) Long pid,
                                                          @RequestParam(value = "sort", defaultValue = "ASC", required = false) String sort,
                                                          @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(processFrameworkMenuService.overview(pid, sort, searchVo));
    }


    @ApiOperation("查序号")
    @Log(title = "流程框架菜单")
    @GetMapping(value = "/querySerialNumber")
    public R<Integer> querySerialNumber(@RequestParam Boolean menuFlag) {
        return R.ok(processFrameworkMenuService.querySerialNumber(menuFlag));
    }

}
