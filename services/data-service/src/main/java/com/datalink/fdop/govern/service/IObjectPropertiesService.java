package com.datalink.fdop.govern.service;


import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.govern.api.domain.ImportDataLog;
import com.datalink.fdop.govern.api.domain.ObjectProperties;

import java.util.List;

public interface IObjectPropertiesService {

    int create(ObjectProperties entity);

    int update(ObjectProperties entity);

    int delete(List<Long> ids);

    ObjectProperties selectById(Long id);

    PageDataInfo<ObjectProperties> list(ObjectProperties entity, String sort);

    int lock(List<Long> ids);

    int unlock(List<Long> ids);

    List<ObjectProperties> selectByIds(List<Long> ids);

    List<ObjectProperties> selectByPids(List<Long> pids);

    int updateVersion(List<Long> ids, String version);

    /**
     * 导入数据
     * @param fileName 文件名称
     * @param list excel数据
     * @param operatorName 操作员（日志记录用）
     */
    void importData(String fileName, List<ObjectProperties> list, String operatorName);

    /**
     * 导出数据
     * @param entity 查询条件
     * @param sort 排序
     * @return
     */
    List<ObjectProperties> getExportList(ObjectProperties entity, String sort);


    /**
     * 导入日志
     * @param importDataLog 查询条件
     * @param sort 排序
     * @return
     */
    PageDataInfo<ImportDataLog> importLog(ImportDataLog importDataLog, String sort);

    /**
     * 日志详情
     * @param id
     * @return
     */
    ImportDataLog selectByIdLog(Long id);
}
