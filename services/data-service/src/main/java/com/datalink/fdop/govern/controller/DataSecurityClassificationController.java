package com.datalink.fdop.govern.controller;


import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.govern.api.domain.DataSecurityClassification;
import com.datalink.fdop.govern.api.domain.DataSecurityClassificationTree;
import com.datalink.fdop.govern.api.domain.DataSecurityLevel;
import com.datalink.fdop.govern.service.IDataSecurityClassificationService;
import io.swagger.annotations.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping(value = "/govern/security/classification")
@RestController
@Api(tags = "安全分类api")
public class DataSecurityClassificationController {

    @Autowired
    private IDataSecurityClassificationService dataSecurityClassificationService;

    @ApiOperation("创建安全分类")
    @Log(title = "数据治理")
    @PostMapping(value = "/create")
    public R create(@Validated @RequestBody DataSecurityClassification dataSecurityClassification) {
        return R.toResult(dataSecurityClassificationService.create(dataSecurityClassification));
    }

    @ApiOperation("修改安全分类")
    @Log(title = "数据治理")
    @PostMapping(value = "/update")
    public R update(@RequestBody DataSecurityClassification dataSecurityClassification) {
        if (dataSecurityClassification.getId() == null) {
            throw new ServiceException(Status.PARAM_ERROR);
        }
        return R.toResult(dataSecurityClassificationService.update(dataSecurityClassification));
    }

    @ApiOperation("删除安全分类")
    @Log(title = "数据治理")
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Long> ids, @RequestParam(required = false) Long userId) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.DATA_SECURITY_CLASS_TO_DELETE);
        }
        return R.toResult(dataSecurityClassificationService.delete(ids));
    }

    @ApiOperation("根据id查询查询数据安全分类信息")
    @Log(title = "数据治理")
    @GetMapping(value = "/selectById")
    public R<DataSecurityClassification> selectById(@RequestParam Long id) {
        return R.ok(dataSecurityClassificationService.selectById(id));
    }

    @ApiOperation(value = "根据code查询数据安全分类信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "安全分类编码", required = true, dataType = "String", paramType = "query"),
    })
    @GetMapping(value = "/selectByCode")
    @Log(title = "数据治理")
    public R<DataSecurityClassification> selectByCode(@RequestParam(value = "code") String code) {
        return R.ok(dataSecurityClassificationService.selectByCode(code));
    }

    @ApiOperation(value = "查询数据安全分类信息列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功", response = PageDataInfo.class)
    })
    @PostMapping(value = "/list")
    @Log(title = "全局参数")
    public R<PageDataInfo<DataSecurityClassification>> list(@RequestBody(required = false) DataSecurityClassification dataSecurityClassification) {
        return R.ok(dataSecurityClassificationService.list(dataSecurityClassification));
    }

    @ApiOperation("查询数据安全等级（下拉框）")
    @Log(title = "数据治理")
    @PostMapping(value = "/selectVoList")
    public R<List<SelectVo>> selectVoList(@RequestBody(required = false) DataSecurityClassification dataSecurityClassification) {
        return R.ok(dataSecurityClassificationService.selectVoList(dataSecurityClassification));
    }


    @ApiOperation("查询数据安全分类信息树结构列表")
    @Log(title = "数据元素")
    @GetMapping(value = "/tree")
    public R<List<DataSecurityClassificationTree>> treeList(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestParam(required = false) String code,
            @RequestParam(required = false) String description) {
        List<DataSecurityClassificationTree> dataSecurityClassificationTrees = dataSecurityClassificationService.treeList(sort, code, description);
        return R.ok(dataSecurityClassificationTrees);
    }

    @ApiOperation("复制")
    @Log(title = "数据治理")
    @PostMapping(value = "/copy")
    public R copy(
            @RequestParam(value = "pid", defaultValue = "-1") Long pid,
            @RequestBody List<DataSecurityClassification> dataSecurityClassificationList) {
        return R.ok(dataSecurityClassificationService.copy(pid, dataSecurityClassificationList));
    }

}
