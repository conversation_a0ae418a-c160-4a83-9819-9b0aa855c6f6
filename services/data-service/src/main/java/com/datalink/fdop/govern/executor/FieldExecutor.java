package com.datalink.fdop.govern.executor;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.drive.api.domain.dto.Field;
import com.datalink.fdop.govern.api.domain.SynchronizationTable;
import com.datalink.fdop.govern.api.domain.SynchronizationView;
import com.datalink.fdop.govern.mapper.DataSourceSynchronizationMapper;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
public class FieldExecutor {




    private DataSourceSynchronizationMapper dataSourceSynchronizationMapper;


    public FieldExecutor(DataSourceSynchronizationMapper dataSourceSynchronizationMapper){
        this.dataSourceSynchronizationMapper=dataSourceSynchronizationMapper;
    }



    public Field getHistoryField(Field field,Field fieldHistory ){
        if (!field.getFieldType().equals(fieldHistory.getFieldType()) ||
                field.getLength()!=fieldHistory.getLength() ||
                field.getDecimalLength()!=fieldHistory.getDecimalLength() ||
                field.getIsPk()!=fieldHistory.getIsPk() ||
                (StringUtils.isEmpty(field.getFieldDefault())&&StringUtils.isNotEmpty(fieldHistory.getFieldDefault()))||
                (StringUtils.isNotEmpty(field.getFieldDefault())&&StringUtils.isEmpty(fieldHistory.getFieldDefault()))||
                (StringUtils.isNotEmpty(field.getFieldDefault())&&StringUtils.isNotEmpty(fieldHistory.getFieldDefault())&&!field.getFieldDefault().equals(fieldHistory.getFieldDefault()))

        ){
            return fieldHistory;
        }
        return null;
    }

    public void  saveTableFieldInfo(List<Field> fields, SynchronizationTable syncTable, List<Long> fieldIdList){
        for (Field field : fields) {
            Field fieldVo = dataSourceSynchronizationMapper.getFieldByName(field.getFieldName(), syncTable.getId());
            if (fieldVo==null) {
                field.setId(IdWorker.getId());
                fieldIdList.add(field.getId());
                field.setDataSourceId(syncTable.getDataSourceId());
                field.setTableName(syncTable.getCode());
                dataSourceSynchronizationMapper.createSyncField(field);
                dataSourceSynchronizationMapper.createSyncTableSyncFieldEdge(field.getId(),syncTable.getId());
            }else {
                field.setId(fieldVo.getId());
                field.setHisField("");
                fieldIdList.add(field.getId());
                Field historyField = this.getHistoryField(field,fieldVo);
                if (historyField!=null) {
                    field.setHisField(JSONObject.toJSONString(historyField));
                }
                dataSourceSynchronizationMapper.updateField(field);
            }

        }
    }

    public void  saveViewFieldInfo(List<Field> fields, SynchronizationView syncView, List<Long> fieldIdList){
        for (Field field : fields) {
            Field fieldVo = dataSourceSynchronizationMapper.getFieldByName(field.getFieldName(),syncView.getId());
            if (fieldVo==null) {
                field.setId(IdWorker.getId());
                field.setDataSourceId(syncView.getDataSourceId());
                field.setViewName(syncView.getCode());
                dataSourceSynchronizationMapper.createSyncField(field);
                dataSourceSynchronizationMapper.createSyncViewSyncFieldEdge(field.getId(), syncView.getId());
                fieldIdList.add(field.getId());
            }else {
                field.setId(fieldVo.getId());
                Field historyField = this.getHistoryField(field,fieldVo);
                if (historyField!=null) {
                    field.setHisField(JSONObject.toJSONString(historyField));
                }
                dataSourceSynchronizationMapper.updateField(field);
                fieldIdList.add(field.getId());
            }
        }
    }




}
