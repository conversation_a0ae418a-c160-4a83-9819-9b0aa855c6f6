package com.datalink.fdop.element.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datalink.fdop.common.core.enums.FieldType;
import com.datalink.fdop.element.api.domain.DataElementStructure;
import com.datalink.fdop.element.api.model.vo.DataElementStructureVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface DataElementStructureMapper extends BaseMapper<DataElementStructure> {

    List<DataElementStructure> selectByElementId(@Param("dataElementId") Long dataElementId,
                                                 @Param("fieldType") FieldType fieldType,
                                                 @Param("length") Long length,
                                                 @Param("decimalLength") Long decimalLength);

    List<DataElementStructure> selectMainFatherById(@Param("dataElementId") Long dataElementId,
                                                    @Param("fieldType") FieldType fieldType,
                                                    @Param("length") Long length,
                                                    @Param("decimalLength") Long decimalLength);

    int insertStructure(@Param("dataElementId") Long dataElementId, @Param("structureList") List<DataElementStructure> structureList);

    int updateStructure(@Param("dataElementId") Long dataElementId, @Param("structureList") List<DataElementStructure> structureList);

    Boolean isMainStructureExist(@Param("dataElementId") Long dataElementId, @Param("mainStructureId") Long mainStructureId);

    int deleteStructure(@Param("dataElementId") Long dataElementId, @Param("structureList") List<DataElementStructure> structureList);

    List<DataElementStructureVo> selectMenuAndElementAndFieldList(@Param("dataElementId") Long dataElementId, @Param("code") String code);

    int deleteStructureTemplate(@Param("structureList") List<DataElementStructure> structureList);

    List<Integer> selectElementAllMinSeq(@Param("dataElementId") Long dataEntityId, @Param("isPk") Boolean isPk);

    List<Integer> selectElementAllMaxSeq(@Param("dataElementId") Long dataEntityId, @Param("isPk") Boolean isPk);

}
