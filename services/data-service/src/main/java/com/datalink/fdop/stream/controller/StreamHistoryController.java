package com.datalink.fdop.stream.controller;


import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.stream.api.domain.StreamHistory;
import com.datalink.fdop.stream.service.IStreamHistoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping(value = "/stream/history")
@RestController
@Api(tags = "流任务历史记录api")
public class StreamHistoryController {

    @Autowired
    private IStreamHistoryService streamHistoryService;


    @ApiOperation("查询历史记录")
    @Log(title = "流任务历史记录")
    @PostMapping(value = "/getHistoryListByStreamId")
    public R<PageDataInfo<StreamHistory>> getHistoryListByStreamId(@RequestParam(value = "streamId", required = true) Long streamId,
                                                   @RequestParam(value = "sort", defaultValue = "ASC", required = false) String sort,
                                                   @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(streamHistoryService.selectHistoryListByStreamId(streamId, sort, searchVo));
    }

    @ApiOperation("删除流任务历史记录")
    @Log(title = "流任务历史记录", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/delete/{streamId}")
    public R delete(@PathVariable(value = "streamId") Long streamId, @RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.PLEASE_SPECIFY_THE_FLOW_MENU_TO_DELETE);
        }
        return R.toResult(streamHistoryService.delete(ids,streamId));
    }
}
