package com.datalink.fdop.element.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.idempotent.annotation.RepeatSubmit;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.element.api.domain.DataElement;
import com.datalink.fdop.element.api.enums.DataElementType;
import com.datalink.fdop.element.api.model.vo.DataElementCopyVo;
import com.datalink.fdop.element.service.IDataElementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
@RequestMapping(value = "/element/element")
@RestController
@Api(tags = "数据元素api")
public class DataElementController extends BaseController {

    @Autowired
    private IDataElementService dataElementService;

    @ApiOperation("创建数据元素")
    @Log(title = "数据元素", businessType = BusinessType.INSERT)
    @RepeatSubmit(interval = 1000)
    @PostMapping(value = "/create")
    public R<DataElement> create(@Validated @RequestBody DataElement dataElement) {
        return R.ok(dataElementService.create(dataElement));
    }

    @ApiOperation("修改数据元素")
    @Log(title = "数据元素", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public R update(@Validated @RequestBody DataElement dataElement) {
        // 主数据类型默认自身是主键，字段类型为非主键
        if (dataElement.getDataElementType() == DataElementType.MAIN) {
            dataElement.setIsPk(true);
        } else if (dataElement.getDataElementType() == DataElementType.FIELD) {
            dataElement.setIsPk(false);
        }
        return R.toResult(dataElementService.update(dataElement));
    }

    @ApiOperation("保存来源json")
    @Log(title = "数据元素", businessType = BusinessType.INSERT)
    @PostMapping(value = "/saveSource")
    public R saveSource(@RequestParam("id") Long id, @RequestBody String taskJson) {
        return R.toResult(dataElementService.saveSource(id, taskJson));
    }

    @ApiOperation("复制数据元素")
    @Log(title = "数据元素", businessType = BusinessType.INSERT)
    @PostMapping(value = "/copy/{pid}")
    public R copy(@PathVariable("pid") Long pid, @Validated @RequestBody List<DataElementCopyVo> dataElementCopyList) {
        dataElementService.copy(pid, dataElementCopyList);
        return R.ok();
    }

    @ApiOperation("删除数据元素")
    @Log(title = "数据元素", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.PLEASE_SPECIFY_THE_ELEMENT_THAT_NEEDS_TO_BE_REMOVED);
        }
        return R.toResult(dataElementService.delete(ids));
    }

    @ApiOperation("删除前检查")
    @Log(title = "数据元素", businessType = BusinessType.OTHER)
    @PostMapping(value = "/checkDeleteElement")
    public R<Map<String, Object>> checkDeleteElement(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.SPECIFY_THE_ELEMENTS_TO_BE_CHECKED);
        }
        return R.ok(dataElementService.checkDeleteElement(ids));
    }

    @ApiOperation("查询数据元素")
    @Log(title = "数据元素")
    @PostMapping(value = "/list")
    public R<PageDataInfo> list(@RequestBody(required = false) DataElement dataElement) {
        return R.ok(dataElementService.list(dataElement));
    }

    @ApiOperation("根据元素id查询数据元素")
    @Log(title = "数据元素")
    @GetMapping(value = "/selectById/{id}")
    public R<DataElement> selectById(@PathVariable(value = "id") Long id) {
        return R.ok(dataElementService.selectById(id));
    }

    @ApiOperation("根据元素code查询数据元素")
    @Log(title = "数据元素")
    @GetMapping(value = "/selectByCode")
    public R<DataElement> selectByCode(@RequestParam(value = "code") String code) {
        return R.ok(dataElementService.selectByCode(code));
    }

}
