package com.datalink.fdop.element.service.impl;

import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.element.api.domain.DataElement;
import com.datalink.fdop.element.api.domain.DataElementStructure;
import com.datalink.fdop.element.api.enums.DataElementType;
import com.datalink.fdop.element.mapper.DataElementMapper;
import com.datalink.fdop.element.mapper.GraphTableMapper;
import com.datalink.fdop.element.service.IDataElementStructureService;
import com.datalink.fdop.element.service.IDataElementValueService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/12 13:58
 */
@Service
public class DataElementValueServiceImpl implements IDataElementValueService {

    @Autowired
    private DataElementMapper dataElementMapper;

    @Autowired
    private IDataElementStructureService dataElementStructureService;

    @Autowired
    private GraphTableMapper graphTableMapper;

    private DataElement checkDataElement(Long dataElementId) {
        DataElement dataElement = dataElementMapper.selectById(dataElementId);
        if (dataElement == null) {
            throw new ServiceException(Status.DATA_ELEMENT_NOT_EXIST);
        }
        if (dataElement.getDataElementType() != DataElementType.MAIN) {
            throw new ServiceException(Status.NOT_MAIN_OPERATION_NOT_ALLOWED);
        }
        return dataElement;
    }

    @Override
    public List<Map<String, Object>> previewData(Long dataElementId, Boolean isPk) {

        // 验证数据元素
        DataElement dataElement = checkDataElement(dataElementId);

        // 映射的表名
        String mapTableName = dataElement.getMapTableName();

        // 获取表结构
        List<DataElementStructure> dataElementStructureList = dataElementStructureService.selectStructureByDataElementId(dataElementId);

        if (isPk != null) {
            List<DataElementStructure> isPkDataElementStructureList = dataElementStructureList.stream()
                    .filter(dataElementStructure -> dataElementStructure.getIsPk() == isPk)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(isPkDataElementStructureList)) {
                throw new ServiceException(MessageFormat.format(Status.UNABLE_TO_QUERY_DATA.getMsg(), isPk ? "主键" : "非主键"));
            }
            return graphTableMapper.getData(mapTableName, isPkDataElementStructureList);
        }

        return graphTableMapper.getData(mapTableName, dataElementStructureList);
    }

    @Override
    public PageDataInfo<Map<String, Object>> previewDataTable(Long dataElementId, Boolean isPk, DataElement dataElement, List<DataElementStructure> structures) {
        if (isPk != null) {
            List<DataElementStructure> isPkDataElementStructureList = structures.stream()
                    .filter(dataElementStructure -> dataElementStructure.getIsPk() == isPk)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(isPkDataElementStructureList)) {
                throw new ServiceException(MessageFormat.format(Status.UNABLE_TO_QUERY_DATA.getMsg(), isPk ? "主键" : "非主键"));
            }
            return PageUtils
                    .getPageInfo(graphTableMapper.getData(dataElement.getMapTableName(), isPkDataElementStructureList),
                            graphTableMapper.getDataCount(dataElement.getMapTableName(), isPkDataElementStructureList));
        }

        return PageUtils
                .getPageInfo(graphTableMapper.getData(dataElement.getMapTableName(), structures),
                        graphTableMapper.getDataCount(dataElement.getMapTableName(), structures));
    }

    @Override
    public DataElement getDataElement(Long dataElementId) {
        return  checkDataElement(dataElementId);
    }

    @Override
    public List<DataElementStructure> getDataElementStructure(Long dataElementId) {
        return dataElementStructureService.selectStructureByDataElementId(dataElementId);
    }
}
