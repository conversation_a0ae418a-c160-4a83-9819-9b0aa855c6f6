package com.datalink.fdop.govern.service;

import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.govern.api.domain.BusinessObject;
import com.datalink.fdop.govern.api.domain.ImportDataLog;

import java.util.List;

public interface IBusinessObjectService {

    int create(BusinessObject entity);

    int update(BusinessObject entity);

    int delete(List<Long> ids);

    BusinessObject selectById(Long id);

    BusinessObject selectByCode(String code);

    PageDataInfo<BusinessObject> list(BusinessObject entity, String sort);

    int register(List<Long> ids);

    int unregister(List<Long> ids);

    /**
     * 导入数据
     * @param fileName 文件名称
     * @param list excel数据
     * @param operatorName 操作员（日志记录用）
     */
    void importData(String fileName, List<BusinessObject> list, String operatorName);

    /**
     * 导出数据
     * @param entity 查询条件
     * @param sort 排序
     * @return
     */
    List<BusinessObject> getExportList(BusinessObject entity, String sort);


    /**
     * 导入日志
     * @param importDataLog 查询条件
     * @param sort 排序
     * @return
     */
    PageDataInfo<ImportDataLog> importLog(ImportDataLog importDataLog, String sort);

    /**
     * 日志详情
     * @param id
     * @return
     */
    ImportDataLog selectByIdLog(Long id);
}
