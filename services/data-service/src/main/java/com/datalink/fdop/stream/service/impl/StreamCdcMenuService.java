package com.datalink.fdop.stream.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.enums.DbType;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.tree.TreeUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.stream.api.domain.StreamCdc;
import com.datalink.fdop.stream.api.domain.StreamCdcMenu;
import com.datalink.fdop.stream.api.domain.StreamCdcTree;
import com.datalink.fdop.stream.mapper.StreamCdcMapper;
import com.datalink.fdop.stream.mapper.StreamCdcMenuMapper;
import com.datalink.fdop.stream.service.DebeziumService;
import com.datalink.fdop.stream.service.IStreamCdcMenuService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
@Service
@Slf4j
public class StreamCdcMenuService implements IStreamCdcMenuService {

    @Autowired
    private StreamCdcMapper streamCdcMapper;

    @Autowired
    private StreamCdcMenuMapper streamCdcMenuMapper;

    @Autowired
    private StreamCdcService streamCdcService;

    /**
     * 全局序号间隔参数 当前数领 * 序号间隔数量
     */
    @Value("${dwms.serial-interval}")
    private Integer serialIntervalNumber;

//    @Autowired
//    private KubernetesUtils kubernetesUtils;


    @Autowired
    private DebeziumService debeziumService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int create(StreamCdcMenu streamCdcMenu) {
        if (streamCdcMenuMapper.selectByCode(streamCdcMenu.getCode()) != null) {
            throw new ServiceException(Status.STREAM_MENU_ALREADY_EXISTS);
        }
        streamCdcMenu.setId(IdWorker.getId());
        int insert = streamCdcMenuMapper.insertStreamtMenu(streamCdcMenu);
        // 创建菜单边关系
        if (insert > 0 && streamCdcMenu.getPid() != -1L) {
            // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
            streamCdcMenuMapper.createStreamMenuEdge(streamCdcMenu.getPid(), Arrays.asList(streamCdcMenu.getId()));
        }
        return insert;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(StreamCdcMenu streamCdcMenu) {
        VlabelItem<StreamCdcMenu> vlabelItem = streamCdcMenuMapper.selectById(streamCdcMenu.getId());
        if (vlabelItem == null) {
            throw new ServiceException(Status.STREAM_MENU_DOES_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(streamCdcMenu.getCode()) && streamCdcMenuMapper.checkCodeIsExists(streamCdcMenu.getId(), streamCdcMenu.getCode()) != null) {
            throw new ServiceException(Status.STREAM_MENU_ALREADY_EXISTS);
        }
        int update = streamCdcMenuMapper.updateById(streamCdcMenu);
        if (update > 0) {
            // 获取修改前的菜单pid,并删除修改前的边关系
            streamCdcMenuMapper.deleteStreamMenuEdge(Arrays.asList(streamCdcMenu.getId()), vlabelItem.getProperties().getPid());
            // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
            if (streamCdcMenu.getPid() != -1L) {
                streamCdcMenuMapper.createStreamMenuEdge(streamCdcMenu.getPid(), Arrays.asList(streamCdcMenu.getId()));
            }
        }
        return update;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int delete(List<Long> ids) {
        for (Long id : ids) {
            VlabelItem<StreamCdcMenu> vlabelItem = streamCdcMenuMapper.selectById(id);
            if (vlabelItem == null) {
                continue;
            }
            StreamCdcMenu streamMenu = vlabelItem.getProperties();
            // 获取当前被删除菜单的所有子级菜单
            List<Long> menuIdList = streamCdcMenuMapper.selectIdsByPid(id);
            // 如果存在子级菜单，则将所有子级菜单的pid修改为被删除菜单的pid
            if (CollectionUtils.isNotEmpty(menuIdList)) {
                int update = streamCdcMenuMapper.bacthUpdatePidById(menuIdList, streamMenu.getPid());
                if (update > 0 && streamMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    streamCdcMenuMapper.createStreamMenuEdge(streamMenu.getPid(), menuIdList);
                }
            }

            // 获取当前被删除菜单的所有子级
            List<Long> elementIdList = streamCdcMapper.selectIdsByPid(id);
            // 如果存在子级，则将所有子级的pid修改为被删除菜单的pid
            if (CollectionUtils.isNotEmpty(elementIdList)) {
                int update = streamCdcMapper.bacthUpdatePidById(elementIdList, streamMenu.getPid());
                if (update > 0 && streamMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    streamCdcMapper.createStreamAndMenuEdge(streamMenu.getPid(), elementIdList);
                }
            }
        }
        // 删除菜单并且删除菜单的边关系
        return streamCdcMenuMapper.deleteBatchIds(ids);
    }

    @Override
    public PageDataInfo<StreamCdc> overview(Long pid, String sort, SearchVo searchVo) {
        // 获取分页参数
        Page<StreamCdc> page = PageUtils.getPage(StreamCdc.class);
        log.info("streamCdcMenuMapper overview begin");
        IPage<StreamCdc> dataEntityIPage = streamCdcMenuMapper.overview(page, pid, sort, searchVo);
        log.info("streamCdcMenuMapper overview end");
        List<StreamCdc> records = dataEntityIPage.getRecords();
        for (StreamCdc streamCdc : records) {
            /*String podStatus = kubernetesUtils.getPodStatus(streamCdc.getCode());
            streamCdc.setStatus(podStatus);*/
            // 查询 dbz 任务状态
            int running = 0;
            int failed = 0;
            if (StringUtils.isNotEmpty(streamCdc.getTaskInfo())) {
                if (streamCdc.getType().equalsIgnoreCase(DbType.DORIS.getCode())) {
                    streamCdc.setTaskStatus(streamCdcService.showDorisStatus(streamCdc));
                } else {
                    try {
                        JSONObject jsonObject = JSON.parseObject(streamCdc.getTaskInfo());
                        String name = jsonObject.getString("name");
                        name = getName(name);
                        jsonObject.put("name", name);
                        log.info("streamCdcMenuMapper getConnectorStatus begin");
                        JSONObject re = debeziumService.getConnectorStatus(name);
                        log.info("streamCdcMenuMapper getConnectorStatus end");
                        streamCdc.setStatus(re.getJSONObject("connector").getString("state"));
                        List<JSONObject> tasks = JSONArray.parseArray(JSONObject.toJSONString(re.get("tasks")), JSONObject.class);
                        for (JSONObject objest : tasks) {
                            String state = objest.getString("state");
                            if ("RUNNING".equals(state)) {
                                running++;
                            } else if ("FAILED".equals(state)) {
                                failed++;
                            }
                        }
                        JSONObject status = new JSONObject();
                        status.put("RUNNING", running);
                        status.put("FAILED", failed);
                        status.put("EXECUTE_INFO", tasks);
                        streamCdc.setTaskStatus(status);
                    } catch (Exception e) {
                        log.error("debezium服务未找到！或者没有上线该dbz");
                    }
                }
            }
        }
        return PageUtils.getPageInfo(records, (int) dataEntityIPage.getTotal());
    }

    @Override
    public List<StreamCdcTree> tree(String sort, String code, Boolean isQueryNode) {
        List<StreamCdcTree> trees = new ArrayList<>();
        // 添加数据元素树
        if (isQueryNode) {
            log.info("menu selectTree begin");
            trees.addAll(streamCdcMapper.selectTree(sort, code));
            log.info("menu selectTree end");
        }
        log.info("menu selectMenuTree begin");
        // 添加数据元素菜单树
        trees.addAll(streamCdcMenuMapper.selectMenuTree(sort, null));
        log.info("menu selectMenuTree end");
        log.info("menu TreeUtils.getTree begin");
        // 递归成树结构
        List<StreamCdcTree> treeList = (List<StreamCdcTree>) TreeUtils.getTree(trees);
        // 条件查找删除空集合的菜单
        if (StringUtils.isNotEmpty(code) && isQueryNode) {
            TreeUtils.removeEmptyChilderAndMenu(treeList);
        }
        log.info("menu TreeUtils.getTree end");
        return treeList;
    }


    @Override
    public Integer querySerialNumber(Boolean menuFlag) {
        if (menuFlag) {
            return streamCdcMenuMapper.querySerialNumber() * serialIntervalNumber;
        } else {
            return streamCdcMapper.querySerialNumber() * serialIntervalNumber;
        }

    }


    private String getName(String name) {
        name = name.replaceAll("_", "");
        name = "debezium" + name;
        name = name.toLowerCase();
        return name;
    }

}
