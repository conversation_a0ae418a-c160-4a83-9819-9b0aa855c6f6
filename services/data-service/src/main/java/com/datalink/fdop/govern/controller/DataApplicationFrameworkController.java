package com.datalink.fdop.govern.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.govern.api.domain.DataApplicationFramework;
import com.datalink.fdop.govern.api.domain.DataApplicationFrameworkTree;
import com.datalink.fdop.govern.service.IDataApplicationFrameworkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RequestMapping(value = "/govern/application/framework")
@RestController
@Api(tags = "应用框架api")
public class DataApplicationFrameworkController {

    @Autowired
    private IDataApplicationFrameworkService applicationFrameworkService;

    @ApiOperation("创建应用框架")
    @Log(title = "应用框架", businessType = BusinessType.INSERT)
    @PostMapping(value = "/create")
    public R create(@Validated @RequestBody DataApplicationFramework applicationFramework) {
        return R.toResult(applicationFrameworkService.create(applicationFramework));
    }

    @ApiOperation("修改应用框架")
    @Log(title = "应用框架", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public R update(@RequestBody DataApplicationFramework applicationFramework) {
        return R.toResult(applicationFrameworkService.update(applicationFramework));
    }

    @ApiOperation("删除应用框架")
    @Log(title = "应用框架", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.DATA_APP_TO_DELETE);
        }
        return R.toResult(applicationFrameworkService.delete(ids));
    }

    @ApiOperation("根据id查询应用框架信息")
    @Log(title = "应用框架")
    @GetMapping(value = "/selectById/{id}")
    public R<DataApplicationFramework> selectById(@PathVariable("id") Long id) {
        return R.ok(applicationFrameworkService.selectById(id));
    }

    @ApiOperation(value = "根据code查询应用框架信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "应用框架编码", required = true, dataType = "String", paramType = "query"),
    })
    @GetMapping(value = "/selectByCode")
    @Log(title = "应用框架")
    public R<DataApplicationFramework> selectByCode(@RequestParam(value = "code") String code) {
        return R.ok(applicationFrameworkService.selectByCode(code));
    }

    @ApiOperation("应用框架树结构")
    @Log(title = "应用框架")
    @GetMapping(value = "/tree")
    public R<List<DataApplicationFrameworkTree>> treeList(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestParam(required = false) String code,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String description) {
        return R.ok(applicationFrameworkService.treeList(sort, code, name, description));
    }

    @ApiOperation("总览")
    @Log(title = "应用框架")
    @PostMapping(value = "/overview")
    public R<PageDataInfo<DataApplicationFramework>> overview(@RequestParam(value = "pid", defaultValue = "-1", required = false) Long pid,
                                                          @RequestParam(value = "sort", defaultValue = "ASC", required = false) String sort,
                                                          @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(applicationFrameworkService.overview(pid, sort, searchVo));
    }

    @ApiOperation("查询应用框架数据（下拉框）")
    @Log(title = "应用框架")
    @PostMapping(value = "/selectVoList")
    public R<List<SelectVo>> selectVoList(@RequestBody(required = false) DataApplicationFramework applicationFramework) {
        return R.ok(applicationFrameworkService.selectVoList(applicationFramework));
    }


    @ApiOperation("复制应用框架")
    @Log(title = "应用框架", businessType = BusinessType.INSERT)
    @PostMapping(value = "/copy/{pid}")
    public R copy(@PathVariable("pid") Long pid, @Validated @RequestBody List<DataApplicationFramework> processFrameworkList) {
        return R.ok(applicationFrameworkService.copy(pid, processFrameworkList));
    }

}
