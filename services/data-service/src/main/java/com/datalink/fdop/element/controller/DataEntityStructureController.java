package com.datalink.fdop.element.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.idempotent.annotation.RepeatSubmit;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.element.api.domain.DataEntityRelation;
import com.datalink.fdop.element.api.domain.DataEntityStructure;
import com.datalink.fdop.element.api.enums.EntityType;
import com.datalink.fdop.element.api.model.dto.DataEntityStructureDto;
import com.datalink.fdop.element.api.model.vo.DataElementStructureVo;
import com.datalink.fdop.element.api.model.vo.DataEntityStructureVo;
import com.datalink.fdop.element.service.IDataEntityStructureService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
@RequestMapping(value = "/element/entity/structure/")
@RestController
@Api(tags = "数据实体结构api")
public class DataEntityStructureController extends BaseController {

    @Autowired
    private IDataEntityStructureService dataEntityStructureService;

    @ApiOperation("实体创建列(只新增自定义的列)")
    @Log(title = "数据元素", businessType = BusinessType.UPDATE)
    @RepeatSubmit(interval = 1000)
    @PostMapping(value = "/{dataEntityId}/create")
    public R create(@PathVariable(value = "dataEntityId") Long dataEntityId, @Validated @RequestBody DataEntityStructure dataEntityStructure) {
        dataEntityStructure.setDataEntityId(dataEntityId);
        return R.toResult(dataEntityStructureService.create(dataEntityStructure));
    }

    @ApiOperation("实体批量创建列(只新增自定义的列)")
    @Log(title = "数据元素", businessType = BusinessType.UPDATE)
    @RepeatSubmit(interval = 1000)
    @PostMapping(value = "/{dataEntityId}/batchCreate")
    public R batchCreate(@PathVariable(value = "dataEntityId") Long dataEntityId, @Validated @RequestBody List<DataEntityStructure> dataEntityStructureList) {
        return R.toResult(dataEntityStructureService.batchCreate(dataEntityId, dataEntityStructureList));
    }

    @ApiOperation("实体修改列和删除列前需要校验被哪些etl任务引用")
    @Log(title = "数据元素")
    @PostMapping(value = "/{dataEntityId}/checkEntityStructure")
    public R checkEntityStructure(@PathVariable(value = "dataEntityId") Long dataEntityId, @RequestBody List<DataEntityStructureVo> dataEntityStructureVoList) {
        return R.ok(dataEntityStructureService.checkEntityStructure(dataEntityId, dataEntityStructureVoList));
    }

    @ApiOperation("实体修改列(只能修改自定义的列)")
    @Log(title = "数据元素", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/{dataEntityId}/update")
    public R update(@PathVariable(value = "dataEntityId") Long dataEntityId, @Validated @RequestBody DataEntityStructure dataEntityStructure) {
        dataEntityStructure.setDataEntityId(dataEntityId);
        return R.toResult(dataEntityStructureService.update(dataEntityStructure));
    }

    @ApiOperation("实体删除列")
    @Log(title = "数据元素", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/{dataEntityId}/delete")
    public R delete(@PathVariable(value = "dataEntityId") Long dataEntityId, @RequestBody List<DataEntityStructureVo> dataEntityStructureVoList) {
        if (CollectionUtils.isEmpty(dataEntityStructureVoList)) {
            throw new ServiceException(Status.PLEASE_SPECIFY_THE_ENTITY_FIELD_TO_DELETE);
        }
        return R.toResult(dataEntityStructureService.delete(dataEntityId, dataEntityStructureVoList));
    }

    @ApiOperation("实体添加预定义列")
    @Log(title = "数据元素", businessType = BusinessType.INSERT)
    @PostMapping(value = "/{dataEntityId}/addPredefined")
    public R addPredefined(@PathVariable(value = "dataEntityId") Long dataEntityId, @RequestBody List<DataElementStructureVo> dataElementStructureVoList) {
        return R.toResult(dataEntityStructureService.addPredefined(dataEntityId, dataElementStructureVoList));
    }

    @ApiOperation("查询数据实体的结构")
    @Log(title = "数据元素")
    @GetMapping(value = "/{dataEntityId}/selectStructureById")
    public R<List<DataEntityStructureVo>> selectStructureById(@PathVariable(value = "dataEntityId") Long dataEntityId, @RequestParam(value = "isSort", required = false, defaultValue = "true") Boolean isSort) {
        List<DataEntityStructureVo> dataEntityStructureList = dataEntityStructureService.selectStructureById(dataEntityId, isSort);
        return R.ok(dataEntityStructureList);
    }

    @ApiOperation("根据租户id查询数据实体的结构")
    @GetMapping(value = "/{dataEntityId}/selectStructureByIdAndTenantId/{tenantId}")
    public R<List<DataEntityStructureVo>> selectStructureByIdAndTenantId(@PathVariable(value = "tenantId") Long tenantId, @PathVariable(value = "dataEntityId") Long dataEntityId, @RequestParam(value = "isSort", required = false, defaultValue = "true") Boolean isSort) {
        List<DataEntityStructureVo> dataEntityStructureList = dataEntityStructureService.selectStructureByIdAndTenantId(tenantId, dataEntityId, isSort);
        return R.ok(dataEntityStructureList);
    }

    @ApiOperation("查询数据实体的结构分页")
    @Log(title = "数据元素")
    @PostMapping(value = "/{dataEntityId}/selectStructureById/paging")
    public R selectStructureByIdPaging(@PathVariable(value = "dataEntityId") Long dataEntityId,
                                       @RequestParam(value = "sort", required = false, defaultValue = "ASC") String sort,
                                       @RequestBody DataEntityStructureDto dataEntityStructureDto) {
        return R.ok(dataEntityStructureService.selectStructureByIdPaging(dataEntityId, sort, dataEntityStructureDto));
    }

    @ApiOperation("导入实体的表结构")
    @Log(title = "数据元素", businessType = BusinessType.IMPORT)
    @PostMapping("/{dataEntityId}/importEntity")
    @ResponseBody()
    @RepeatSubmit(interval = 1000)
    public R importEntity(MultipartFile file, @PathVariable(value = "dataEntityId") Long dataEntityId) {
        dataEntityStructureService.importEntity(file, dataEntityId);
        return R.ok();
    }

    @ApiOperation("导出实体的表结构")
    @Log(title = "数据元素", businessType = BusinessType.EXPORT)
    @PostMapping("/exportEntity")
    @ResponseBody()
    @RepeatSubmit(interval = 1000)
    public void exportEntity(HttpServletResponse response,
                             @RequestBody List<Long> dataEntityIds,
                             @RequestParam(value = "isExportData") Boolean isExportData) {
        dataEntityStructureService.exportEntity(response, dataEntityIds, isExportData);
    }


    @ApiOperation("查询实体表的关联字段")
    @Log(title = "数据元素")
    @GetMapping(value = "/{dataEntityId}/selectEntityMappingField")
    public R<List<DataEntityStructureVo>> selectEntityMappingField(@PathVariable(value = "dataEntityId") Long dataEntityId) {
        return R.ok(dataEntityStructureService.selectEntityMappingField(dataEntityId));
    }

    @ApiOperation("根据资产创建结构字段")
    @Log(title = "实体结构")
    @PostMapping("/createByL5/{dataEntityId}")
    public R createByL5(@PathVariable(value = "dataEntityId") Long dataEntityId,
                        @RequestBody List<Long> ids) {
        return R.ok(dataEntityStructureService.createByL5(dataEntityId, ids));
    }

    @ApiOperation("新建模型关系")
    @Log(title = "实体结构")
    @PostMapping("/createEntityRelation")
    public R createEntityRelation(@RequestBody List<DataEntityRelation> dataEntityRelations) {
        return R.ok(dataEntityStructureService.createEntityRelation(dataEntityRelations));
    }

    @ApiOperation("更新模型关系")
    @Log(title = "实体结构")
    @PostMapping("/updateEntityRelation")
    public R updateEntityRelation(@RequestBody List<DataEntityRelation> dataEntityRelations) {
        return R.ok(dataEntityStructureService.updateEntityRelation(dataEntityRelations));
    }

    @ApiOperation("删除模型关系")
    @Log(title = "实体结构")
    @PostMapping("/deleteEntityRelation")
    public R deleteEntityRelation(@RequestBody List<Long> ids) {
        return R.ok(dataEntityStructureService.deleteEntityRelation(ids));
    }

    @ApiOperation("查询模型关系")
    @Log(title = "实体结构")
    @PostMapping("/queryEntityRelation/{dataEntityId}")
    public R queryEntityRelation(@PathVariable(value = "dataEntityId") Long dataEntityId,
                                 @RequestParam(value = "entityType") EntityType entityType,
                                 @RequestBody DataEntityRelation dataEntityRelation) {
        return R.ok(dataEntityStructureService.queryEntityRelation(dataEntityId, entityType, dataEntityRelation));
    }

}
