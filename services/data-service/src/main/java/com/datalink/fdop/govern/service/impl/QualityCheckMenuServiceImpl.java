package com.datalink.fdop.govern.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.govern.api.domain.QualityCheck;
import com.datalink.fdop.govern.api.domain.QualityCheckMenu;
import com.datalink.fdop.govern.api.domain.QualityCheckMenuTree;
import com.datalink.fdop.govern.api.domain.QualityCheckPlan;
import com.datalink.fdop.govern.mapper.QualityCheckMapper;
import com.datalink.fdop.govern.mapper.QualityCheckMenuMapper;
import com.datalink.fdop.govern.mapper.QualityCheckPlanMapper;
import com.datalink.fdop.govern.service.QualityCheckMenuService;
import com.datalink.fdop.govern.service.QualityCheckService;
import com.datalink.fdop.govern.utils.TreeUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class QualityCheckMenuServiceImpl implements QualityCheckMenuService {

    @Autowired
    private QualityCheckMenuMapper qualityCheckMenuMapper;

    @Autowired
    private QualityCheckPlanMapper qualityCheckPlanMapper;

    @Autowired
    private QualityCheckMapper qualityCheckMapper;

    @Autowired
    private QualityCheckService createQualityCheckService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public int create(QualityCheckMenu SeaTunnelCmdMenu) {
        if (qualityCheckMenuMapper.selectByCode(SeaTunnelCmdMenu.getCode()) != null) {
            throw new ServiceException(Status.SEATUNNEL_CMD_MENU_ALREADY_EXISTS);
        }
        SeaTunnelCmdMenu.setId(IdWorker.getId());
        int insert = qualityCheckMenuMapper.insertSeaTunnelCmdMenu(SeaTunnelCmdMenu);
        // 创建菜单边关系
        if (insert > 0 && SeaTunnelCmdMenu.getPid() != -1L) {
            // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
            qualityCheckMenuMapper.createSeaTunnelCmdMenuEdge(SeaTunnelCmdMenu.getPid(), Arrays.asList(SeaTunnelCmdMenu.getId()));
        }
        return insert;
    }

    @Override
    public int createPlan(QualityCheckPlan qualityCheckPlan) {
        if (qualityCheckPlanMapper.selectByCode(qualityCheckPlan.getCode()) != null) {
            throw new ServiceException(Status.SEATUNNEL_CMD_MENU_ALREADY_EXISTS);
        }
        qualityCheckPlan.setId(IdWorker.getId());
        int insert = qualityCheckPlanMapper.insertQualityCheckPlan(qualityCheckPlan);
        // 创建菜单边关系
        if (insert > 0 && qualityCheckPlan.getPid() != -1L) {
            // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
            qualityCheckMenuMapper.createMenuAndPlanEdge(qualityCheckPlan.getPid(), Arrays.asList(qualityCheckPlan.getId()));
        }
        return insert;
    }


    @Override
    public int update(QualityCheckMenu SeaTunnelCmdMenu) {
        return 0;
    }


    @Override
    public List<QualityCheckMenuTree> tree(String sort, String code, Boolean isQueryNode) {
        List<QualityCheckMenuTree> trees = new ArrayList<>();
        if (isQueryNode) {
            trees.addAll(qualityCheckMapper.selectTree(sort, code));
        }
        trees.addAll(qualityCheckMenuMapper.selectMenuTree(sort, null));
        trees.addAll(qualityCheckPlanMapper.selectMenuTree(sort, null));
        // 递归成树结构
        List<QualityCheckMenuTree> treeList = (List<QualityCheckMenuTree>) TreeUtils.getTree(trees);
        // 条件查找删除空集合的菜单
        if (StringUtils.isNotEmpty(code) && isQueryNode) {
            TreeUtils.removeEmptyChilderAndMenu(treeList);
        }
        return treeList;
    }

    @Override
    public int delete(List<Long> ids) {
        for (Long id : ids) {
            VlabelItem<QualityCheckMenu> vlabelItem = qualityCheckMenuMapper.selectById(id);
            if (vlabelItem == null) {
                continue;
            }
            QualityCheckMenu SeaTunnelCmdMenu = vlabelItem.getProperties();
            // 获取当前被删除菜单的所有子级菜单
            List<Long> menuIdList = qualityCheckMenuMapper.selectIdsByPid(id);
            // 如果存在子级菜单，则将所有子级菜单的pid修改为被删除菜单的pid
            if (CollectionUtils.isNotEmpty(menuIdList)) {
                int update = qualityCheckMenuMapper.bacthUpdatePidById(menuIdList, SeaTunnelCmdMenu.getPid());
                if (update > 0 && SeaTunnelCmdMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    qualityCheckMenuMapper.createSeaTunnelCmdMenuEdge(SeaTunnelCmdMenu.getPid(), menuIdList);
                }
            }
            // 获取当前被删除菜单的所有子级
            List<Long> elementIdList = qualityCheckMenuMapper.selectIdsByPid(id);
            // 如果存在子级，则将所有子级的pid修改为被删除菜单的pid
            if (CollectionUtils.isNotEmpty(elementIdList)) {
                int update = qualityCheckMapper.bacthUpdatePidById(elementIdList, SeaTunnelCmdMenu.getPid());
                if (update > 0 && SeaTunnelCmdMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    qualityCheckMapper.createSeaTunnelCmdAndMenuEdge(SeaTunnelCmdMenu.getPid(), elementIdList);
                }
            }
        }
        // 删除菜单并且删除菜单的边关系
        return qualityCheckMenuMapper.deleteBatchIds(ids);
    }

    @Override
    public PageDataInfo<QualityCheckMenu> overview(Long pid, String sort, SearchVo searchVo) {
        // 获取分页参数
        Page<QualityCheckMenu> page = PageUtils.getPage(QualityCheckMenu.class);
        IPage<QualityCheckMenu> SeaTunnelCmdIpage = qualityCheckMenuMapper.overview(page, pid, sort, searchVo);
        List<QualityCheckMenu> records = SeaTunnelCmdIpage.getRecords();
        return PageUtils.getPageInfo(records, (int) SeaTunnelCmdIpage.getTotal());
    }


    @Override
    public PageDataInfo<QualityCheck> overviewNode(Long pid, String sort, SearchVo searchVo) {
        // 获取分页参数
        Page<QualityCheck> page = PageUtils.getPage(QualityCheck.class);
        IPage<QualityCheck> SeaTunnelCmdIpage = qualityCheckMenuMapper.overviewNode(page, pid, sort, searchVo);
        List<QualityCheck> records = SeaTunnelCmdIpage.getRecords();
        return PageUtils.getPageInfo(records, (int) SeaTunnelCmdIpage.getTotal());
    }


}
