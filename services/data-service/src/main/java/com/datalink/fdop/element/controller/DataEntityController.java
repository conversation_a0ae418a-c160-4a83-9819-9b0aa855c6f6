package com.datalink.fdop.element.controller;

import com.alibaba.fastjson.JSONObject;
import com.datalink.fdop.common.core.ExportJSONFileUtil;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.idempotent.annotation.RepeatSubmit;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.drive.api.domain.dto.Field;
import com.datalink.fdop.element.api.domain.DataEntity;
import com.datalink.fdop.element.api.domain.DataEntitySynLog;
import com.datalink.fdop.element.api.domain.DataEntityTableVo;
import com.datalink.fdop.element.api.model.DataEntityTree;
import com.datalink.fdop.element.api.model.vo.DataEntityCopyVo;
import com.datalink.fdop.element.api.model.vo.DataEntitySynVo;
import com.datalink.fdop.element.api.model.vo.DataJsonEntityVo;
import com.datalink.fdop.element.service.IDataEntityService;
import com.datalink.fdop.element.service.IDataEntitySynLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import com.datalink.fdop.common.core.utils.DateUtils;
import com.datalink.fdop.element.service.impl.DataEntityServiceImpl;
import java.util.ArrayList;
import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
@RequestMapping(value = "/element/entity")
@RestController
@Api(tags = "数据实体api")
@Slf4j
public class DataEntityController extends BaseController {

    @Autowired
    private IDataEntityService dataEntityService;

    @Autowired
    private IDataEntitySynLogService dataEntitySynLogService;

    @ApiOperation("创建数据实体")
    @Log(title = "实体", businessType = BusinessType.INSERT)
    @RepeatSubmit(interval = 1000)
    @PostMapping(value = "/create")
    public R create(@Validated @RequestBody DataEntity dataEntity) {
        return R.ok(dataEntityService.create(dataEntity));
    }

    @ApiOperation("通过sql创建数据实体")
    @Log(title = "实体", businessType = BusinessType.INSERT)
    @RepeatSubmit(interval = 1000)
    @PostMapping(value = "/createBysql")
    public R createBysql(@Validated @RequestBody DataEntity dataEntity) {
        dataEntityService.createBySql(dataEntity);
        return R.ok();
    }

    @ApiOperation("同步数据实体")
    @Log(title = "实体", businessType = BusinessType.INSERT)
    @PostMapping(value = "/synchronize")
    @RepeatSubmit(interval = 3000)
    public R synchronize(@RequestBody DataEntitySynVo dataEntitySyn) {
        dataEntitySynLogService.synchronize(dataEntitySyn);
        return R.ok("同步中，请移步日志");
    }

    @ApiOperation("同步数据实体以及创表")
    @Log(title = "实体", businessType = BusinessType.INSERT)
    @PostMapping(value = "/synchronizeCopyAndCreateTable")
    @RepeatSubmit(interval = 3000)
    public R synchronizeCopyAndCreateTable(@RequestBody DataEntitySynVo dataEntitySyn) {
        dataEntitySynLogService.synchronizeCopyAndCreateTable(dataEntitySyn);
        return R.ok("同步中，请移步日志");
    }

    @ApiOperation("查询同步日志")
    @Log(title = "实体", businessType = BusinessType.INSERT)
    @GetMapping(value = "/getSynLog")
    public R<PageDataInfo<DataEntitySynLog>> getSynLog(String Code,String status) {
        return R.ok(dataEntitySynLogService.getSynLog(Code,status));
    }

    @ApiOperation("修改数据实体")
    @Log(title = "实体", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public R update(@Validated @RequestBody DataEntity dataEntity) {
        return R.toResult(dataEntityService.update(dataEntity));
    }

    @ApiOperation("复制数据实体")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pid", value = "实体菜单id", required = true, dataType = "Long", paramType = "query", example = "1"),
    })
    @Log(title = "实体", businessType = BusinessType.INSERT)
    @PostMapping(value = "/copy/{pid}")
    public R copy(@PathVariable(value = "pid") Long pid, @Validated @RequestBody List<DataEntityCopyVo> dataEntityCopyList) {
        dataEntityService.copy(pid, dataEntityCopyList);
        return R.ok();
    }

    @ApiOperation("删除数据实体同步日志")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "实体同步日志id集合", required = true, dataType = "Long", allowMultiple = true, paramType = "body", example = "[1,2]"),
    })
    @Log(title = "实体", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/deleteSynLog")
    public R deleteSynLog(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.PLEASE_SPECIFY_THE_ENTITY_LOG_TO_DELETE);
        }
        return R.toResult(dataEntityService.deleteSynLog(ids));
    }

    @ApiOperation("删除数据实体前的校验")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "实体id集合", required = true, dataType = "Long", allowMultiple = true, paramType = "body", example = "[1,2]"),
    })
    @Log(title = "实体", businessType = BusinessType.DELETE)
    @PostMapping(value = "/checkDeleteEntity")
    public R checkDeleteEntity(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.PLEASE_SPECIFY_THE_ENTITY_TO_DELETE);
        }
        return R.ok(dataEntityService.checkDeleteEntity(ids));
    }

    @ApiOperation("删除数据实体")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "isDeleteTable", value = "是否删除实体对应的关联表", required = true, dataType = "Boolean", paramType = "param"),
            @ApiImplicitParam(name = "ids", value = "实体id集合", required = true, dataType = "Long", allowMultiple = true, paramType = "body", example = "[1,2]"),
    })
    @Log(title = "实体", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/delete")
    public R delete(@RequestParam(value = "isDeleteTable", required = false, defaultValue = "false") Boolean isDeleteTable, @RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.PLEASE_SPECIFY_THE_ENTITY_TO_DELETE);
        }
        return R.toResult(dataEntityService.delete(isDeleteTable, ids));
    }

    @Log(title = "删除数据实体", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/batchDelete")
    public R batchDelete(@RequestParam(value = "isDeleteTable", required = false, defaultValue = "false") Boolean isDeleteTable,
                         @RequestBody List<DataEntity> dataEntities) {
        if (CollectionUtils.isEmpty(dataEntities)) {
            throw new ServiceException(Status.PLEASE_SPECIFY_THE_ENTITY_TO_DELETE);
        }
        return R.toResult(dataEntityService.batchDelete(isDeleteTable, dataEntities));
    }

    @ApiOperation("查询数据实体")
    @Log(title = "实体")
    @PostMapping(value = "/list")
    public R<PageDataInfo<DataEntity>> list(@RequestBody(required = false) DataEntity dataEntity) {
        return R.ok(dataEntityService.list(dataEntity));
    }

    @ApiOperation("查询dwd/dim数据实体")
    @Log(title = "实体")
    @PostMapping(value = "/dwdOrDim/list")
    public R<List<DataEntity>> dwdOrDimList(@RequestBody(required = false) DataEntity dataEntity) {
        return R.ok(dataEntityService.dwdOrDimList(dataEntity));
    }

    @ApiOperation("根据实体id查询数据实体")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "实体菜单id", required = true, dataType = "Long", paramType = "path", example = "1"),
    })
    @Log(title = "数据实体")
    @GetMapping(value = "/selectById/{id}")
    public R<DataEntity> selectById(@PathVariable(value = "id") Long id) {
        return R.ok(dataEntityService.selectById(id));
    }

    @ApiOperation("根据实体code查询数据实体")
    @Log(title = "数据实体")
    @GetMapping(value = "/selectByCode")
    public R<DataEntity> selectByCode(@RequestParam String code) {
        return R.ok(dataEntityService.selectByCode(code));
    }

    @ApiOperation("实体菜单、实体、实体关联表的树结构")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "菜单code/实体code/关联表code", required = false, dataType = "String", paramType = "query", example = "a"),
            @ApiImplicitParam(name = "isRead", value = "读取方式", required = false, dataType = "Boolean", paramType = "query", example = "a"),
    })
    @Log(title = "数据实体")
    @GetMapping(value = "/getEntityMenuTableTree")
    public R<List<DataEntityTree>> getEntityMenuTableTree(@RequestParam(value = "code", required = false) String code, @RequestParam(value = "isRead", required = false) Boolean isRead,@RequestParam(value = "dataTableId", required = false)Long dataSourceId) {
        return R.ok(dataEntityService.getEntityMenuTableTree(code, isRead,dataSourceId));
    }

    @ApiOperation("生成实体")
    @ApiImplicitParams({
    })
    @Log(title = "数据实体")
    @RepeatSubmit(interval = 1000)
    @PostMapping(value = "/generateEntity")
    public R<DataEntity> generateEntity(@RequestParam(value = "pid") Long pid,
                                        @RequestParam(value = "code") String code,
                                        @RequestParam(value = "name", required = false) String name,
                                        @RequestParam(value = "description", required = false) String description,
                                        @RequestBody List<Field> fieldList) {
        return R.ok(dataEntityService.generateEntity(pid, code, name, description, fieldList));
    }


    @ApiOperation(value = "实体批量导出")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "需要导出的实体ID集合", required = true, dataTypeClass = Long.class)
    })
    @Log(title = "数据实体")
    @PostMapping(value = "/batch-export")
    public void batchExportDataEntityByIds(@RequestBody List<Long> ids, HttpServletResponse response) {
        dataEntityService.batchExportDataEntityByIds(ids, response);
    }

    @ApiOperation(value = "实体导入")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "需要导入的实体ID集合", required = true, dataTypeClass = Long.class)
    })
    @Log(title = "数据实体")
    @PostMapping(value = "/import")
    public R importDataEntity(@RequestParam("file") MultipartFile file) {
        return dataEntityService.importDataEntity(file);
    }

    @ApiOperation("根据实体id数组和租户id查询实体关联表")
    @PostMapping(value = "/selectDataEntityTableVoByTenantIdAndIds/{tenantId}")
    public R<List<DataEntityTableVo>> selectDataEntityTableVoByTenantIdAndIds(@PathVariable(value = "tenantId") Long tenantId, @RequestBody List<Long> dataEntityIds) {
        return R.ok(dataEntityService.selectDataEntityTableVoByTenantIdAndIds(tenantId, dataEntityIds));
    }

    @ApiOperation("导入JSON数据")
    @Log(title = "实体", businessType = BusinessType.IMPORT)
    @PostMapping(value = "/synImportJson")
    public R synImportJson(@RequestParam("file") MultipartFile file) {
        String jsonContent = ExportJSONFileUtil.file2String(file);

        try {
            List<DataJsonEntityVo> dataJsonEntityList = JSONObject.parseArray(jsonContent, DataJsonEntityVo.class);

            if (dataJsonEntityList == null || dataJsonEntityList.isEmpty()) {
                return R.fail("导入的JSON文件为空或格式错误");
            }

            // 使用多线程批量处理，控制并发数量
            dataEntitySynLogService.batchImportWithConcurrencyControl(dataJsonEntityList);

            return R.ok("导入任务已提交，请移步日志查看详情");
        } catch (Exception e) {
            log.error("导入json文件失败: {}", e.getMessage());
            return R.fail("导入失败: " + e.getMessage());
        }
    }
    
    @ApiOperation("导出JSON数据")
    @Log(title = "实体", businessType = BusinessType.EXPORT)
    @PostMapping(value = "/exportJson")
    public void exportJson(@RequestBody List<Long> ids, HttpServletResponse response) {
        // 设置文件名
        String fileName = "entity_export_" + DateUtils.dateTimeNow() + ".json";
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

        List<DataJsonEntityVo> entityList = dataEntityService.exportEntityJson(ids);

        ExportJSONFileUtil.exportJson(response, entityList);
    }
}
