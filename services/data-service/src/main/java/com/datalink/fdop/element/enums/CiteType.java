package com.datalink.fdop.element.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @date 2022/5/18 13:46
 */
public enum CiteType {

    MENU(0, "MENU"),
    MAIN(1, "MAIN"),
    FIELD(2, "FIELD"),
    INPUT(3, "INPUT"),
    ENTITY(4, "ENTITY"),
    TABLE(5, "TABLE"),
    ;

    private final int code;
    @EnumValue
    private final String desc;

    CiteType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


}
