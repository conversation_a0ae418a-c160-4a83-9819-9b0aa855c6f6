package com.datalink.fdop.element.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.constant.Constants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.MenuType;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.tree.TreeUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.datasource.dynamic.DataSourceHolder;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.drive.api.RemoteDriveService;
import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.element.api.domain.DataEntity;
import com.datalink.fdop.element.api.domain.DataEntityMenu;
import com.datalink.fdop.element.api.domain.DataEntityTable;
import com.datalink.fdop.element.api.model.DataEntityTree;
import com.datalink.fdop.element.api.model.vo.DataEntityHierarchyTreeVo;
import com.datalink.fdop.element.api.model.vo.DataEntityStructureVo;
import com.datalink.fdop.element.mapper.DataEntityMapper;
import com.datalink.fdop.element.mapper.DataEntityMenuMapper;
import com.datalink.fdop.element.mapper.DataEntityTableMapper;
import com.datalink.fdop.element.service.IDataEntityMenuService;
import com.datalink.fdop.element.service.IDataEntityStructureService;
import com.datalink.fdop.element.service.IDataEntityTableService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/12 13:58
 */
@Service
@Slf4j
public class DataEntityMenuServiceImpl implements IDataEntityMenuService {

    @Autowired
    private DataEntityMapper dataEntityMapper;

    @Autowired
    private DataEntityMenuMapper dataEntityMenuMapper;

    @Autowired
    private DataEntityTableMapper dataEntityTableMapper;

    @Autowired
    private IDataEntityStructureService dataEntityStructureService;

    @Autowired
    private IDataEntityTableService dataEntityTableService;


    @Autowired
    private RemoteDriveService remoteDriveService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int create(DataEntityMenu dataEntityMenu) {
        if (dataEntityMenuMapper.selectByCode(dataEntityMenu.getCode()) != null) {
            throw new ServiceException(Status.DATA_ENTITY_MENU_EXIST);
        }
        if (dataEntityMenu.getId() == null || dataEntityMenu.getId() == 0L) {
            dataEntityMenu.setId(IdWorker.getId());
        }
        int insert = dataEntityMenuMapper.insertEntityMenu(dataEntityMenu);
        // 创建菜单边关系
        if (insert > 0 && dataEntityMenu.getPid() != -1L) {
            // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
            dataEntityMenuMapper.createEntityMenuEdge(dataEntityMenu.getPid(), Arrays.asList(dataEntityMenu.getId()));
        }
        return insert;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(DataEntityMenu dataEntityMenu) {
        DataEntityMenu checkDataEntityMenu = dataEntityMenuMapper.selectById(dataEntityMenu.getId());
        if (checkDataEntityMenu == null) {
            throw new ServiceException(Status.DATA_ENTITY_MENU_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(dataEntityMenu.getCode()) && dataEntityMenuMapper.checkCodeIsExists(dataEntityMenu.getId(), dataEntityMenu.getCode()) != null) {
            throw new ServiceException(Status.DATA_ENTITY_MENU_EXIST);
        }
        int update = dataEntityMenuMapper.updateById(dataEntityMenu);
        if (update > 0) {
            // 获取修改前的菜单pid,并删除修改前的边关系
            dataEntityMenuMapper.deleteEntityMenuEdge(Arrays.asList(dataEntityMenu.getId()), checkDataEntityMenu.getPid());
            // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
            if (dataEntityMenu.getPid() != -1L) {
                dataEntityMenuMapper.createEntityMenuEdge(dataEntityMenu.getPid(), Arrays.asList(dataEntityMenu.getId()));
            }
        }
        return update;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateL3(DataEntityMenu dataEntityMenu, Boolean isDelete) {
        DataEntityMenu checkDataEntityMenu = dataEntityMenuMapper.selectById(dataEntityMenu.getId());
        if (checkDataEntityMenu == null) {
            throw new ServiceException(Status.DATA_ENTITY_MENU_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(dataEntityMenu.getCode()) && dataEntityMenuMapper.checkCodeIsExists(dataEntityMenu.getId(), dataEntityMenu.getCode()) != null) {
            throw new ServiceException(Status.DATA_ENTITY_MENU_EXIST);
        }
        if (dataEntityMenu.getL3Id() != null) {
            List<VlabelItem<DataEntityMenu>> vlabelItemList = dataEntityMenuMapper.selectByL3Id(dataEntityMenu.getL3Id());
            List<DataEntityMenu> menus = vlabelItemList.stream().map(VlabelItem::getProperties).collect(Collectors.toList());
            if (menus.size() > 1) {
                throw new ServiceException("l3已被其他实体引用，编码为：" + JSONObject.toJSONString(menus.stream().filter(e->!e.getId().equals(dataEntityMenu.getId())).map(DataEntityMenu::getCode).collect(Collectors.toList())));
            } else if (menus.size() == 1 && menus.get(0).getId().equals(dataEntityMenu.getId())) {
                throw new ServiceException("l3已被其他实体引用，编码为：" + menus.get(0).getCode());
            }
        }
        int update = dataEntityMenuMapper.updateById(dataEntityMenu);
        if (update > 0) {
            // 获取修改前的菜单pid,并删除修改前的边关系
            dataEntityMenuMapper.deleteEntityMenuEdge(Arrays.asList(dataEntityMenu.getId()), checkDataEntityMenu.getPid());
            // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
            if (dataEntityMenu.getPid() != -1L) {
                dataEntityMenuMapper.createEntityMenuEdge(dataEntityMenu.getPid(), Arrays.asList(dataEntityMenu.getId()));
            }
        }
        Map<Long, List<DataEntityStructureVo>> map = new HashMap<>();
        List<Long> ids = dataEntityMapper.selectIdsByPid(dataEntityMenu.getId());
        for (Long id : ids) {
            List<DataEntityStructureVo> dataEntityStructureVos = dataEntityStructureService.selectStructureById(id, true);
            if (CollectionUtils.isNotEmpty(dataEntityStructureVos)) {
                map.put(id, dataEntityStructureVos);
            }
        }
        if (CollectionUtils.isNotEmpty(map.keySet())) {
            if (isDelete != null && isDelete) {
                for (Long id : map.keySet()) {
                    dataEntityStructureService.delete(id, map.get(id));
                }
            } else {
                List<Long> structureIds = new ArrayList<>();
                for (Long id : map.keySet()) {
                    structureIds.addAll(map.get(id).stream().map(DataEntityStructureVo::getId).collect(Collectors.toList()));
                }
                dataEntityStructureService.deleteL5Id(structureIds);
            }
        }
        return update;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int delete(List<Long> ids) {
        for (Long id : ids) {
            DataEntityMenu dataEntityMenu = dataEntityMenuMapper.selectById(id);
            if (dataEntityMenu == null) {
                continue;
            }
            // 获取当前被删除菜单的所有子级菜单
            List<Long> menuIdList = dataEntityMenuMapper.selectIdsByPid(id);
            // 如果存在子级菜单，则将所有子级菜单的pid修改为被删除菜单的pid
            if (CollectionUtils.isNotEmpty(menuIdList)) {
                int update = dataEntityMenuMapper.bacthUpdatePidById(menuIdList, dataEntityMenu.getPid());
                if (update > 0 && dataEntityMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    dataEntityMenuMapper.createEntityMenuEdge(dataEntityMenu.getPid(), menuIdList);
                }
            }

            // 获取当前被删除菜单的所有子级
            List<Long> entityIdList = dataEntityMapper.selectIdsByPid(id);
            // 如果存在子级，则将所有子级的pid修改为被删除菜单的pid
            if (CollectionUtils.isNotEmpty(entityIdList)) {
                int update = dataEntityMapper.batchUpdatePidById(entityIdList, dataEntityMenu.getPid());
                if (update > 0 && dataEntityMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    dataEntityMapper.createEntityAndMenuEdge(dataEntityMenu.getPid(), entityIdList);
                }
            }
        }
        // 删除菜单并且删除菜单的边关系
        return dataEntityMenuMapper.deleteBatchIds(ids);
    }

    @Override
    public List<DataEntityTree> tree(String sort, String code, Boolean isQueryNode, Long dataSourceId, Boolean filterReadOnly) {
        if (StringUtils.isNotEmpty(code)) {
            code = code.toLowerCase();
        }
        // 所有的数据集合
        List<DataEntityTree> trees = new ArrayList<>();
        // 添加节点数据
        if (isQueryNode) {
            trees.addAll(dataEntityMapper.selectNodeTree(sort, code, dataSourceId));
        }
        // 添加菜单数据
        trees.addAll(dataEntityMenuMapper.selectMenuTree(sort, null));
        // 递归成树结构
        List<DataEntityTree> treeList = (List<DataEntityTree>) getTree(trees, filterReadOnly);
        // 条件查找删除空集合的菜单
        if (StringUtils.isNotEmpty(code) && isQueryNode) {
            TreeUtils.removeEmptyChilderAndMenu(treeList);
        }
        return treeList;
    }

    @Override
    public List<DataEntityTree> readMarkedtree(String sort, String code, Boolean isQueryNode, Long dataSourceId, Boolean filterReadOnly, Boolean read) {
        if (StringUtils.isNotEmpty(code)) {
            code = code.toLowerCase();
            log.info("------code:{}--------", code);
        }
        List<DataSource> dataSources = remoteDataSourceList();
        // 所有的数据集合
        List<DataEntityTree> trees = new ArrayList<>();
        if (isQueryNode) {
            trees.addAll(dataEntityMapper.selectNodeTree(sort, code, dataSourceId));
        }
        trees.addAll(dataEntityMenuMapper.selectMenuTree(sort, null));
        // 递归成树结构
        List<DataEntityTree> treeList = (List<DataEntityTree>) getMarkTree(trees, filterReadOnly, read);
        // 条件查找删除空集合的菜单
        if (StringUtils.isNotEmpty(code) && isQueryNode) {
            TreeUtils.removeEmptyChilderAndMenu(treeList);
        }
        return treeList;
    }

    @Override
    public PageDataInfo<DataEntity> overview(Long pid, String sort, SearchVo searchVo) {
        // 获取分页参数
        Page<DataEntity> page = PageUtils.getPage(DataEntity.class);
        IPage<DataEntity> dataEntityIPage = dataEntityMapper.overview(page, pid, sort, searchVo);
        // 获取数据源列表
        List<DataSource> dataSources = remoteDataSourceList();
        List<DataEntity> entities = dataEntityIPage.getRecords();
        for (DataEntity entity : entities) {
            Long tableId = entity.getTableId();
            if (tableId != null) {
                DataEntityTable dataEntityTable = dataEntityTableService.checkDataEntityTable(tableId);
                if (dataEntityTable != null) {
                    // 查询数据源信息
                    Long dataSourceId = dataEntityTable.getDataSourceId();
                    dataSources.stream().filter(dataSource -> dataSource.getId()
                            .equals(dataSourceId)).findFirst().ifPresent(dataSource -> entity.setIsRead(dataSource.getIsRead()));
                }
            }
        }
        return PageUtils.getPageInfo(dataEntityIPage.getRecords(), (int) dataEntityIPage.getTotal());
    }

    @Override
    public List<DataEntity> list(Long dataEntityId) {
        List<DataEntity> entities = dataEntityMapper.list(dataEntityId);
        List<DataSource> dataSources = remoteDataSourceList();
        for (DataEntity entity : entities) {
            Long tableId = entity.getTableId();
            if (tableId != null) {
                DataEntityTable dataEntityTable = dataEntityTableService.checkDataEntityTable(tableId);
                if (dataEntityTable != null) {
                    // 查询数据源信息
                    Long dataSourceId = dataEntityTable.getDataSourceId();
                    dataSources.stream().filter(dataSource -> dataSource.getId()
                            .equals(dataSourceId)).findFirst().ifPresent(dataSource -> entity.setIsRead(dataSource.getIsRead()));
                }
            }
        }
        return entities;
    }

    @Override
    public List<DataEntityHierarchyTreeVo> selectEntityHierarchyTree(Long dataSourceId, String code) {
        // 所有的数据集合
        List<DataEntityHierarchyTreeVo> trees = new ArrayList<>();

        // 添加数据元素树 (过滤空实体)
        if (StringUtils.isNotEmpty(code)) {
            code = code.toLowerCase();
        }

        // 添加数据实体菜单树
        List<DataEntityTree> dataEntityMenuTreeList = dataEntityMenuMapper.selectMenuTree("ASC", code);
        if (CollectionUtils.isNotEmpty(dataEntityMenuTreeList)) {
            trees.addAll(dataEntityMenuTreeList.stream().map(dataEntityTree -> {
                DataEntityHierarchyTreeVo dataEntityHierarchyTreeVo = new DataEntityHierarchyTreeVo();
                BeanUtils.copyProperties(dataEntityTree, dataEntityHierarchyTreeVo);
                return dataEntityHierarchyTreeVo;
            }).collect(Collectors.toList()));
        }

        // 添加数据实体树
        List<DataEntityTree> dataEntityTreeList = dataEntityMapper.selectNodeTree("ASC", code, dataSourceId);
        if (CollectionUtils.isNotEmpty(dataEntityTreeList)) {
            List<DataEntityHierarchyTreeVo> dataEntityMenuTableVoList = dataEntityTreeList.stream().map(dataEntityTree -> {
                DataEntityHierarchyTreeVo dataEntityHierarchyTreeVo = new DataEntityHierarchyTreeVo();
                BeanUtils.copyProperties(dataEntityTree, dataEntityHierarchyTreeVo);
                // 添加子节点
                if (dataEntityHierarchyTreeVo != null) {
                    // 查询实体的表结构,并添加到实体的树上
                    dataEntityHierarchyTreeVo.setDataEntityStructureList(dataEntityStructureService.selectStructureById(dataEntityHierarchyTreeVo.getId(), true));

                    DataEntityTable dataEntityTable = new DataEntityTable();
                    dataEntityTable.setDataEntityId(dataEntityHierarchyTreeVo.getId());
                    // 查询该实体的关联表
                    List<DataEntityTable> dataEntityTableList = dataEntityTableMapper.selectList(dataEntityTable);
                    // 添加实体的关联表
                    dataEntityHierarchyTreeVo.setChildren(dataEntityTableList.stream()
                            // 过滤数据源
                            .filter(entityTable -> dataSourceId.equals(entityTable.getDataSourceId()))
                            .map(entityTable -> {
                                DataEntityHierarchyTreeVo dataEntityTableVo = new DataEntityHierarchyTreeVo();
                                BeanUtils.copyProperties(entityTable, dataEntityTableVo);
                                // 添加实体关联表的映射字段
                                dataEntityTableVo.setDataEntityTableMappingList(dataEntityTableService.selectTableMapping(dataEntityHierarchyTreeVo.getId(), dataEntityTableVo.getId()));
                                return dataEntityTableVo;
                            }).collect(Collectors.toList()));
                }
                return dataEntityHierarchyTreeVo;
            }).collect(Collectors.toList());

            trees.addAll(dataEntityMenuTableVoList);
        }

        if (CollectionUtils.isEmpty(trees)) {
            return trees;
        }

        // 递归成树结构
        return (List<DataEntityHierarchyTreeVo>) TreeUtils.getTree(trees);
    }

    @Override
    public List<DataEntityTree> getTaskTree(Set<Long> menuIds) {
        List<DataEntityTree> trees = dataEntityMenuMapper.selectMenuTree("ASC", null);
        Map<Long, Boolean> visited = new HashMap<>(); // 缓存已访问的节点
        for (Long menuId : menuIds) {
            Optional<DataEntityTree> taskTree = trees.stream()
                    .filter(t -> t.getId().equals(menuId))
                    .findFirst();
            taskTree.ifPresent(t -> {
                List<DataEntityTree> ancestors = (List<DataEntityTree>) TreeUtils.findAncestors(t, trees, visited);
                trees.addAll(ancestors);
                trees.add(t); // 添加自身
            });
        }
        // 去重
        List<DataEntityTree> distinctCollect = trees.stream().filter(TreeUtils.distinctByKey(t -> t.getId()))
                .collect(Collectors.toList());
        // 递归成树结构
        return (List<DataEntityTree>) TreeUtils.getTree(distinctCollect);
    }

    @Override
    public PageDataInfo<DataEntity> readMarked(Long pid, String sort, SearchVo searchVo, Boolean read) {
        // 获取分页参数
        Page<DataEntity> page = PageUtils.getPage(DataEntity.class);
        IPage<DataEntity> dataEntityIPage = new Page<DataEntity>();
        List<String> allTaskName = getAllTaskNameByTenantId();
        if (read) {
            // 查询所有实体数据，过滤掉已同步为数据基础的任务，再进行分页
            List<DataEntity> dataEntityNoPeList = dataEntityMapper.overviewNoPage(pid, sort, searchVo);
            List<DataEntity> dataEntityNoPe = dataEntityNoPeList.stream()
                    .filter(dataEntity -> !allTaskName.contains(dataEntity.getCode()))
                    .collect(Collectors.toList());
            // 对dataEntities 进行分页
            List<DataEntity> dataEntities = dataEntityNoPe.stream()
                    .skip((page.getCurrent() - 1) * page.getSize())
                    .limit(page.getSize())
                    .collect(Collectors.toList());
            dataEntityIPage.setRecords(dataEntities);
            dataEntityIPage.setTotal(dataEntityNoPe.size());
        } else {
            dataEntityIPage = dataEntityMapper.overview(page, pid, sort, searchVo);
        }
        return PageUtils.getPageInfo(dataEntityIPage.getRecords(), (int) dataEntityIPage.getTotal());
    }

    /**
     * 获取数据源列表
     *
     * @return 数据源列表
     */
    private List<DataSource> remoteDataSourceList() {
        // 查询数据源
        R<List<DataSource>> dataSourceR = remoteDriveService.selectAll("ASC");
        if (dataSourceR.getCode() == Status.SUCCESS.getCode()) {
            return dataSourceR.getData();
        }
        return new ArrayList<>();
    }

    /**
     * 递归成树结构
     */
    private Collection<DataEntityTree> getTree(Collection<DataEntityTree> trees, Boolean filterReadOnly) {
        // 一级菜单集合
        Collection<DataEntityTree> menuList = new ArrayList<>();
        // 获取第一级节点
        for (DataEntityTree tree : trees) {
            if (tree.getPid().equals(-1L)) {
                menuList.add(tree);
            }
        }
        // 递归获取子节点
        for (DataEntityTree parent : menuList) {
            recursiveTree(parent, trees, filterReadOnly);
        }
        return menuList;
    }

    /**
     * 递归获取子节点
     */
    private DataEntityTree recursiveTree(DataEntityTree parent, Collection<DataEntityTree> menuList, Boolean filterReadOnly) {
        for (DataEntityTree menu : menuList) {
            if (parent.getMenuType() == MenuType.MENU && parent.getId().equals(menu.getPid())) {
                // 如果是菜单就继续递归查询
                if (menu.getMenuType() == MenuType.MENU) {
                    menu = recursiveTree(menu, menuList, filterReadOnly);
                }
                // 过滤掉只读的实体
                if (filterReadOnly) {
                    if (menu.getIsRead() == null || !menu.getIsRead()) {
                        parent.getChildren().add(menu);
                    }
                } else {
                    parent.getChildren().add(menu);
                }
            }
        }
        return parent;
    }

    private Collection<DataEntityTree> getMarkTree(Collection<DataEntityTree> trees, Boolean filterReadOnly, Boolean read) {
        // 一级菜单集合
        Collection<DataEntityTree> menuList = new ArrayList<>();
        // 获取第一级节点
        for (DataEntityTree tree : trees) {
            if (tree.getPid().equals(-1L)) {
                menuList.add(tree);
            }
        }
        // 递归获取子节点
        for (DataEntityTree parent : menuList) {
            recursiveReadMarkTree(parent, trees, filterReadOnly, read);
        }
        return menuList;
    }

    private DataEntityTree recursiveReadMarkTree(DataEntityTree parent, Collection<DataEntityTree> menuList, Boolean filterReadOnly, Boolean read) {
        List<String> allTaskName = getAllTaskNameByTenantId();
        for (DataEntityTree menu : menuList) {
            if (parent.getMenuType() == MenuType.MENU && parent.getId().equals(menu.getPid())) {
                // 如果是菜单就继续递归查询
                if (menu.getMenuType() == MenuType.MENU) {
                    menu = recursiveReadMarkTree(menu, menuList, filterReadOnly, read);
                }
                // 过滤掉只读的实体
                if (filterReadOnly) {
                    if (menu.getIsRead() == null || !menu.getIsRead()) {
                        parent.getChildren().add(menu);
                    }
                } else {
                    if (read) {
                        if (!allTaskName.contains(menu.getCode())) {
                            menu.setReadMark(true);
                            parent.getChildren().add(menu);
                        }
                    } else {
                        parent.getChildren().add(menu);
                    }
                }
            }
        }
        return parent;
    }

    /**
     * 获取所有任务名称
     *
     * @return 所有任务名称
     */
    public List<String> getAllTaskNameByTenantId() {
        Long tenantId = SecurityUtils.getLoginUser().getTenantId();
        // 切换到MAIN租户
        DataSourceHolder.setDataSourceKey(Constants.FORCE_SWITCH_TENANT_PREFIX + "master");
        // 根据租户ID查询调度的项目code
        Long projectCode = dataEntityMenuMapper.selectProjectCodeByTenantId(String.valueOf(tenantId));
        List<String> taskNameList = dataEntityMenuMapper.selectAllTaskNameByProjectCode(projectCode);
        // 还原租户
        DataSourceHolder.setDataSourceKey(null);
        return taskNameList;
    }



}
