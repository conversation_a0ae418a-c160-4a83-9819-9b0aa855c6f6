package com.datalink.fdop.stream.service;

import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.stream.api.domain.Stream;
import com.datalink.fdop.stream.api.domain.StreamMenu;
import com.datalink.fdop.stream.api.domain.StreamTree;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
public interface IStreamMenuService {

    int create(StreamMenu streamMenu);

    int update(StreamMenu streamMenu);

    int delete(List<Long> ids);

    List<StreamTree> tree(String sort, String code, Boolean isQueryNode);

    PageDataInfo<Stream> overview(Long pid, String sort, SearchVo searchVo);

    /**
     * 查询最大序号
     * @param menuFlag 是否为菜单
     * @return
     */
    Integer querySerialNumber(Boolean menuFlag);

}
