package com.datalink.fdop.govern.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.MenuType;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.govern.api.domain.MetadataMenu;
import com.datalink.fdop.govern.api.domain.MetadataTree;
import com.datalink.fdop.govern.api.model.vo.MetadataMenuVo;
import com.datalink.fdop.govern.service.MetadataMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;



@RequestMapping(value = "/govern/metadata/menu")
@RestController
@Api(tags = "元数据目录管理菜单api")
public class MetadataMenuController {



    @Autowired
    private MetadataMenuService metadataMenuService;

    @ApiOperation("创建菜单")
    @Log(title = "数据治理", businessType = BusinessType.INSERT)
    @PostMapping(value = "/create")
    public R create(@Validated @RequestBody MetadataMenu metadataMenu) {
        if (metadataMenu.getPid() == null) {
            metadataMenu.setPid(-1L);
        }
        return R.toResult(metadataMenuService.create(metadataMenu));
    }

    @ApiOperation("修改菜单")
    @Log(title = "数据治理", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public R update(@RequestBody MetadataMenu metadataMenu) {
        if (metadataMenu.getId() == null) {
            throw new ServiceException(Status.PARAM_ERROR);
        }
        return R.toResult(metadataMenuService.update(metadataMenu));
    }

    @ApiOperation("删除菜单")
    @Log(title = "数据治理", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.PLEASE_SPECIFY_THE_GOVERN_MENU_TO_DELETE);
        }
        return R.toResult(metadataMenuService.delete(ids));
    }


    @ApiOperation("创建节点菜单")
    @Log(title = "数据治理", businessType = BusinessType.DELETE)
    @PostMapping(value = "/create/node")
    public R createNode(@RequestBody List<MetadataMenuVo> metadataMenuVos) {
        for (MetadataMenuVo metadataMenuVo : metadataMenuVos) {
            metadataMenuService.createNode(metadataMenuVo.getMenuId(), metadataMenuVo.getNodeId(),metadataMenuVo.getMenuType(),metadataMenuVo.getCode(),metadataMenuVo.getSerial());
        }
        return R.ok();
    }


    @ApiOperation("删除节点菜单")
    @Log(title = "数据治理", businessType = BusinessType.DELETE)
    @PostMapping(value = "/del/node")
    public R delNode(@RequestBody List<MetadataMenuVo> metadataMenuVos) {
        for (MetadataMenuVo metadataMenuVo : metadataMenuVos) {
            metadataMenuService.delNode(metadataMenuVo.getMenuId(),metadataMenuVo.getNodeId(),metadataMenuVo.getCode());
        }
        return R.ok();
    }



    @ApiOperation("总览")
    @Log(title = "数据治理")
    @PostMapping(value = "/overview")
    public R<PageDataInfo> overview(
            @RequestParam(value = "pid", defaultValue = "-1") Long pid,
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) SearchVo searchVo
    ) {
        return R.ok(metadataMenuService.overview(pid, sort, searchVo));
    }

    @ApiOperation("展示数据菜单树结构")
    @Log(title = "数据元素")
    @GetMapping(value = "/tree")
    public R<List<MetadataTree>> tree(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestParam(required = false) String code,
            @RequestParam(required = false, defaultValue = "true") Boolean isQueryNode) {
        List<MetadataTree> list = metadataMenuService.tree(sort, code, isQueryNode);
        return R.ok(list);
    }
}
