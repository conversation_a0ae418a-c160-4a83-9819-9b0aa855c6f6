package com.datalink.fdop.govern.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.datalink.fdop.common.core.enums.MenuType;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;

import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.tree.TreeUtils;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.govern.api.domain.DataStandardMenu;
import com.datalink.fdop.govern.api.domain.DataStandardTree;
import com.datalink.fdop.govern.api.enums.ApprovalStatusType;
import com.datalink.fdop.govern.mapper.DataStandardMapper;
import com.datalink.fdop.govern.mapper.DataStandardMenuMapper;
import com.datalink.fdop.govern.mapper.DataStandardTemporaryMapper;
import com.datalink.fdop.govern.service.IDataStandardMenuService;
import com.datalink.fdop.govern.version.VersionHandle;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class DataStandardMenuService implements IDataStandardMenuService
{
    @Autowired
    private DataStandardMapper dataStandardMapper;

    @Autowired
    private DataStandardTemporaryMapper dataStandardTemporaryMapper;

    @Autowired
    private DataStandardMenuMapper dataStandardMenuMapper;
    
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int create(DataStandardMenu dataStandardMenu) {
        if (dataStandardMenuMapper.selectByCode(dataStandardMenu.getCode()) != null) {
            throw new ServiceException(Status.STANDARD_MENU_ALREADY_EXISTS);
        }
        dataStandardMenu.setId(IdWorker.getId());
        int insert = dataStandardMenuMapper.insertStandardMenu(dataStandardMenu);
        // 创建菜单边关系
        if (insert > 0 && dataStandardMenu.getPid() != -1L) {
            // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
            dataStandardMenuMapper.createStandardMenuEdge(dataStandardMenu.getPid(), Arrays.asList(dataStandardMenu.getId()));
        }
        return insert;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(DataStandardMenu dataStandardMenu) {
        VlabelItem<DataStandardMenu> vlabelItem = dataStandardMenuMapper.selectById(dataStandardMenu.getId());
        if (vlabelItem == null) {
            throw new ServiceException(Status.STANDARD_MENU_DOES_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(dataStandardMenu.getCode()) && dataStandardMenuMapper.checkCodeIsExists(dataStandardMenu.getId(), dataStandardMenu.getCode()) != null) {
            throw new ServiceException(Status.STANDARD_MENU_ALREADY_EXISTS);
        }
        int update = dataStandardMenuMapper.updateById(dataStandardMenu);
        if (update > 0 && dataStandardMenu.getPid() != null) {
            // 获取修改前的菜单pid,并删除修改前的边关系
            dataStandardMenuMapper.deleteStandardMenuEdge(Arrays.asList(dataStandardMenu.getId()), vlabelItem.getProperties().getPid());
            // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
            if (dataStandardMenu.getPid() != -1L) {
                dataStandardMenuMapper.createStandardMenuEdge(dataStandardMenu.getPid(), Arrays.asList(dataStandardMenu.getId()));
            }
        }
        return update;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int delete(List<Long> ids) {
        for (Long id : ids) {
            VlabelItem<DataStandardMenu> vlabelItem = dataStandardMenuMapper.selectById(id);
            if (vlabelItem == null) {
                continue;
            }
            DataStandardMenu dataStandardMenu = vlabelItem.getProperties();
            // 获取当前被删除菜单的所有子级菜单
            List<Long> menuIdList = dataStandardMenuMapper.selectIdsByPid(id);
            // 如果存在子级菜单，则将所有子级菜单的pid修改为被删除菜单的pid
            if (CollectionUtils.isNotEmpty(menuIdList)) {
                int update = dataStandardMenuMapper.bacthUpdatePidById(menuIdList, dataStandardMenu.getPid());
                if (update > 0 && dataStandardMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    dataStandardMenuMapper.createStandardMenuEdge(dataStandardMenu.getPid(), menuIdList);
                }
            }

            // 获取当前被删除菜单的所有子级
            List<Long> standardList = dataStandardMapper.selectIdsByPid(id);
            // 如果存在子级，则将所有子级的pid修改为被删除菜单的pid
            if (CollectionUtils.isNotEmpty(standardList)) {
                int update = dataStandardMapper.bacthUpdatePidById(standardList, dataStandardMenu.getPid());
                if (update > 0 && dataStandardMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    dataStandardMapper.createStandardAndMenuEdge(dataStandardMenu.getPid(), standardList);
                }
            }
        }
        // 删除菜单并且删除菜单的边关系
        return dataStandardMenuMapper.deleteBatchIds(ids);
    }

    @Override
    public List<DataStandardTree> tree(String sort, String code, Boolean isQueryNode, ApprovalStatusType approvalStatusType) {
        // 所有的数据集合
        List<DataStandardTree> trees = new ArrayList<>();
        // 添加数据元素树
        if (isQueryNode) {
            //正式表
            List<DataStandardTree> standardTrees = dataStandardMapper.selectStandardTree(sort, code,approvalStatusType);
            List<DataStandardTree> dataStandardTreeList = VersionHandle.standardTreesSort(standardTrees);
            trees.addAll(dataStandardTreeList);
        }
        // 添加数据元素菜单树
        trees.addAll(dataStandardMenuMapper.selectMenuTree(sort, code));

        // 一级菜单集合
        List<DataStandardTree> menuList = new ArrayList<>();

        // 获取第一级节点
        for (DataStandardTree tree : trees) {
            if (tree.getPid().equals(-1L)) {
                menuList.add(tree);
            }
        }
        // 递归获取子节点
        for (DataStandardTree parent : menuList) {
            recursiveTree(parent, trees);
        }
        // 条件查找删除空集合的菜单
        if (StringUtils.isNotEmpty(code) && isQueryNode) {
            TreeUtils.removeEmptyChilderAndMenu(menuList);
        }
        return menuList;
    }

    /**
     * 递归获取子节点
     */
    private DataStandardTree recursiveTree(DataStandardTree parent, List<DataStandardTree> menuList) {
        for (DataStandardTree menu : menuList) {
            if (parent.getMenuType() == MenuType.MENU && parent.getId().equals(menu.getPid())) {
                // 如果是菜单就继续递归查询
                if (menu.getMenuType() == MenuType.MENU) {
                    menu = recursiveTree(menu, menuList);
                }
                parent.getChildren().add(menu);
            }
        }
        return parent;
    }

    @Override
    public List<Long> getChildrenMenuId(List<Long> ids) {
        List<Long> childrenMenuIds = new ArrayList<>();
        List<Long> menuIds = dataStandardMenuMapper.selectByPids(ids);
        if (CollectionUtils.isNotEmpty(menuIds)) {
            childrenMenuIds.addAll(getChildrenMenuId(menuIds));
        }
        childrenMenuIds.addAll(ids);
        return childrenMenuIds;
    }
}
