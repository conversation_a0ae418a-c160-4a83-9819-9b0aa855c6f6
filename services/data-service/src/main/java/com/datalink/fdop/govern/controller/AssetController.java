package com.datalink.fdop.govern.controller;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.datalink.fdop.common.core.domain.R;

import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.govern.api.enums.AssetType;
import com.datalink.fdop.govern.service.AssetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RequestMapping(value = "/govern/asset")
@RestController
@Api(tags = "资产api")
public class AssetController {

    @Autowired
    private AssetService assetService;

    @ApiOperation("资产搜索")
    @Log(title = "数据治理")
    @PostMapping(value = "/search")
    public R search(@RequestBody List<AssetType> assetTypes,@RequestParam(required = false) String searchValue) {
        if (CollectionUtils.isEmpty(assetTypes)) {
            throw new ServiceException("请选择类型");
        }
        return R.ok(assetService.search(assetTypes,searchValue) );
    }


}
