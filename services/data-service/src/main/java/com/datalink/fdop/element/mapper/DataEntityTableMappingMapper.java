package com.datalink.fdop.element.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datalink.fdop.element.api.domain.DataEntityTableMapping;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/12 11:45
 */
public interface DataEntityTableMappingMapper extends BaseMapper<DataEntityTableMapping> {

    int deleteMapping(@Param("tableId") Long tableId);

    int batchDeleteMapping(@Param("tableIdList") List<Long> tableIdList);

    int insertCustomizeMapping(@Param("tableId") Long tableId, @Param("dataEntityTableMappingList") List<DataEntityTableMapping> dataEntityTableMappingList);

    int insertPredefinedMapping(@Param("tableId") Long tableId, @Param("dataEntityTableMappingList") List<DataEntityTableMapping> dataEntityTableMappingList);

}
