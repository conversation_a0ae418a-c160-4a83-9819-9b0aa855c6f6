package com.datalink.fdop.govern.service;

import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.govern.api.domain.ClassifyLevelConfig;
import com.datalink.fdop.govern.api.domain.DataTag;

import java.util.List;

public interface IClassifyLevelConfigService {
    int create(ClassifyLevelConfig classifyLevelConfig);

    int update(ClassifyLevelConfig classifyLevelConfig);

    int delete(List<Long> ids);

    PageDataInfo<ClassifyLevelConfig> overview(String sort, String fieldName, String category, String level);

    void importData(List<ClassifyLevelConfig> list);

    List<ClassifyLevelConfig> list(String sort, String fieldName, String category, String level);

}
