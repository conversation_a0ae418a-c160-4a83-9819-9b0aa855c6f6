package com.datalink.fdop.element.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.element.api.domain.DataElement;
import com.datalink.fdop.element.api.domain.DataEntitySynLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface DataEntitytSynLogMapper extends BaseMapper<DataEntitySynLog> {

    int insertSynLog(@Param("dataEntitySynLog") DataEntitySynLog dataEntitySynLog);

    int insertMenuSynLog(@Param("pid") Long pid, @Param("id") Long id);

    int insertDataSourceSynLog(@Param("dataSourceId") Long dataSourceId, @Param("id") Long id);

    int updateById(DataEntitySynLog dataEntitySynLog);

    int deleteBatchIds(@Param("ids") List<Long> ids);

    IPage<DataEntitySynLog> selectList(IPage<DataEntitySynLog> page,@Param(value = "Code") String Code,@Param(value = "status") String status);

}
