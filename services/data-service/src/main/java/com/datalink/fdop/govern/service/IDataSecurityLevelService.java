package com.datalink.fdop.govern.service;

import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.govern.api.domain.DataSecurityLevel;

import java.util.List;

public interface IDataSecurityLevelService {

    int create(DataSecurityLevel dataSecurityLevel);

    int update(DataSecurityLevel dataSecurityLevel);

    int delete(List<Long> ids);

    DataSecurityLevel selectById(Long id);

    DataSecurityLevel selectByCode(String code);

    PageDataInfo<DataSecurityLevel> list(DataSecurityLevel dataSecurityLevel);

    List<SelectVo> selectVoList(DataSecurityLevel dataSecurityLevel);

    int copy(Long pid, List<DataSecurityLevel> dataSecurityLevelList);

}
