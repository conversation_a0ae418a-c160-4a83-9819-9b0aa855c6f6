package com.datalink.fdop.stream.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/8/24 15:57
 */
@Component
@ConfigurationProperties(prefix = "kubernetes")
public class KubernetesProperties {

    private String address;

    private String token;

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

}
