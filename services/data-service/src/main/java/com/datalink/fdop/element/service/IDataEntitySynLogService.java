package com.datalink.fdop.element.service;

import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.element.api.domain.DataEntitySynLog;
import com.datalink.fdop.element.api.enums.EntityType;
import com.datalink.fdop.element.api.model.vo.DataEntitySynVo;
import com.datalink.fdop.element.api.model.vo.DataJsonEntityVo;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2022/4/12 13:58
 */
public interface IDataEntitySynLogService {

    void synchronize(DataEntitySynVo dataEntitySyn);

    void synchronizeCopyAndCreateTable(DataEntitySynVo dataEntitySyn);

    /**
     * 同步表结构并创建实体异步方法
     *
     * @param pid              上级菜单ID
     * @param entityCode       实体编码
     * @param tenantId         租户ID
     * @param dataSourceId     数据源ID
     * @param databaseName     数据库名
     * @param tableName        表名
     * @param isView           是否视图
     * @param dataEntitySynLog 同步日志
     * @param description      表描述
     */
    void buildDataEntity(Long pid, String entityCode, Long tenantId, Long dataSourceId, String databaseName, String tableName, Boolean isView, DataEntitySynLog dataEntitySynLog, EntityType entityType, String description);

    /**
     * 同步表结构创建实体并自动创表异步方法
     *
     * @param pid              上级菜单ID
     * @param entityCode       实体编码
     * @param tenantId         租户ID
     * @param dataSourceId     数据源ID
     * @param databaseName     数据库名
     * @param tableName        表名
     * @param isView           是否视图
     * @param dataEntitySynLog 同步日志
     */
    void buildDataEntityAndCreateTable(Long pid, String entityCode, Long tenantId, Long dataSourceId, String databaseName, String tableName, Boolean isView, DataEntitySynLog dataEntitySynLog, long skinDataSourceId, String skinDataBaseName, EntityType entityType, String description);

    /**
     * Spring管理的异步事务方法，用于创建实体和表
     *
     * @param pid              上级菜单ID
     * @param entityCode       实体编码
     * @param tenantId         租户ID
     * @param dataSourceId     数据源ID
     * @param databaseName     数据库名
     * @param tableName        表名
     * @param isView           是否视图
     * @param sinkDataSourceId 目标数据源ID
     * @param sinkDataBaseName 目标数据库名
     * @param entityType       实体类型
     * @param description      表描述
     * @return CompletableFuture 异步任务结果
     */
    CompletableFuture<Void> buildDataEntityAndCreateTableAsync(Long globalLogId, String logInfo, Long pid, String entityCode, Long tenantId, Long dataSourceId, String databaseName, String tableName, Boolean isView, long sinkDataSourceId, String sinkDataBaseName, EntityType entityType, String description);

    int insertSynLog(DataEntitySynLog dataEntitySynLog);

    int updateSynLog(DataEntitySynLog dataEntitySynLog);

    PageDataInfo<DataEntitySynLog> getSynLog(String Code, String status);

    /**
     * 导入JSON数据
     *
     * @param dataJsonEntityVo JSON数据实体
     */
    void synImportJson(DataJsonEntityVo dataJsonEntityVo);

    /**
     * 批量导入并控制并发数量
     *
     * @param dataJsonEntityList JSON数据实体列表
     */
    void batchImportWithConcurrencyControl(List<DataJsonEntityVo> dataJsonEntityList);

}
