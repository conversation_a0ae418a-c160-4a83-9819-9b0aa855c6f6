package com.datalink.fdop.stream.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.stream.api.domain.StreamCdc;
import com.datalink.fdop.stream.api.domain.StreamCdcTree;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface StreamCdcMapper extends BaseMapper<StreamCdc> {

    int createStreamAndMenuEdge(@Param("pid") Long pid, @Param("ids") List<Long> ids);

    int insertStream(@Param("stream") StreamCdc stream);

    int updateById(StreamCdc stream);

    int bacthUpdatePidById(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteStreamAndMenuEdge(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteBatchIds(@Param("ids") List<Long> ids);

    VlabelItem<StreamCdc> selectById(Long id);

    VlabelItem<StreamCdc> selectByCode(String code);

    VlabelItem<StreamCdc> checkCodeIsExists(@Param("id") Long id, @Param("code") String code);

    List<Long> selectIdsByPid(Long pid);

    List<StreamCdcTree> selectTree(@Param("sort") String sort, @Param("code") String code);


    Integer querySerialNumber();


}
