package com.datalink.fdop.element.service;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.drive.api.domain.dto.Field;
import com.datalink.fdop.element.api.domain.DataEntity;
import com.datalink.fdop.element.api.domain.DataEntityTableVo;
import com.datalink.fdop.element.api.model.DataEntityTree;
import com.datalink.fdop.element.api.model.vo.DataEntityCopyVo;
import com.datalink.fdop.element.api.model.vo.DataJsonEntityVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/4/12 13:58
 */
public interface IDataEntityService {

    DataEntity create(DataEntity dataEntity);

    void createBySql(DataEntity dataEntity);

    int update(DataEntity dataEntity);

    void copy(Long pid, List<DataEntityCopyVo> dataEntityCopyList);

    Map<String, Object> checkDeleteEntity(List<Long> ids);

    int delete(Boolean isDeleteTable, List<Long> ids);

    int batchDelete(Boolean isDeleteTable, List<DataEntity> dataEntities);

    int deleteSynLog(List<Long> ids);

    PageDataInfo<DataEntity> list(DataEntity dataEntity);

    List<DataEntity> dwdOrDimList(DataEntity dataEntity);

    DataEntity selectById(Long id);

    List<DataEntityTree> getEntityMenuTableTree(String code, Boolean isRead, Long dataSourceId);

    DataEntity selectByCode(String code);

    DataEntity generateEntity(Long pid, String code, String name, String description, List<Field> fieldList);

    /**
     * 批量导出实体
     * @param ids 实体ID集合
     */
    void batchExportDataEntityByIds(List<Long> ids, HttpServletResponse response);

    /**
     * 导入实体
     * @param file 导入的JSON文件
     * @return 导入结果
     */
    R importDataEntity(MultipartFile file);

    List<DataEntityTableVo> selectDataEntityTableVoByTenantIdAndIds(Long tenantId, List<Long> dataEntityIds);
    
    /**
     * 导出实体为JSON格式
     * @param ids 实体ID集合
     * @return JSON数据结构列表
     */
    List<DataJsonEntityVo> exportEntityJson(List<Long> ids);

}
