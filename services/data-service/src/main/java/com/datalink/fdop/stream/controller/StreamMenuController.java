package com.datalink.fdop.stream.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.stream.api.domain.Stream;
import com.datalink.fdop.stream.api.domain.StreamMenu;
import com.datalink.fdop.stream.api.domain.StreamTree;
import com.datalink.fdop.stream.service.IStreamMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
@RequestMapping(value = "/stream/menu")
@RestController
@Api(tags = "流菜单api")
public class StreamMenuController extends BaseController {

    @Autowired
    private IStreamMenuService streamMenuService;

    @ApiOperation("创建流菜单")
    @Log(title = "Flink流任务菜单", businessType = BusinessType.INSERT)
    @PostMapping(value = "/create")
    public R create(@Validated @RequestBody StreamMenu streamMenu) {
        return R.toResult(streamMenuService.create(streamMenu));
    }

    @ApiOperation("修改流菜单")
    @Log(title = "Flink流任务菜单", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public R update(@RequestBody StreamMenu streamMenu) {
        return R.toResult(streamMenuService.update(streamMenu));
    }

    @ApiOperation("删除流菜单")
    @Log(title = "Flink流任务菜单", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.PLEASE_SPECIFY_THE_FLOW_MENU_TO_DELETE);
        }
        return R.toResult(streamMenuService.delete(ids));
    }


    @ApiOperation("流任务树菜单")
    @Log(title = "Flink流任务菜单")
    @GetMapping(value = "/tree")
    public R<List<StreamTree>> tree(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestParam(required = false) String code,
            @RequestParam(required = false, defaultValue = "true") Boolean isQueryNode) {
        List<StreamTree> list = streamMenuService.tree(sort, code, isQueryNode);
        return R.ok(list);
    }


    @ApiOperation("总览")
    @Log(title = "Flink流任务菜单")
    @PostMapping(value = "/overview")
    public R<PageDataInfo<Stream>> overview(@RequestParam(value = "pid", defaultValue = "-1", required = false) Long pid,
                                            @RequestParam(value = "sort", defaultValue = "ASC", required = false) String sort,
                                            @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(streamMenuService.overview(pid, sort, searchVo));
    }


    @ApiOperation("查序号")
    @Log(title = "Flink流任务")
    @GetMapping(value = "/querySerialNumber")
    public R<Integer> querySerialNumber(@RequestParam Boolean menuFlag) {
        return R.ok(streamMenuService.querySerialNumber(menuFlag));
    }

}
