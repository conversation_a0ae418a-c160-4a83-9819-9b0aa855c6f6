package com.datalink.fdop.govern.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.govern.api.domain.DirectoryStructure;
import com.datalink.fdop.govern.api.domain.DirectoryStructureTree;
import com.datalink.fdop.govern.api.enums.Level;
import com.datalink.fdop.govern.mapper.DirectoryStructureMapper;
import com.datalink.fdop.govern.service.IDirectoryStructureService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class DirectoryStructureServiceImpl implements IDirectoryStructureService {

    @Autowired
    private DirectoryStructureMapper directoryStructureMapper;

    @Override
    public int create(DirectoryStructure entity) {
        if (StringUtils.isEmpty(entity.getCode())) {
            throw new ServiceException("编码不能为空");
        }
        if (entity.getPid() == null) {
            entity.setPid(-1L);
        }
        if (entity.getPid() == -1L) {
            entity.setLevel(Level.L1);
        } else {
            if (directoryStructureMapper.selectById(entity.getPid()) == null) {
                throw new ServiceException("上级不存在");
            }
        }
        if (directoryStructureMapper.selectByCode(entity.getCode()) != null) {
            throw new ServiceException("已存在编码为["+entity.getCode()+"]的数据");
        }
        return directoryStructureMapper.insert(entity);
    }

    @Override
    public int update(DirectoryStructure entity) {
        if (entity.getId() == null) {
            throw new ServiceException("id不能为空");
        }
        if (StringUtils.isEmpty(entity.getCode())) {
            throw new ServiceException("编码不能为空");
        }
        if (directoryStructureMapper.codeIsOnly(entity.getId(), entity.getCode()) > 0) {
            throw new ServiceException("已存在编码为["+entity.getCode()+"]的数据");
        }
        return directoryStructureMapper.updateById(entity);
    }

    @Override
    public int delete(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException("请指定要删除的内容");
        }
        List<String> codes = directoryStructureMapper.queryExistChildrenNodes(ids);
        if (CollectionUtils.isNotEmpty(codes)) {
            throw new ServiceException("存在下级，不允许删除");
        }
        return directoryStructureMapper.delete(ids);
    }

    @Override
    public DirectoryStructure selectById(Long id) {
        VlabelItem<DirectoryStructure> vlabelItem = directoryStructureMapper.selectById(id);
        if (vlabelItem == null) {
            throw new ServiceException("不存在");
        }
        return vlabelItem.getProperties();
    }

    @Override
    public PageDataInfo<DirectoryStructure> list(DirectoryStructure entity, String sort) {
        Page<VlabelItem> page = PageUtils.getPage(VlabelItem.class);
        IPage<VlabelItem<DirectoryStructure>> vlabelItemIPage = directoryStructureMapper.selectList(page, entity, sort);
        return PageUtils.getPageInfo(vlabelItemIPage.getRecords().stream().map(VlabelItem::getProperties).collect(Collectors.toList()), (int) vlabelItemIPage.getTotal());
    }

    @Override
    public List<DirectoryStructureTree> tree(String sort, String code, String name, String description) {
        List<DirectoryStructureTree> allNodes = directoryStructureMapper.getAllNode(sort);
        List<DirectoryStructureTree> topNodes = allNodes.stream().filter(e->e.getPid() == -1L).collect(Collectors.toList());
        for (DirectoryStructureTree parent : topNodes) {
            recursiveTree(parent, allNodes, 0);
        }
        if (StringUtils.isNotEmpty(code)) {
            removeEmptyChildrenAndMenuByCode(topNodes, code);
        }
        if (StringUtils.isNotEmpty(name)) {
            removeEmptyChildrenAndMenuByName(allNodes, name);
        }
        return topNodes;
    }

    @Override
    public List<DirectoryStructureTree> treeApplication(String sort, String code, String name, String description) {
        List<DirectoryStructureTree> allNodes = directoryStructureMapper.getApplicationAllNode(sort);
        List<DirectoryStructureTree> topNodes = allNodes.stream().filter(e->e.getPid() == -1L).collect(Collectors.toList());
        for (DirectoryStructureTree parent : topNodes) {
            recursiveTree(parent, allNodes, 0);
        }
        if (StringUtils.isNotEmpty(code)) {
            removeEmptyChildrenAndMenuByCode(topNodes, code);
        }
        if (StringUtils.isNotEmpty(name)) {
            removeEmptyChildrenAndMenuByName(allNodes, name);
        }
        return topNodes;
    }

    public static DirectoryStructureTree recursiveApplicationTree(DirectoryStructureTree parent, List<DirectoryStructureTree> list, int index) {
        for (DirectoryStructureTree tree : list) {
            if (parent.getId().equals(tree.getApplicationId())) {
                if (index != list.size()) {
                    tree = recursiveApplicationTree(tree, list, index++);
                }
                parent.getChildren().add(tree);
            }
        }
        return parent;
    }

    public static DirectoryStructureTree recursiveTree(DirectoryStructureTree parent, List<DirectoryStructureTree> list, int index) {
        for (DirectoryStructureTree tree : list) {
            if (parent.getId().equals(tree.getPid())) {
                if (index != list.size()) {
                    tree = recursiveTree(tree, list, index++);
                }
                parent.getChildren().add(tree);
            }
        }
        return parent;
    }

    public static void removeEmptyChildrenAndMenuByCode(List<DirectoryStructureTree> trees, String code) {
        if (CollectionUtils.isEmpty(trees)) {
            return;
        }
        Iterator<DirectoryStructureTree> iterator = trees.iterator();
        while (iterator.hasNext()) {
            DirectoryStructureTree node = iterator.next();
            removeEmptyChildrenAndMenuByCode(node.getChildren(), code);
            if (node.getChildren().isEmpty() && !node.getCode().toLowerCase().contains(code.toLowerCase())) {
                iterator.remove();
            }
        }
    }

    public static void removeEmptyChildrenAndMenuByName(List<DirectoryStructureTree> trees, String name) {
        if (CollectionUtils.isEmpty(trees)) {
            return;
        }
        Iterator<DirectoryStructureTree> iterator = trees.iterator();
        while (iterator.hasNext()) {
            DirectoryStructureTree node = iterator.next();
            removeEmptyChildrenAndMenuByName(node.getChildren(), name);
            if (node.getChildren().isEmpty() && !node.getName().toLowerCase().contains(name.toLowerCase())) {
                iterator.remove();
            }
        }
    }

}
