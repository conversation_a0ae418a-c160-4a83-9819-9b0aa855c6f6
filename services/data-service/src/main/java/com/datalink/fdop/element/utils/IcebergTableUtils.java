package com.datalink.fdop.element.utils;

import com.alibaba.fastjson.JSONObject;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.drive.api.domain.DataSourceBasicInfo;
import com.datalink.fdop.element.api.domain.DataElementStructure;
import com.datalink.fdop.element.api.domain.DataElementTable;
import org.apache.hadoop.conf.Configuration;
import org.apache.iceberg.PartitionSpec;
import org.apache.iceberg.Schema;
import org.apache.iceberg.Table;
import org.apache.iceberg.catalog.TableIdentifier;
import org.apache.iceberg.hadoop.HadoopCatalog;
import org.apache.iceberg.types.Type;
import org.apache.iceberg.types.Types;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/17 14:41
 */
public class IcebergTableUtils {

    public static void createTable(DataSource dataSource, DataElementTable dataElementTable) {

        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        String warehousePath = dataSourceBasicInfo.getJdbcUrl();

        HadoopCatalog hadoopCatalog = new HadoopCatalog(new Configuration(), warehousePath);

        // 获取Schema
        List<Types.NestedField> nestedFieldList = new ArrayList<>();
        for (DataElementStructure dataElementStructure : dataElementTable.getDataElementStructureList()) {
            Types.NestedField nestedField = null;
            if (dataElementStructure.getIsPk()) {
                nestedField = Types.NestedField.required(dataElementStructure.getSeq(), dataElementStructure.getCode(), getType(dataElementStructure));
            } else {
                nestedField = Types.NestedField.optional(dataElementStructure.getSeq(), dataElementStructure.getCode(), getType(dataElementStructure));
            }
            nestedFieldList.add(nestedField);
        }
        Schema schema = new Schema(nestedFieldList);

        JSONObject createTableConfigJson = JSONObject.parseObject(dataElementTable.getCreateTableConfig());

        // 分区
        PartitionSpec.Builder builder = PartitionSpec.builderFor(schema);

        if (StringUtils.isNotEmpty(createTableConfigJson.getString("identity"))) {
            builder = builder.identity(createTableConfigJson.getString("identity"));
        }
        PartitionSpec partitionSpec = builder.build();

        // 设置库、表
        TableIdentifier tableIdentifier = TableIdentifier.of(dataElementTable.getSchemaName(), dataElementTable.getTableName());

        // 创表
        Table table = hadoopCatalog.createTable(tableIdentifier, schema, partitionSpec);
    }

    // 获取iceberg中的类型
    private static Type getType(DataElementStructure dataElementStructure) {
        switch (dataElementStructure.getFieldType()) {
            case 短整数类型:
            case 整数类型:
                return Types.IntegerType.get();
            case 长整数类型:
                return Types.LongType.get();
            case 浮点类型:
                return Types.FloatType.get();
            case 长浮点类型:
                return Types.DoubleType.get();
            case 数值类型:
                return Types.DecimalType.of(dataElementStructure.getLength().intValue(), dataElementStructure.getDecimalLength().intValue());
            case 字符类型:
                return Types.StringType.get();
            case 文本类型:
                return Types.StringType.get();
            case 时间类型:
                return Types.DateType.get();
            case 日期类型:
                return Types.TimeType.get();
            case 时戳类型:
                return Types.TimestampType.withoutZone();
            case 时刻类型:
                return Types.TimeType.get();
            case 二进制类型:
                return Types.BinaryType.get();
            case 布尔类型:
                return Types.BooleanType.get();
            default:
                return null;
        }
    }

}
