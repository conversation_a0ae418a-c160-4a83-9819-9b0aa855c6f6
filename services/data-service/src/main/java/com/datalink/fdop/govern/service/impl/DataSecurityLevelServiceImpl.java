package com.datalink.fdop.govern.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.govern.api.domain.DataSecurityLevel;
import com.datalink.fdop.govern.mapper.DataSecurityLevelMapper;
import com.datalink.fdop.govern.service.IDataSecurityLevelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DataSecurityLevelServiceImpl implements IDataSecurityLevelService {

    @Autowired
    private DataSecurityLevelMapper dataSecurityLevelMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int create(DataSecurityLevel dataSecurityLevel) {
        VlabelItem<DataSecurityLevel> vlabelItem = dataSecurityLevelMapper.selectByCode(dataSecurityLevel.getCode());
        if (vlabelItem != null) {
            throw new ServiceException(Status.DATA_SECURITY_LEVEL_EXIST);
        }
        dataSecurityLevel.setId(IdWorker.getId());
        return dataSecurityLevelMapper.insert(dataSecurityLevel);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(DataSecurityLevel dataSecurityLevel) {
        VlabelItem<DataSecurityLevel> vlabelItem = dataSecurityLevelMapper.selectById(dataSecurityLevel.getId());
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATA_SECURITY_LEVEL_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(dataSecurityLevel.getCode()) && dataSecurityLevelMapper.checkCodeIsExists(dataSecurityLevel.getId(), dataSecurityLevel.getCode()) != null) {
            throw new ServiceException(Status.DATA_SECURITY_LEVEL_EXIST);
        }
        return dataSecurityLevelMapper.updateById(dataSecurityLevel);
    }

    @Override
    public int delete(List<Long> ids) {
        return dataSecurityLevelMapper.deleteBatchIds(ids);
    }

    @Override
    public DataSecurityLevel selectById(Long id) {
        VlabelItem<DataSecurityLevel> vlabelItem = dataSecurityLevelMapper.selectById(id);
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATA_SECURITY_LEVEL_NOT_EXIST);
        }
        return vlabelItem.getProperties();
    }

    @Override
    public DataSecurityLevel selectByCode(String code) {
        VlabelItem<DataSecurityLevel> vlabelItem = dataSecurityLevelMapper.selectByCode(code);
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATA_SECURITY_LEVEL_NOT_EXIST);
        }
        return vlabelItem.getProperties();
    }

    @Override
    public PageDataInfo<DataSecurityLevel> list(DataSecurityLevel dataSecurityLevel) {
        // 获取分页参数
        Page<VlabelItem> page = PageUtils.getPage(VlabelItem.class);
        IPage<VlabelItem<DataSecurityLevel>> vlabelItemIPage = dataSecurityLevelMapper.selectList(page, dataSecurityLevel);
        return PageUtils.getPageInfo(vlabelItemIPage.getRecords().stream().map(VlabelItem::getProperties).collect(Collectors.toList()), (int) vlabelItemIPage.getTotal());
    }

    @Override
    public List<SelectVo> selectVoList(DataSecurityLevel dataSecurityLevel) {
        return dataSecurityLevelMapper.selectListAll(dataSecurityLevel);
    }

    @Override
    public int copy(Long pid, List<DataSecurityLevel> dataSecurityLevelList) {
        return 0;
    }
}
