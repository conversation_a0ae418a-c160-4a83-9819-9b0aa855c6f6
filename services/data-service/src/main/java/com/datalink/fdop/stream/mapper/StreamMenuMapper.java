package com.datalink.fdop.stream.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.stream.api.domain.Stream;
import com.datalink.fdop.stream.api.domain.StreamMenu;
import com.datalink.fdop.stream.api.domain.StreamTree;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface StreamMenuMapper extends BaseMapper<StreamMenu> {

    int createStreamMenuEdge(@Param("pid") Long pid, @Param("ids") List<Long> ids);

    int insertStreamtMenu(@Param("streamMenu") StreamMenu streamMenu);

    int updateById(StreamMenu streamMenu);

    int bacthUpdatePidById(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteStreamMenuEdge(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteBatchIds(@Param("ids") List<Long> ids);

    VlabelItem<StreamMenu> selectById(Long id);

    VlabelItem<StreamMenu> selectByCode(String code);

    VlabelItem<StreamMenu> selectByPid(Long pid);

    VlabelItem<StreamMenu> checkCodeIsExists(@Param("id") Long id, @Param("code") String code);

    List<Long> selectIdsByPid(Long pid);

    IPage<Stream> overview(@Param("page") Page<Stream> page, @Param("pid") Long pid, @Param("sort") String sort, @Param("searchVo") SearchVo searchVo);

    List<StreamTree> selectMenuTree(@Param("sort") String sort, @Param("code") String code);

    Integer querySerialNumber();

}
