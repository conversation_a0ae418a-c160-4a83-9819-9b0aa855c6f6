package com.datalink.fdop.element.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.idempotent.annotation.RepeatSubmit;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.element.api.domain.DataEntity;
import com.datalink.fdop.element.api.domain.DataEntityMenu;
import com.datalink.fdop.element.api.model.DataEntityTree;
import com.datalink.fdop.element.api.model.vo.DataEntityHierarchyTreeVo;
import com.datalink.fdop.element.service.IDataEntityMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
@RequestMapping(value = "/element/entity/menu")
@RestController
@Api(tags = "数据实体菜单api")
public class DataEntityMenuController extends BaseController {

    @Autowired
    private IDataEntityMenuService dataEntityMenuService;

    @ApiOperation("创建数据实体菜单")
    @Log(title = "实体", businessType = BusinessType.INSERT)
    @RepeatSubmit(interval = 1000)
    @PostMapping(value = "/create")
    public R create(@Validated @RequestBody DataEntityMenu dataEntityMenu) {
        return R.toResult(dataEntityMenuService.create(dataEntityMenu));
    }

    @ApiOperation("修改数据实体菜单")
    @Log(title = "实体", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public R update(@Validated @RequestBody DataEntityMenu dataEntityMenu) {
        return R.toResult(dataEntityMenuService.update(dataEntityMenu));
    }

    @ApiOperation("修改数据实体L3")
    @Log(title = "实体", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update/L3")
    public R updateL3(@Validated @RequestBody DataEntityMenu dataEntityMenu,
                      @RequestParam(required = false, defaultValue = "true", value = "isDelete") Boolean isDelete) {
        return R.toResult(dataEntityMenuService.updateL3(dataEntityMenu, isDelete));
    }

    @ApiOperation("删除数据实体菜单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "实体菜单id集合", required = true, dataType = "Long", allowMultiple = true, paramType = "body", example = "[1,2]"),
    })
    @Log(title = "实体", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.PLEASE_SPECIFY_THE_ENTITY_MENU_TO_DELETE);
        }
        return R.toResult(dataEntityMenuService.delete(ids));
    }

    @ApiOperation("数据实体树结构")
    @Log(title = "实体")
    @GetMapping(value = "/tree")
    public R<List<DataEntityTree>> tree(
            @RequestParam(value = "sort", required = false, defaultValue = "ASC") String sort,
            @RequestParam(value = "code", required = false) String code,
            @RequestParam(value = "isQueryNode", required = false, defaultValue = "true") Boolean isQueryNode,
            @RequestParam(value = "dataSourceId", required = false) Long dataSourceId,
            @RequestParam(value = "filterReadOnly", required = false, defaultValue = "false") Boolean filterReadOnly) {
        return R.ok(dataEntityMenuService.tree(sort, code, isQueryNode, dataSourceId, filterReadOnly));
    }

    @ApiOperation("数据实体树结构")
    @Log(title = "实体")
    @GetMapping(value = "/readMarkedTree")
    public R<List<DataEntityTree>> readMarkedtree(
            @RequestParam(value = "sort", required = false, defaultValue = "ASC") String sort,
            @RequestParam(value = "code", required = false) String code,
            @RequestParam(value = "isQueryNode", required = false, defaultValue = "true") Boolean isQueryNode,
            @RequestParam(value = "dataSourceId", required = false) Long dataSourceId,
            @RequestParam(value = "filterReadOnly", required = false, defaultValue = "false") Boolean filterReadOnly,
            @RequestParam(value = "read", required = false, defaultValue = "false") Boolean read
    ) {
        return R.ok(dataEntityMenuService.readMarkedtree(sort, code, isQueryNode, dataSourceId, filterReadOnly, read));
    }

    @ApiOperation("数据实体总览")
    @Log(title = "实体")
    @PostMapping(value = "/overview")
    public R<PageDataInfo<DataEntity>> overview(@RequestParam(value = "pid", defaultValue = "-1", required = false) Long pid,
                                                @RequestParam(value = "sort", defaultValue = "ASC", required = false) String sort,
                                                @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(dataEntityMenuService.overview(pid, sort, searchVo));
    }

    @ApiOperation("数据实体总览")
    @Log(title = "实体")
    @PostMapping(value = "/readMarked")
    public R<PageDataInfo<DataEntity>> overviewReadMarked(@RequestParam(value = "pid", defaultValue = "-1", required = false) Long pid,
                                                @RequestParam(value = "sort", defaultValue = "ASC", required = false) String sort,
                                                @RequestBody(required = false) SearchVo searchVo,
                                                @RequestParam(value = "read", required = false, defaultValue = "false") Boolean read ){
        return R.ok(dataEntityMenuService.readMarked(pid, sort, searchVo,read));
    }

    @ApiOperation("数据实体列表")
    @Log(title = "实体")
    @PostMapping(value = "/list/{dataEntityId}")
    public R<List<DataEntity>> list(@PathVariable Long dataEntityId) {
        return R.ok(dataEntityMenuService.list(dataEntityId));
    }

    @ApiOperation("查询菜单/实体/实体结构&实体映射表/实体映射表字段")
    @Log(title = "实体")
    @GetMapping(value = "/selecEntityHierarchyTree")
    public R<List<DataEntityHierarchyTreeVo>> selectEntityHierarchyTree(
            @RequestParam(value = "dataSourceId", required = false) Long dataSourceId,
            @RequestParam(value = "code", required = false) String code
    ) {
        return R.ok(dataEntityMenuService.selectEntityHierarchyTree(dataSourceId, code));
    }



}
