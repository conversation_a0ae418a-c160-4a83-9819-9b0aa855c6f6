package com.datalink.fdop.stream.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.datalink.fdop.common.core.domain.BaseDataModel;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.graph.api.RemoteFlinkGraphService;
import com.datalink.fdop.graph.api.RemoteFlinkSqlService;
import com.datalink.fdop.graph.api.dto.TaskInfo;
import com.datalink.fdop.graph.api.enums.RuntimeMode;
import com.datalink.fdop.graph.api.flink.FlinkAPI;
import com.datalink.fdop.graph.api.flink.SavePointResult;
import com.datalink.fdop.graph.api.flink.SavePointType;
import com.datalink.fdop.graph.api.flink.SelectResult;
import com.datalink.fdop.graph.api.flink.util.SqlParser;
import com.datalink.fdop.param.api.model.vo.ParamVo;
import com.datalink.fdop.stream.api.domain.Stream;
import com.datalink.fdop.stream.api.domain.StreamHistory;
import com.datalink.fdop.stream.api.domain.StreamSavePoint;
import com.datalink.fdop.stream.api.dto.ReturnVo;
import com.datalink.fdop.stream.config.KubernetesProperties;
import com.datalink.fdop.stream.mapper.StreamHistoryMapper;
import com.datalink.fdop.stream.mapper.StreamMapper;
import com.datalink.fdop.stream.mapper.StreamMenuMapper;
import com.datalink.fdop.stream.service.IStreamHistoryService;
import com.datalink.fdop.stream.service.IStreamSavePointService;
import com.datalink.fdop.stream.service.IStreamService;
import com.datalink.fdop.stream.utils.K8sManager;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
@Service
@Slf4j
public class StreamService implements IStreamService {

    @Autowired
    private StreamMapper streamMapper;

    @Autowired
    private StreamMenuMapper streamMenuMapper;

    @Autowired
    private RemoteFlinkGraphService remoteFlinkGraphService;

    @Autowired
    private RemoteFlinkSqlService remoteFlinkSqlService;

    @Autowired
    private IStreamHistoryService streamHistoryService;

    @Autowired
    private IStreamSavePointService streamSavePointService;

    @Autowired
    private K8sManager k8sManager;

    @Value("${flink.host}")
    private String flinkHost;

    @Value("${flink.port}")
    private String flinkPort;

    @Autowired
    private KubernetesProperties kubernetesProperties;

    private Stream checkStream(Long id) {
        VlabelItem<Stream> vlabelItem = streamMapper.selectById(id);
        if (vlabelItem == null) {
            throw new ServiceException(Status.STREAM_DOES_NOT_EXIST);
        }
        return vlabelItem.getProperties();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int create(Stream stream) {
        if (stream.getPid() != -1L && streamMenuMapper.selectById(stream.getPid()) == null) {
            throw new ServiceException(Status.STREAM_MENU_DOES_NOT_EXIST);
        }
        if (streamMapper.selectByCode(stream.getCode()) != null) {
            throw new ServiceException(Status.STREAM_ALREADY_EXISTS);
        }
        stream.setId(IdWorker.getId());
        int insert = streamMapper.insertStream(stream);
        // 创建元素边关系
        if (insert > 0 && stream.getPid() != -1L) {
            // 如果修改了元素层级，并且不是置为顶级菜单，则需要添加点和菜单的边关系
            streamMapper.createStreamAndMenuEdge(stream.getPid(), Arrays.asList(stream.getId()));
        }
        return insert;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(Stream stream) {
        VlabelItem<Stream> vlabelItem = streamMapper.selectById(stream.getId());
        if (vlabelItem == null) {
            throw new ServiceException(Status.STREAM_DOES_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(stream.getCode()) && streamMapper.checkCodeIsExists(stream.getId(), stream.getCode()) != null) {
            throw new ServiceException(Status.STREAM_ALREADY_EXISTS);
        }
        // 不能拖拽到节点里面
        if (stream.getPid() != null && stream.getPid() != -1L && streamMenuMapper.selectById(stream.getPid()) == null) {
            throw new ServiceException(Status.UNKNOWN_STREAM_MENU);
        }
        if (stream.getTaskInfo() != null){
            // 转义 存入PG age
            stream.setTaskInfo(stream.getTaskInfo().replace("'","\\'"));
        }
        int update = streamMapper.updateById(stream);
        if (update > 0) {
            // 获取修改前的菜单pid,并删除修改前的点和菜单的边关系
            streamMapper.deleteStreamAndMenuEdge(Arrays.asList(stream.getId()), vlabelItem.getProperties().getPid());
            if (stream.getPid() != -1L) {
                // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                streamMapper.createStreamAndMenuEdge(stream.getPid(), Arrays.asList(stream.getId()));
            }
        }
        if (stream.getTaskInfo() != null){
            // 更新参数的边关系
            TaskInfo taskInfo = JSONObject.parseObject(stream.getTaskInfo().replace("\\'", "'"), TaskInfo.class);
            updateStreamAndParamVoEdge(stream.getId(),taskInfo.getInputParams());
        }
        return update;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int delete(List<Long> ids) {
        return streamMapper.deleteBatchIds(ids);
    }

    @Override
    public Stream selectById(Long id) {
        VlabelItem<Stream> vlabelItem = streamMapper.selectById(id);
        if (vlabelItem == null) {
            throw new ServiceException(Status.STREAM_DOES_NOT_EXIST);
        }
        Stream stream = vlabelItem.getProperties();
        if (stream.getTaskInfo() != null){
            stream.setTaskInfoObj(JSONObject.parseObject(stream.getTaskInfo().replace("\\'","'"), TaskInfo.class));
        }
        try {
            if (StringUtils.isNotEmpty(stream.getJobId())) {
                //查询任务状态
                FlinkAPI flinkAPI = FlinkAPI.build(flinkHost + ":" + flinkPort);
                JsonNode jobInfo = flinkAPI.getJobInfo(stream.getJobId());
                if (jobInfo != null) {
                    //"INITIALIZING"，"CREATED"，"RUNNING"，"FAILING"，"FAILED"，"CANCELLING"，"CANCELED"，"FINISHED"，"RESTARTING"，"SUSPENDED"，"RECONCILING'
                    //“初始化"、"已创建"、“正在运行"、"失败"、“未成功”、“取消”、“被取消"、"已完成"、"重新启动"、"已挂起"、"正在协调
                    stream.setStatus(jobInfo.get("state").asText());
                }
            }
        } catch (Exception e){
            log.error(e.getLocalizedMessage());
        }
        return stream;
    }

    @Override
    public Boolean checkStatus(Long id) {
        Stream stream = checkStream(id);

        return k8sManager.checkStreamStatus(stream.getCode());
    }

    @Override
    public Boolean checkTaskIsExists(String taskName) {
        // TODO:检查的都是批任务，批任务完成会自动关闭pod，所以如果结果为true则说明任务是执行失败的
        return k8sManager.checkTaskIsExists(taskName);
    }

    @Override
    public String getLog(String taskName) {

        return k8sManager.getLog(taskName);
    }

    @Override
    public void start(Long id) {
        Stream stream = checkStream(id);
        if (stream.getIsOnline()) {
            throw new ServiceException(Status.THE_TASK_IS_RUNNING_AND_CANNOT_BE_RESTARTED);
        }
        if (stream.getTaskInfo() == null){
            throw new ServiceException("未获取到流任务执行配置，请先完成配置后上线");
        }
        // 转义 提交 flink sql 到集群执行
        TaskInfo taskInfo = JSONObject.parseObject(stream.getTaskInfo().replace("\\'","'"), TaskInfo.class);
        // 根据flink sql规则切分sql
        List<String> sqlList = SqlParser.parseStatements(taskInfo.getSql());
        // 如果最后一句是insert into的语法，把他remote掉，
    }

    @Override
    public void stop(Long id) {

    }

    @Override
    public void stop(String taskName) {
        k8sManager.stopStream(taskName);
    }

    @Override
    public KubernetesProperties getK8sUrlAndToken() {
        return kubernetesProperties;
    }

    @Override
    public ReturnVo submitFlink2K8s(TaskInfo taskInfo) {
        return k8sManager.startStream(taskInfo);
    }

    @Override
    public JSONObject online(Long id) {
        Stream stream = checkStream(id);
        if (stream.getIsOnline()) {
            throw new ServiceException(Status.THE_TASK_IS_RUNNING_AND_CANNOT_BE_RESTARTED);
        }
        if (stream.getTaskInfo() == null){
            throw new ServiceException("未获取到流任务执行配置，请先完成配置后上线");
        }
        // 转义 提交 flink sql 到集群执行
        TaskInfo taskInfo = JSONObject.parseObject(stream.getTaskInfo().replace("\\'","'"), TaskInfo.class);
        taskInfo.setTaskName(stream.getCode());
        taskInfo.setRuntimeMode(RuntimeMode.STREAMING);
        R<String> r = remoteFlinkGraphService.submitFlinkCluster(taskInfo);
        if (r.getCode() != Status.SUCCESS.getCode()){
            throw new ServiceException(r.getMsg());
        }
        String jobId = r.getData();
        stream.setJobId(jobId);
        stream.setIsOnline(true);
        // 创建历史记录 转义 存入PG age 历史几轮里面的流任务数据跟taskjinfo数据要分开存 放一起json无法转义为实体
        stream.setTaskInfo(null);
        StreamHistory streamHistory = new StreamHistory(stream.getId(),jobId, "RUNNING", null , JSON.toJSONString(stream), JSON.toJSONString(taskInfo),null);
        streamHistory = streamHistoryService.create(streamHistory);
        stream.setLastHistoryId(streamHistory.getId());
        this.update(stream);
        return null;
    }

    @Override
    public JSONObject offline(Long id) {
        Stream stream = checkStream(id);
        if (stream.getTaskInfo() == null){
            throw new ServiceException("未获取到流任务配置，请先完成配置后下线");
        }
        TaskInfo taskInfo = JSONObject.parseObject(stream.getTaskInfo().replace("\\'","'"), TaskInfo.class);
        taskInfo.setTaskName(stream.getCode());
        stream.setIsOnline(false);
        stream.setTaskInfo(stream.getTaskInfo().replace("'","\\'"));
        // 转义 存入PG age
        streamMapper.updateById(stream);
        FlinkAPI flinkAPI = FlinkAPI.build(flinkHost + ":" + flinkPort);
        SavePointResult savepoints = flinkAPI.savepoints(stream.getJobId(), SavePointType.CANCEL, null);
        String savepoint = JSON.toJSONString(savepoints);
        // 历史记录状态为取消 自动保存savepoint
        StreamHistory streamHistory = new StreamHistory();
        streamHistory.setId(stream.getLastHistoryId());
        streamHistory.setStatus("CANCELED");
        streamHistory.setEndTime(new Date());
        streamHistoryService.update(streamHistory);
        // 更新保存点信息
        StreamSavePoint savePoint = new StreamSavePoint();
        streamSavePointService.create(savePoint);
        return JSONObject.parseObject(savepoint);
    }

    @Override
    public String generateFlinkCreateTableStructure(BaseDataModel dataModel) {
        R<String> r = remoteFlinkSqlService.getFlinkSqlByDataModel(dataModel.getTableName(), dataModel);
        if (r.getCode() != Status.SUCCESS.getCode()){
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }

    @Override
    public String getExceptionByJobId(String jobId) {
        FlinkAPI flinkAPI = FlinkAPI.build(flinkHost + ":" + flinkPort);
        JsonNode exception = flinkAPI.getException(jobId);
        if (exception != null){
            return exception.get("root-exception").asText();
        }
        return "无异常日志信息...";
    }

    @Override
    public SelectResult getJobData(String jobId) {
        Set<String> columns = new HashSet<>();
        columns.add("mandt");
        columns.add("matnr");
        columns.add("spras");
        columns.add("maktx");
        columns.add("maktg");
        List<Map<String,Object>> datas = new ArrayList<>();
        Map<String,Object> map1 = new HashMap<>();
        map1.put("mandt","1");
        map1.put("matnr","2");
        map1.put("spras","3");
        map1.put("maktx","4");
        map1.put("maktg","5");
        datas.add(map1);
        Map<String,Object> map2 = new HashMap<>();
        map1.put("mandt","5");
        map1.put("matnr","4");
        map1.put("spras","3");
        map1.put("maktx","2");
        map1.put("maktg","1");
        datas.add(map2);
        SelectResult result = new SelectResult(datas,2,1,columns,"412412412312312",true);
        return result;
    }


    /**
     * 更新流任务与参数之间的关系
     * @param streamId 流任务ID
     * @param params 参数
     */
    public void updateStreamAndParamVoEdge(Long streamId, List<ParamVo> params){
        if (params == null || params.isEmpty()){
            return;
        }
        List<Long> ids = params.stream().map(ParamVo::getId).collect(Collectors.toList());
        // 删除流任务与参数的边关系
        streamMapper.deleteStreamAndParamEdge(streamId);
        // 保存流任务与参数的边关系
        streamMapper.createStreamAndParamEdge(streamId,ids);
    }
    
}
