package com.datalink.fdop.element.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.idempotent.annotation.RepeatSubmit;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.element.api.domain.DataEntityTable;
import com.datalink.fdop.element.api.domain.DataEntityTableMapping;
import com.datalink.fdop.element.api.domain.DataEntityTableVo;
import com.datalink.fdop.element.service.IDataEntityTableService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
@RequestMapping(value = "/element/entity/table/{dataEntityId}")
@RestController
@Api(tags = "数据实体关联表api")
public class DataEntityTableController extends BaseController {

    @Autowired
    private IDataEntityTableService dataEntityTableService;

    @ApiOperation("保存实体关联表")
    @Log(title = "数据元素", businessType = BusinessType.INSERT)
    @RepeatSubmit(interval = 1000)
    @PostMapping(value = "/createOnUpdate")
    public R createOnUpdate(@PathVariable(value = "dataEntityId") Long dataEntityId, @Validated @RequestBody DataEntityTable dataEntityTable) {
        dataEntityTable.setDataEntityId(dataEntityId);
        return R.ok(dataEntityTableService.createOnUpdate(dataEntityTable));
    }

    @ApiOperation("修改实体关联表")
    @Log(title = "数据元素", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public R update(@PathVariable(value = "dataEntityId") Long dataEntityId, @Validated @RequestBody DataEntityTable dataEntityTable) {
        dataEntityTable.setDataEntityId(dataEntityId);
        return R.toResult(dataEntityTableService.update(dataEntityTable));
    }

    @ApiOperation("修改源、库、表信息时判断是否删除旧表")
    @Log(title = "数据元素", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/isDeleteOldTable")
    public R isDeleteOldTable(@PathVariable(value = "dataEntityId") Long dataEntityId,
                              @RequestParam(value = "dataSourceId") Long dataSourceId,
                              @RequestParam(value = "databaseName") String databaseName,
                              @RequestParam(value = "tableName") String tableName,
                              @RequestParam(value = "isDelete") Boolean isDelete) {
        return R.toResult(dataEntityTableService.isDeleteOldTable(dataEntityId, dataSourceId, databaseName, tableName, isDelete));
    }

    @ApiOperation("创建表")
    @Log(title = "数据元素", businessType = BusinessType.INSERT)
    @RepeatSubmit(interval = 1000)
    @PostMapping(value = "/createTable")
    public R createTable(@PathVariable(value = "dataEntityId") Long dataEntityId, @RequestBody DataEntityTable dataEntityTable) {
        return R.toResult(dataEntityTableService.createTable(dataEntityId, dataEntityTable));
    }

    @ApiOperation("删除实体关联表")
    @Log(title = "数据元素", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/delete")
    public R delete(@PathVariable(value = "dataEntityId") Long dataEntityId, @RequestParam(value = "isDelete") Boolean isDelete, @RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.PLEASE_SPECIFY_THE_ENTITY_ASSOCIATION_TABLE_TO_BE_DELETED);
        }
        return R.toResult(dataEntityTableService.delete(dataEntityId, isDelete, ids));
    }

    @ApiOperation("根据实体id查询实体关联表")
    @Log(title = "数据元素")
    @GetMapping(value = "/selectById")
    public R<DataEntityTable> selectById(@PathVariable(value = "dataEntityId") Long dataEntityId) {
        return R.ok(dataEntityTableService.selectById(dataEntityId));
    }

    @ApiOperation("根据实体id和租户id查询实体关联表")
    @GetMapping(value = "/selectByIdAndTenantId/{tenantId}")
    public R<DataEntityTable> selectByIdAndTenantId(@PathVariable(value = "dataEntityId") Long dataEntityId, @PathVariable(value = "tenantId") Long tenantId) {
        return R.ok(dataEntityTableService.selectById(dataEntityId, tenantId));
    }

    @ApiOperation("根据实体id数组查询实体关联表")
    @Log(title = "数据元素")
    @PostMapping(value = "/selectByIds")
    public R<List<DataEntityTable>> selectByIds(@RequestBody DataEntityTable dataEntityIds) {
        System.out.println("dataEntityIdsdataEntityIdsdataEntityIds"+dataEntityIds.toString());
        return R.ok(dataEntityTableService.selectByIds(dataEntityIds.getDataEntityIds()));
    }

    @ApiOperation("查询实体关联表")
    @Log(title = "数据元素")
    @PostMapping(value = "/list")
    public R<PageDataInfo<DataEntityTable>> list(@PathVariable(value = "dataEntityId") Long dataEntityId, @RequestBody(required = false) DataEntityTable dataEntityTable) {
        if (dataEntityTable == null) {
            dataEntityTable = new DataEntityTable();
        }
        dataEntityTable.setDataEntityId(dataEntityId);
        startPage();
        return R.ok(dataEntityTableService.list(dataEntityTable));
    }

    @ApiOperation("查询关联表的映射关系")
    @Log(title = "数据元素")
    @GetMapping(value = "/selectTableMapping")
    public R<List<DataEntityTableMapping>> selectTableMapping(@PathVariable(value = "dataEntityId") Long dataEntityId, @RequestParam(value = "tableId") Long tableId) {
        return R.ok(dataEntityTableService.selectTableMapping(dataEntityId, tableId));
    }

    @ApiOperation("根据租户id查询关联表的映射关系")
    @GetMapping(value = "/selectTableMappingByTenantId/{tenantId}")
    public R<List<DataEntityTableMapping>> selectTableMappingByTenantId(@PathVariable(value = "dataEntityId") Long dataEntityId, @PathVariable(value = "tenantId") Long tenantId, @RequestParam(value = "tableId") Long tableId) {
        return R.ok(dataEntityTableService.selectTableMapping(dataEntityId, tenantId, tableId));
    }

    @ApiOperation("查询关联表的映射关系分页")
    @Log(title = "数据元素")
    @GetMapping(value = "/selectTableMapping/paging")
    public R selectTableMappingPaging(@PathVariable(value = "dataEntityId") Long dataEntityId, @RequestParam(value = "tableId") Long tableId) {
        return R.ok(dataEntityTableService.selectTableMappingPaging(dataEntityId, tableId));
    }

    @ApiOperation("查询关联表的映射关系")
    @Log(title = "数据元素")
    @GetMapping(value = "/selectEntityTableMapping")
    public R<List<DataEntityTableMapping>> selectEntityTableMapping(@PathVariable(value = "dataEntityId") Long dataEntityId) {
        return R.ok(dataEntityTableService.selectEntityTableMapping(dataEntityId));
    }

    @ApiOperation("根据租户ID查询关联表的映射关系")
    @GetMapping(value = "/selectEntityTableMappingByTenantId/{tenantId}")
    public R<List<DataEntityTableMapping>> selectEntityTableMappingByTenantId(@PathVariable(value = "dataEntityId") Long dataEntityId, @PathVariable(value = "tenantId") Long tenantId) {
        return R.ok(dataEntityTableService.selectEntityTableMapping(dataEntityId, tenantId));
    }

    @ApiOperation("删除关联表的数据")
    @Log(title = "数据元素", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/deleteEntityTableData")
    public R deleteEntityTableData(@PathVariable(value = "dataEntityId") Long dataEntityId, @RequestParam(value = "where", required = false) String where) {
        dataEntityTableService.deleteEntityTableData(dataEntityId, where);
        return R.ok();
    }

}
