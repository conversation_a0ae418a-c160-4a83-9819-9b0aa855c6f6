package com.datalink.fdop.stream.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.ResourceUtils;
import com.datalink.fdop.common.core.utils.RestTemplateUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.graph.api.enums.RuntimeMode;
import com.datalink.fdop.stream.api.dto.ReturnVo;
import com.datalink.fdop.graph.api.dto.TaskInfo;
import com.datalink.fdop.graph.api.dto.TaskParam;
import com.datalink.fdop.stream.config.KubernetesProperties;
import io.fabric8.kubernetes.client.Config;
import io.fabric8.kubernetes.client.DefaultKubernetesClient;
import io.fabric8.kubernetes.client.KubernetesClient;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2022/8/18 15:00
 */
@Component
public class K8sManager {

    private static final Logger logger = LoggerFactory.getLogger(K8sManager.class);

    private static final String AUTHORIZATION = "Authorization";
    private static final String BEARER = "Bearer ";

    @Autowired
    private KubernetesProperties kubernetesProperties;


    /**
     * 获取请求头
     *
     * @return
     */
    public Map<String, Object> getHeader() {
        Map<String, Object> header = new HashMap<>();
        header.put(AUTHORIZATION, BEARER + kubernetesProperties.getToken());
        return header;
    }

    /**
     * 获取deployments url
     * https://*********:6443/apis/apps/v1/namespaces/{namespaces}/deployments
     *
     * @return
     */
    public String getDeploymentsUrl(String taskName) {
        return kubernetesProperties.getAddress() + "/apis/apps/v1/namespaces/flink/deployments?labelSelector=app=" + taskName + ",component=jobmanager,type=flink-native-kubernetes";
    }

    /**
     * 根据名称获取deployments url
     * https://*********:6443/apis/apps/v1/namespaces/{namespaces}/deployments/{name}
     *
     * @return
     */
    public String getDeploymentsByNameUrl(String taskName) {
        return kubernetesProperties.getAddress() + "/apis/apps/v1/namespaces/flink/deployments/" + taskName;
    }

    /**
     * 根据名称获取pods url
     * https://*********:6443/api/v1/namespaces/{namespace}/pods?labelSelector=app=mysqlcdc2iceberg,component=jobmanager,type=flink-native-kubernetes
     *
     * @return
     */
    public String getPodsUrl(String taskName) {
        return kubernetesProperties.getAddress() + "/api/v1/namespaces/flink/pods?labelSelector=app=" + taskName + ",component=jobmanager,type=flink-native-kubernetes";
    }

    /**
     * 获取任务日志
     * https://*********:6443/api/v1/namespaces/flink/pods/{name}/log?container=flink-main-container&tailLines=1000&timestamps=true&follow=false
     *
     * @param podName
     * @return
     */
    public String getLogUrl(String podName) {
        return kubernetesProperties.getAddress() + "/api/v1/namespaces/flink/pods/" + podName + "/log?container=flink-main-container&tailLines=1000&timestamps=false&follow=false";
    }

    /**
     * 删除deployments url
     * https://*********:6443/api/v1/namespaces/{namespace}/deployments/{name}
     *
     * @return
     */
    public String getDeleteDeploymentsUrl(String taskName) {
        return kubernetesProperties.getAddress() + "/apis/apps/v1/namespaces/flink/deployments/" + taskName;
    }

    /**
     * 系统定义的code是只能包含 _ 特殊符号，但提交到K8s中的flink任务的名称只能包含 - ,所以需要转换
     *
     * @param taskName
     * @return
     */
    private String convertTaskName(String taskName) {
        taskName = taskName.toLowerCase();
        if (taskName.contains("_")) {
            return taskName.replace("_", "-");
        }
        return taskName;
    }

    /**
     * 检查任务状态
     *
     * @param taskName
     * @return
     */
    public Boolean checkStreamStatus(String taskName) {
        // 转换任务名称
        taskName = convertTaskName(taskName);

        // 获取deployments
        Map deploymentsMap = RestTemplateUtils.doGet(getDeploymentsUrl(taskName), getHeader(), Map.class);

        JSONArray jsonArray = JSONObject.parseArray(JSONObject.toJSONString(MapUtils.getObject(deploymentsMap, "items")));
        if (jsonArray.isEmpty()) {
            logger.error("检查失败");
            return false;
        }

        // 判断deployments是否启动成功
        String deploymentsJson = JSONObject.toJSONString(deploymentsMap);

        String availableStatus = JSONPath.read(deploymentsJson, "$.items[0].status.conditions[0].status").toString();
        if ("false".equalsIgnoreCase(availableStatus)) {
            logger.error("检查失败:{}", JSONPath.read(deploymentsJson, "$.items[0].status.conditions[0].message").toString());
            return false;
        }

        String progressingStatus = JSONPath.read(deploymentsJson, "$.items[0].status.conditions[1].status").toString();
        if ("false".equalsIgnoreCase(progressingStatus)) {
            logger.error("检查失败:{}", JSONPath.read(deploymentsJson, "$.items[0].status.conditions[1].message").toString());
            return false;
        }

        // 判断pods是否启动成功
        Map podsMap = RestTemplateUtils.doGet(getPodsUrl(taskName), getHeader(), Map.class);
        List items = (List) MapUtils.getObject(podsMap, "items");
        if (CollectionUtils.isEmpty(items)) {
            logger.error("pods未启动");
            return false;
        }

        String podsJson = JSONObject.toJSONString(podsMap);

        String initializedStatus = JSONPath.read(podsJson, "$.items[0].status.conditions[0].status").toString();
        if ("false".equalsIgnoreCase(initializedStatus)) {
            logger.error("Initialized 失败:{}", JSONPath.read(podsJson, "$.items[0].status.conditions[0].message").toString());
            return false;
        }

        String readyStatus = JSONPath.read(podsJson, "$.items[0].status.conditions[1].status").toString();
        if ("false".equalsIgnoreCase(readyStatus)) {
            logger.error("Ready 失败:{}", JSONPath.read(podsJson, "$.items[0].status.conditions[1].message").toString());
            return false;
        }

        String containersReadyStatus = JSONPath.read(podsJson, "$.items[0].status.conditions[2].status").toString();
        if ("false".equalsIgnoreCase(containersReadyStatus)) {
            logger.error("ContainersReady 失败:{}", JSONPath.read(podsJson, "$.items[0].status.conditions[2].message").toString());
            return false;
        }

        String podScheduledStatus = JSONPath.read(podsJson, "$.items[0].status.conditions[3].status").toString();
        if ("false".equalsIgnoreCase(podScheduledStatus)) {
            logger.error("PodScheduled 失败:{}", JSONPath.read(podsJson, "$.items[0].status.conditions[3].message").toString());
            return false;
        }

        return true;
    }

    /**
     * 检查任务是否存在
     *
     * @param taskName
     * @return
     */
    public Boolean checkTaskIsExists(String taskName) {
        // 转换任务名称
        taskName = convertTaskName(taskName);

        // 获取deployments
        Map deploymentsMap = RestTemplateUtils.doGet(getDeploymentsUrl(taskName), getHeader(), Map.class);

        JSONArray jsonArray = JSONObject.parseArray(JSONObject.toJSONString(MapUtils.getObject(deploymentsMap, "items")));
        if (jsonArray.isEmpty()) {
            return false;
        }

        // 判断deployments是否启动成功
        String deploymentsJson = JSONObject.toJSONString(deploymentsMap);

        String availableStatus = JSONPath.read(deploymentsJson, "$.items[0].status.conditions[0].status").toString();
        if ("false".equalsIgnoreCase(availableStatus)) {
            logger.error("检查失败:{}", JSONPath.read(deploymentsJson, "$.items[0].status.conditions[0].message").toString());
            return true;
        }

        String progressingStatus = JSONPath.read(deploymentsJson, "$.items[0].status.conditions[1].status").toString();
        if ("false".equalsIgnoreCase(progressingStatus)) {
            logger.error("检查失败:{}", JSONPath.read(deploymentsJson, "$.items[0].status.conditions[1].message").toString());
            return true;
        }

        // 判断pods是否启动成功
        Map podsMap = RestTemplateUtils.doGet(getPodsUrl(taskName), getHeader(), Map.class);
        List items = (List) MapUtils.getObject(podsMap, "items");
        if (CollectionUtils.isEmpty(items)) {
            logger.error("pods未启动");
            return true;
        }

        String podsJson = JSONObject.toJSONString(podsMap);

        String initializedStatus = JSONPath.read(podsJson, "$.items[0].status.conditions[0].status").toString();
        if ("false".equalsIgnoreCase(initializedStatus)) {
            logger.error("Initialized 失败:{}", JSONPath.read(podsJson, "$.items[0].status.conditions[0].message").toString());
            return true;
        }

        String readyStatus = JSONPath.read(podsJson, "$.items[0].status.conditions[1].status").toString();
        if ("false".equalsIgnoreCase(readyStatus)) {
            logger.error("Ready 失败:{}", JSONPath.read(podsJson, "$.items[0].status.conditions[1].message").toString());
            return true;
        }

        String containersReadyStatus = JSONPath.read(podsJson, "$.items[0].status.conditions[2].status").toString();
        if ("false".equalsIgnoreCase(containersReadyStatus)) {
            logger.error("ContainersReady 失败:{}", JSONPath.read(podsJson, "$.items[0].status.conditions[2].message").toString());
            return true;
        }

        String podScheduledStatus = JSONPath.read(podsJson, "$.items[0].status.conditions[3].status").toString();
        if ("false".equalsIgnoreCase(podScheduledStatus)) {
            logger.error("PodScheduled 失败:{}", JSONPath.read(podsJson, "$.items[0].status.conditions[3].message").toString());
            return true;
        }

        return true;
    }


    /**
     * 获取任务日志
     *
     * @param taskName
     * @return
     */
    public String getLog(String taskName) {
        // 转换任务名称
        taskName = convertTaskName(taskName);

        // 获取deployments
        Map deploymentsMap = RestTemplateUtils.doGet(getDeploymentsUrl(taskName), getHeader(), Map.class);

        JSONArray jsonArray = JSONObject.parseArray(JSONObject.toJSONString(MapUtils.getObject(deploymentsMap, "items")));
        if (jsonArray.isEmpty()) {
            return "";
            // logger.error("检查失败");
            // throw new ServiceException("检查失败");
        }

        // 判断deployments是否启动成功
        String deploymentsJson = JSONObject.toJSONString(deploymentsMap);

        String availableStatus = JSONPath.read(deploymentsJson, "$.items[0].status.conditions[0].status").toString();
        if ("false".equalsIgnoreCase(availableStatus)) {
            logger.error("检查失败:{}", JSONPath.read(deploymentsJson, "$.items[0].status.conditions[0].message").toString());
            // throw new ServiceException("检查失败:" + JSONPath.read(deploymentsJson, "$.items[0].status.conditions[0].message").toString());
            return "";
        }

        String progressingStatus = JSONPath.read(deploymentsJson, "$.items[0].status.conditions[1].status").toString();
        if ("false".equalsIgnoreCase(progressingStatus)) {
            logger.error("检查失败:{}", JSONPath.read(deploymentsJson, "$.items[0].status.conditions[1].message").toString());
            // throw new ServiceException("检查失败:" + JSONPath.read(deploymentsJson, "$.items[0].status.conditions[1].message").toString());
            return "";
        }

        // 判断pods是否启动成功
        Map podsMap = RestTemplateUtils.doGet(getPodsUrl(taskName), getHeader(), Map.class);
        List items = (List) MapUtils.getObject(podsMap, "items");
        if (CollectionUtils.isEmpty(items)) {
            logger.error("pods未启动");
            // throw new ServiceException("pods未启动");
            return "";
        }

        String podsJson = JSONObject.toJSONString(podsMap);

        String initializedStatus = JSONPath.read(podsJson, "$.items[0].status.conditions[0].status").toString();
        if ("false".equalsIgnoreCase(initializedStatus)) {
            logger.error("Initialized 失败:{}", JSONPath.read(podsJson, "$.items[0].status.conditions[0].message").toString());
            // throw new ServiceException("Initialized 失败:" + JSONPath.read(podsJson, "$.items[0].status.conditions[0].message").toString());
            return "";
        }

        String readyStatus = JSONPath.read(podsJson, "$.items[0].status.conditions[1].status").toString();
        if ("false".equalsIgnoreCase(readyStatus)) {
            logger.error("Ready 失败:{}", JSONPath.read(podsJson, "$.items[0].status.conditions[1].message").toString());
            // throw new ServiceException("Ready 失败:" + JSONPath.read(podsJson, "$.items[0].status.conditions[1].message").toString());
            return "";
        }

        String containersReadyStatus = JSONPath.read(podsJson, "$.items[0].status.conditions[2].status").toString();
        if ("false".equalsIgnoreCase(containersReadyStatus)) {
            logger.error("ContainersReady 失败:{}", JSONPath.read(podsJson, "$.items[0].status.conditions[2].message").toString());
            // throw new ServiceException("ContainersReady 失败:" + JSONPath.read(podsJson, "$.items[0].status.conditions[2].message").toString());
            return "";
        }

        String podScheduledStatus = JSONPath.read(podsJson, "$.items[0].status.conditions[3].status").toString();
        if ("false".equalsIgnoreCase(podScheduledStatus)) {
            logger.error("PodScheduled 失败:{}", JSONPath.read(podsJson, "$.items[0].status.conditions[3].message").toString());
            // throw new ServiceException("PodScheduled 失败:" + JSONPath.read(podsJson, "$.items[0].status.conditions[3].message").toString());
            return "";
        }

        // 获取日志
        String podName = JSONPath.read(podsJson, "$.items[0].metadata.name").toString();
        String log = RestTemplateUtils.doGet(getLogUrl(podName), getHeader(), String.class);
        return log;

    }

    public String getFlinkK8sCommand(TaskInfo taskInfo) {
        // 转换任务名称
        String taskName = convertTaskName(taskInfo.getTaskName());

        TaskParam taskParam = taskInfo.getTaskParam();
        String slot = (taskParam.getSlot() == null ? 4 : taskParam.getSlot()) + "";
        String parallelism = (taskParam.getParallelism() == null ? 1 : taskParam.getParallelism()) + "";
        String jobManagerMemory = (taskParam.getJobManagerMemory() == null ? 1600 : taskParam.getJobManagerMemory()) + "m";
        String taskManagerMemory = (taskParam.getTaskManagerMemory() == null ? 1728 : taskParam.getTaskManagerMemory()) + "m";
        String runtimeMode = (taskInfo.getRuntimeMode() == null || taskInfo.getRuntimeMode() == RuntimeMode.NONE ? RuntimeMode.BATCH : taskInfo.getRuntimeMode()).name();

        // sql
        // sql = JSONObject.toJSONString(JSONObject.toJSONString(taskInfo.getSqls()).replace("\"", "\\\""));

        // 拼接sql
        String resourceContent = ResourceUtils.getResourceContent("kubernetes/flink-on-k8s");
        String shellCommand = resourceContent.replaceAll("\\$taskName", taskName)
                .replaceAll("\\$slot", slot)
                .replaceAll("\\$parallelism", parallelism)
                .replaceAll("\\$jobManagerMemory", jobManagerMemory)
                .replaceAll("\\$taskManagerMemory", taskManagerMemory)
        //        .replaceAll("\\$sql", sql)
                .replaceAll("\\$runtimeMode", runtimeMode);

//        if (StringUtils.isNotEmpty(taskInfo.getGateway())) {
//            shellCommand += " -gateway " + taskInfo.getGateway();
//        }
//        if (MapUtils.isNotEmpty(taskInfo.getInputParams())) {
//            shellCommand += " -inputParams " + JSONObject.toJSONString(taskInfo.getInputParams());
//        }
//        if (MapUtils.isNotEmpty(taskInfo.getSystemKeyMap())) {
//            shellCommand += " -systemKeys " + JSONObject.toJSONString(taskInfo.getSystemKeyMap());
//        }

        // 如果包含`则替换
        shellCommand = shellCommand.replaceAll("\\`", "%%");

        return shellCommand;
    }

    /**
     * 开启流任务
     */
    public ReturnVo startStream(TaskInfo taskInfo) {
        String shellCommand = getFlinkK8sCommand(taskInfo);

        logger.info("提交到kubernetes中命令: {}", shellCommand);
        List<String> outputValueList = new ArrayList<>();
        List<String> errorValueList = new ArrayList<>();

        try {
            Process process = Runtime.getRuntime().exec(new String[]{"/bin/sh", "-c", shellCommand});
            //用输入输出流来截取结果
            BufferedReader outputBuff = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String outputLine = null;
            while ((outputLine = outputBuff.readLine()) != null) {
                outputValueList.add(outputLine);
            }
            outputBuff.close();

            //获取错误信息
            BufferedReader errorBuff = new BufferedReader(new InputStreamReader(process.getErrorStream()));
            String errorLine = null;
            while ((errorLine = errorBuff.readLine()) != null) {
                errorValueList.add(errorLine);
            }
            errorBuff.close();
            process.waitFor();
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
        Map<String, Object> result = new HashMap<>(2);
        result.put("command", shellCommand);
        if (CollectionUtils.isNotEmpty(errorValueList)) {
            outputValueList.addAll(errorValueList);
        }
        result.put("returnValueList", outputValueList);
        return new ReturnVo(shellCommand, outputValueList);

    }

    /**
     * 停止流任务
     *
     * @param taskName
     */
    public void stopStream(String taskName) {
        // 转换任务名称
        taskName = convertTaskName(taskName);

        Map map = RestTemplateUtils.doDelete(getDeleteDeploymentsUrl(taskName), getHeader(), Map.class);
        String status = MapUtils.getString(map, "status");
        if (!"Success".equalsIgnoreCase(status)) {
            throw new ServiceException(MapUtils.getString(map, "message"));
        }
    }


    @Bean
    public KubernetesClient getKubernetesClient(){
        KubernetesClient client=new DefaultKubernetesClient();
        try {
            String content = ResourceUtils.getResourceContent("kubernetes/k8s-config.yml");
            List<String> infos = Stream.of(content.split("\n")).collect(Collectors.toList());
            Config config =Config.fromKubeconfig( infos.stream().collect(Collectors.joining(System.lineSeparator())));
            client=new DefaultKubernetesClient(config);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
        return client;
    }

}
