package com.datalink.fdop.stream.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.stream.api.domain.StreamCdc;
import com.datalink.fdop.stream.api.domain.StreamCdcMenu;
import com.datalink.fdop.stream.api.domain.StreamCdcTree;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;


public interface StreamCdcMenuMapper extends BaseMapper<StreamCdcMenu> {

    int createStreamMenuEdge(@Param("pid") Long pid, @Param("ids") List<Long> ids);

    int insertStreamtMenu(@Param("streamMenu") StreamCdcMenu streamMenu);

    int updateById(StreamCdcMenu streamMenu);

    int bacthUpdatePidById(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteStreamMenuEdge(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteBatchIds(@Param("ids") List<Long> ids);

    VlabelItem<StreamCdcMenu> selectById(Long id);

    VlabelItem<StreamCdcMenu> selectByCode(String code);

    VlabelItem<StreamCdcMenu> selectByPid(Long pid);

    VlabelItem<StreamCdcMenu> checkCodeIsExists(@Param("id") Long id, @Param("code") String code);

    List<Long> selectIdsByPid(Long pid);

    IPage<StreamCdc> overview(@Param("page") Page<StreamCdc> page, @Param("pid") Long pid, @Param("sort") String sort, @Param("searchVo") SearchVo searchVo);

    List<StreamCdcTree> selectMenuTree(@Param("sort") String sort, @Param("code") String code);

    Integer querySerialNumber();
}
