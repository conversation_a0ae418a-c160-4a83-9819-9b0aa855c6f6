package com.datalink.fdop.govern.service;

import com.datalink.fdop.common.core.enums.MenuType;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.govern.api.domain.MetadataMenu;
import com.datalink.fdop.govern.api.domain.MetadataTree;

import java.util.List;

public interface MetadataMenuService {
    int create(MetadataMenu metadataMenu);

    int update(MetadataMenu metadataMenu);

    int delete(List<Long> ids);

    List<MetadataTree> tree(String sort, String code, Boolean isQueryNode);

    int createNode(Long menuId, Long nodeId, MenuType menuType, String code, int serial);

    PageDataInfo overview(Long pid, String sort, SearchVo searchVo);

    int delNode(Long menuId, Long nodeId, String code);

}
