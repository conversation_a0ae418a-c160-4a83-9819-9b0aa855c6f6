package com.datalink.fdop.govern.service.impl;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.TreeVo;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.tree.TreeUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.govern.api.domain.DataProcessFramework;
import com.datalink.fdop.govern.api.domain.DataProcessFrameworkMenu;
import com.datalink.fdop.govern.mapper.DataProcessFrameworkMapper;
import com.datalink.fdop.govern.mapper.DataProcessFrameworkMenuMapper;
import com.datalink.fdop.govern.service.IDataProcessFrameworkMenuService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class DataProcessFrameworkMenuServiceImpl implements IDataProcessFrameworkMenuService {

    /**
     * 全局序号间隔参数 当前数领 * 序号间隔数量
     */
    @Value("${dwms.serial-interval}")
    private Integer serialIntervalNumber;

    @Autowired
    private DataProcessFrameworkMapper processFrameworkMapper;
    
    @Autowired
    private DataProcessFrameworkMenuMapper processFrameworkMenuMapper;

    @Override
    public int create(DataProcessFrameworkMenu processFrameworkMenu) {
        if (processFrameworkMenuMapper.selectByCode(processFrameworkMenu.getCode()) != null) {
            throw new ServiceException(Status.DATA_PROCESS_FRAMEWORK_MENU_EXIST);
        }
        processFrameworkMenu.setId(IdWorker.getId());
        int insert = processFrameworkMenuMapper.insertMenu(processFrameworkMenu);
        // 创建菜单边关系
        if (insert > 0 && processFrameworkMenu.getPid() != -1L) {
            // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
            processFrameworkMenuMapper.createMenuEdge(processFrameworkMenu.getPid(), Arrays.asList(processFrameworkMenu.getId()));
        }
        return insert;
    }

    @Override
    public int update(DataProcessFrameworkMenu processFrameworkMenu) {
        VlabelItem<DataProcessFrameworkMenu> vlabelItem = processFrameworkMenuMapper.selectById(processFrameworkMenu.getId());
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATA_PROCESS_FRAMEWORK_MENU_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(processFrameworkMenu.getCode()) && processFrameworkMenuMapper.checkCodeIsExists(processFrameworkMenu.getId(), processFrameworkMenu.getCode()) != null) {
            throw new ServiceException(Status.DATA_PROCESS_FRAMEWORK_MENU_EXIST);
        }
        int update = processFrameworkMenuMapper.updateById(processFrameworkMenu);
        if (update > 0) {
            // 获取修改前的菜单pid,并删除修改前的边关系
            processFrameworkMenuMapper.deleteMenuEdge(Arrays.asList(processFrameworkMenu.getId()), vlabelItem.getProperties().getPid());
            // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
            if (processFrameworkMenu.getPid() != -1L) {
                processFrameworkMenuMapper.createMenuEdge(processFrameworkMenu.getPid(), Arrays.asList(processFrameworkMenu.getId()));
            }
        }
        return update;
    }

    @Override
    public int delete(List<Long> ids) {
        for (Long id : ids) {
            VlabelItem<DataProcessFrameworkMenu> vlabelItem = processFrameworkMenuMapper.selectById(id);
            if (vlabelItem == null) {
                continue;
            }
            DataProcessFrameworkMenu processFrameworkMenu = vlabelItem.getProperties();
            // 获取当前被删除菜单的所有子级菜单
            List<Long> menuIdList = processFrameworkMenuMapper.selectIdsByPid(id);
            // 如果存在子级菜单，则将所有子级菜单的pid修改为被删除菜单的pid
            if (CollectionUtils.isNotEmpty(menuIdList)) {
                int update = processFrameworkMenuMapper.batchUpdatePidById(menuIdList, processFrameworkMenu.getPid());
                if (update > 0 && processFrameworkMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    processFrameworkMenuMapper.createMenuEdge(processFrameworkMenu.getPid(), menuIdList);
                }
            }
            // 获取当前被删除菜单的所有子级
            List<Long> elementIdList = processFrameworkMenuMapper.selectIdsByPid(id);
            // 如果存在子级，则将所有子级的pid修改为被删除菜单的pid
            if (CollectionUtils.isNotEmpty(elementIdList)) {
                int update = processFrameworkMenuMapper.batchUpdatePidById(elementIdList, processFrameworkMenu.getPid());
                if (update > 0 && processFrameworkMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    processFrameworkMenuMapper.createMenuEdge(processFrameworkMenu.getPid(), elementIdList);
                }
            }
        }
        // 删除菜单并且删除菜单的边关系
        return processFrameworkMenuMapper.deleteBatchIds(ids);
    }

    @Override
    public DataProcessFrameworkMenu selectById(Long id) {
        VlabelItem<DataProcessFrameworkMenu> vlabelItem = processFrameworkMenuMapper.selectById(id);
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATA_PROCESS_FRAMEWORK_MENU_NOT_EXIST);
        }
        return vlabelItem.getProperties();
    }

    @Override
    public List<TreeVo> tree(String sort, String code, Boolean isQueryNode) {
        List<TreeVo> trees = new ArrayList<>();
        // 添加节点
        if (isQueryNode) {
            //trees.addAll(processFrameworkMapper.selectTree(sort, code));
        }
        // 添加菜单树
        trees.addAll(processFrameworkMenuMapper.selectMenuTree(sort, null));
        // 递归成树结构
        List<TreeVo> treeList = (List<TreeVo>) TreeUtils.getTree(trees);
        // 条件查找删除空集合的菜单
        if (StringUtils.isNotEmpty(code) && isQueryNode) {
            TreeUtils.removeEmptyChilderAndMenu(treeList);
        }
        return treeList;
    }

    @Override
    public PageDataInfo<DataProcessFramework> overview(Long pid, String sort, SearchVo searchVo) {
        Page<DataProcessFramework> page = PageUtils.getPage(DataProcessFramework.class);
        // 获取分页参数
        IPage<DataProcessFramework> pageInfoList = processFrameworkMenuMapper.overview(page, pid, sort, searchVo);
        List<DataProcessFramework> records = pageInfoList.getRecords();
        return PageUtils.getPageInfo(records, (int) pageInfoList.getTotal());
    }

    @Override
    public Integer querySerialNumber(Boolean menuFlag) {
        if (menuFlag) {
            return processFrameworkMenuMapper.querySerialNumber() * serialIntervalNumber;
        } else {
            return processFrameworkMapper.querySerialNumber() * serialIntervalNumber;
        }
    }
}
