package com.datalink.fdop.element.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datalink.fdop.element.api.domain.DataElementStructure;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


public interface GraphTableMapper extends BaseMapper {

    // 判断表是否存在
    Boolean isExistsTable(String tableName);

    // 创建表
    List<Map<String, Object>> createGraphTable(@Param("tableName") String tableName, @Param("ageStr") String ageStr);

    String deleteGraphTable(String tableName);

    List<Map<String, Object>> getData(
            @Param("mapTableName") String mapTableName,
            @Param("dataElementStructureList") List<DataElementStructure> dataElementStructureList
    );

    int getDataCount(
            @Param("mapTableName")String mapTableName,
            @Param("dataElementStructureList")List<DataElementStructure> dataElementStructureList);
}