package com.datalink.fdop.element.controller;

import com.google.common.collect.Maps;
import com.datalink.fdop.common.core.constant.Constants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.element.api.domain.DataElement;
import com.datalink.fdop.element.api.domain.DataElementStructure;
import com.datalink.fdop.element.service.IDataElementValueService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
@RequestMapping(value = "/element/value")
@RestController
@Api(tags = "数据元素表数据api")
public class DataElementValueController extends BaseController {

    @Autowired
    private IDataElementValueService dataElementValueService;

    @ApiOperation("预览数据")
    @Log(title = "数据元素", businessType = BusinessType.OTHER)
    @GetMapping(value = "/{dataElementId}/previewData")
    public R<List<Map<String, Object>>> previewData(
            @PathVariable(value = "dataElementId") Long dataElementId,
            @RequestParam(value = "isPk", required = false) Boolean isPk
    ) {
        return R.ok(dataElementValueService.previewData(dataElementId, isPk));
    }


    @ApiOperation("预览表格数据")
    @Log(title = "数据元素", businessType = BusinessType.OTHER)
    @GetMapping(value = "/{dataElementId}/previewDataTable")
    public R<Map<String, Object>> previewDataTable(
            @PathVariable(value = "dataElementId") Long dataElementId,
            @RequestParam(value = "isPk", required = false) Boolean isPk
    ) {
        Map<String,Object> result= Maps.newHashMap();
        //获取元素信息
        DataElement dataElement=dataElementValueService.getDataElement(dataElementId);
        //获取表结构
        List<DataElementStructure> structures=dataElementValueService.getDataElementStructure(dataElementId);
        startPage();
        PageDataInfo<Map<String, Object>> pageDataInfo=dataElementValueService.previewDataTable(dataElementId, isPk,dataElement,structures);
        if (isPk!=null) {
            List<DataElementStructure> isPkDataElementStructureList = structures.stream()
                    .filter(dataElementStructure -> dataElementStructure.getIsPk() == isPk)
                    .collect(Collectors.toList());
            result.put(Constants.DATA,isPkDataElementStructureList);
            result.put(Constants.VALUE,pageDataInfo);
        }else {
            result.put(Constants.DATA,structures);
            result.put(Constants.VALUE,pageDataInfo);
        }
        return R.ok(result) ;
    }
}
