package com.datalink.fdop.stream.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.graph.api.dto.TaskInfo;
import com.datalink.fdop.stream.api.domain.Stream;
import com.datalink.fdop.stream.api.domain.StreamHistory;
import com.datalink.fdop.stream.mapper.StreamHistoryMapper;
import com.datalink.fdop.stream.service.IStreamHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 流任务历史执行记录Service实现类
 */
@Service
public class StreamHistoryServiceImpl implements IStreamHistoryService {

    @Autowired
    private StreamHistoryMapper streamHistoryMapper;

    @Override
    public StreamHistory create(StreamHistory streamHistory) {
        streamHistory.setId(IdWorker.getId());
        // 设置序号
        Integer sort = streamHistoryMapper.querySerialNumber();
        streamHistory.setSort(sort);
        streamHistory.setStream(streamHistory.getStream());
        streamHistory.setTaskInfo(streamHistory.getTaskInfo().replace("'","\\'"));
        int insert = streamHistoryMapper.insertStreamHistory(streamHistory);
        // 创建与流任务的边关系
        if (insert > 0 && streamHistory.getStreamId() != -1L) {
            streamHistoryMapper.createStreamAndStreamHistoryEdge(streamHistory.getStreamId(), Arrays.asList(streamHistory.getId()));
        }
        return streamHistory;
    }

    @Override
    public int update(StreamHistory streamHistory) {
        return 0;
    }

    @Override
    public int delete(List<Long> ids, Long streamId) {
        // 先删除边关系
        streamHistoryMapper.deleteStreamAndStreamHistoryEdge(ids, streamId);
        return streamHistoryMapper.deleteBatchIds(ids);
    }

    @Override
    public PageDataInfo<StreamHistory> selectHistoryListByStreamId(Long streamId, String sort, SearchVo searchVo) {
        // 获取分页参数
        Page<StreamHistory> page = PageUtils.getPage(StreamHistory.class);
        IPage<StreamHistory> streamHistoryIPage = streamHistoryMapper.selectHistoryListByStreamId(page, streamId, sort, searchVo);
        List<StreamHistory> records = streamHistoryIPage.getRecords();
        for (StreamHistory history : records){
            history.setStreamObj(JSON.parseObject(history.getStream(), Stream.class));
            history.setTaskInfoObj(JSON.parseObject(history.getTaskInfo().replace("\\'","'"), TaskInfo.class));
        }
        return PageUtils.getPageInfo(records, (int) streamHistoryIPage.getTotal());
    }

}
