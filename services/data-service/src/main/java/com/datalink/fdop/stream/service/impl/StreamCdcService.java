package com.datalink.fdop.stream.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.DataModelType;
import com.datalink.fdop.common.core.enums.DbType;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.drive.api.domain.DataSourceBasicInfo;
import com.datalink.fdop.drive.service.IDataSourceService;
import com.datalink.fdop.element.api.domain.DataEntityTable;
import com.datalink.fdop.element.service.IDataEntityTableService;
import com.datalink.fdop.seatunnel.api.domain.DataModelOptions;
import com.datalink.fdop.seatunnel.api.domain.SeaTunnelField;
import com.datalink.fdop.stream.api.domain.DorisConfig;
import com.datalink.fdop.stream.api.domain.StreamCdc;
import com.datalink.fdop.stream.config.KubernetesProperties;
import com.datalink.fdop.stream.mapper.StreamCdcMapper;
import com.datalink.fdop.stream.mapper.StreamCdcMenuMapper;
import com.datalink.fdop.stream.service.DebeziumService;
import com.datalink.fdop.stream.service.IStreamCdcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
@Service
@Slf4j
public class StreamCdcService implements IStreamCdcService {

    @Autowired
    private StreamCdcMapper streamCdcMapper;

    @Autowired
    private StreamCdcMenuMapper streamCdcMenuMapper;

    @Autowired
    private IDataEntityTableService dataEntityTableService;

    @Autowired
    private IDataSourceService dataSourceService;

    @Autowired
    private DebeziumService debeziumService;


    @Autowired
    private KubernetesProperties kubernetesProperties;

    private StreamCdc checkStream(Long id) {
        VlabelItem<StreamCdc> vlabelItem = streamCdcMapper.selectById(id);
        if (vlabelItem == null) {
            throw new ServiceException(Status.STREAM_DOES_NOT_EXIST);
        }
        return vlabelItem.getProperties();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int create(StreamCdc streamCdc) {
        if (streamCdc.getPid() != -1L && streamCdcMenuMapper.selectById(streamCdc.getPid()) == null) {
            throw new ServiceException(Status.STREAM_MENU_DOES_NOT_EXIST);
        }
        if (streamCdcMapper.selectByCode(streamCdc.getCode()) != null) {
            throw new ServiceException(Status.STREAM_ALREADY_EXISTS);
        }
        streamCdc.setId(IdWorker.getId());
        int insert = streamCdcMapper.insertStream(streamCdc);
        // 创建元素边关系
        if (insert > 0 && streamCdc.getPid() != -1L) {
            // 如果修改了元素层级，并且不是置为顶级菜单，则需要添加点和菜单的边关系

            streamCdcMapper.createStreamAndMenuEdge(streamCdc.getPid(), Arrays.asList(streamCdc.getId()));
        }
        return insert;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(StreamCdc streamCdc) {
        VlabelItem<StreamCdc> vlabelItem = streamCdcMapper.selectById(streamCdc.getId());
        if (vlabelItem == null) {
            throw new ServiceException(Status.STREAM_DOES_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(streamCdc.getCode()) && streamCdcMapper.checkCodeIsExists(streamCdc.getId(), streamCdc.getCode()) != null) {
            throw new ServiceException(Status.STREAM_ALREADY_EXISTS);
        }
        // 不能拖拽到节点里面
        if (streamCdc.getPid() != null && streamCdc.getPid() != -1L && streamCdcMenuMapper.selectById(streamCdc.getPid()) == null) {
            throw new ServiceException(Status.UNKNOWN_STREAM_MENU);
        }
        StreamCdc streamCdcOrg = vlabelItem.getProperties();
        if (streamCdcOrg.getType().equalsIgnoreCase(DbType.DORIS.getCode())) {
            // 拼接Doris Routine Load SQL
            streamCdcOrg.setDorisConfig(streamCdc.getDorisConfig());
            String dorisRoutineLoadSql = updateDorisRoutineLoadSql(streamCdcOrg);
            // 更新生成配置
            streamCdc.setBaseTaskInfo(dorisRoutineLoadSql);
            // 处理页面传过来的转义符\"
            streamCdc.setTaskInfo(streamCdc.getTaskInfo().replace("\\\"", "\\\\\""));
            if (StringUtils.isEmpty(streamCdc.getTaskInfo())) {
                streamCdc.setTaskInfo(dorisRoutineLoadSql);
            }
        }
        String code = vlabelItem.getProperties().getCode();
        if (streamCdc.getBaseTaskInfo() != null) {
            // 转义 存入PG age
            streamCdc.setBaseTaskInfo(streamCdc.getBaseTaskInfo().replace("'", "\\'").replace("{{TASK_CODE}}", code));
        }
        if (streamCdc.getTaskInfo() != null) {
            // 转义 存入PG age
            streamCdc.setTaskInfo(streamCdc.getTaskInfo().replace("'", "\\'").replace("{{TASK_CODE}}", code));
        }
        int update = streamCdcMapper.updateById(streamCdc);
        if (update > 0) {
            // 获取修改前的菜单pid,并删除修改前的点和菜单的边关系
            streamCdcMapper.deleteStreamAndMenuEdge(Arrays.asList(streamCdc.getId()), vlabelItem.getProperties().getPid());
            if (streamCdc.getPid() != -1L) {
                // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                streamCdcMapper.createStreamAndMenuEdge(streamCdc.getPid(), Arrays.asList(streamCdc.getId()));
            }
        }

        return update;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int delete(List<Long> ids) {
        return streamCdcMapper.deleteBatchIds(ids);
    }

    @Override
    public StreamCdc selectById(Long id) {
        VlabelItem<StreamCdc> vlabelItem = streamCdcMapper.selectById(id);
        if (vlabelItem == null) {
            throw new ServiceException(Status.STREAM_DOES_NOT_EXIST);
        }
        StreamCdc streamCdc = vlabelItem.getProperties();
        if (StringUtils.isNotEmpty(streamCdc.getTaskInfo())) {
            int running = 0;
            int failed = 0;
            if (streamCdc.getType().equalsIgnoreCase(DbType.DORIS.getCode())) {
                streamCdc.setTaskStatus(showDorisStatus(streamCdc));
            } else {
                try {
                    JSONObject jsonObject = JSON.parseObject(streamCdc.getTaskInfo());
                    String name = jsonObject.getString("name");
                    name = getName(name);
                    jsonObject.put("name", name);
                    log.info("begin getConnectorStatus");
                    JSONObject re = debeziumService.getConnectorStatus(name);
                    log.info("end getConnectorStatus");
                    streamCdc.setStatus(re.getJSONObject("connector").getString("state"));
                    List<JSONObject> tasks = JSONArray.parseArray(JSONObject.toJSONString(re.get("tasks")), JSONObject.class);
                    for (JSONObject objest : tasks) {
                        String state = objest.getString("state");
                        if ("RUNNING".equals(state)) {
                            running++;
                        } else if ("FAILED".equals(state)) {
                            failed++;
                        }
                    }
                    JSONObject status = new JSONObject();
                    status.put("RUNNING", running);
                    status.put("FAILED", failed);
                    status.put("EXECUTE_INFO", tasks);
                    streamCdc.setTaskStatus(status);
                } catch (Exception e) {
                    log.error("debezium服务未找到！或者没有上线该dbz");
                }
            }
        }
        return streamCdc;
    }

    @Override
    public int online(String code) {
        return 1;
    }

    @Override
    public int offline(String code) {
        try {
            debeziumService.deleteConnector(code);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 1;
    }

    @Override
    public JSONObject showDorisStatus(StreamCdc streamCdc) {
        JSONObject status = new JSONObject();
        int running = 0;
        int failed = 0;
        String executeInfo = null;
        try {
            // 获取物理模型对应的源库表信息
            DorisConfig dorisConfig = JSONObject.parseObject(streamCdc.getDorisConfig(), DorisConfig.class);
            if (dorisConfig == null) {
                failed++;
                status.put("RUNNING", running);
                status.put("FAILED", failed);
                status.put("EXECUTE_INFO", "无法获取DorisRoutineLoad的配置信息");
                return status;
            }
            DataModelOptions sinkOptions = dorisConfig.getSinkOptions();
            Long dataSourceId = sinkOptions.getId();
            String databaseName = sinkOptions.getDatabaseName();
            if (sinkOptions.getModelType() == DataModelType.ENTITY) {
                DataEntityTable dataEntityTable = this.getRemoteDataEntityTable(sinkOptions.getTenantId(), sinkOptions.getId());
                dataSourceId = dataEntityTable.getDataSourceId();
                databaseName = dataEntityTable.getDatabaseName();
            }
            List<Map<String, Object>> routineLoadList = dataSourceService.execDqlSql(dataSourceId, String.format(DorisUtil.SHOW_ROUTINE_LOAD, databaseName, streamCdc.getCode()));
            if (CollectionUtils.isEmpty(routineLoadList)) {
                failed++;
                status.put("RUNNING", running);
                status.put("FAILED", failed);
                status.put("EXECUTE_INFO", "未找到DorisRoutineLoad的运行信息");
                return status;
            }
            Map<String, Object> routineLoadMap = null;
            // 遍历集合，Name与code相同的，去State字段
            for (Map<String, Object> map : routineLoadList) {
                if (streamCdc.getCode().equals(map.get("Name"))) {
                    routineLoadMap = map;
                    break;
                }
            }
            if (routineLoadMap != null) {
                // 判断状态 不等于RUNNING就获取Doris的配置信息
                executeInfo = "DorisRoutineLoad的运行状态为：" + routineLoadMap.get("State") + "，\n" +
                        "作业运行进度：" + routineLoadMap.get("Progress") + "，\n" +
                        "作业延迟状态：" + routineLoadMap.get("Lag") + "，\n" +
                        "暂停时间：" + routineLoadMap.get("PauseTime") + "，\n" +
                        "异常原因：" + routineLoadMap.get("ReasonOfStateChanged") + "，\n" +
                        "异常详情地址：" + routineLoadMap.get("ErrorLogUrls") + "，\n" +
                        "其他异常消息：" + routineLoadMap.get("OtherMsg") + "，\n";
                running++;
            } else {
                failed++;
                executeInfo = "未找到DorisRoutineLoad的运行信息";
            }
        } catch (Exception e) {
            failed++;
            executeInfo = "获取DorisRoutineLoad的运行信息失败：" + e.getMessage();
        }
        status.put("RUNNING", running);
        status.put("FAILED", failed);
        status.put("EXECUTE_INFO", executeInfo);
        return status;
    }

    @Override
    public String getDorisMonitorInfo() {
        return "";
    }

    @Override
    public R dorisOnline(StreamCdc streamCdc) {
        // 获取物理模型对应的源库表信息
        DorisConfig dorisConfig = JSONObject.parseObject(streamCdc.getDorisConfig(), DorisConfig.class);
        if (dorisConfig == null) {
            return R.fail("未检测到Doris配置");
        }
        DataModelOptions sinkOptions = dorisConfig.getSinkOptions();
        Long dataSourceId = sinkOptions.getId();
        if (sinkOptions.getModelType() == DataModelType.ENTITY) {
            DataEntityTable dataEntityTable = this.getRemoteDataEntityTable(sinkOptions.getTenantId(), sinkOptions.getId());
            if (dataEntityTable == null) {
                return R.fail("未检测到实体源库表映射信息");
            }
            dataSourceId = dataEntityTable.getDataSourceId();
        }
        return executeDorisSql(dorisConfig, dataSourceId, streamCdc.getTaskInfo());
    }

    @Override
    public R dorisOffline(StreamCdc streamCdc) {
        return dorisStop(streamCdc);
    }

    @Override
    public R dorisPause(StreamCdc streamCdc) {
        // 获取物理模型对应的源库表信息
        DorisConfig dorisConfig = JSONObject.parseObject(streamCdc.getDorisConfig(), DorisConfig.class);
        if (dorisConfig == null) {
            return R.fail("未检测到Doris配置");
        }
        DataModelOptions sinkOptions = dorisConfig.getSinkOptions();
        Long dataSourceId = sinkOptions.getId();
        String databaseName = sinkOptions.getDatabaseName();
        if (sinkOptions.getModelType() == DataModelType.ENTITY) {
            DataEntityTable dataEntityTable = this.getRemoteDataEntityTable(sinkOptions.getTenantId(), sinkOptions.getId());
            if (dataEntityTable == null) {
                return R.fail("未检测到实体源库表映射信息");
            }
            dataSourceId = dataEntityTable.getDataSourceId();
            databaseName = dataEntityTable.getDatabaseName();
        }
        return executeDorisSql(dorisConfig, dataSourceId, String.format(DorisUtil.PAUSE_ROUTINE_LOAD, databaseName, streamCdc.getCode()));
    }

    @Override
    public R dorisResume(StreamCdc streamCdc) {
        // 获取物理模型对应的源库表信息
        DorisConfig dorisConfig = JSONObject.parseObject(streamCdc.getDorisConfig(), DorisConfig.class);
        if (dorisConfig == null) {
            return R.fail("未检测到Doris配置");
        }
        DataModelOptions sinkOptions = dorisConfig.getSinkOptions();
        Long dataSourceId = sinkOptions.getId();
        String databaseName = sinkOptions.getDatabaseName();
        if (sinkOptions.getModelType() == DataModelType.ENTITY) {
            DataEntityTable dataEntityTable = this.getRemoteDataEntityTable(sinkOptions.getTenantId(), sinkOptions.getId());
            if (dataEntityTable == null) {
                return R.fail("未检测到实体源库表映射信息");
            }
            dataSourceId = dataEntityTable.getDataSourceId();
            databaseName = dataEntityTable.getDatabaseName();
        }
        return executeDorisSql(dorisConfig, dataSourceId, String.format(DorisUtil.RESUME_ROUTINE_LOAD, databaseName, streamCdc.getCode()));
    }

    @Override
    public R dorisStop(StreamCdc streamCdc) {
        // 获取物理模型对应的源库表信息
        DorisConfig dorisConfig = JSONObject.parseObject(streamCdc.getDorisConfig(), DorisConfig.class);
        if (dorisConfig == null) {
            return R.fail("未检测到Doris配置");
        }
        DataModelOptions sinkOptions = dorisConfig.getSinkOptions();
        Long dataSourceId = sinkOptions.getId();
        String databaseName = sinkOptions.getDatabaseName();
        if (sinkOptions.getModelType() == DataModelType.ENTITY) {
            DataEntityTable dataEntityTable = this.getRemoteDataEntityTable(sinkOptions.getTenantId(), sinkOptions.getId());
            if (dataEntityTable == null) {
                return R.fail("未检测到实体源库表映射信息");
            }
            dataSourceId = dataEntityTable.getDataSourceId();
            databaseName = dataEntityTable.getDatabaseName();
        }
        return executeDorisSql(dorisConfig, dataSourceId, String.format(DorisUtil.STOP_ROUTINE_LOAD, databaseName, streamCdc.getCode()));
    }

    @Override
    public R checkDorisSql(String sql) {
        return R.ok();
    }

    private String getName(String name) {
        name = name.replaceAll("_", "");
        name = "debezium" + name;
        name = name.toLowerCase();
        return name;
    }

    /**
     * 更新DorisRoutineLoadSql
     *
     * @param streamCdc
     */
    private String updateDorisRoutineLoadSql(StreamCdc streamCdc) {
        DorisConfig dorisConfig = JSONObject.parseObject(streamCdc.getDorisConfig(), DorisConfig.class);
        if (dorisConfig == null) {
            return "";
        }
        // 来源信息 Kafka
        DataModelOptions sourceOptions = dorisConfig.getSourceOptions();
        // 目标信息 Doris
        DataModelOptions sinkOptions = dorisConfig.getSinkOptions();
        if (sinkOptions.getModelType() == DataModelType.ENTITY) {
            DataEntityTable dataEntityTable = this.getRemoteDataEntityTable(sinkOptions.getTenantId(), sinkOptions.getId());
            // 更新实体映射的库表信息
            sinkOptions.setDatabaseName(dataEntityTable.getDatabaseName());
            sinkOptions.setTableName(dataEntityTable.getTableName());
        }
        String dataBaseName = sinkOptions.getDatabaseName();
        String tableName = sinkOptions.getTableName();
//        String columns = String.join(",",flinkApiService.getSourceTableColumn(dataModel));
        List<SeaTunnelField> sourceFields = sourceOptions.getSeaTunnelFields();
        List<SeaTunnelField> sinkFields = sinkOptions.getSeaTunnelFields();

        StringBuffer strBuffer = new StringBuffer();
        strBuffer.append("CREATE ROUTINE LOAD ").append(dataBaseName).append(".")
                .append(streamCdc.getCode()).append(" ON ").append(tableName).append("\n")
                .append("COLUMNS TERMINATED BY \",\",\n");
        if (StringUtils.isNotEmpty(dorisConfig.getWhereExpr())) {
            strBuffer.append("WHERE ").append(dorisConfig.getWhereExpr()).append(",\n");
        }
        if (StringUtils.isNotEmpty(dorisConfig.getDeleteExpr())) {
            strBuffer.append("DELETE ON ").append(dorisConfig.getDeleteExpr()).append(",\n");
        }
        if (StringUtils.isNotEmpty(dorisConfig.getOrderByColumn())) {
            strBuffer.append("ORDER BY ").append(dorisConfig.getOrderByColumn()).append(",\n");
        }
        String columns = sinkFields.stream().map(SeaTunnelField::getFieldName).collect(Collectors.joining(","));
        // 拼接debezium json 特殊处理逻辑
        if ("debezium".equals(dorisConfig.getImportFormat())) {
            StringBuffer debeziumColumns = new StringBuffer("op,before,after,");
            for (int i = 0; i < sinkFields.size(); i++) {
                SeaTunnelField sourceField = sourceFields.get(i);
                SeaTunnelField sinkField = sinkFields.get(i);
                String debeziumColName = String.format("%s = if(op = 'd', get_json_string(before, \"$.%s\"), get_json_string(after,\"$.%s\"))", sinkField.getFieldName(), sourceField.getFieldName(), sourceField.getFieldName());
                if ("datetime".equals(sinkField.getFieldType()) || "时戳类型".equals(sinkField.getFieldType())) {
                    debeziumColName = String.format("%s = CONCAT(FROM_UNIXTIME(if(op = 'd', get_json_string(before, \"$.%s\"), get_json_string(after,\"$.%s\")) / 1000 - 8 * 3600, '%s'), '.0')", sinkField.getFieldName(), sourceField.getFieldName(), sourceField.getFieldName(), "%Y-%m-%d %H:%i:%S");
                }
                debeziumColumns.append(debeziumColName).append(",");
            }
            debeziumColumns.append("__DORIS_DELETE_SIGN__ = if(op = 'd', 1, 0)");
            columns = debeziumColumns.toString();
        }
        strBuffer.append("COLUMNS(").append(columns).append(")")
                .append("\nPROPERTIES(\n");
        StringBuffer propertiesBuffer = new StringBuffer();
        // 期望的并发度
        if (!StringUtils.isEmpty(dorisConfig.getParallelism())) {
            propertiesBuffer.append("   \"desired_concurrent_number\" = ").append("\"").append(dorisConfig.getParallelism()).append("\"").append(",\n");
        }
        // 每个子任务最大执行时间，单位是秒。范围为 5 到 60。默认为10。
        if (!StringUtils.isEmpty(dorisConfig.getMaximumExeTime())) {
            propertiesBuffer.append("   \"max_batch_interval\" = ").append("\"").append(dorisConfig.getMaximumExeTime()).append("\"").append(",\n");
        }
        // 每个子任务最多读取的行数。必须大于等于200000。默认是200000。
        if (!StringUtils.isEmpty(dorisConfig.getMaximumRowsNumber())) {
            propertiesBuffer.append("   \"max_batch_rows\" = ").append("\"").append(dorisConfig.getMaximumRowsNumber()).append("\"").append(",\n");
        }
        // 每个子任务最多读取的字节数。单位是字节，范围是 100MB 到 1GB。默认是 1G。
        if (!StringUtils.isEmpty(dorisConfig.getMaximumByteNumber())) {
            propertiesBuffer.append("   \"max_batch_size\" = ").append("\"").append(dorisConfig.getMaximumByteNumber()).append("\"").append(",\n");
        }
        // 采样窗口内，允许的最大错误行数。必须大于等于0。默认是 0，即不允许有错误行。
        if (!StringUtils.isEmpty(dorisConfig.getMaximumErrorNumber())) {
            propertiesBuffer.append("   \"max_error_number\" = ").append("\"").append(dorisConfig.getMaximumErrorNumber()).append("\"").append(",\n");
        }
        strBuffer.append(propertiesBuffer);
        if ("debezium".equals(dorisConfig.getImportFormat())) {
            strBuffer.append("  \"strict_mode\" = \"false\"").append(",\n");
            strBuffer.append("  \"format\" = \"json\"").append(",\n");
            strBuffer.append("  \"json_root\" = \"$.payload\"").append(",\n");
            strBuffer.append("  \"strip_outer_array\" = ").append("\"").append(dorisConfig.getBooleanType()).append("\"");
        } else if ("json".equals(dorisConfig.getImportFormat())) {
            String jsonPaths = sourceFields.stream()
                    .map(field -> StringUtils.isNotEmpty(sourceOptions.getQuerySql()) ? sourceOptions.getQuerySql() : "$." + field.getFieldName())
                    .map(path -> "\\\\\"" + path + "\\\\\"")
                    .collect(Collectors.joining(","));
            strBuffer.append("  \"jsonpaths\" = ").append("\"[").append(jsonPaths).append("]\",\n");
            //  导入json方式分为：简单模式和匹配模式。如果设置了jsonpath则为匹配模式导入，否则为简单模式导入，具体可参考示例。
            strBuffer.append("  \"jsonpaths\" = ").append("\"[").append(jsonPaths).append("]\",\n");
            strBuffer.append("  \"strict_mode\" = \"false\"").append(",\n");
            strBuffer.append("  \"format\" = \"json\"").append(",\n");
            strBuffer.append("  \"strip_outer_array\" = ").append("\"").append(dorisConfig.getBooleanType()).append("\"");
        } else {
            // 是否开启严格模式，默认为关闭。如果开启后，非空原始数据的列类型变换如果结果为 NULL，则会被过滤。指定方式为 "strict_mode" = "true"
            strBuffer.append("  \"strict_mode\" = ").append("\"").append(dorisConfig.getStrictMode()).append("\"").append(",\n");
            // 指定导入数据格式，默认是csv，支持json格式。
            strBuffer.append("  \"format\" = ").append("\"").append(dorisConfig.getImportFormat()).append("\"").append(",\n");
            // 布尔类型，为true表示json数据以数组对象开始且将数组对象中进行展平，默认值是false。
            strBuffer.append("  \"strip_outer_array\" = ").append("\"").append(dorisConfig.getBooleanType()).append("\"");
        }
        // 通过kafka数据源id 查询kafka server
        DataSource dataSource = dataSourceService.getDataSource(dorisConfig.getSourceOptions().getId());
        if (dataSource == null) {
            throw new ServiceException("未获取到kafka数据源");
        }
        DataSourceBasicInfo dataSourceBasicInfo = JSONObject.parseObject(dataSource.getDataSourceBasicInfo(), DataSourceBasicInfo.class);
        strBuffer.append("\n)")
                .append("FROM KAFKA(\n")
                .append("   \"kafka_broker_list\" = ").append("\"").append(dataSourceBasicInfo.getBootstrapServers()).append("\"").append(",\n")
                .append("   \"property.group.id\" = ").append("\"").append(streamCdc.getCode()).append("_consumer_group").append("\"").append(",\n")
                .append("   \"kafka_topic\" = ").append("\"").append(sourceOptions.getTableName()).append("\"").append(",\n");
        // - OFFSET_BEGINNING: 从有数据的位置开始订阅。
        //
        //- OFFSET_END: 从末尾开始订阅。
        //
        //- 时间格式，如："2021-05-22 11:00:00"
        if (!"NONE".equals(dorisConfig.getConsumeMode())) {
            if ("OFFSET_TIME".equals(dorisConfig.getConsumeMode())) {
                strBuffer.append("  \"kafka_offsets\" = ").append("\"").append(dorisConfig.getConsumeModeTime()).append("\"");
            } else {
                strBuffer.append("  \"kafka_partitions\" = ").append("\"").append(dorisConfig.getKafkaPartitions()).append("\"").append(",\n");
                strBuffer.append("  \"kafka_offsets\" = ").append("\"").append(dorisConfig.getConsumeMode()).append("\"");
            }
        } else {
            strBuffer.append("  \"property.kafka_default_offsets\" = \"OFFSET_BEGINNING\"");
        }
        strBuffer.append("\n)");
        return strBuffer.toString();
    }


    /**
     * 执行Doris SQL方法 封装
     *
     * @param dorisConfig doris配置
     * @param commitSql   执行的sql
     * @return 执行结果
     */
    private R executeDorisSql(DorisConfig dorisConfig, Long dataSourceId, String commitSql) {
        DataModelOptions sinkOptions = dorisConfig.getSinkOptions();
        try {
            if (sinkOptions == null) {
                return R.fail("请先配置Doris目标数据源");
            }
            dataSourceService.execDdlSqlByTenantId(sinkOptions.getTenantId(), dataSourceId, commitSql);
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail("上线失败，", e.getMessage());
        }
        return R.ok("上线成功");
    }

    /**
     * 获取实体底表映射信息
     *
     * @param tenantId 租户ID
     * @param id       实体ID
     * @return 实体底表映射信息
     */
    private DataEntityTable getRemoteDataEntityTable(Long tenantId, Long id) {
        // 是否根据租户ID查询数据源信息(兼容之前版本)
        DataEntityTable dataEntityTable = null;
        if (tenantId == null || tenantId == 0L) {
            dataEntityTable = dataEntityTableService.selectById(id);
        } else {
            dataEntityTable = dataEntityTableService.selectById(id, tenantId);
        }
        if (dataEntityTable == null) {
            throw new ServiceException("未获取到实体表信息");
        }
        return dataEntityTable;
    }

    private class DorisUtil {
        public static final String SHOW_ROUTINE_LOAD = "SHOW ROUTINE LOAD FOR %s.%s";
        public static final String STOP_ROUTINE_LOAD = "STOP ROUTINE LOAD FOR %s.%s";
        public static final String PAUSE_ROUTINE_LOAD = "PAUSE ROUTINE LOAD FOR %s.%s";
        public static final String RESUME_ROUTINE_LOAD = "RESUME ROUTINE LOAD FOR %s.%s";
    }
}
