package com.datalink.fdop.stream.kubernetes;


import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import io.fabric8.kubernetes.api.model.*;
import io.fabric8.kubernetes.api.model.apps.Deployment;
import io.fabric8.kubernetes.api.model.apps.DeploymentList;
import io.fabric8.kubernetes.api.model.apps.DeploymentSpec;
import io.fabric8.kubernetes.client.KubernetesClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class KubernetesUtils {


    private static final String NAMESPACE = "fdop";

    @Autowired
    private KubernetesClient kubernetesClient;

    public Integer getK8sServicePort(String name) {
        ServiceList list = kubernetesClient.services().inNamespace(NAMESPACE).list();
        for (io.fabric8.kubernetes.api.model.Service service : list.getItems()) {
            if (service.getMetadata().getName().equals(name)) {
                return service.getSpec().getPorts().get(0).getNodePort();
            }
        }
        return null;
    }

    public String getPodStatus (String code) {
        PodList debezium = kubernetesClient.pods ().inNamespace (NAMESPACE) .list();
        for (Pod pod : debezium.getItems()) {
            if (pod.getMetadata()!=null&&pod.getMetadata().getName().contains(code)){
                return pod.getStatus().getPhase();
            }
        }
        return "UNKNOWN";
    }


    public Boolean getDeploymentStatus (String code) {
        DeploymentList list = kubernetesClient.apps().deployments().inNamespace(NAMESPACE).list();
        for (Deployment deployment : list.getItems()) {
            if (deployment.getMetadata().getName().equals(code)) {
                return true;
            }
        }
        return false;
    }

    public String getDeploymentLog (String code) {
        DeploymentList list = kubernetesClient.apps() .deployments ().inNamespace (NAMESPACE) .list();
        for (Deployment deployment : list.getItems()) {
            if (deployment.getMetadata () .getName () .equals (code)) {
                return kubernetesClient.apps().deployments().inNamespace(NAMESPACE).withName(code).getLog();
            }
        }
        return null;
    }

    public String getPodLog(String code) {
        PodList debezium = kubernetesClient.pods().inNamespace(NAMESPACE).list();
        for (Pod pod : debezium.getItems()) {
            if (pod.getMetadata().getName().contains(code)) {
                return kubernetesClient.pods().inNamespace(NAMESPACE).withName(pod.getMetadata().getName()).getLog();
            }
        }
        return null;
    }

    public static Deployment getDeploymentByDebeziumConfig(String code, String kafkaServers, String image) {
        Deployment deployment = new Deployment();
        deployment.setMetadata(getMetadata(code));
        DeploymentSpec spec = new DeploymentSpec();
        LabelSelector labelSelector = new LabelSelector();
        Map<String, String> map = new HashMap<>();
        map.put("app", code);
        labelSelector.setMatchLabels(map);
        spec.setSelector(labelSelector);
        PodTemplateSpec podTemplateSpec = new PodTemplateSpec();
        ObjectMeta objectMeta = new ObjectMeta();
        objectMeta.setLabels(map);
        podTemplateSpec.setMetadata(objectMeta);
        PodSpec podSpec = new PodSpec();
//        (List <Volume> volumes = new Arraylist<>O;
//       volumes.add (getVolume (getPersistentVolumeClaimVolumeSource("dbz-public-pvc"), code));
//        podSpec.setVolumes (volumes);
        List<Container> containers = new ArrayList<>();
        containers.add(getContainer(image, code, kafkaServers));
        podSpec.setContainers(containers);
        podTemplateSpec.setSpec(podSpec);
        spec.setTemplate(podTemplateSpec);
        deployment.setSpec(spec);
        return deployment;
    }

    public static Service getK8sService (String code) {
        Service service = new Service();
        service.setMetadata(getMetadata(code));
        service.setSpec(getServiceSpec(code));
        return service;
    }

    private static ServiceSpec getServiceSpec(String code) {
        ServiceSpec spec = new ServiceSpec();
        spec.setPorts(getServicePorts());
        Map<String, String> selector = new HashMap<>();
        selector.put("app", code);
        spec.setSelector(selector);
        spec.setType("NodePort");
        spec.setSessionAffinity("None");
        spec.setExternalTrafficPolicy("Cluster");
        spec.setIpFamilies(Collections.singletonList("IPv4"));
        spec.setIpFamilyPolicy("SingleStack");
        return spec;
    }

    private static List<ServicePort> getServicePorts() {
        List<ServicePort> servicePorts = new ArrayList<> ();
        servicePorts.add(new ServicePort(  null,  "http-8083",  null,  8083,  "TCP", new IntOrString(  8083)));
        return servicePorts;
    }

    private static ObjectMeta getMetadata(String code) {
        ObjectMeta meta = new ObjectMeta();
        meta.setName(code);
        meta.setNamespace("fdop");
        Map<String, String> map = new HashMap<>();
        map.put("app", code);
        meta.setLabels(map);
        return meta;
    }
    private static Container getContainer (String image, String code, String kafkaServers) {
        Container container = new Container();
        container.setImage(image);
        container.setPorts(getContainerPorts());
        container.setEnv(getEnv(code, kafkaServers));
//        container.setVolumeMounts(getVolumeMounts(code));
        container.setName(code);
        return container;
    }

    private static List<ContainerPort> getContainerPorts() {
        List<ContainerPort> containerPorts = new ArrayList<> ();
        containerPorts.add(getContainerPort(  "http-8083",  8083,  "TCP"));
        containerPorts.add(getContainerPort ("http-8080",  8080,  "TCP"));
        return containerPorts;
    }

    private static ContainerPort getContainerPort(String code, Integer port, String protocol) {
        ContainerPort containerPort = new ContainerPort ();
        containerPort.setName (code);
        containerPort.setContainerPort(port);
        containerPort.setProtocol (protocol);
        return containerPort;
    }
    private static List<EnvVar> getEnv(String code, String kafkaServers) {
        List<EnvVar> envVars = new ArrayList<> ();
        envVars. add(getEnvVar ( "JMXPORT",  "9012"));
 //       envVars.add (getEnvVar ("KAFKA_OPTS", "javaagent:/kafka/etc/jmx-prometheus_javaagent-0.18.1-semi.jar=8080:/kafka/etc/config.yml"));
        envVars.add(getEnvVar(  "GROUP_ID", String.valueOf(IdWorker.getId())));
        envVars.add (getEnvVar( "CONFIG_STORAGE_TOPIC",  code+"-config"));
        envVars. add(getEnvVar( "OFFSET_STORAGE_TOPIC",  code+"-offset"));
        envVars. add(getEnvVar(  "STATUS_STORAGE_TOPIC", code+"-status"));
        envVars.add(getEnvVar( "CONNECT_key.converter.schemas.enable",  "false"));
        envVars. add(getEnvVar (  "CONNECT_value.converter.schemas.enable",  "false"));
        envVars. add (getEnvVar ( "JMXHOST",  "0.0.0.0"));
        envVars. add(getEnvVar(  "BOOTSTRAP_SERVERS", kafkaServers));
        return envVars;
    }

    private static EnvVar getEnvVar(String code, String value) {
        EnvVar envVar = new EnvVar();
        envVar.setName (code);
        envVar.setValue (value);
        return envVar;
    }

    public static List<VolumeMount> getVolumeMounts(String code) {
        List<VolumeMount> volumeMounts = new ArrayList<>();
        volumeMounts.add(getVolumeMount(true, "/kafka/etc/", code));
        return volumeMounts;
    }

    public static VolumeMount getVolumeMount (Boolean readOnly, String mountPath, String code) {
        VolumeMount volumeMount = new VolumeMount();
        volumeMount.setReadOnly(readOnly);
        volumeMount.setMountPath(mountPath);
        volumeMount.setName(code);
        return volumeMount;
    }
}
