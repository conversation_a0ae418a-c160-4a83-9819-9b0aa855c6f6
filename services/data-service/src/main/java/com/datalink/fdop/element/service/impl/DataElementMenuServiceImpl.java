package com.datalink.fdop.element.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.tree.TreeUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.element.api.domain.DataElement;
import com.datalink.fdop.element.api.domain.DataElementMenu;
import com.datalink.fdop.element.api.model.DataElementTree;
import com.datalink.fdop.element.mapper.DataElementMapper;
import com.datalink.fdop.element.mapper.DataElementMenuMapper;
import com.datalink.fdop.element.service.IDataElementMenuService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/12 13:58
 */
@Service
public class DataElementMenuServiceImpl implements IDataElementMenuService {

    @Autowired
    private DataElementMapper dataElementMapper;

    @Autowired
    private DataElementMenuMapper dataElementMenuMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int create(DataElementMenu dataElementMenu) {
        if (dataElementMenuMapper.selectByCode(dataElementMenu.getCode()) != null) {
            throw new ServiceException(Status.DATA_ELEMENT_MENU_EXIST);
        }
        dataElementMenu.setId(IdWorker.getId());
        int insert = dataElementMenuMapper.insertElementMenu(dataElementMenu);
        // 创建菜单边关系
        if (insert > 0 && dataElementMenu.getPid() != -1L) {
            // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
            dataElementMenuMapper.createElementMenuEdge(dataElementMenu.getPid(), Arrays.asList(dataElementMenu.getId()));
        }
        return insert;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(DataElementMenu dataElementMenu) {
        DataElementMenu checkDataElementMenu = dataElementMenuMapper.selectById(dataElementMenu.getId());
        if (checkDataElementMenu == null) {
            throw new ServiceException(Status.DATA_ELEMENT_MENU_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(dataElementMenu.getCode()) && dataElementMenuMapper.checkCodeIsExists(dataElementMenu.getId(), dataElementMenu.getCode()) != null) {
            throw new ServiceException(Status.DATA_ELEMENT_MENU_EXIST);
        }
        int update = dataElementMenuMapper.updateById(dataElementMenu);
        if (update > 0) {
            // 获取修改前的菜单pid,并删除修改前的边关系
            dataElementMenuMapper.deleteElementMenuEdge(Arrays.asList(dataElementMenu.getId()), checkDataElementMenu.getPid());
            // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
            if (dataElementMenu.getPid() != -1L) {
                dataElementMenuMapper.createElementMenuEdge(dataElementMenu.getPid(), Arrays.asList(dataElementMenu.getId()));
            }
        }
        return update;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int delete(List<Long> ids) {
        for (Long id : ids) {
            DataElementMenu dataElementMenu = dataElementMenuMapper.selectById(id);
            if (dataElementMenu == null) {
                continue;
            }
            // 获取当前被删除菜单的所有子级菜单
            List<Long> menuIdList = dataElementMenuMapper.selectIdsByPid(id);
            // 如果存在子级菜单，则将所有子级菜单的pid修改为被删除菜单的pid
            if (CollectionUtils.isNotEmpty(menuIdList)) {
                int update = dataElementMenuMapper.bacthUpdatePidById(menuIdList, dataElementMenu.getPid());
                if (update > 0 && dataElementMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    dataElementMenuMapper.createElementMenuEdge(dataElementMenu.getPid(), menuIdList);
                }
            }

            // 获取当前被删除菜单的所有子级
            List<Long> elementIdList = dataElementMapper.selectIdsByPid(id);
            // 如果存在子级，则将所有子级的pid修改为被删除菜单的pid
            if (CollectionUtils.isNotEmpty(elementIdList)) {
                int update = dataElementMapper.bacthUpdatePidById(elementIdList, dataElementMenu.getPid());
                if (update > 0 && dataElementMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    dataElementMapper.createElementAndMenuEdge(dataElementMenu.getPid(), elementIdList);
                }
            }
        }
        // 删除菜单并且删除菜单的边关系
        return dataElementMenuMapper.deleteBatchIds(ids);
    }

    @Override
    public List<DataElementTree> tree(String sort, String code, Boolean isQueryNode) {
        // 所有的数据集合
        List<DataElementTree> trees = new ArrayList<>();
        // 添加节点数据
        if (isQueryNode) {
            trees.addAll(dataElementMapper.selectNodeTree(sort, code));
        }
        // 添加菜单数据
        trees.addAll(dataElementMenuMapper.selectMenuTree(sort, null));

        // 递归成树结构
        List<DataElementTree> treeList = (List<DataElementTree>) TreeUtils.getTree(trees);
        // 条件查找删除空集合的菜单
        if (StringUtils.isNotEmpty(code) && isQueryNode) {
            TreeUtils.removeEmptyChilderAndMenu(treeList);
        }
        return treeList;
    }

    @Override
    public PageDataInfo<DataElement> overview(Long pid, String sort, SearchVo searchVo) {
        // 获取分页参数
        Page<DataElement> page = PageUtils.getPage(DataElement.class);
        IPage<DataElement> dataElementIPage = dataElementMapper.overview(page, pid, sort, searchVo);

        return PageUtils.getPageInfo(dataElementIPage.getRecords(), (int) dataElementIPage.getTotal());
    }

}
