package com.datalink.fdop.stream.service;

import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.stream.api.domain.StreamHistory;

import java.util.List;

/**
 * 流任务历史执行记录Service
 */
public interface IStreamHistoryService {

    StreamHistory create(StreamHistory streamHistory);

    int update(StreamHistory streamHistory);

    int delete(List<Long> ids, Long streamId);

    PageDataInfo<StreamHistory> selectHistoryListByStreamId(Long streamId, String sort, SearchVo searchVo);

}

