package com.datalink.fdop.govern.service;

import com.datalink.fdop.govern.api.domain.DataStandardMenu;
import com.datalink.fdop.govern.api.domain.DataStandardTree;
import com.datalink.fdop.govern.api.enums.ApprovalStatusType;

import java.util.List;

public interface IDataStandardMenuService
{
    int create(DataStandardMenu dataStandardMenu);

    int update(DataStandardMenu dataStandardMenu);

    int delete(List<Long> ids);

    List<DataStandardTree> tree(String sort, String code, Boolean isQueryNode, ApprovalStatusType approvalStatusType);

    List<Long> getChildrenMenuId(List<Long> ids);
}
