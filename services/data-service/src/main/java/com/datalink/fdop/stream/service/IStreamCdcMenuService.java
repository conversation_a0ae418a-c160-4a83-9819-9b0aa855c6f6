package com.datalink.fdop.stream.service;

import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.stream.api.domain.StreamCdc;
import com.datalink.fdop.stream.api.domain.StreamCdcMenu;
import com.datalink.fdop.stream.api.domain.StreamCdcTree;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
public interface IStreamCdcMenuService {

    int create(StreamCdcMenu streamCdcMenu);

    int update(StreamCdcMenu streamCdcMenu);

    int delete(List<Long> ids);

    PageDataInfo<StreamCdc> overview(Long pid, String sort, SearchVo searchVo);

    List<StreamCdcTree> tree(String sort, String code, Boolean isQueryNode);


    Integer querySerialNumber(Boolean menuFlag);
}
