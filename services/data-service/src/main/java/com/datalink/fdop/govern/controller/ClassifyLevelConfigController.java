package com.datalink.fdop.govern.controller;


import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.govern.api.domain.ClassifyLevelConfig;
import com.datalink.fdop.govern.api.domain.DataTag;
import com.datalink.fdop.govern.api.domain.DataTagExport;
import com.datalink.fdop.govern.api.domain.ImportDataLog;
import com.datalink.fdop.govern.service.IClassifyLevelConfigService;
import io.swagger.annotations.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RequestMapping(value = "/govern/security/classifyLevelConfig")
@RestController
@Api(tags = "数据安全分类分级配置")
public class ClassifyLevelConfigController extends BaseController {

    @Autowired
    private IClassifyLevelConfigService classifyLevelConfigService;

    @ApiOperation("创建安全分类分级")
    @Log(title = "数据治理")
    @PostMapping(value = "/create")
    public R create(@Validated @RequestBody ClassifyLevelConfig classifyLevelConfig) {
        return R.toResult(classifyLevelConfigService.create(classifyLevelConfig));
    }

    @ApiOperation("修改安全分类分级")
    @Log(title = "数据治理")
    @PostMapping(value = "/update")
    public R update(@RequestBody ClassifyLevelConfig classifyLevelConfig) {
        if (classifyLevelConfig.getId() == null) {
            throw new ServiceException(Status.PARAM_ERROR);
        }
        return R.toResult(classifyLevelConfigService.update(classifyLevelConfig));
    }

    @ApiOperation("删除安全分类分级")
    @Log(title = "数据治理")
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.DATA_SECURITY_CLASS_TO_DELETE);
        }
        return R.toResult(classifyLevelConfigService.delete(ids));
    }

    @ApiOperation("查询数据安全分类分级")
    @Log(title = "数据治理")
    @GetMapping(value = "/overview")
    public R<PageDataInfo<ClassifyLevelConfig>> overview(
            @RequestParam(required = false, defaultValue = "DESC") String sort,
            @RequestParam(required = false) String fieldName,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String level) {
        PageDataInfo<ClassifyLevelConfig> classifyLevelConfigList = classifyLevelConfigService.overview(sort, fieldName, category,level);
        return R.ok(classifyLevelConfigList);
    }

    @ApiOperation(value = "导入数据")
    @Log(title = "数据治理")
    @PostMapping("/importData")
    public R importData(MultipartFile file) throws Exception {
        if (file == null || file.isEmpty()) {
            return R.fail("文件上传失败！");
        }
        if (!file.getOriginalFilename().endsWith(".xlsx") && !file.getOriginalFilename().endsWith(".xls")) {
            return R.fail("文件不是Excel文件，请上传.xlsx或.xls后缀文件");
        }
        ExcelUtil<ClassifyLevelConfig> util = new ExcelUtil<>(ClassifyLevelConfig.class);
        List<ClassifyLevelConfig> list = util.importExcel(file.getInputStream());
        classifyLevelConfigService.importData(list);
        return R.ok("导入成功");
    }

    @ApiOperation(value = "导出模板")
    @Log(title = "数据治理")
    @PostMapping("/exportTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<ClassifyLevelConfig> util = new ExcelUtil<>(ClassifyLevelConfig.class);
        util.importTemplateExcel(response, "config");
    }

    @ApiOperation("导出数据")
    @Log(title = "数据治理")
    @PostMapping("/exportData")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false, defaultValue = "DESC") String sort,
                       @RequestParam(required = false) String fieldName,
                       @RequestParam(required = false) String category,
                       @RequestParam(required = false) String level) {
        List<ClassifyLevelConfig> list = classifyLevelConfigService.list(sort,fieldName,category,level);
        ExcelUtil<ClassifyLevelConfig> util = new ExcelUtil<>(ClassifyLevelConfig.class);
        util.exportExcel(response, list, "配置数据");
    }

}
