package com.datalink.fdop.stream.service.factory;


import com.alibaba.fastjson.JSONObject;
import com.datalink.fdop.stream.service.DebeziumService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/17 9:42
 * drive-center降级处理
 *
 */
@Component
public class DebeziumFallbackFactory implements FallbackFactory<DebeziumService> {

    private static final Logger log = LoggerFactory.getLogger(DebeziumFallbackFactory.class);

    @Override
    public DebeziumService create(Throwable throwable) {
        log.error("DebeziumService调用失败:{}", throwable.getMessage());
        return new DebeziumService() {

            @Override
            public List getConnectorList() {
                log.error("获取Debezium连接列表失败:" + throwable.getMessage());
                return null;
            }

            @Override
            public JSONObject createConnector(JSONObject jsonObject) {
                log.error("创建Debezium连接失败:" + throwable.getMessage());
                return null;
            }

            @Override
            public void pauseConnector(String name) {
                log.error("暂停Debezium连接失败:" + throwable.getMessage());
            }

            @Override
            public void resumeConnector(String name) {
                log.error("恢复Debezium连接失败:" + throwable.getMessage());
            }

            @Override
            public void restartConnector(String name) {
                log.error("重启Debezium连接失败:" + throwable.getMessage());
            }

            @Override
            public void deleteConnector(String name) {
                log.error("删除Debezium连接失败:" + throwable.getMessage());
            }

            @Override
            public JSONObject getConnector(String name) {
                log.error("获取Debezium连接失败:" + throwable.getMessage());
                return null;
            }

            @Override
            public JSONObject getConfig(String name) {
                log.error("获取Debezium配置失败:" + throwable.getMessage());
                return null;
            }

            @Override
            public JSONObject updateConfig(String name) {
                log.error("更新Debezium配置失败:" + throwable.getMessage());
                return null;
            }

            @Override
            public JSONObject getConnectorStatus(String name) {
                log.error("获取Debezium连接状态失败:" + throwable.getMessage());
                return null;
            }

            @Override
            public JSONObject validateJson(String type, JSONObject jsonObject) {
                log.error("验证Debezium配置失败:" + throwable.getMessage());
                return null;
            }
        };
    }
}
