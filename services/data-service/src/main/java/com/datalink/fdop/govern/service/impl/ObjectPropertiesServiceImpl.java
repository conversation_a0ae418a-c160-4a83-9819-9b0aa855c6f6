package com.datalink.fdop.govern.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.constant.Constants;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.drive.mapper.DataSourceMapper;
import com.datalink.fdop.drive.service.IDataSourceService;
import com.datalink.fdop.element.api.domain.DataEntity;
import com.datalink.fdop.element.api.domain.DataEntityStructure;
import com.datalink.fdop.element.mapper.DataEntityMapper;
import com.datalink.fdop.element.mapper.DataEntityStructureMapper;
import com.datalink.fdop.govern.api.domain.*;
import com.datalink.fdop.govern.api.enums.FillingMethod;
import com.datalink.fdop.govern.api.enums.Required;
import com.datalink.fdop.govern.mapper.LogicalObjectMapper;
import com.datalink.fdop.govern.mapper.ObjectPropertiesMapper;
import com.datalink.fdop.govern.service.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class ObjectPropertiesServiceImpl implements IObjectPropertiesService {

    @Autowired
    private ObjectPropertiesMapper objectPropertiesMapper;

    @Autowired
    private LogicalObjectMapper logicalObjectMapper;

    @Autowired
    private IDataSecurityClassificationService dataSecurityClassificationService;

    @Autowired
    private IDataSecurityLevelService dataSecurityLevelService;

    @Autowired
    private IDataApplicationFrameworkService applicationFrameworkService;

    @Autowired
    private DataSourceMapper dataSourceMapper;

    @Autowired
    private DataEntityMapper dataEntityMapper;

    @Autowired
    private DataEntityStructureMapper dataEntityStructureMapper;

    @Autowired
    private ImportDataLogService importDataLogService;

    /**
     * 导入日志表
     */
    private final String LOG_TABLE_NAME = "d_g_object_properties_import_log";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int create(ObjectProperties entity) {
        if (StringUtils.isEmpty(entity.getCode())) {
            throw new ServiceException("编码不能为空");
        }
        if (entity.getPid() == null) {
            throw new ServiceException("请选择上级");
        }
        if (logicalObjectMapper.selectById(entity.getPid()) == null) {
            throw new ServiceException("上级不存在");
        }
        if (objectPropertiesMapper.selectByCode(entity.getCode()) != null) {
            throw new ServiceException("已存在编码为[" + entity.getCode() + "]的数据");
        }
        if (entity.getFillingMethod() != null) {
            entity.setFillingMethodLabel(entity.getFillingMethod().getDesc());
        }
        if (entity.getRequired() != null) {
            entity.setRequiredLabel(entity.getRequired().getDesc());
        }
        if (entity.getId() == null) {
            entity.setId(IdWorker.getId());
        }
        entity.setActiveState(true);
        entity.setVersion(Constants.VERSION_INITIALIZE);
        int insert = objectPropertiesMapper.insert(entity);
        if (insert > 0) {
            objectPropertiesMapper.updateById(entity);
        }
        return insert;
    }

    @Override
    public int update(ObjectProperties entity) {
        if (entity.getId() == null) {
            throw new ServiceException("id不能为空");
        }
        ObjectProperties objectProperties = selectById(entity.getId());
        if (objectProperties.getLockStatus()) {
            throw new ServiceException("锁定状态不能修改");
        }
        if (StringUtils.isEmpty(entity.getCode())) {
            throw new ServiceException("编码不能为空");
        }
        if (objectPropertiesMapper.codeIsOnly(entity.getId(), entity.getCode()) > 0) {
            throw new ServiceException("已存在编码为[" + entity.getCode() + "]的数据");
        }
        if (entity.getFillingMethod() != null) {
            entity.setFillingMethodLabel(entity.getFillingMethod().getDesc());
        }
        if (entity.getRequired() != null) {
            entity.setRequiredLabel(entity.getRequired().getDesc());
        }
        return objectPropertiesMapper.updateById(entity);
    }

    @Override
    public int delete(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException("请指定要删除的内容");
        }
        return objectPropertiesMapper.delete(ids);
    }

    @Override
    public ObjectProperties selectById(Long id) {
        ObjectProperties objectProperties = objectPropertiesMapper.selectById(id);
        if (objectProperties == null) {
            throw new ServiceException("不存在");
        }
        return objectProperties;
    }

    @Override
    public PageDataInfo<ObjectProperties> list(ObjectProperties entity, String sort) {
        Page<ObjectProperties> page = PageUtils.getPage(ObjectProperties.class);
        IPage<ObjectProperties> objectPropertiesIPage = objectPropertiesMapper.selectList(page, entity, sort);
        return PageUtils.getPageInfo(objectPropertiesIPage.getRecords(), (int) objectPropertiesIPage.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int lock(List<Long> ids) {
        List<VlabelItem<ObjectProperties>> vlabelItems = objectPropertiesMapper.selectByIds(ids);
        List<String> collect = vlabelItems.stream().map(VlabelItem::getProperties).filter(ObjectProperties::getLockStatus).map(ObjectProperties::getCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            throw new ServiceException("存在以下编码的对象属性已锁定：" + JSONObject.toJSONString(collect));
        }
        return objectPropertiesMapper.lock(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int unlock(List<Long> ids) {
        List<VlabelItem<ObjectProperties>> vlabelItems = objectPropertiesMapper.selectByIds(ids);
        List<String> collect = vlabelItems.stream().map(VlabelItem::getProperties).filter(e -> !e.getLockStatus()).map(ObjectProperties::getCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            throw new ServiceException("存在以下编码的对象属性未锁定：" + JSONObject.toJSONString(collect));
        }
        return objectPropertiesMapper.unlock(ids);
    }

    @Override
    public List<ObjectProperties> selectByIds(List<Long> ids) {
        List<VlabelItem<ObjectProperties>> vlabelItems = objectPropertiesMapper.selectByIds(ids);
        return vlabelItems.stream().map(VlabelItem::getProperties).collect(Collectors.toList());
    }

    @Override
    public List<ObjectProperties> selectByPids(List<Long> pids) {
        List<VlabelItem<ObjectProperties>> vlabelItems = objectPropertiesMapper.selectByPids(pids);
        return vlabelItems.stream().map(VlabelItem::getProperties).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateVersion(List<Long> ids, String version) {
        List<VlabelItem<ObjectProperties>> vlabelItems = objectPropertiesMapper.selectByIds(ids);
        if (CollectionUtils.isNotEmpty(vlabelItems)) {
            List<ObjectProperties> objectPropertiesList = vlabelItems.stream().map(VlabelItem::getProperties).collect(Collectors.toList());
            objectPropertiesMapper.updateActiveStateByIds(ids);
            for (ObjectProperties objectProperties : objectPropertiesList) {
                objectProperties.setVersion(version);
                objectProperties.setActiveState(true);
                objectProperties.setUpdateBy(SecurityUtils.getUsername());
                objectProperties.setUpdateTime(new Date());
                objectPropertiesMapper.insert(objectProperties);
            }
        }
        return 0;
    }

    @Async
    @Override
    public void importData(String fileName, List<ObjectProperties> list, String operatorName) {
        ImportDataLog importDataLog = new ImportDataLog();
        importDataLog.setId(IdWorker.getId());
        importDataLog.setStatus("导入中");
        importDataLog.setImportBy(operatorName);
        importDataLog.setImportTime(new Date());
        importDataLog.setFileName(fileName);
        String log = "--INFO 数据正在导入中...\n";
        importDataLog.setLog(log);
        importDataLog.setTableName(LOG_TABLE_NAME);
        importDataLogService.insert(importDataLog);
        try {
            if (CollectionUtils.isEmpty(list)) {
                throw new ServiceException("导入的数据不能为空");
            }
            int successNum = 0;
            int failureNum = 0;
            StringBuilder successMsg = new StringBuilder();
            StringBuilder failureMsg = new StringBuilder();
            for (ObjectProperties entity : list) {
                try {
                    if (StringUtils.isEmpty(entity.getPCode())) {
                        throw new ServiceException("逻辑对象编码必填");
                    }
                    VlabelItem<LogicalObject> logicalObjectVlabelItem = logicalObjectMapper.selectByCode(entity.getPCode());
                    if (logicalObjectVlabelItem == null) {
                        throw new ServiceException("逻辑对象不存在");
                    }
                    entity.setPid(logicalObjectVlabelItem.getProperties().getId());
                    if (StringUtils.isEmpty(entity.getCode())) {
                        throw new ServiceException("编码必填");
                    }
                    if (StringUtils.isEmpty(entity.getName())) {
                        throw new ServiceException("名称必填");
                    }
                    boolean updateFlag = false;
                    VlabelItem<ObjectProperties> objectPropertiesVlabelItem = objectPropertiesMapper.selectByCode(entity.getCode());
                    if (objectPropertiesVlabelItem != null) {
                        updateFlag = true;
                        ObjectProperties dbEntity = objectPropertiesVlabelItem.getProperties();
                        ExcelUtil<ObjectProperties> excelUtil = new ExcelUtil<>(ObjectProperties.class);
                        entity = excelUtil.copyImportDataToDbData(entity, dbEntity);
                    } else {
                        entity.setId(IdWorker.getId());
                    }
                    if (StringUtils.isNotEmpty(entity.getSecuritySortCode())) {
                        // 校验数据安全分类
                        DataSecurityClassification dataSecurityClassification = dataSecurityClassificationService.selectByCode(entity.getSecuritySortCode());
                        if (dataSecurityClassification == null) {
                            throw new ServiceException("数据安全分类[" + entity.getSecuritySortCode() + "]不存在");
                        }
                        entity.setSecuritySortId(dataSecurityClassification.getId());
                        entity.setSecuritySortName(dataSecurityClassification.getName());
                    }
                    if (StringUtils.isNotEmpty(entity.getSecurityLevelCode())) {
                        // 校验数据安全等级
                        DataSecurityLevel dataSecurityLevel = dataSecurityLevelService.selectByCode(entity.getSecurityLevelCode());
                        if (dataSecurityLevel == null) {
                            throw new ServiceException("数据安全分级[" + entity.getSecurityLevelCode() + "]不存在");
                        }
                        entity.setSecurityLevelId(dataSecurityLevel.getId());
                        entity.setSecurityLevelName(dataSecurityLevel.getName());
                    }
                    if (StringUtils.isNotEmpty(entity.getSourceSystemCode())) {
                        // 校验源系统
                        DataApplicationFramework applicationFramework = applicationFrameworkService.selectByCode(entity.getSourceSystemCode());
                        if (applicationFramework == null) {
                            throw new ServiceException("数据来源系统[" + entity.getSourceSystemCode() + "]不存在");
                        }
                        entity.setSourceSystemId(applicationFramework.getId());
                        entity.setSourceSystemName(applicationFramework.getName());
                    }
                    if (StringUtils.isNotEmpty(entity.getDataSourceCode())) {
                        // 校验数据源是否存在
                        VlabelItem<DataSource> dataSourceVlabelItem = dataSourceMapper.selectByCode(entity.getDataSourceCode());
                        if (dataSourceVlabelItem == null) {
                            throw new ServiceException("数据源[" + entity.getDataSourceCode() + "]不存在");
                        }
                        entity.setDataSourceId(dataSourceVlabelItem.getProperties().getId());
                        entity.setDataSourceName(dataSourceVlabelItem.getProperties().getName());
                    }
                    if (StringUtils.isNotEmpty(entity.getModelCode())) {
                        // 校验实体是否存在
                        DataEntity dataEntity = dataEntityMapper.selectByCode(entity.getModelCode());
                        if (dataEntity == null) {
                            throw new ServiceException("实体[" + entity.getModelCode() + "]不存在");
                        }
                        entity.setModelId(dataEntity.getId());
                        entity.setModelName(dataEntity.getName());
                    }
                    if (StringUtils.isNotEmpty(entity.getOdsModelCode())) {
                        // 校验ODS实体是否存在
                        DataEntity dataEntity = dataEntityMapper.selectByCode(entity.getOdsModelCode());
                        if (dataEntity == null) {
                            throw new ServiceException("ODS实体[" + entity.getModelCode() + "]不存在");
                        }
                        entity.setOdsModelId(dataEntity.getId());
                        entity.setOdsModelName(dataEntity.getName());
                    }
                    if (StringUtils.isNotEmpty(entity.getModelFieldCode())) {
                        // 校验实体结构是否存在
                        DataEntityStructure dataEntityStructure = dataEntityStructureMapper.selectByCode(entity.getModelId(),entity.getModelFieldCode());
                        if (dataEntityStructure == null) {
                            throw new ServiceException("实体字段[" + entity.getModelFieldCode() + "]不存在");
                        }
                    }
                    if (StringUtils.isNotEmpty(entity.getOdsModelFieldCode())) {
                        // 校验实体结构是否存在
                        DataEntityStructure dataEntityStructure = dataEntityStructureMapper.selectByCode(entity.getOdsModelId(),entity.getOdsModelFieldCode());
                        if (dataEntityStructure == null) {
                            throw new ServiceException("ODS实体字段[" + entity.getOdsModelFieldCode() + "]不存在");
                        }
                    }
                    // 枚举值转换
                    // 填写方式
                    if (StringUtils.isNotEmpty(entity.getFillingMethodLabel())) {
                        entity.setFillingMethod(FillingMethod.valueOf(entity.getFillingMethodLabel()));
                        entity.setFillingMethodLabel(FillingMethod.valueOf(entity.getFillingMethodLabel()).getDesc());
                    }
                    // 是否必填
                    if (StringUtils.isNotEmpty(entity.getRequiredLabel())) {
                        entity.setRequired(Required.valueOf(entity.getRequiredLabel()));
                        entity.setRequiredLabel(Required.valueOf(entity.getRequiredLabel()).getDesc());
                    }
                    if (updateFlag) {
                        int update = objectPropertiesMapper.updateById(entity);
                        if (update > 0) {
                            successNum++;
                            successMsg.append("\n" + successNum + "、对象属性 " + entity.getCode() + " 导入更新成功");
                        }
                    } else {
                        entity.setActiveState(true);
                        entity.setVersion(Constants.VERSION_INITIALIZE);
                        int insert = objectPropertiesMapper.insert(entity);
                        if (insert > 0) {
                            successNum++;
                            successMsg.append("\n" + successNum + "、对象属性 " + entity.getCode() + " 导入成功");
                        }
                    }
                } catch (Exception e) {
                    failureNum++;
                    String msg = "\n" + failureNum + "、对象属性 " + entity.getCode() + " 导入失败：";
                    failureMsg.append(msg + e.getMessage());
                }
            }
            if (failureNum > 0) {
                failureMsg.insert(0, "很抱歉，导入失败！成功导入 " + successNum + " 条数据， 数据如下：" + successMsg + "， \n共 " + failureNum + " 条数据格式不正确，错误如下：");
                throw new ServiceException(failureMsg.toString());
            } else {
                successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
                importDataLog.setLog(log + "\n" + successMsg.toString());
                importDataLog.setEndTime(new Date());
                importDataLog.setStatus("成功");
                importDataLogService.update(importDataLog);
            }
        } catch (Exception e) {
            String message = e.getMessage();
            importDataLog.setLog(log + "\n" + message);
            importDataLog.setEndTime(new Date());
            importDataLog.setStatus("失败");
            importDataLogService.update(importDataLog);
        }
    }

    @Override
    public List<ObjectProperties> getExportList(ObjectProperties entity, String sort) {
        return objectPropertiesMapper.getExportList(entity, sort);
    }

    @Override
    public PageDataInfo<ImportDataLog> importLog(ImportDataLog importDataLog, String sort) {
        if (importDataLog == null) {
            importDataLog = new ImportDataLog();
        }
        importDataLog.setTableName(LOG_TABLE_NAME);
        return importDataLogService.list(importDataLog, sort);
    }

    @Override
    public ImportDataLog selectByIdLog(Long id) {
        return importDataLogService.selectById(LOG_TABLE_NAME, id);
    }
}
