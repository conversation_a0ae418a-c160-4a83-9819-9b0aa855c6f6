package com.datalink.fdop.govern.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.constant.Constants;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.DateUtils;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.govern.api.domain.*;
import com.datalink.fdop.govern.api.enums.*;
import com.datalink.fdop.govern.mapper.BusinessObjectMapper;
import com.datalink.fdop.govern.mapper.DataTagMapper;
import com.datalink.fdop.govern.service.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class BusinessObjectServiceImpl implements IBusinessObjectService {

    @Autowired
    private BusinessObjectMapper businessObjectMapper;

    @Autowired
    private ILogicalObjectService logicalObjectService;

    @Autowired
    private IObjectPropertiesService objectPropertiesService;

    @Autowired
    private IDataProcessFrameworkService dataProcessFrameworkService;

    @Autowired
    private IDataApplicationFrameworkService applicationFrameworkService;

    @Autowired
    private IDataResourcesService dataResourcesService;

    @Autowired
    private IDataSecurityLevelService dataSecurityLevelService;

    @Autowired
    private IDataSecurityClassificationService dataSecurityClassificationService;

    @Autowired
    private ImportDataLogService importDataLogService;

    @Autowired
    private DataTagMapper dataTagMapper;

    /**
     * 导入日志表
     */
    private final String LOG_TABLE_NAME = "d_g_business_object_import_log";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int create(BusinessObject entity) {
        checkData(entity);
        if (businessObjectMapper.selectByCode(entity.getCode()) != null) {
            throw new ServiceException("已存在编码为[" + entity.getCode() + "]的数据");
        }
        if (entity.getId() == null) {
            entity.setId(IdWorker.getId());
        }
        int insert = businessObjectMapper.insert(entity);
        if (insert > 0) {
            businessObjectMapper.updateById(entity);
        }
        return insert;
    }

    private void checkData(BusinessObject entity) {
        if (StringUtils.isEmpty(entity.getCode())) {
            throw new ServiceException("编码不能为空");
        }
        // 校验主题L2
        if (entity.getPid() == null) {
            throw new ServiceException("请选择上级");
        }
        if (dataProcessFrameworkService.selectById(entity.getPid()) == null) {
            throw new ServiceException("上级不存在");
        }
        // 校验数据安全等级
        if (entity.getSecurityLevelId() == null) {
            throw new ServiceException("数据安全分级必填");
        }
        if (dataSecurityLevelService.selectById(entity.getSecurityLevelId()) == null) {
            throw new ServiceException(Status.DATA_SECURITY_LEVEL_NOT_EXIST);
        }
        // 校验数据安全分类
        if (entity.getSecuritySortId() == null) {
            throw new ServiceException("数据安全分类必填");
        }
        if (dataSecurityClassificationService.selectById(entity.getSecuritySortId()) == null) {
            throw new ServiceException(Status.DATA_SECURITY_CLASS_NOT_EXIST);
        }
        // 校验数据资源
        if (entity.getDataResourcesId() == null) {
            throw new ServiceException("数据资源不能为空");
        }
        DataResources dataResources = dataResourcesService.selectById(entity.getDataResourcesId());
        if (dataResources == null) {
            throw new ServiceException(Status.DATA_RESOURCES_NOT_EXIST);
        }
        // 检查数据资源是否已经被(指标目录)引用
        VlabelItem<DataTag> dataTagVlabelItem = dataTagMapper.selectByCode(dataResources.getCode());
        if (dataTagVlabelItem != null) {
            throw new ServiceException("数据资源[" + entity.getDataResourcesCode() + "]已被指标目录[" + dataTagVlabelItem.getProperties().getName() + "]引用");
        }
        if (entity.getIsInformation() == null) {
            entity.setIsInformation(InformationEnum.TRUE);
        }
        entity.setInformationLabel(entity.getIsInformation().getDesc());
        if (entity.getPriority() == null) {
            entity.setPriority(PriorityEnum.M);
        }
        entity.setPriorityLabel(entity.getPriority().getDesc());
        if (entity.getBusinessObjectClassification() != null) {
            entity.setBusinessObjectClassificationLabel(entity.getBusinessObjectClassification().getDesc());
        }
        if (entity.getGovernStatus() == null) {
            entity.setGovernStatus(GovernStatus.GOVERNING);
        }
        entity.setGovernStatusLabel(entity.getGovernStatus().getDesc());
        if (entity.getRegisterStatus() == null) {
            entity.setRegisterStatus(RegisterStatus.UNREGISTER);
        }
        entity.setRegisterStatusLabel(entity.getRegisterStatus().getDesc());
        if (StringUtils.isEmpty(entity.getVersion())) {
            entity.setVersion(Constants.VERSION_INITIALIZE);
        }
    }

    @Override
    public int update(BusinessObject entity) {
        if (entity.getId() == null) {
            throw new ServiceException("id不能为空");
        }
        checkData(entity);
        if (businessObjectMapper.codeIsOnly(entity.getId(), entity.getCode()) > 0) {
            throw new ServiceException("已存在编码为[" + entity.getCode() + "]的数据");
        }
        return businessObjectMapper.updateById(entity);
    }

    @Override
    public int delete(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException("请指定要删除的内容");
        }
        List<String> codes = businessObjectMapper.queryExistChildrenNodes(ids);
        if (CollectionUtils.isNotEmpty(codes)) {
            throw new ServiceException("存在下级，不允许删除");
        }
        return businessObjectMapper.delete(ids);
    }

    @Override
    public BusinessObject selectById(Long id) {
        BusinessObject businessObject = businessObjectMapper.selectById(id);
        if (businessObject == null) {
            throw new ServiceException("不存在");
        }
        return businessObject;
    }

    @Override
    public BusinessObject selectByCode(String code) {
        VlabelItem<BusinessObject> businessObjectVlabelItem = businessObjectMapper.selectByCode(code);
        if (businessObjectVlabelItem == null) {
            throw new ServiceException("不存在");
        }
        return businessObjectVlabelItem.getProperties();
    }

    @Override
    public PageDataInfo<BusinessObject> list(BusinessObject entity, String sort) {
        Page<BusinessObject> page = PageUtils.getPage(BusinessObject.class);
        IPage<BusinessObject> businessObjectIPage = businessObjectMapper.selectList(page, entity, sort);
        return PageUtils.getPageInfo(businessObjectIPage.getRecords(), (int) businessObjectIPage.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int register(List<Long> ids) {
        List<VlabelItem<BusinessObject>> vlabelItems = businessObjectMapper.selectByIds(ids);
        List<String> collect = vlabelItems.stream().map(VlabelItem::getProperties).filter(e -> e.getRegisterStatus() == RegisterStatus.REGISTER).map(BusinessObject::getCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            throw new ServiceException("存在以下编码的业务对象已锁定：" + JSONObject.toJSONString(collect));
        }
        List<String> codes = businessObjectMapper.queryExistUnLockChildrenNodes(ids);
        if (CollectionUtils.isNotEmpty(codes)) {
            throw new ServiceException("存在未锁定的下级");
        }
        for (Long id : ids) {
            BusinessObject businessObject = businessObjectMapper.selectById(id);
            String version = businessObject.getVersion();
            if (version.equals(Constants.VERSION_INITIALIZE)) {
                version = "V0-" + DateUtils.dateTime();
            } else {
                String[] split = version.split("-");
                version = "V" + (Integer.parseInt(split[0].substring(1, split[0].length() - 1)) + 1) + "-" + DateUtils.dateTime();
            }
            businessObjectMapper.registerById(id, version);
            List<LogicalObject> logicalObjects = logicalObjectService.selectByPid(id);
            List<Long> l4Ids = logicalObjects.stream().map(LogicalObject::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(l4Ids)) {
                logicalObjectService.updateVersion(l4Ids, version);
            }
            List<ObjectProperties> objectProperties = objectPropertiesService.selectByPids(l4Ids);
            if (CollectionUtils.isNotEmpty(objectProperties)) {
                objectPropertiesService.updateVersion(objectProperties.stream().map(ObjectProperties::getId).collect(Collectors.toList()), version);
            }
        }
        return ids.size();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int unregister(List<Long> ids) {
        List<VlabelItem<BusinessObject>> vlabelItems = businessObjectMapper.selectByIds(ids);
        List<String> collect = vlabelItems.stream().map(VlabelItem::getProperties).filter(e -> e.getRegisterStatus() == RegisterStatus.UNREGISTER).map(BusinessObject::getCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            throw new ServiceException("存在以下编码的业务对象未锁定：" + JSONObject.toJSONString(collect));
        }
        return businessObjectMapper.register(ids);
    }

    @Async
    @Override
    public void importData(String fileName, List<BusinessObject> list, String operatorName) {
        ImportDataLog importDataLog = new ImportDataLog();
        importDataLog.setId(IdWorker.getId());
        importDataLog.setStatus("导入中");
        importDataLog.setImportBy(operatorName);
        importDataLog.setImportTime(new Date());
        importDataLog.setFileName(fileName);
        String log = "--INFO 数据正在导入中...\n";
        importDataLog.setLog(log);
        importDataLog.setTableName(LOG_TABLE_NAME);
        importDataLogService.insert(importDataLog);
        try {
            if (CollectionUtils.isEmpty(list)) {
                throw new ServiceException("导入的数据不能为空");
            }
            int successNum = 0;
            int failureNum = 0;
            StringBuilder successMsg = new StringBuilder();
            StringBuilder failureMsg = new StringBuilder();
            for (BusinessObject entity : list) {
                try {
                    if (StringUtils.isEmpty(entity.getDataResourcesCode())) {
                        throw new ServiceException("数据资源编码必填");
                    }
                    DataResources dataResources = dataResourcesService.selectByCode(entity.getDataResourcesCode());
                    if (dataResources == null) {
                        throw new ServiceException("数据资源[" + entity.getDataResourcesCode() + "]不存在");
                    }
                    if (dataResources.getDataType() != DataTypeEnum.reportData_KPI && dataResources.getDataType() != DataTypeEnum.reportData) {
                        throw new ServiceException("数据资源[" + entity.getDataResourcesCode() + "]只能为报告数据类型");
                    }
                    // 检查数据资源是否已经被(指标目录)引用
                    VlabelItem<DataTag> dataTagVlabelItem = dataTagMapper.selectByCode(entity.getDataResourcesCode());
                    if (dataTagVlabelItem != null) {
                        throw new ServiceException("数据资源[" + entity.getDataResourcesCode() + "]已被指标目录[" + dataTagVlabelItem.getProperties().getName() + "]引用");
                    }
                    DataProcessFramework theme = dataProcessFrameworkService.selectByCode(entity.getThemeCode());
                    if (theme == null) {
                        throw new ServiceException("主题L2[" + entity.getThemeCode() + "]不存在");
                    }
                    // 业务对象编码
                    entity.setCode(dataResources.getCode());
                    boolean updateFlag = false;
                    VlabelItem<BusinessObject> businessObjectVlabelItem = businessObjectMapper.selectByCode(entity.getCode());
                    if (businessObjectVlabelItem != null) {
                        updateFlag = true;
                        BusinessObject dbEntity = businessObjectVlabelItem.getProperties();
                        ExcelUtil<BusinessObject> excelUtil = new ExcelUtil<>(BusinessObject.class);
                        // 获取实体属性excel注解的值，用entity覆盖查询出来的值，实现更新效果
                        entity = excelUtil.copyImportDataToDbData(entity, dbEntity);
                    } else {
                        entity.setId(IdWorker.getId());
                    }
                    entity.setPid(theme.getId());
                    entity.setThemeCode(theme.getCode());
                    // 数据资源
                    entity.setDataResourcesId(dataResources.getId());
                    entity.setDataResourcesName(dataResources.getName());
                    // 指标名称
                    entity.setName(dataResources.getName());
                    // 描述
                    entity.setDescription(dataResources.getDescription());
                    // 默认值
                    if (entity.getRegisterStatus() == null) {
                        entity.setRegisterStatus(RegisterStatus.UNREGISTER);
                        entity.setRegisterStatusLabel(RegisterStatus.UNREGISTER.getDesc());
                    }
                    // 校验
                    if (StringUtils.isNotEmpty(entity.getSourceSystemCode())) {
                        // 校验数据来源系统
                        DataApplicationFramework applicationFramework = applicationFrameworkService.selectByCode(entity.getSourceSystemCode());
                        if (applicationFramework == null) {
                            throw new ServiceException("数据来源系统[" + entity.getSourceSystemCode() + "]不存在");
                        }
                        entity.setSourceSystemId(applicationFramework.getId());
                        entity.setSourceSystemName(applicationFramework.getName());
                    }
                    if (StringUtils.isNotEmpty(entity.getSecuritySortCode())) {
                        // 校验数据安全分类
                        DataSecurityClassification dataSecurityClassification = dataSecurityClassificationService.selectByCode(entity.getSecuritySortCode());
                        if (dataSecurityClassification == null) {
                            throw new ServiceException("数据安全分类[" + entity.getSecuritySortCode() + "]不存在");
                        }
                        entity.setSecuritySortId(dataSecurityClassification.getId());
                        entity.setSecuritySortName(dataSecurityClassification.getName());
                    }
                    if (StringUtils.isNotEmpty(entity.getSecurityLevelCode())) {
                        // 校验数据安全等级
                        DataSecurityLevel dataSecurityLevel = dataSecurityLevelService.selectByCode(entity.getSecurityLevelCode());
                        if (dataSecurityLevel == null) {
                            throw new ServiceException("数据安全分级[" + entity.getSecurityLevelCode() + "]不存在");
                        }
                        entity.setSecurityLevelId(dataSecurityLevel.getId());
                        entity.setSecurityLevelName(dataSecurityLevel.getName());
                    }
                    // 信息化为空时-默认值
                    if (StringUtils.isEmpty(dataResources.getInformationLabel())) {
                        entity.setIsInformation(dataResources.getIsInformation());
                        entity.setInformationLabel(dataResources.getInformationLabel());
                    }
                    // 来源系统为空时-默认值
                    if (StringUtils.isEmpty(entity.getSourceSystemCode())) {
                        entity.setSourceSystemId(dataResources.getSourceSystemId());
                        entity.setSourceSystemCode(dataResources.getSourceSystemCode());
                        entity.setSourceSystemName(dataResources.getSourceSystemName());
                    }
                    // 安全分类为空时-默认值
                    if (StringUtils.isEmpty(entity.getSecuritySortCode())) {
                        entity.setSecuritySortId(dataResources.getSecuritySortId());
                        entity.setSecuritySortCode(dataResources.getSecuritySortCode());
                        entity.setSecuritySortName(dataResources.getSecuritySortName());
                    }
                    // 安全分级为空时-默认值
                    if (StringUtils.isEmpty(entity.getSecurityLevelCode())) {
                        entity.setSecurityLevelId(dataResources.getSecurityLevelId());
                        entity.setSecurityLevelCode(dataResources.getSecurityLevelCode());
                        entity.setSecurityLevelName(dataResources.getSecurityLevelName());
                    }
                    // 枚举值转换
                    // 信息化
                    if (StringUtils.isNotEmpty(entity.getInformationLabel())) {
                        entity.setIsInformation(InformationEnum.valueOf(entity.getInformationLabel()));
                        entity.setInformationLabel(InformationEnum.valueOf(entity.getInformationLabel()).getDesc());
                    }
                    // 治理状态
                    if (StringUtils.isNotEmpty(entity.getGovernStatusLabel())) {
                        entity.setGovernStatus(GovernStatus.valueOf(entity.getGovernStatusLabel()));
                        entity.setGovernStatusLabel(GovernStatus.valueOf(entity.getGovernStatusLabel()).getDesc());
                    }
                    // 优先级
                    if (StringUtils.isNotEmpty(entity.getPriorityLabel())) {
                        entity.setPriority(PriorityEnum.valueOf(entity.getPriorityLabel()));
                        entity.setPriorityLabel(PriorityEnum.valueOf(entity.getPriorityLabel()).getDesc());
                    }
                    // 业务对象分类
                    if (StringUtils.isNotEmpty(entity.getBusinessObjectClassificationLabel())) {
                        entity.setBusinessObjectClassification(BusinessObjectClassification.valueOf(entity.getBusinessObjectClassificationLabel()));
                        entity.setBusinessObjectClassificationLabel(BusinessObjectClassification.valueOf(entity.getBusinessObjectClassificationLabel()).getDesc());
                    }
                    if (StringUtils.isEmpty(entity.getVersion())) {
                        entity.setVersion(Constants.VERSION_INITIALIZE);
                    }
                    if (updateFlag) {
                        int update = businessObjectMapper.updateById(entity);
                        if (update > 0) {
                            successNum++;
                            successMsg.append("\n" + successNum + "、业务对象 " + entity.getCode() + " 导入更新成功");
                        }
                    } else {
                        int insert = businessObjectMapper.insert(entity);
                        // 补偿新增字段，图库无法一次性新增太多字段，暂时用这方法替换
                        if (insert > 0) {
                            businessObjectMapper.updateById(entity);
                            successNum++;
                            successMsg.append("\n" + successNum + "、业务对象 " + entity.getCode() + " 导入成功");
                        }
                    }

                } catch (Exception e) {
                    failureNum++;
                    String code = entity.getCode();
                    if (StringUtils.isNotEmpty(code)) {
                        code = entity.getDataResourcesCode();
                    }
                    String msg = "\n" + failureNum + "、业务对象 " + code + " 导入失败：";
                    failureMsg.append(msg + e.getMessage());
                }
            }
            if (failureNum > 0) {
                failureMsg.insert(0, "很抱歉，导入失败！成功导入 " + successNum + " 条数据， 数据如下：" + successMsg + "， \n共 " + failureNum + " 条数据格式不正确，错误如下：");
                throw new ServiceException(failureMsg.toString());
            } else {
                successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
                importDataLog.setLog(log + "\n" + successMsg.toString());
                importDataLog.setEndTime(new Date());
                importDataLog.setStatus("成功");
                importDataLogService.update(importDataLog);
            }
        } catch (Exception e) {
            String message = e.getMessage();
            importDataLog.setLog(log + "\n" + message);
            importDataLog.setEndTime(new Date());
            importDataLog.setStatus("失败");
            importDataLogService.update(importDataLog);
        }
    }

    @Override
    public List<BusinessObject> getExportList(BusinessObject entity, String sort) {
        return businessObjectMapper.getExportList(entity, sort);
    }

    @Override
    public PageDataInfo<ImportDataLog> importLog(ImportDataLog importDataLog, String sort) {
        if (importDataLog == null) {
            importDataLog = new ImportDataLog();
        }
        importDataLog.setTableName(LOG_TABLE_NAME);
        return importDataLogService.list(importDataLog, sort);
    }

    @Override
    public ImportDataLog selectByIdLog(Long id) {
        return importDataLogService.selectById(LOG_TABLE_NAME, id);
    }

}
