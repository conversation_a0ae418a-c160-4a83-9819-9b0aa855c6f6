package com.datalink.fdop.govern.service;

import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.govern.api.domain.DataStandard;
import com.datalink.fdop.govern.api.domain.DataStandardTemporary;

import java.util.List;
import java.util.Map;

public interface IDataStandardService {
    int create(DataStandard dataStandard, Long userId);

    int update(DataStandard dataStandard, Long userId, Boolean flag);

    Map<String, Object> checkDeleteStandard(List<Long> ids);

    int delete(List<Long> ids, Long userId);

    DataStandardTemporary selectById(Long id);

    DataStandardTemporary selectByCodeAndVersion(String code, String version);

    List<DataStandard> selectVersion(Long standardId);

    PageDataInfo<DataStandard> overview(Long pid, String sort, SearchVo searchVo);

    int submitApproval(DataStandard dataStandard, Boolean flag);

    DataStandard getByField(Long fieldId);

    int copy(Long pid, List<DataStandard> dataStandardList);
}
