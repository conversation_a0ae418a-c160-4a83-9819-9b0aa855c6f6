package com.datalink.fdop.govern.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.poi.ExcelUtil;
import com.datalink.fdop.common.core.utils.tree.TreeUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.govern.api.domain.*;
import com.datalink.fdop.govern.api.enums.DataTypeEnum;
import com.datalink.fdop.govern.api.enums.GovernStatus;
import com.datalink.fdop.govern.api.enums.PriorityEnum;
import com.datalink.fdop.govern.api.enums.RegisterStatus;
import com.datalink.fdop.govern.mapper.BusinessObjectMapper;
import com.datalink.fdop.govern.mapper.DataProcessFrameworkMapper;
import com.datalink.fdop.govern.mapper.DataTagMapper;
import com.datalink.fdop.govern.service.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class DataTagServiceImpl implements IDataTagService {

    @Autowired
    private DataTagMapper dataTagMapper;

    @Autowired
    private IDataResourcesService dataResourcesService;

    @Autowired
    private DataProcessFrameworkMapper processFrameworkMapper;

    @Autowired
    private IDataProcessFrameworkService dataProcessFrameworkService;

    @Autowired
    private BusinessObjectMapper businessObjectMapper;

    @Autowired
    private IDataSecurityLevelService dataSecurityLevelService;

    @Autowired
    private IDataSecurityClassificationService dataSecurityClassificationService;

    @Autowired
    private ImportDataLogService importDataLogService;

    /**
     * 导入日志表
     */
    private final String LOG_TABLE_NAME = "d_g_data_tag_import_log";

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int create(DataTag entity) {
        if (entity.getPid() != -1L && dataProcessFrameworkService.selectById(entity.getPid()) == null) {
            throw new ServiceException("主题L2" + Status.DATA_PROCESS_FRAMEWORK_NOT_EXIST);
        }
        if (dataTagMapper.selectByCode(entity.getCode()) != null) {
            throw new ServiceException(Status.DATA_TAG_EXIST);
        }
        entity.setId(IdWorker.getId());
        // 校验字典值
        checkDictionaryValues(entity);
        int insert = dataTagMapper.insert(entity);
        // 补偿新增字段，图库无法一次性新增太多字段，暂时用这方法替换
        if (insert > 0) {
            dataTagMapper.updateInsertById(entity);
        }
        return insert;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(DataTag entity) {
        VlabelItem<DataTag> vlabelItem = dataTagMapper.selectById(entity.getId());
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATA_TAG_NOT_EXIST);
        }
        DataTag properties = vlabelItem.getProperties();
        // 注册状态指标不允许修改
        if (properties.getRegisterStatus() == RegisterStatus.REGISTER) {
            throw new ServiceException(Status.DATA_TAG_REGISTER_NOT_ALLOW_UPDATE);
        }
        if (StringUtils.isNotEmpty(entity.getCode()) && dataTagMapper.checkCodeIsExists(entity.getId(), entity.getCode()) != null) {
            throw new ServiceException(Status.DATA_TAG_EXIST);
        }
        // 校验字典值
        checkDictionaryValues(entity);
        return dataTagMapper.updateById(entity);
    }

    private void checkDictionaryValues(DataTag entity) {
        if (entity.getSecurityLevelId() == null || entity.getSecurityLevelId() == -1L) {
            throw new ServiceException(Status.DATA_SECURITY_LEVEL_NOT_EXIST);
        }
        // 校验数据安全等级
        DataSecurityLevel dataSecurityLevel = dataSecurityLevelService.selectById(entity.getSecurityLevelId());
        if (dataSecurityLevel == null) {
            throw new ServiceException(Status.DATA_SECURITY_LEVEL_NOT_EXIST);
        }
        entity.setSecurityLevelId(dataSecurityLevel.getId());
        entity.setSecurityLevelCode(dataSecurityLevel.getCode());
        entity.setSecurityLevelName(dataSecurityLevel.getName());
        if (entity.getSecuritySortId() == null || entity.getSecuritySortId() == -1L) {
            throw new ServiceException(Status.DATA_SECURITY_CLASS_NOT_EXIST);
        }
        // 校验数据安全分类
        DataSecurityClassification dataSecurityClassification = dataSecurityClassificationService.selectById(entity.getSecuritySortId());
        if (dataSecurityClassification == null) {
            throw new ServiceException(Status.DATA_SECURITY_CLASS_NOT_EXIST);
        }
        DataResources dataResources = dataResourcesService.selectById(entity.getDataResourceId());
        if (dataResources == null) {
            throw new ServiceException(Status.DATA_RESOURCES_NOT_EXIST);
        }
        // 检查数据资源是否已经被(业务对象)引用
        VlabelItem<BusinessObject> businessObjectVlabelItem = businessObjectMapper.selectByCode(entity.getDataResourceName());
        if (businessObjectVlabelItem != null) {
            throw new ServiceException("数据资源[" + entity.getDataResourceName() + "]已被业务对象[" + businessObjectVlabelItem.getProperties().getName() + "]引用");
        }
        entity.setSecuritySortId(dataSecurityClassification.getId());
        entity.setSecuritySortCode(dataSecurityClassification.getCode());
        entity.setSecuritySortName(dataSecurityClassification.getName());
    }

    @Override
    public int delete(List<Long> ids) {
        return dataTagMapper.deleteBatchIds(ids);
    }

    @Override
    public int register(List<Long> ids) {
        return dataTagMapper.batchRegister(ids);
    }

    @Override
    public int unregister(List<Long> ids) {
        return dataTagMapper.batchUnRegister(ids);
    }

    @Override
    public List<DataTag> getList(DataTag DataTag, Boolean disuseStatus) {
        return null;
    }

    @Override
    public DataTag selectByCode(String code) {
        VlabelItem<DataTag> vlabelItem = dataTagMapper.selectByCode(code);
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATA_TAG_NOT_EXIST);
        }
        return vlabelItem.getProperties();
    }

    @Override
    public PageDataInfo<DataTag> selectAll(DataTag entity) {
        return null;
    }

    @Override
    public DataTag selectById(Long id) {
        VlabelItem<DataTag> vlabelItem = dataTagMapper.selectById(id);
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATA_TAG_NOT_EXIST);
        }
        return vlabelItem.getProperties();
    }

    @Override
    public PageDataInfo<DataTag> overview(Long pid, String sort, SearchVo searchVo) {
        Page<DataTag> page = PageUtils.getPage(DataTag.class);
        // 获取分页参数
        IPage<DataTag> pageInfoList = dataTagMapper.overview(pid, page, sort, searchVo);
        List<DataTag> records = pageInfoList.getRecords();
        return PageUtils.getPageInfo(records, (int) pageInfoList.getTotal());
    }

    @Override
    public List<DataProcessFrameworkTree> treeList(String sort, String code, String name) {
        // 所有的数据集合
        List<DataProcessFrameworkTree> trees = new ArrayList<>();
        // 查询流程框架L1 L2
        List<DataProcessFrameworkTree> processTreeList = processFrameworkMapper.selectTree(sort, code, name, null);
        trees.addAll(processTreeList);
        // 查询指标目录L3
        List<DataProcessFrameworkTree> tagTreeList = dataTagMapper.selectTree(sort, code, name);
        trees.addAll(tagTreeList);
        // 递归获取子节点
        List<DataProcessFrameworkTree> newTreeList = (List<DataProcessFrameworkTree>) TreeUtils.getTree(trees);
        // 条件查找删除空集合的菜单
        if (StringUtils.isNotEmpty(code)) {
            TreeUtils.removeEmptyChilderAndMenu(newTreeList);
        }
        return newTreeList;
    }

    @Async
    @Override
    public void importData(String fileName, List<DataTag> list, String operatorName) {
        ImportDataLog importDataLog = new ImportDataLog();
        importDataLog.setId(IdWorker.getId());
        importDataLog.setStatus("导入中");
        importDataLog.setImportBy(operatorName);
        importDataLog.setImportTime(new Date());
        importDataLog.setFileName(fileName);
        String log = "--INFO 数据正在导入中...\n";
        importDataLog.setLog(log);
        importDataLog.setTableName(LOG_TABLE_NAME);
        importDataLogService.insert(importDataLog);
        try {
            if (CollectionUtils.isEmpty(list)) {
                throw new ServiceException("导入的数据不能为空");
            }
            int successNum = 0;
            int failureNum = 0;
            StringBuilder successMsg = new StringBuilder();
            StringBuilder failureMsg = new StringBuilder();
            for (DataTag entity : list) {
                try {
                    if (StringUtils.isEmpty(entity.getDataResourceName())) {
                        throw new ServiceException("数据资源编码必填");
                    }
                    DataResources dataResources = dataResourcesService.selectByCode(entity.getDataResourceName());
                    if (dataResources == null) {
                        throw new ServiceException("数据资源[" + entity.getDataResourceName() + "]不存在");
                    }
                    if (dataResources.getDataType() != DataTypeEnum.reportData_KPI && dataResources.getDataType() != DataTypeEnum.reportData) {
                        throw new ServiceException("数据资源[" + entity.getDataResourceName() + "]只能为报告数据类型");
                    }
                    // 检查数据资源是否已经被(业务对象)引用
                    VlabelItem<BusinessObject> businessObjectVlabelItem = businessObjectMapper.selectByCode(entity.getDataResourceName());
                    if (businessObjectVlabelItem != null) {
                        throw new ServiceException("数据资源[" + entity.getDataResourceName() + "]已被业务对象[" + businessObjectVlabelItem.getProperties().getName() + "]引用");
                    }
                    DataProcessFramework theme = dataProcessFrameworkService.selectByCode(entity.getThemeCode());
                    if (theme == null) {
                        throw new ServiceException("主题L2[" + entity.getThemeCode() + "]不存在");
                    }
                    // 指标编码
                    entity.setCode(dataResources.getCode());
                    boolean updateFlag = false;
                    // 校验编码是否已存在，已存在则更新
                    VlabelItem<DataTag> vlabelItem = dataTagMapper.selectByCode(entity.getCode());
                    if (vlabelItem != null) {
                        updateFlag = true;
                        DataTag dbEntity = vlabelItem.getProperties();
                        ExcelUtil<DataTag> excelUtil = new ExcelUtil<>(DataTag.class);
                        // 获取实体属性excel注解的值，用entity覆盖查询出来的值，实现更新效果
                        entity = excelUtil.copyImportDataToDbData(entity, dbEntity);
                    } else {
                        entity.setId(IdWorker.getId());
                    }
                    // 指标名称
                    entity.setName(dataResources.getName());
                    entity.setPid(theme.getId());
                    entity.setThemeCode(theme.getCode());
                    // 数据资源
                    entity.setDataResourceId(dataResources.getId());
                    entity.setDataResourceName(dataResources.getName());
                    // 信息化
                    entity.setIsInformation(dataResources.getIsInformation());
                    entity.setInformationLabel(dataResources.getInformationLabel());
                    // 数据类型
                    entity.setDataType(dataResources.getDataType());
                    entity.setDataTypeLabel(dataResources.getDataTypeLabel());
                    // 校验
                    if (StringUtils.isNotEmpty(entity.getSecuritySortCode())) {
                        // 校验数据安全分类
                        DataSecurityClassification dataSecurityClassification = dataSecurityClassificationService.selectByCode(entity.getSecuritySortCode());
                        if (dataSecurityClassification == null) {
                            throw new ServiceException("数据安全分类[" + entity.getSecuritySortCode() + "]不存在");
                        }
                        entity.setSecuritySortId(dataSecurityClassification.getId());
                        entity.setSecuritySortName(dataSecurityClassification.getName());
                    }
                    if (StringUtils.isNotEmpty(entity.getSecurityLevelCode())) {
                        // 校验数据安全等级
                        DataSecurityLevel dataSecurityLevel = dataSecurityLevelService.selectByCode(entity.getSecurityLevelCode());
                        if (dataSecurityLevel == null) {
                            throw new ServiceException("数据安全分级[" + entity.getSecurityLevelCode() + "]不存在");
                        }
                        entity.setSecurityLevelId(dataSecurityLevel.getId());
                        entity.setSecurityLevelName(dataSecurityLevel.getName());
                    }
                    // 安全分类为空时 - 默认值
                    if (StringUtils.isEmpty(entity.getSecuritySortCode())) {
                        entity.setSecuritySortId(dataResources.getSecuritySortId());
                        entity.setSecuritySortCode(dataResources.getSecuritySortCode());
                        entity.setSecuritySortName(dataResources.getSecuritySortName());
                    }
                    // 安全分级为空时 - 默认值
                    if (StringUtils.isEmpty(entity.getSecurityLevelCode())) {
                        entity.setSecurityLevelId(dataResources.getSecurityLevelId());
                        entity.setSecurityLevelCode(dataResources.getSecurityLevelCode());
                        entity.setSecurityLevelName(dataResources.getSecurityLevelName());
                    }
                    // 枚举值转换
                    // 注册状态
                    if (StringUtils.isNotEmpty(entity.getRegisterStatusLabel())) {
                        entity.setRegisterStatus(RegisterStatus.valueOf(entity.getRegisterStatusLabel()));
                        entity.setRegisterStatusLabel(RegisterStatus.valueOf(entity.getRegisterStatusLabel()).getDesc());
                    }
                    // 治理状态
                    if (StringUtils.isNotEmpty(entity.getGovernStatusLabel())) {
                        entity.setGovernStatus(GovernStatus.valueOf(entity.getGovernStatusLabel()));
                        entity.setGovernStatusLabel(GovernStatus.valueOf(entity.getGovernStatusLabel()).getDesc());
                    }
                    // 优先级
                    if (StringUtils.isNotEmpty(entity.getPriorityLabel())) {
                        entity.setPriority(PriorityEnum.valueOf(entity.getPriorityLabel()));
                        entity.setPriorityLabel(PriorityEnum.valueOf(entity.getPriorityLabel()).getDesc());
                    }
                    if (updateFlag) {
                        int update = dataTagMapper.updateById(entity);
                        if (update > 0) {
                            successNum++;
                            successMsg.append("\n" + successNum + "、指标目录 " + entity.getCode() + " 导入更新成功");
                        }
                    } else {
                        int insert = dataTagMapper.insert(entity);
                        // 补偿新增字段，图库无法一次性新增太多字段，暂时用这方法替换
                        if (insert > 0) {
                            dataTagMapper.updateInsertById(entity);
                            successNum++;
                            successMsg.append("\n" + successNum + "、指标目录 " + entity.getCode() + " 导入成功");
                        }
                    }
                } catch (Exception e) {
                    failureNum++;
                    String code = entity.getCode();
                    if (StringUtils.isNotEmpty(code)) {
                        code = entity.getDataResourceName();
                    }
                    String msg = "\n" + failureNum + "、指标目录 " + code + " 导入失败：";
                    failureMsg.append(msg + e.getMessage());
                }
            }
            if (failureNum > 0) {
                failureMsg.insert(0, "很抱歉，导入失败！成功导入 " + successNum + " 条数据， 数据如下：" + successMsg + "， \n共 " + failureNum + " 条数据格式不正确，错误如下：");
                throw new ServiceException(failureMsg.toString());
            } else {
                successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
                importDataLog.setLog(log + "\n" + successMsg);
                importDataLog.setEndTime(new Date());
                importDataLog.setStatus("成功");
                importDataLogService.update(importDataLog);
            }
        } catch (Exception e) {
            String message = e.getMessage();
            importDataLog.setLog(log + "\n" + message);
            importDataLog.setEndTime(new Date());
            importDataLog.setStatus("失败");
            importDataLogService.update(importDataLog);
        }
    }

    @Override
    public List<DataTagExport> getExportList(Long pid, String sort, SearchVo searchVo) {
        return dataTagMapper.getExportList(pid, sort, searchVo);
    }

    @Override
    public PageDataInfo<ImportDataLog> importLog(ImportDataLog importDataLog, String sort) {
        if (importDataLog == null) {
            importDataLog = new ImportDataLog();
        }
        importDataLog.setTableName(LOG_TABLE_NAME);
        return importDataLogService.list(importDataLog, sort);
    }

    @Override
    public ImportDataLog selectByIdLog(Long id) {
        return importDataLogService.selectById(LOG_TABLE_NAME, id);
    }

}
