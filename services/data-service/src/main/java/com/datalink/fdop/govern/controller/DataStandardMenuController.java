package com.datalink.fdop.govern.controller;



import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.govern.api.domain.DataStandardMenu;
import com.datalink.fdop.govern.api.domain.DataStandardTree;
import com.datalink.fdop.govern.api.enums.ApprovalStatusType;
import com.datalink.fdop.govern.service.IDataStandardMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping(value = "/govern/standard/menu")
@RestController
@Api(tags = "数据标准菜单api")
public class DataStandardMenuController
{

    @Autowired
    private IDataStandardMenuService dataStandardMenuService;

    @ApiOperation("创建菜单")
    @Log(title = "数据治理", businessType = BusinessType.INSERT)
    @PostMapping(value = "/create")
    public R create(@Validated @RequestBody DataStandardMenu dataStandardMenu) {
        if (dataStandardMenu.getPid() == null) {
            dataStandardMenu.setPid(-1L);
        }
        return R.toResult(dataStandardMenuService.create(dataStandardMenu));
    }

    @ApiOperation("修改菜单")
    @Log(title = "数据治理", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public R update(@RequestBody DataStandardMenu dataStandardMenu) {
        if (dataStandardMenu.getId() == null) {
            throw new ServiceException(Status.PARAM_ERROR);
        }
        return R.toResult(dataStandardMenuService.update(dataStandardMenu));
    }

    @ApiOperation("删除菜单")
    @Log(title = "数据治理", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.PLEASE_SPECIFY_THE_GOVERN_MENU_TO_DELETE);
        }
        return R.toResult(dataStandardMenuService.delete(ids));
    }

    @ApiOperation("展示数据菜单树结构")
    @Log(title = "数据元素")
    @GetMapping(value = "/tree")
    public R<List<DataStandardTree>> tree(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestParam(required = false) String code,
            @RequestParam(required = false, defaultValue = "true") Boolean isQueryNode,
            @RequestParam(required = false) ApprovalStatusType approvalStatusType) {
        List<DataStandardTree> list = dataStandardMenuService.tree(sort, code, isQueryNode,approvalStatusType);
        return R.ok(list);
    }
}
