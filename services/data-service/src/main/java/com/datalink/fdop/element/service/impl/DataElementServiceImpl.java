package com.datalink.fdop.element.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.element.api.domain.DataElement;
import com.datalink.fdop.element.api.domain.DataElementMenu;
import com.datalink.fdop.element.api.domain.DataElementStructure;
import com.datalink.fdop.element.api.domain.DataEntity;
import com.datalink.fdop.element.api.enums.DataElementType;
import com.datalink.fdop.element.api.model.vo.DataElementCopyVo;
import com.datalink.fdop.element.mapper.*;
import com.datalink.fdop.element.service.IDataElementMenuService;
import com.datalink.fdop.element.service.IDataElementService;
import com.datalink.fdop.element.service.IDataElementStructureService;
import com.datalink.fdop.element.service.IDataEntityStructureService;
import com.datalink.fdop.element.utils.GraphTableUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/12 13:58
 */
@Service
public class DataElementServiceImpl implements IDataElementService {

    @Autowired
    private DataElementMapper dataElementMapper;

    @Autowired
    private DataElementMenuMapper dataElementMenuMapper;

    @Autowired
    private DataElementStructureMapper dataElementStructureMapper;

    @Autowired
    private IDataElementMenuService dataElementMenuService;

    @Autowired
    private IDataElementStructureService dataElementStructureService;

    @Autowired
    private DataEntityMapper dataEntityMapper;

    @Autowired
    private GraphTableMapper graphTableMapper;

    @Autowired
    private IDataEntityStructureService dataEntityStructureService;

    private DataElement checkElement(Long id) {
        DataElement dataElement = dataElementMapper.selectById(id);
        if (dataElement == null) {
            throw new ServiceException(Status.DATA_ELEMENT_NOT_EXIST);
        }
        return dataElement;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DataElement create(DataElement dataElement) {
        if (dataElement.getPid() != -1L && dataElementMenuMapper.selectById(dataElement.getPid()) == null) {
            throw new ServiceException(Status.DATA_ELEMENT_MENU_NOT_EXIST);
        }
        if (dataElementMapper.selectByCode(dataElement.getCode()) != null) {
            throw new ServiceException(Status.DATA_ELEMENT_EXIST);
        }
        dataElement.setId(IdWorker.getId());
        int insert = dataElementMapper.insertElement(dataElement);
        // 创建元素边关系
        if (insert > 0 && dataElement.getPid() != -1L) {
            // 如果修改了元素层级，并且不是置为顶级菜单，则需要添加点和菜单的边关系
            dataElementMapper.createElementAndMenuEdge(dataElement.getPid(), Arrays.asList(dataElement.getId()));
        }
        return dataElement;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(DataElement dataElement) {
        return this.update(dataElement, true);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(DataElement dataElement, Boolean isCheckLength) {
        // 检查元素
        DataElement checkElement = checkElement(dataElement.getId());
        if (StringUtils.isEmpty(checkElement.getMapTableName())) {
            throw new ServiceException(Status.DATA_ELEMENT_DAMAGE);
        }
        if (StringUtils.isNotEmpty(dataElement.getCode()) && dataElementMapper.checkCodeIsExists(dataElement.getId(), dataElement.getCode()) != null) {
            throw new ServiceException(Status.DATA_ELEMENT_EXIST);
        }
        // 不能拖拽到节点里面
        if (dataElement.getPid() != null && dataElement.getPid() != -1L && dataElementMenuMapper.selectById(dataElement.getPid()) == null) {
            throw new ServiceException(Status.UNKNOWN_ELEMENT_MENU);
        }

        // 不为空检查该数据元素是否存在修改后同名的列
        if (checkElement.getDataElementType() != null && checkElement.getDataElementType() == DataElementType.MAIN) {
            List<DataElementStructure> dataElementStructureList = dataElementStructureService.selectStructureByDataElementId(dataElement.getId());
            if (dataElementStructureList.stream()
                    .filter(dataElementStructure -> !dataElement.getId().toString().equalsIgnoreCase(dataElementStructure.getId().toString()))
                    .anyMatch(dataElementStructure -> dataElement.getCode().equalsIgnoreCase(dataElementStructure.getCode()))) {
                throw new ServiceException(Status.A_FIELD_WITH_THE_SAME_NAME_EXISTS_IN_THE_ELEMENT_TABLE_STRUCTURE);
            }
        }

        // 映射的字段名
        dataElement.setMapFieldName(checkElement.getMapFieldName());
        // 映射的表名
        String tableName = checkElement.getMapTableName();

        // 验证长度和类型
        if (isCheckLength && dataElement.getFieldType() != null) {
            GraphTableUtils.initLength(dataElement);
        }

        int update = dataElementMapper.updateById(dataElement);
        if (update > 0) {
            // 先删除自己的边关系，再添加自己的边关系
            dataElementMapper.deleteElementOwnEdge(dataElement.getId());
            int insert = dataElementMapper.insertElementOwnEdge(dataElement);
            if (insert <= 0) {
                throw new ServiceException(Status.DATA_ELEMENT_OWN_EDGE_FAIL);
            }

            // 获取修改前的菜单pid,并删除修改前的点和菜单的边关系
            dataElementMapper.deleteElementAndMenuEdge(Arrays.asList(dataElement.getId()), checkElement.getPid());
            if (dataElement.getPid() != -1L) {
                // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                dataElementMapper.createElementAndMenuEdge(dataElement.getPid(), Arrays.asList(dataElement.getId()));
            }

            // 创建表
            if (dataElement.getDataElementType() != null && dataElement.getDataElementType() == DataElementType.MAIN && dataElement.getFieldType() != null) {
                // 第一次需要建表
                Boolean isCreateTable = graphTableMapper.isExistsTable(tableName);
                if (!isCreateTable) {
                    //         select COUNT(1)
                    //         from ag_catalog.cypher('zjdata_element_graph', $$ CREATE (table:${tableName} ${ageStr}) RETURN id(table),
                    //                                properties(table) $$) as (id ag_catalog.agtype,properties ag_catalog.agtype)
                    graphTableMapper.createGraphTable(tableName, GraphTableUtils.createGraphTable(dataElement));
                }
            }
        }
        return update;
    }

    @Override
    public int saveSource(Long id, String taskJson) {
        DataElement dataElement = checkElement(id);
        dataElement.setId(id);
        dataElement.setTaskJson(taskJson);

        // TODO:同步创建任务,工作流

        return dataElementMapper.updateById(dataElement);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void copy(Long pid, List<DataElementCopyVo> dataElementCopyList) {
        // 检查菜单
        DataElementMenu dataElementMenu = dataElementMenuMapper.selectById(pid);
        if (dataElementMenu == null) {
            throw new ServiceException(Status.DATA_ELEMENT_MENU_NOT_EXIST);
        }

        if (CollectionUtils.isEmpty(dataElementCopyList)) {
            throw new ServiceException(Status.THE_COPIED_ELEMENT_INFORMATION_CANNOT_BE_EMPTY);
        }

        int insert = 0;
        for (DataElementCopyVo dataElementCopyVo : dataElementCopyList) {
            // 检查实体
            DataElement dataElement = checkElement(dataElementCopyVo.getCodeNodeId());

            DataElement copyDataElement = new DataElement();
            // 设置修改后的pid
            copyDataElement.setPid(pid);

            // 修改基本信息
            copyDataElement.setCode(dataElementCopyVo.getCode());
            copyDataElement.setName(dataElementCopyVo.getName());
            copyDataElement.setDescription(dataElementCopyVo.getDescription());
            // 创建节点
            copyDataElement = this.create(copyDataElement);
            // 设置元素信息
            copyDataElement.setDataElementType(dataElement.getDataElementType());
            copyDataElement.setElementSourceType(dataElement.getElementSourceType());
            copyDataElement.setFieldType(dataElement.getFieldType());
            copyDataElement.setLength(dataElement.getLength());
            copyDataElement.setDecimalLength(dataElement.getDecimalLength());
            copyDataElement.setIsPk(dataElement.getIsPk());
            int update = this.update(copyDataElement, false);
            if (update <= 0) {
                throw new ServiceException(Status.COPY_NODE_FAILED);
            }

            // 获取被复制的元素的字段
            List<DataElementStructure> dataElementStructureList = dataElementStructureService.selectStructureByDataElementId(dataElementCopyVo.getCodeNodeId()).stream()
                    // 将元素自身的字段过滤掉
                    .filter(dataElementStructure -> !dataElementCopyVo.getCodeNodeId().equals(dataElementStructure.getId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(dataElementStructureList)) {
                dataElementStructureService.referenceAdd(copyDataElement, dataElementStructureList);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateElementElementEdge(DataElement dataElement) {
        // 先查询该元素被其他元素哪些元素引用，只查询子级
        List<DataElement> sonDataElementList = dataElementMapper.selectSonElement(dataElement.getId());
        if (CollectionUtils.isEmpty(sonDataElementList)) {
            return 1;
        }

        // 将子级的元素字段变成录入类型字段
        DataElementStructure dataElementStructure = new DataElementStructure(dataElement.getCode(), dataElement.getName(), dataElement.getDescription(), DataElementType.INPUT, dataElement.getFieldType(), dataElement.getLength(), dataElement.getDecimalLength(), false);

        int update = 0;
        for (DataElement sonDataElement : sonDataElementList) {
            // 设置seq
            List<Integer> seqs = dataElementStructureMapper.selectElementAllMaxSeq(sonDataElement.getId(), dataElementStructure.getIsPk());
            dataElementStructure.setSeq(seqs.stream().max(Integer::compare).get());

            update += dataElementStructureMapper.insertStructure(sonDataElement.getId(), Arrays.asList(dataElementStructure));
        }

        return update;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int delete(List<Long> ids) {
        for (Long id : ids) {
            DataElement dataElement = checkElement(id);
            if (dataElement.getDataElementType() == DataElementType.MAIN) {
                String tableName = dataElement.getMapTableName();

                // 如果其他元素引用了当前被删除的元素字段，需要将该元素字段转换为元素的录入类型字段
                this.updateElementElementEdge(dataElement);

                // 如果实体引用了当前被删除的元素字段,需要将该元素字段转换为实体的自定义类型字段
                dataEntityStructureService.updateEntityElementEdge(dataElementStructureService.selectStructureByDataElementId(id, false));

                // 存在表则删除
                if (graphTableMapper.isExistsTable(tableName)) {
                    graphTableMapper.deleteGraphTable(dataElementMapper.selectById(id).getMapTableName());
                }
            }
        }

        return dataElementMapper.deleteBatchIds(ids);
    }

    @Override
    public Map<String, Object> checkDeleteElement(List<Long> ids) {
        Map<String, Object> result = new HashMap<>();
        for (Long id : ids) {
            DataElement dataElement = checkElement(id);

            Map<String, Object> map = new HashMap<>();
            // 获取引用的元素
            List<DataElement> dataElementList = getSonElement(dataElement);
            if (CollectionUtils.isNotEmpty(dataElementList)) {
                map.put("ELEMENT", dataElementList);
            }

            // 获取引用的实体
            List<DataEntity> dataEntityList = dataEntityMapper.selectEntityAndElementRef(id);
            if (CollectionUtils.isNotEmpty(dataEntityList)) {
                map.put("ENTITY", dataEntityList);
            }
            result.put(dataElement.getCode(), map);
        }
        return result;
    }

    /**
     * 获取所有子级元素
     *
     * @param dataElement
     * @return
     */
    private List<DataElement> getSonElement(DataElement dataElement) {
        // 判断元素类型
        if (dataElement.getDataElementType() == DataElementType.FIELD) {
            return dataElementMapper.selectSonElementField(dataElement.getId());
        } else if (dataElement.getDataElementType() == DataElementType.MAIN) {
            List<DataElement> iterateStructureList = new ArrayList<>();
            List<DataElement> sonMainElementList = dataElementMapper.selectSonElement(dataElement.getId());
            if (CollectionUtils.isEmpty(sonMainElementList)) {
                return iterateStructureList;
            }
            for (DataElement sonMainElement : sonMainElementList) {
                iterateStructureList.addAll(getSonElement(sonMainElement));
            }
            iterateStructureList.addAll(sonMainElementList);
            return iterateStructureList;
        }
        return null;
    }

    @Override
    public PageDataInfo<DataElement> list(DataElement dataElement) {
        // 获取分页参数
        Page<DataElement> page = PageUtils.getPage(DataElement.class);
        IPage<DataElement> dataElementIPage = dataElementMapper.selectList(page, dataElement);

        return PageUtils.getPageInfo(dataElementIPage.getRecords(), (int) dataElementIPage.getTotal());
    }

    @Override
    public DataElement selectById(Long id) {
        return checkElement(id);
    }

    @Override
    public DataElement selectByCode(String code) {
        DataElement dataElement = dataElementMapper.selectByCode(code);
        if (dataElement == null) {
            throw new ServiceException(Status.DATA_ELEMENT_NOT_EXIST);
        }
        return dataElement;
    }
}
