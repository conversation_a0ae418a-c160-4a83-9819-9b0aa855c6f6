package com.datalink.fdop.govern.service;

import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.govern.api.domain.DataResources;
import com.datalink.fdop.govern.api.domain.ImportDataLog;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.List;

public interface IDataResourcesService {

    int create(DataResources entity);

    int update(DataResources dataResources);

    int delete(List<Long> ids);

    List<DataResources> listByDataTypeIsReport(DataResources dataResources);

    List<DataResources> listByNotReference(DataResources dataResources);

    int resume(List<DataResources> list);

    List<DataResources> getList(String sort, SearchVo searchVo);

    void importData(String fileName, List<DataResources> list, String operName);

    DataResources selectByCode(String code);

    PageDataInfo<DataResources> selectAll(DataResources entity);

    DataResources selectById(Long id);

    PageDataInfo<DataResources> overview(String sort, SearchVo searchVo);

    PageDataInfo<ImportDataLog> importLog(ImportDataLog importDataLog, String sort);

    ImportDataLog selectByIdLog(Long id);
}