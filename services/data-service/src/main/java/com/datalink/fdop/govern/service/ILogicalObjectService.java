package com.datalink.fdop.govern.service;

import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.govern.api.domain.ImportDataLog;
import com.datalink.fdop.govern.api.domain.LogicalObject;

import java.util.List;

public interface ILogicalObjectService {

    int create(LogicalObject entity);

    int update(LogicalObject entity);

    int delete(List<Long> ids);

    LogicalObject selectById(Long id);

    PageDataInfo<LogicalObject> list(LogicalObject entity, String sort);

    int lock(List<Long> ids);

    int unlock(List<Long> ids);

    List<LogicalObject> selectByPid(Long id);

    int updateVersion(List<Long> ids, String version);

    /**
     * 导入数据
     * @param fileName 文件名称
     * @param list excel数据
     * @param operatorName 操作员（日志记录用）
     */
    void importData(String fileName, List<LogicalObject> list, String operatorName);

    /**
     * 导出数据
     * @param entity 查询条件
     * @param sort 排序
     * @return
     */
    List<LogicalObject> getExportList(LogicalObject entity, String sort);


    /**
     * 导入日志
     * @param importDataLog 查询条件
     * @param sort 排序
     * @return
     */
    PageDataInfo<ImportDataLog> importLog(ImportDataLog importDataLog, String sort);

    /**
     * 日志详情
     * @param id
     * @return
     */
    ImportDataLog selectByIdLog(Long id);
}
