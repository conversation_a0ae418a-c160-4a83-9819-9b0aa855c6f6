package com.datalink.fdop.govern.service;

import com.datalink.fdop.common.core.domain.TreeVo;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.govern.api.domain.DataProcessFramework;
import com.datalink.fdop.govern.api.domain.DataProcessFrameworkMenu;

import java.util.List;


public interface IDataProcessFrameworkMenuService {

    int create(DataProcessFrameworkMenu processFrameworkMenu);

    int update(DataProcessFrameworkMenu processFrameworkMenu);

    int delete(List<Long> ids);

    DataProcessFrameworkMenu selectById(Long id);

    List<TreeVo> tree(String sort, String code, Boolean isQueryNode);

    PageDataInfo<DataProcessFramework> overview(Long pid, String sort, SearchVo searchVo);

    /**
     * 查询最大序号
     * @param menuFlag 是否为菜单
     * @return
     */
    Integer querySerialNumber(Boolean menuFlag);

}
