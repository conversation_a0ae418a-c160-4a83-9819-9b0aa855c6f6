package com.datalink.fdop.element.service;

import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.element.api.domain.DataElement;
import com.datalink.fdop.element.api.domain.DataElementMenu;
import com.datalink.fdop.element.api.model.DataElementTree;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/12 13:58
 */
public interface IDataElementMenuService {

    int create(DataElementMenu dataElementMenu);

    int update(DataElementMenu dataElementMenu);

    int delete(List<Long> ids);

    List<DataElementTree> tree(String sort, String code, Boolean isQueryNode);

    PageDataInfo<DataElement> overview(Long pid, String sort, SearchVo searchVo);

}
