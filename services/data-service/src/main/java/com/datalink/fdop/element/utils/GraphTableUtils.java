package com.datalink.fdop.element.utils;

import com.datalink.fdop.common.core.enums.FieldType;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.drive.api.domain.dto.Field;
import com.datalink.fdop.element.api.domain.DataElement;
import com.datalink.fdop.element.api.domain.DataElementStructure;
import com.datalink.fdop.element.api.domain.DataEntityStructure;

/**
 * <AUTHOR>
 * @date 2022/5/11 14:35
 */
public class GraphTableUtils {

    private static final long DEFAULT_NUMERIC_LENGTH = 32L;
    private static final long DEFAULT_STRING_LENGTH = 255L;
    private static final long DEFAULT_LENGTH = 0L;

    public static String createGraphTable(DataElement dataElement) {
        return String.format("{%s:%s}", dataElement.getMapFieldName(), getDefaultValue(dataElement.getFieldType()));
    }

    /**
     * 初始化长度
     *
     * @param field
     */
    public static void initLength(Field field) {
        FieldType fieldType = field.getBaseFieldType();
        Long length = field.getLength();
        Long decimalLength = field.getDecimalLength();
        if (length == null) {
            length = 0L;
        }
        if (decimalLength == null) {
            decimalLength = 0L;
        }
        switch (fieldType) {
            case 字符类型:
                if (length == 0L) {
                    throw new ServiceException(Status.THE_LENGTH_CANNOT_BE_0);
                }
                break;
            case 数值类型:
                if (length == 0L) {
                    length = 32L;
                }
                if (length < decimalLength) {
                    throw new ServiceException(Status.PRECISION_CANNOT_EXCEED_LENGTH);
                }
                break;
        }
        field.setLength(length);
        field.setDecimalLength(decimalLength);
    }

    public static void initLength(DataEntityStructure dataEntityStructure) {
        FieldType fieldType = dataEntityStructure.getFieldType();
        Long length = dataEntityStructure.getLength();
        Long decimalLength = dataEntityStructure.getDecimalLength();
        if (length == null) {
            length = 0L;
        }
        if (decimalLength == null) {
            decimalLength = 0L;
        }
        switch (fieldType) {
            case 字符类型:
                if (length == 0L) {
                    throw new ServiceException(Status.THE_LENGTH_CANNOT_BE_0);
                }
                break;
            case 数值类型:
                if (length == 0L) {
                    length = 32L;
                }
                if (length < decimalLength) {
                    throw new ServiceException(Status.PRECISION_CANNOT_EXCEED_LENGTH);
                }
                break;
        }
        dataEntityStructure.setLength(length);
        dataEntityStructure.setDecimalLength(decimalLength);
    }

    public static void initLength(DataElement dataElement) {
        FieldType fieldType = dataElement.getFieldType();
        Long length = dataElement.getLength();
        Long decimalLength = dataElement.getDecimalLength();
        if (length == null) {
            length = 0L;
        }
        if (decimalLength == null) {
            decimalLength = 0L;
        }
        switch (fieldType) {
            case 字符类型:
                if (length == 0L) {
                    throw new ServiceException(Status.THE_LENGTH_CANNOT_BE_0);
                }
                break;
            case 数值类型:
                if (length == 0L) {
                    length = 32L;
                }
                if (length < decimalLength) {
                    throw new ServiceException(Status.PRECISION_CANNOT_EXCEED_LENGTH);
                }
                break;
        }
        dataElement.setLength(length);
        dataElement.setDecimalLength(decimalLength);
    }

    public static void initLength(DataElementStructure dataElementStructure) {
        FieldType fieldType = dataElementStructure.getFieldType();
        Long length = dataElementStructure.getLength();
        Long decimalLength = dataElementStructure.getDecimalLength();
        if (length == null) {
            length = 0L;
        }
        if (decimalLength == null) {
            decimalLength = 0L;
        }
        switch (fieldType) {
            case 字符类型:
                if (length == 0L) {
                    throw new ServiceException(Status.THE_LENGTH_CANNOT_BE_0);
                }
                break;
            case 数值类型:
                if (length == 0L) {
                    length = 32L;
                }
                if (length < decimalLength) {
                    throw new ServiceException(Status.PRECISION_CANNOT_EXCEED_LENGTH);
                }
                break;
        }
        dataElementStructure.setLength(length);
        dataElementStructure.setDecimalLength(decimalLength);
    }

    /**
     * 初始化字段类型长度
     * @param field 字段
     */
    public static void initSynLength(Field field) {
        if (field == null) {
            throw new IllegalArgumentException("Field cannot be null");
        }
        // 设置默认长度
        setDefaultLength(field, DEFAULT_LENGTH);
        // 设置默认精度
        setDefaultDecimalLength(field, DEFAULT_LENGTH);
        // 根据字段类型设置特定的默认长度
        switch (field.getBaseFieldType()) {
            case 数值类型:
                setDefaultLength(field, DEFAULT_NUMERIC_LENGTH);
                break;
            case 字符类型:
                setDefaultLength(field, DEFAULT_STRING_LENGTH);
                break;
            default:
                // 其他类型保持默认长度不变
                break;
        }
    }

    private static void setDefaultLength(Field field, long defaultVal) {
        if (field.getLength() == null || field.getLength() == 0L) {
            field.setLength(defaultVal);
        }
    }

    private static void setDefaultDecimalLength(Field field, long defaultVal) {
        if (field.getDecimalLength() == null || field.getDecimalLength() == 0L) {
            field.setDecimalLength(defaultVal);
        }
    }


    /**
     * 获取默认值
     *
     * @param fieldType 字段类型
     * @return 默认字
     */
    private static String getDefaultValue(FieldType fieldType) {
        switch (fieldType) {
            case 字符类型:
            case 文本类型:
            case 时间类型:
            case 日期类型:
                return "''";
            case 长浮点类型:
            case 浮点类型:
            case 数值类型:
                return "0.0";
            case 长整数类型:
            case 时戳类型:
            case 整数类型:
                return "0";
            default:
                return "''";
        }
    }


}
