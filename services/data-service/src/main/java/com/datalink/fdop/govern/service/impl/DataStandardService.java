package com.datalink.fdop.govern.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.govern.api.domain.DataStandard;
import com.datalink.fdop.govern.api.domain.DataStandardTemporary;
import com.datalink.fdop.govern.api.enums.ApprovalStatusType;
import com.datalink.fdop.govern.mapper.DataStandardMapper;
import com.datalink.fdop.govern.mapper.DataStandardMenuMapper;
import com.datalink.fdop.govern.mapper.DataStandardTemporaryMapper;
import com.datalink.fdop.govern.service.IDataStandardMenuService;
import com.datalink.fdop.govern.service.IDataStandardService;
import com.datalink.fdop.quality.api.RemoteQualityService;
import com.datalink.fdop.quality.api.domain.DataQuality;
import com.datalink.fdop.system.api.model.LoginUser;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DataStandardService implements IDataStandardService {

    @Autowired
    private DataStandardMapper dataStandardMapper;

    @Autowired
    private DataStandardTemporaryMapper dataStandardTemporaryMapper;

    @Autowired
    private DataStandardMenuMapper dataStandardMenuMapper;

    @Autowired
    private IDataStandardMenuService dataStandardMenuService;

    //@Autowired
    //private RemoteActivitiService remoteActivitiService;

    @Autowired
    private RemoteQualityService remoteQualityService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int create(DataStandard dataStandard, Long userId) {
        if (dataStandard.getPid() != -1L && dataStandardMenuMapper.selectById(dataStandard.getPid()) == null) {
            throw new ServiceException(Status.STANDARD_MENU_DOES_NOT_EXIST);
        }
        if (dataStandard.getActModelId() != null) {
            if (dataStandardTemporaryMapper.selectByCode(dataStandard.getCode(), dataStandard.getVersion(), userId) != null) {
                throw new ServiceException(Status.STANDARD_ALREADY_EXISTS);
            }
        } else {
            if (dataStandardMapper.selectByCode(dataStandard.getCode(), dataStandard.getVersion()) != null) {
                throw new ServiceException(Status.STANDARD_ALREADY_EXISTS);
            }
        }
        DataStandard properties = dataStandardMapper.findByCode(dataStandard.getCode());
        if (properties != null) {
            dataStandard.setPid(properties.getPid());
            dataStandard.setStandardId(properties.getStandardId());
        }
        if (dataStandard.getStandardId() == null) {
            dataStandard.setStandardId(IdWorker.getId());
        }


        //如过有审批保存到临时表，没有审批保存到正式表
        dataStandard.setId(IdWorker.getId());
        int insert = 0;
        if (dataStandard.getActModelId() != null) {
            DataStandardTemporary dataStandardTemporary = new DataStandardTemporary();
            BeanUtils.copyProperties(dataStandard, dataStandardTemporary);
            dataStandardTemporary.setUserId(userId);
            insert = dataStandardTemporaryMapper.insertStandardTemporary(dataStandardTemporary);
            dataStandard.setLength(null);
            dataStandard.setFieldType(null);
            dataStandard.setDecimalLength(null);
            dataStandard.setRegexRule(null);
            dataStandard.setActiveStatu(null);
        }
        insert = dataStandardMapper.insertStandard(dataStandard);
        // 创建元素边关系
        if (insert > 0 && dataStandard.getPid() != -1L) {
            // 如果修改了元素层级，并且不是置为顶级菜单，则需要添加点和菜单的边关系
            dataStandardMapper.createStandardAndMenuEdge(dataStandard.getPid(), Arrays.asList(dataStandard.getId()));
        }
        return insert;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int update(DataStandard dataStandard, Long userId, Boolean flag) {
        DataStandard standard = dataStandardMapper.selectById(dataStandard.getId());
        if (standard == null) {
            throw new ServiceException(Status.STANDARD_DOES_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(dataStandard.getCode())
                && dataStandardMapper.checkCodeIsExists(dataStandard.getStandardId(), dataStandard.getCode()) != null
                || dataStandardTemporaryMapper.checkCodeIsExists(dataStandard.getStandardId(), dataStandard.getCode()) != null
        ) {
            throw new ServiceException(Status.STANDARD_ALREADY_EXISTS);
        }
        // 不能拖拽到节点里面
        if (dataStandard.getPid() != null && dataStandard.getPid() != -1L && dataStandardMenuMapper.selectById(dataStandard.getPid()) == null) {
            throw new ServiceException(Status.UNKNOWN_GOVERN_MENU);
        }


        //有审批提交审批存临时表，没有审批修改正式表
        int update = 0;
        if (dataStandard.getActModelId() == null || flag) {
            update += dataStandardMapper.updateShellByStandardId(dataStandard);
            update += dataStandardMapper.updateContentById(dataStandard);
        } else {
            update += submitApproval(dataStandard, false);
        }
        if (flag) {
            dataStandardTemporaryMapper.deleteByIdAndUserId(dataStandard.getId(), userId);
        }
        if (update > 0 && dataStandard.getPid() != null) {
            // 获取修改前的菜单pid,并删除修改前的点和菜单的边关系
            dataStandardMapper.deleteStandardMenuEdge(Arrays.asList(dataStandard.getId()), standard.getPid());
            if (dataStandard.getPid() != -1L) {
                // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                dataStandardMapper.createStandardMenuEdge(dataStandard.getPid(), Arrays.asList(dataStandard.getId()));
            }
        }

        return update;
    }

    @Override
    public Map<String, Object> checkDeleteStandard(List<Long> ids) {
        Map<String, Object> result = new HashMap<>();
        if (CollectionUtils.isEmpty(ids)) {
            return result;
        }

        for (Long id : ids) {
            // 查询标准信息
            DataStandard dataStandard = dataStandardMapper.selectById(id);
            if (dataStandard == null) {
                continue;
            }
            // 查询标准引用的规则
            List<DataQuality> qualityList = dataStandardMapper.selectQualityByStandardId(id);
            if (CollectionUtils.isEmpty(qualityList)) {
                continue;
            }
            Map<String, Object> map = new HashMap<>();
            map.put("QUALITY", qualityList.stream().map(DataQuality::getCode).collect(Collectors.toList()));
            result.put(dataStandard.getCode(), map);
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int delete(List<Long> ids, Long userId) {
        int i = 0;
        for (Long id : ids) {
            // 查询标准引用的规则
            List<DataQuality> qualityList = dataStandardMapper.selectQualityByStandardId(id);
            if (CollectionUtils.isEmpty(qualityList)) {
                continue;
            }
            for (DataQuality dataQuality : qualityList) {
                dataQuality.setIsCiteStandard(false);
                // 删除标准需要校验是否被规则引用
                R update = remoteQualityService.update(dataQuality);
                if (update.getCode() != Status.SUCCESS.getCode()) {
                    throw new ServiceException(update.getMsg());
                }
            }
        }
        i += dataStandardMapper.deleteBatchIds(ids);
        if (userId != null) {
            i += dataStandardTemporaryMapper.deleteBatchIds(ids);
        }
        return i;
    }

    @Override
    public DataStandardTemporary selectById(Long id) {
        DataStandard dataStandard = dataStandardMapper.selectById(id);
        if (dataStandard == null) {
            return null;
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            DataStandardTemporary dataStandardTemporary = new DataStandardTemporary();
            BeanUtils.copyProperties(dataStandard, dataStandardTemporary);
            return dataStandardTemporary;
        }
        Long userId = loginUser.getSysUser().getUserId();
        //查看临时表
        DataStandardTemporary dataStandardTemporary = dataStandardTemporaryMapper.selectByCode(dataStandard.getCode(), dataStandard.getVersion(), userId);
        if (dataStandardTemporary == null) {
            dataStandardTemporary = new DataStandardTemporary();
            BeanUtils.copyProperties(dataStandard, dataStandardTemporary);
            return dataStandardTemporary;
        } else {
            return dataStandardTemporary;
        }
    }

    @Override
    public DataStandardTemporary selectByCodeAndVersion(String code, String version) {
        DataStandard dataStandard = dataStandardMapper.selectByCode(code, version);
        if (dataStandard == null) {
            return null;
        }
        Long userId = SecurityUtils.getLoginUser().getSysUser().getUserId();
        //查看临时表
        DataStandardTemporary dataStandardTemporary = dataStandardTemporaryMapper.selectByCode(dataStandard.getCode(), dataStandard.getVersion(), userId);
        if (dataStandardTemporary == null) {
            dataStandardTemporary = new DataStandardTemporary();
            BeanUtils.copyProperties(dataStandard, dataStandardTemporary);
            return dataStandardTemporary;
        } else {
            return dataStandardTemporary;
        }
    }

    @Override
    public List<DataStandard> selectVersion(Long standardId) {
        List<DataStandard> list = dataStandardMapper.selectByStandardId(standardId);
        return list;
    }

    @Override
    public PageDataInfo<DataStandard> overview(Long pid, String sort, SearchVo searchVo) {
        // 获取分页参数
        Page<DataStandard> page = PageUtils.getPage(DataStandard.class);
        // 查询数据
        List<Long> menuIds = dataStandardMenuService.getChildrenMenuId(Arrays.asList(pid));
        IPage<DataStandard> dataEntityIPage = dataStandardMapper.selectAll(page, sort, menuIds, searchVo);
        List<DataStandard> records = dataEntityIPage.getRecords();
        List<DataStandard> result = Lists.newArrayList();
        for (DataStandard record : records) {
            Long standardId = record.getStandardId();
            List<DataStandard> list = dataStandardMapper.selectByStandardId(standardId);
            if (list != null) {
                DataStandard dataStandard = new DataStandard();
                for (int i = 0; i < list.size(); i++) {
                    DataStandard dataStandard1 = list.get(i);
                    if (i == 0) {
                        dataStandard = dataStandard1;
                    } else {
                        dataStandard.getChildren().add(dataStandard1);
                    }
                }
                result.add(dataStandard);
            }
        }

        return PageUtils.getPageInfo(result, (int) dataEntityIPage.getTotal());
    }

    @Override
    public int submitApproval(DataStandard dataStandard, Boolean flag) {
        int update = 0;
        Long userId = SecurityUtils.getLoginUser().getSysUser().getUserId();
        DataStandardTemporary dataStandardTemporary = new DataStandardTemporary();
        dataStandardTemporary.setUserId(userId);
        BeanUtils.copyProperties(dataStandard, dataStandardTemporary);
        DataStandardTemporary standardTemporary = dataStandardTemporaryMapper.selectByCode(dataStandardTemporary.getCode(), dataStandard.getVersion(), userId);
        //提交审批
        if (flag) {
            List<DataStandardTemporary> list = dataStandardTemporaryMapper.selectALLByCode(dataStandardTemporary.getCode(), dataStandard.getVersion());
            List<DataStandardTemporary> temporaryList = list.stream().filter(r -> r.getApprovalStatusType() == ApprovalStatusType.IN_APPROVAL).collect(Collectors.toList());
            if (standardTemporary != null && CollectionUtils.isNotEmpty(temporaryList)) {
                throw new ServiceException("该版本正在审批中，不可修改");
            }
            //R<String> r = remoteActivitiService.submitApplyById(dataStandardTemporary.getActModelId(), String.valueOf(userId), JSONObject.toJSONString(dataStandardTemporary));
            //if (r.getCode() != 200) {
            //    log.error("提交错误失败：{}", r.getMsg());
            //    throw new ServiceException("提交审批失败");
            //}
            //String instanceId = r.getMsg();
            //dataStandardTemporary.setInstanceId(instanceId);
            dataStandardTemporary.setApprovalStatusType(ApprovalStatusType.IN_APPROVAL);
        } else {
            dataStandardTemporary.setApprovalStatusType(ApprovalStatusType.UNCOMMITTED);
        }
        DataStandardTemporary standardTemporary1 = dataStandardTemporaryMapper.selectByCode(dataStandard.getCode(), dataStandard.getVersion(), userId);
        if (standardTemporary1 != null) {
            update += dataStandardTemporaryMapper.updateContentById(dataStandardTemporary);
            update += dataStandardTemporaryMapper.updateShellByStandardId(dataStandardTemporary);
        } else {
            update += dataStandardTemporaryMapper.insertStandardTemporary(dataStandardTemporary);
        }
        return update;
    }

    @Override
    public DataStandard getByField(Long fieldId) {
        DataStandard standard = dataStandardMapper.getFieldStandard(fieldId);
        if (standard == null) {
            return null;
        }
        return standard;
    }

    @Override
    public int copy(Long pid, List<DataStandard> dataStandardList) {
        for (DataStandard standard : dataStandardList) {
            DataStandard dataStandard = dataStandardMapper.selectById(standard.getId());
            if (dataStandard == null) {
                throw new ServiceException("标准不存在");
            }
            dataStandard.setId(IdWorker.getId());
            dataStandard.setCode(standard.getCode());
            dataStandard.setName(standard.getName());
            dataStandard.setStandardId(null);
            dataStandard.setVersion(standard.getVersion());
            dataStandard.setPid(pid);
            Long userId = SecurityUtils.getLoginUser().getSysUser().getUserId();
            create(dataStandard, userId);
        }
        return 1;
    }


}
