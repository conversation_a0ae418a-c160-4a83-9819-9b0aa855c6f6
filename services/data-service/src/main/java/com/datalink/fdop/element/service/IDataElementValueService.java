package com.datalink.fdop.element.service;

import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.element.api.domain.DataElement;
import com.datalink.fdop.element.api.domain.DataElementStructure;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/4/12 13:58
 */
public interface IDataElementValueService {

    List<Map<String, Object>> previewData(Long dataElementId, Boolean isPk);


    PageDataInfo<Map<String, Object>> previewDataTable(Long dataElementId, Boolean isPk, DataElement dataElement, List<DataElementStructure> structures);

    DataElement getDataElement(Long dataElementId);

    List<DataElementStructure> getDataElementStructure(Long dataElementId);
}
