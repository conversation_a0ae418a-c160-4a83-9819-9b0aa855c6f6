package com.datalink.fdop.element.service;

import com.datalink.fdop.common.core.enums.FieldType;
import com.datalink.fdop.element.api.domain.DataElement;
import com.datalink.fdop.element.api.domain.DataElementStructure;
import com.datalink.fdop.element.api.model.vo.DataElementStructureVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/12 13:58
 */
public interface IDataElementStructureService {

    int create(Long dataElementId, DataElementStructure dataElementStructure);

    int update(Long dataElementId, DataElementStructure dataElementStructure);

    int updateSequence(Long dataElementId, List<DataElementStructure> dataElementStructureList);

    int delete(Long dataElementId, List<DataElementStructure> dataElementStructureList);

    int referenceAdd(Long dataElementId, List<DataElementStructure> dataElementStructureList);

    int referenceAdd(DataElement dataElement, List<DataElementStructure> dataElementStructureList);

    // int createOnUpdate(Long dataElementId, List<DataElementStructure> dataElementStructureList);

    List<DataElementStructure> selectStructureByDataElementId(Long dataElementId, Boolean isQueryFather);

    List<DataElementStructure> selectStructureByDataElementId(DataElement dataElement, Boolean isQueryFather, FieldType fieldType, Long length, Long decimalLength);

    List<DataElementStructure> selectStructureByDataElementId(Long dataElementId);

    List<DataElementStructure> selectStructureByDataElementId(Long dataElementId, FieldType fieldType,
                                                              Long length, Long decimalLength);

    List<DataElementStructureVo> selectMenuAndElementAndFieldList(Long dataElementId, String code, String name,
                                                                  Boolean isFilerInput);

    List<DataElementStructureVo> selectQualityMenuAndElementAndFieldList(Long dataElementId, String code, String name,
                                                                  Boolean isFilerInput, FieldType fieldType,
                                                                  Long length, Long decimalLength);

    List<DataElementStructure> getIterateStructure(Long dataElementId, FieldType fieldType, Long length, Long decimalLength);

    // Object checkElementStructure(List<Long> ids);

    int importElement(MultipartFile file, Long dataElementId);

    void exportElement(HttpServletResponse response, Long dataElementId);

}
