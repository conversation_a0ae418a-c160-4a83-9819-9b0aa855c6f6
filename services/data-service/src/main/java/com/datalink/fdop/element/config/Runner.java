package com.datalink.fdop.element.config;

import com.datalink.fdop.element.api.domain.DataElement;
import com.datalink.fdop.element.api.enums.DataElementType;
import com.datalink.fdop.element.mapper.DataElementMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;

/**
 * <AUTHOR>
 * @Date 2022/6/9 17:42
 */
public class Runner implements ApplicationRunner {
    private Logger LOGGER = LoggerFactory.getLogger(Runner.class);

    @Autowired
    public DataElementMapper dataElementMapper;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        try {
            // 添加内置元素0_sys_element
            DataElement dataElement = dataElementMapper.selectByCode("0_sys_element");
            if (dataElement == null) {
                dataElement = new DataElement();
                dataElement.setCode("0_sys_element");
                dataElement.setName("0_sys_element");
                dataElement.setDataElementType(DataElementType.INLAY);
                dataElement.setPid(-1L);
                dataElementMapper.insertElement(dataElement);
            }
        } catch (Exception e) {
            LOGGER.error("添加内置元素失败:{}", e);
        }
    }
}
