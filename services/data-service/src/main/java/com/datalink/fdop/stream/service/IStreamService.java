package com.datalink.fdop.stream.service;

import com.alibaba.fastjson.JSONObject;
import com.datalink.fdop.common.core.domain.BaseDataModel;
import com.datalink.fdop.graph.api.flink.SelectResult;
import com.datalink.fdop.stream.api.domain.Stream;
import com.datalink.fdop.stream.api.dto.ReturnVo;
import com.datalink.fdop.graph.api.dto.TaskInfo;
import com.datalink.fdop.stream.config.KubernetesProperties;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
public interface IStreamService {

    int create(Stream stream);

    int update(Stream stream);

    int delete(List<Long> ids);

    Stream selectById(Long id);

    Boolean checkStatus(Long id);

    Boolean checkTaskIsExists(String taskName);

    String getLog(String taskName);

    void start(Long id);

    void stop(Long id);

    void stop(String taskName);

    KubernetesProperties getK8sUrlAndToken();

    ReturnVo submitFlink2K8s(TaskInfo taskInfo);

    /**
     * flink 流任务上线
     * @param id 任务ID
     * @return 上线结果
     */
    JSONObject online(Long id);

    /**
     * flink 流任务下线
     * @param id 任务ID
     * @return 下线结果
     */
    JSONObject offline(Long id);


    /**
     * 根据实体/表 生成Flink Create Table sql
     * @param dataModel 数据模型
     * @return Flink Create Table sql
     */
    String generateFlinkCreateTableStructure(BaseDataModel dataModel);

    /**
     * 获取flink job 异常日志信息
     * @param jobId jobId
     * @return 异常信息
     */
    String getExceptionByJobId(String jobId);

    SelectResult getJobData(String jobId);


}

