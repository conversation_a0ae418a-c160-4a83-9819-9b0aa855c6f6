package com.datalink.fdop.govern.controller;


import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.govern.api.domain.DataStandard;
import com.datalink.fdop.govern.api.domain.DataStandardTemporary;
import com.datalink.fdop.govern.service.IDataStandardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping(value = "/govern/standard")
@RestController
@Api(tags = "数据标准api")
public class DataStandardController {
    @Autowired
    private IDataStandardService dataStandardService;

    JdbcTemplate jdbcTemplate;

    @ApiOperation("创建标准")
    @Log(title = "数据治理")
    @PostMapping(value = "/create")
    public R create(@Validated @RequestBody DataStandard dataStandard, @RequestParam(required = false) Long userId) {
        if (dataStandard.getPid() == null) {
            dataStandard.setPid(-1L);
        }
        return R.toResult(dataStandardService.create(dataStandard, userId));
    }

    @ApiOperation("修改标准")
    @Log(title = "数据治理")
    @PostMapping(value = "/update")
    public R update(@RequestBody DataStandard dataStandard, @RequestParam(required = false) Long userId, @RequestParam(value = "flag", required = false, defaultValue = "false") Boolean flag) {
        if (dataStandard.getId() == null) {
            throw new ServiceException(Status.PARAM_ERROR);
        }
        return R.toResult(dataStandardService.update(dataStandard, userId, flag));
    }

    @ApiOperation("删除标准前校验")
    @Log(title = "数据治理")
    @PostMapping(value = "/checkDeleteStandard")
    public R checkDeleteStandard(@RequestBody List<Long> ids) {
        return R.ok(dataStandardService.checkDeleteStandard(ids));
    }

    @ApiOperation("删除标准")
    @Log(title = "数据治理")
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Long> ids, @RequestParam(required = false) Long userId) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.LEASE_SPECIFY_THE_GOVERN_TO_DELETE);
        }
        return R.toResult(dataStandardService.delete(ids, userId));
    }

    @ApiOperation("根据id查询查询用户最新该版本信息")
    @Log(title = "数据治理")
    @PostMapping(value = "/selectById")
    public R selectById(@RequestParam Long id) {
        return R.ok(dataStandardService.selectById(id));
    }

    @ApiOperation("根据code,version查询用户最新该版本信息")
    @Log(title = "数据治理")
    @PostMapping(value = "/selectByCodeAndVersion")
    public R<DataStandardTemporary> selectByCodeAndVersion(@RequestParam String code, String version) {
        return R.ok(dataStandardService.selectByCodeAndVersion(code, version));
    }

    @ApiOperation("标准下的版本")
    @Log(title = "数据治理")
    @PostMapping(value = "/selectVersion")
    public R selectVersion(@RequestParam Long standardId) {
        return R.ok(dataStandardService.selectVersion(standardId));
    }

    @ApiOperation("总览")
    @Log(title = "数据治理")
    @PostMapping(value = "/overview")
    public R<PageDataInfo> overview(
            @RequestParam(value = "pid", defaultValue = "-1") Long pid,
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestBody(required = false) SearchVo searchVo
    ) {
        return R.ok(dataStandardService.overview(pid, sort, searchVo));
    }


    @ApiOperation("复制")
    @Log(title = "数据治理")
    @PostMapping(value = "/copy")
    public R copy(
            @RequestParam(value = "pid", defaultValue = "-1") Long pid,
            @RequestBody List<DataStandard> dataStandardList
    ) {
        return R.ok(dataStandardService.copy(pid, dataStandardList));
    }


    @ApiOperation("提交审批")
    @Log(title = "数据治理")
    @PostMapping(value = "/submitApproval")
    public R submitApproval(@RequestBody DataStandard dataStandard, @RequestParam(required = false, defaultValue = "true") Boolean flag) {
        dataStandardService.submitApproval(dataStandard, flag);
        return R.ok();
    }


    @ApiOperation("获取字段绑定标准")
    @Log(title = "数据治理")
    @GetMapping(value = "/getByField")
    public R<DataStandard> getByField(@RequestParam(value = "fId") Long fId) {
        return R.ok(dataStandardService.getByField(fId));
    }
}
