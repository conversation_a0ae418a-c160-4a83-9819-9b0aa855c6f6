package com.datalink.fdop.element.service;

import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.element.api.domain.DataEntityTable;
import com.datalink.fdop.element.api.domain.DataEntityTableMapping;
import com.datalink.fdop.element.api.domain.DataEntityTableVo;
import com.datalink.fdop.element.api.model.vo.DataEntityStructureVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/12 13:58
 */
public interface IDataEntityTableService {

    DataEntityTable createOnUpdate(DataEntityTable dataEntityTable);

    void saveMappingOnSyncAndCreatTable(Long tenantId, DataEntityTable dataEntityTable);

    int update(DataEntityTable dataEntityTable);

    int isDeleteOldTable(Long dataEntityId, Long dataSourceId, String databaseName, String tableName, Boolean isDelete);

    int saveMapping(DataEntityTable dataEntityTable);

    int saveMapping(Long tenantId, DataEntityTable dataEntityTable);

    int createTable(Long dataEntityId, DataEntityTable dataEntityTable);

    /**
     * 创建表 异步方法调用
     *
     * @param tenantId        租户ID
     * @param dataEntityId    数据实体ID
     * @param dataEntityTable 实体表信息
     * @return
     */
    int createTable(Long tenantId, Long dataEntityId, DataEntityTable dataEntityTable, boolean dropOldTable);

    int delete(Long dataEntityId, Boolean isDelete, List<Long> ids);

    DataEntityTable selectById(Long dataEntityId);

    DataEntityTable selectById(Long dataEntityId, Long tenantId);

    List<DataEntityTable> selectByIds(List<Long> dataEntityId);

    PageDataInfo<DataEntityTable> list(DataEntityTable dataEntityTable);

    List<DataEntityTableMapping> selectTableMapping(Long dataEntityId, Long tableId);

    List<DataEntityTableMapping> selectTableMapping(Long dataEntityId, Long tenantId, Long tableId);

    PageDataInfo<DataEntityStructureVo> selectTableMappingPaging(Long dataEntityId, Long tableId);

    List<DataEntityTableMapping> selectEntityTableMapping(Long dataEntityId);

    List<DataEntityTableMapping> selectEntityTableMapping(Long dataEntityId, Long tenantId);

    void deleteEntityTableData(Long dataEntityId, String where);

    DataEntityTable checkDataEntityTable(Long tableId);

}
