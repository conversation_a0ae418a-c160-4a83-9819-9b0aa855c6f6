package com.datalink.fdop.govern.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.tree.TreeUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.govern.api.domain.DataProcessFramework;
import com.datalink.fdop.govern.api.domain.DataProcessFrameworkTree;
import com.datalink.fdop.govern.mapper.DataProcessFrameworkMapper;
import com.datalink.fdop.govern.service.IDataProcessFrameworkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class DataProcessFrameworkServiceImpl implements IDataProcessFrameworkService {

    @Autowired
    private DataProcessFrameworkMapper processFrameworkMapper;

    @Override
    public int create(DataProcessFramework processFramework) {
        if (processFrameworkMapper.selectByCode(processFramework.getCode()) != null) {
            throw new ServiceException(Status.DATA_PROCESS_FRAMEWORK_EXIST);
        }
        processFramework.setId(IdWorker.getId());
        return processFrameworkMapper.insert(processFramework);
    }

    @Override
    public int update(DataProcessFramework processFramework) {
        VlabelItem<DataProcessFramework> vlabelItem = processFrameworkMapper.selectById(processFramework.getId());
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATA_PROCESS_FRAMEWORK_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(processFramework.getCode()) && processFrameworkMapper.checkCodeIsExists(processFramework.getId(), processFramework.getCode()) != null) {
            throw new ServiceException(Status.DATA_PROCESS_FRAMEWORK_EXIST);
        }
        return processFrameworkMapper.updateById(processFramework);
    }

    @Override
    public int delete(List<Long> ids) {
        return processFrameworkMapper.deleteBatchIds(ids);
    }

    @Override
    public DataProcessFramework selectById(Long id) {
        VlabelItem<DataProcessFramework> vlabelItem = processFrameworkMapper.selectById(id);
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATA_PROCESS_FRAMEWORK_NOT_EXIST);
        }
        return vlabelItem.getProperties();
    }

    @Override
    public DataProcessFramework selectByCode(String code) {
        VlabelItem<DataProcessFramework> vlabelItem = processFrameworkMapper.selectByCode(code);
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATA_PROCESS_FRAMEWORK_NOT_EXIST);
        }
        return vlabelItem.getProperties();
    }

    @Override
    public List<DataProcessFrameworkTree> treeList(String sort, String code, String name, String description) {
        // 所有的数据集合
        List<DataProcessFrameworkTree> trees = new ArrayList<>();
        List<DataProcessFrameworkTree> treeList = processFrameworkMapper.selectTree(sort, code, name, description);
        trees.addAll(treeList);
        // 递归获取子节点
        List<DataProcessFrameworkTree> newTreeList = (List<DataProcessFrameworkTree>) TreeUtils.getTree(trees);
        // 条件查找删除空集合的菜单
        if (StringUtils.isNotEmpty(code)) {
            TreeUtils.removeEmptyChilderAndMenu(newTreeList);
        }
        return newTreeList;
    }

    @Override
    public PageDataInfo<DataProcessFramework> overview(Long pid, String sort, SearchVo searchVo) {
        Page<DataProcessFramework> page = PageUtils.getPage(DataProcessFramework.class);
        // 获取分页参数
        IPage<DataProcessFramework> pageInfoList = processFrameworkMapper.overview(page, pid, sort, searchVo);
        List<DataProcessFramework> records = pageInfoList.getRecords();
        return PageUtils.getPageInfo(records, (int) pageInfoList.getTotal());
    }

    @Override
    public List<SelectVo> selectVoList(DataProcessFramework processFramework) {
        return processFrameworkMapper.selectListAll(processFramework);
    }

    @Override
    public int copy(Long pid, List<DataProcessFramework> dataProcessFrameworkList) {
        if (processFrameworkMapper.selectById(pid) == null) {
            throw new ServiceException(Status.DATA_PROCESS_FRAMEWORK_NOT_EXIST);
        }
        int count = 0;
        for (DataProcessFramework dataProcessFramework : dataProcessFrameworkList) {
            // 复制到新目录
            dataProcessFramework.setId(IdWorker.getId());
            dataProcessFramework.setPid(pid);
            create(dataProcessFramework);
            count++;
        }
        return count;
    }

}
