package com.datalink.fdop.govern.service.impl;

import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.govern.api.domain.QualityCheckPlan;
import com.datalink.fdop.govern.mapper.QualityCheckMapper;
import com.datalink.fdop.govern.mapper.QualityCheckPlanMapper;
import com.datalink.fdop.govern.service.QualityCheckPlanService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
@Service
public class QualityCheckPlanServiceImpl implements QualityCheckPlanService {


    @Autowired
    private QualityCheckPlanMapper qualityCheckPlanMapper;

    @Autowired
    private QualityCheckMapper qualityCheckMapper;


    @Override
    public int delete(List<Long> ids) {
        for (Long id : ids) {
            VlabelItem<QualityCheckPlan> vlabelItem = qualityCheckPlanMapper.selectById(id);
            if (vlabelItem == null) {
                continue;
            }
            QualityCheckPlan checkPlan = vlabelItem.getProperties();
            // 获取当前被删除菜单的所有子级菜单
            List<Long> menuIdList = qualityCheckPlanMapper.selectIdsByPid(id);

            // 获取当前被删除菜单的所有子级
            List<Long> elementIdList = qualityCheckPlanMapper.selectIdsByPid(id);

            //删除所有方案的任务
            if (CollectionUtils.isNotEmpty(elementIdList)) {
                int delete = qualityCheckMapper.deleteBatchIds(elementIdList);
            }


        }
        // 删除菜单并且删除菜单的边关系
        return qualityCheckPlanMapper.deleteBatchIds(ids);
    }

    @Override
    public QualityCheckPlan selectById(Long id) {
        VlabelItem<QualityCheckPlan> vlabelItem = qualityCheckPlanMapper.selectById(id);
        return vlabelItem.getProperties();
    }
}
