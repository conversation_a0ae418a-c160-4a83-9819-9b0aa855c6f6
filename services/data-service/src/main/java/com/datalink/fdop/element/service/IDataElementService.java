package com.datalink.fdop.element.service;

import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.element.api.domain.DataElement;
import com.datalink.fdop.element.api.model.vo.DataElementCopyVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/4/12 13:58
 */
public interface IDataElementService {

    DataElement create(DataElement dataElement);

    int update(DataElement dataElement);

    public int update(DataElement dataElement, Boolean isCheckLength);

    int saveSource(Long id, String taskJson);

    void copy(Long pid, List<DataElementCopyVo> dataElementCopyList);

    int updateElementElementEdge(DataElement dataElement);

    int delete(List<Long> ids);

    Map<String, Object> checkDeleteElement(List<Long> ids);

    PageDataInfo<DataElement> list(DataElement dataElement);

    DataElement selectById(Long id);


    DataElement selectByCode(String code);
}
