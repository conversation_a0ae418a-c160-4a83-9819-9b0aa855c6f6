package com.datalink.fdop.element.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.FieldType;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.idempotent.annotation.RepeatSubmit;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.element.api.domain.DataElementStructure;
import com.datalink.fdop.element.api.model.vo.DataElementStructureVo;
import com.datalink.fdop.element.service.IDataElementStructureService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/42 11:50
 */
@RequestMapping(value = "/element/structure")
@RestController
@Api(tags = "数据元素表结构api")
public class DataElementStructureController extends BaseController {

    @Autowired
    private IDataElementStructureService dataElementStructureService;

    @ApiOperation("创建列")
    @Log(title = "数据元素", businessType = BusinessType.INSERT)
    @RepeatSubmit(interval = 1000)
    @PostMapping(value = "/{dataElementId}/create")
    public R create(@PathVariable(value = "dataElementId") Long dataElementId, @Validated @RequestBody DataElementStructure dataElementStructure) {
        return R.toResult(dataElementStructureService.create(dataElementId, dataElementStructure));
    }

    // @ApiOperation("修改或删除录入类型数据时检查")
    // @Log(title = "数据元素", businessType = BusinessType.UPDATE)
    // @PostMapping(value = "/checkElementStructure")
    // public R<Map<String, Object>> checkElementStructure(@RequestBody List<Long> ids) {
    //     return R.ok(dataElementStructureService.checkElementStructure(ids));
    // }

    @ApiOperation("修改列")
    @Log(title = "数据元素", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/{dataElementId}/update")
    public R update(@PathVariable(value = "dataElementId") Long dataElementId, @Validated @RequestBody DataElementStructure dataElementStructure) {
        return R.toResult(dataElementStructureService.update(dataElementId, dataElementStructure));
    }

    @ApiOperation("修改列的顺序")
    @Log(title = "数据元素", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/{dataElementId}/updateSequence")
    public R updateSequence(@PathVariable(value = "dataElementId") Long dataElementId, @RequestBody List<DataElementStructure> dataElementStructureList) {
        return R.toResult(dataElementStructureService.updateSequence(dataElementId, dataElementStructureList));
    }

    @ApiOperation("删除列")
    @Log(title = "数据元素", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/{dataElementId}/delete")
    public R delete(@PathVariable(value = "dataElementId") Long dataElementId, @RequestBody List<DataElementStructure> dataElementStructureList) {
        if (CollectionUtils.isEmpty(dataElementStructureList)) {
            throw new ServiceException(Status.PLEASE_SPECIFY_THE_ELEMENT_STRUCTURE_THAT_NEEDS_TO_BE_REMOVED);
        }
        return R.toResult(dataElementStructureService.delete(dataElementId, dataElementStructureList));
    }

    @ApiOperation("引用添加列")
    @Log(title = "数据元素", businessType = BusinessType.INSERT)
    @RepeatSubmit(interval = 1000)
    @PostMapping(value = "/{dataElementId}/referenceAdd")
    public R referenceAdd(@PathVariable(value = "dataElementId") Long dataElementId, @RequestBody List<DataElementStructure> dataElementStructureList) {
        // 引用新增不能添加重复的列
        if (dataElementStructureList.stream().map(DataElementStructure::getCode).distinct().count() != dataElementStructureList.size()) {
            throw new ServiceException(Status.CANNOT_REFERENCE_FIELDS_THAT_ADD_DUPLICATES);
        }
        return R.toResult(dataElementStructureService.referenceAdd(dataElementId, dataElementStructureList));
    }

    @ApiOperation("查询数据元素的表结构")
    @Log(title = "数据元素")
    @GetMapping(value = "/{dataElementId}/selectStructureByDataElementId")
    public R<List<DataElementStructure>> list(@PathVariable(value = "dataElementId") Long dataElementId) {
        List<DataElementStructure> dataElementColumnList = dataElementStructureService.selectStructureByDataElementId(dataElementId);
        return R.ok(dataElementColumnList);
    }

    @ApiOperation("查询数据元素的级联元素结构")
    @Log(title = "数据元素")
    @GetMapping(value = "/{dataElementId}/getIterateStructure")
    public R<List<DataElementStructure>> getIterateStructure(@PathVariable(value = "dataElementId") Long dataElementId) {
        List<DataElementStructure> dataElementColumnList = dataElementStructureService.getIterateStructure(dataElementId, null, null, null);
        return R.ok(dataElementColumnList);
    }

    @ApiOperation("引用添加展示菜单、元素、字段的嵌套表格")
    @Log(title = "数据元素")
    @GetMapping(value = "/selectMenuAndElementAndFieldList")
    public R<List<DataElementStructureVo>> selectMenuAndElementAndFieldList(
            @RequestParam(value = "dataElementId", required = false) Long dataElementId,
            @RequestParam(value = "code", required = false) String code,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "isFilterInput", required = false, defaultValue = "false") Boolean isFilerInput
    ) {
        List<DataElementStructureVo> dataElementStructureVoList = dataElementStructureService.
                selectMenuAndElementAndFieldList(dataElementId, code, name, isFilerInput);
        return R.ok(dataElementStructureVoList);
    }

    @ApiOperation("展示菜单、元素、字段的嵌套表格（数据质量的规则需要使用）")
    @Log(title = "数据元素")
    @GetMapping(value = "/selectQualityMenuAndElementAndFieldList")
    public R<List<DataElementStructureVo>> selectQualityMenuAndElementAndFieldList(
            @RequestParam(value = "dataElementId", required = false) Long dataElementId,
            @RequestParam(value = "code", required = false) String code,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "isFilterInput", required = false, defaultValue = "false") Boolean isFilerInput,
            @RequestParam(value = "fieldType", required = false) FieldType fieldType,
            @RequestParam(value = "length", required = false) Long length,
            @RequestParam(value = "decimalLength", required = false) Long decimalLength
    ) {
        List<DataElementStructureVo> dataElementStructureVoList = dataElementStructureService.
                selectQualityMenuAndElementAndFieldList(dataElementId, code, name, isFilerInput, fieldType, length, decimalLength);
        return R.ok(dataElementStructureVoList);
    }

    @ApiOperation("导入元素的表结构")
    @Log(title = "数据元素", businessType = BusinessType.IMPORT)
    @PostMapping("/importElement/{dataElementId}")
    @ResponseBody()
    @RepeatSubmit(interval = 1000)
    public R importEntity(MultipartFile file, @PathVariable(value = "dataElementId") Long dataElementId) {
        dataElementStructureService.importElement(file, dataElementId);
        return R.ok();
    }

    @ApiOperation("导出元素的表结构")
    @Log(title = "数据元素", businessType = BusinessType.EXPORT)
    @GetMapping("/exportElement/{dataElementId}")
    @ResponseBody()
    @RepeatSubmit(interval = 1000)
    public void exportEntity(HttpServletResponse response, @PathVariable(value = "dataElementId") Long dataElementId) {
        dataElementStructureService.exportElement(response, dataElementId);
    }

}
