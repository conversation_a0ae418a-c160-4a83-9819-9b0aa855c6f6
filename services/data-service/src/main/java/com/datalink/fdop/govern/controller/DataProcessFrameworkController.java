package com.datalink.fdop.govern.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.domain.TreeVo;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.govern.api.domain.DataProcessFramework;
import com.datalink.fdop.govern.api.domain.DataProcessFrameworkTree;
import com.datalink.fdop.govern.service.IDataProcessFrameworkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RequestMapping(value = "/govern/processFramework")
@RestController
@Api(tags = "流程框架api")
public class DataProcessFrameworkController {

    @Autowired
    private IDataProcessFrameworkService processFrameworkService;

    @ApiOperation("创建流程框架")
    @Log(title = "流程框架", businessType = BusinessType.INSERT)
    @PostMapping(value = "/create")
    public R create(@Validated @RequestBody DataProcessFramework processFramework) {
        return R.toResult(processFrameworkService.create(processFramework));
    }

    @ApiOperation("修改流程框架")
    @Log(title = "流程框架", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public R update(@RequestBody DataProcessFramework processFramework) {
        return R.toResult(processFrameworkService.update(processFramework));
    }

    @ApiOperation("删除流程框架")
    @Log(title = "流程框架", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/delete")
    public R delete(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException(Status.DATA_PROCESS_FRAMEWORK_TO_DELETE);
        }
        return R.toResult(processFrameworkService.delete(ids));
    }

    @ApiOperation("根据id查询流程框架信息")
    @Log(title = "流程框架")
    @GetMapping(value = "/selectById/{id}")
    public R<DataProcessFramework> selectById(@PathVariable("id") Long id) {
        return R.ok(processFrameworkService.selectById(id));
    }

    @ApiOperation(value = "根据code查询流程框架信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "流程框架编码", required = true, dataType = "String", paramType = "query"),
    })
    @GetMapping(value = "/selectByCode")
    @Log(title = "流程框架")
    public R<DataProcessFramework> selectByCode(@RequestParam(value = "code") String code) {
        return R.ok(processFrameworkService.selectByCode(code));
    }

    @ApiOperation("流程框架树结构")
    @Log(title = "流程框架")
    @GetMapping(value = "/tree")
    public R<List<DataProcessFrameworkTree>> treeList(
            @RequestParam(required = false, defaultValue = "ASC") String sort,
            @RequestParam(required = false) String code,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String description) {
        return R.ok(processFrameworkService.treeList(sort, code, name, description));
    }

    @ApiOperation("总览")
    @Log(title = "流程框架")
    @PostMapping(value = "/overview")
    public R<PageDataInfo<DataProcessFramework>> overview(@RequestParam(value = "pid", defaultValue = "-1", required = false) Long pid,
                                                          @RequestParam(value = "sort", defaultValue = "ASC", required = false) String sort,
                                                          @RequestBody(required = false) SearchVo searchVo) {
        return R.ok(processFrameworkService.overview(pid, sort, searchVo));
    }

    @ApiOperation("查询流程框架数据（下拉框）")
    @Log(title = "流程框架")
    @PostMapping(value = "/selectVoList")
    public R<List<SelectVo>> selectVoList(@RequestBody(required = false) DataProcessFramework processFramework) {
        return R.ok(processFrameworkService.selectVoList(processFramework));
    }


    @ApiOperation("复制流程框架")
    @Log(title = "流程框架", businessType = BusinessType.INSERT)
    @PostMapping(value = "/copy/{pid}")
    public R copy(@PathVariable("pid") Long pid, @Validated @RequestBody List<DataProcessFramework> processFrameworkList) {
        return R.ok(processFrameworkService.copy(pid, processFrameworkList));
    }

}
