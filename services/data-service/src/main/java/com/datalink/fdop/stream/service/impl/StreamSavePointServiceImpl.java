package com.datalink.fdop.stream.service.impl;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.stream.api.domain.StreamSavePoint;
import com.datalink.fdop.stream.mapper.StreamMapper;
import com.datalink.fdop.stream.mapper.StreamSavePointMapper;
import com.datalink.fdop.stream.service.IStreamSavePointService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 流任务保存点实现类
 */
@Service
public class StreamSavePointServiceImpl implements IStreamSavePointService {

    @Autowired
    private StreamSavePointMapper streamSavePointMapper;

    @Autowired
    private StreamMapper streamMapper;

    @Override
    public int create(StreamSavePoint savePoint) {
        if (savePoint.getStreamId() != -1L && streamMapper.selectById(savePoint.getStreamId()) == null) {
            throw new ServiceException(Status.STREAM_DOES_NOT_EXIST);
        }
        if (streamSavePointMapper.selectByCode(savePoint.getCode()) != null) {
            throw new ServiceException(Status.TASK_SP_EXECUTION_FAILED);
        }
        savePoint.setId(IdWorker.getId());
        // 设置序号
        Integer sort = streamSavePointMapper.querySerialNumber();
        savePoint.setSort(sort);
        int insert = streamSavePointMapper.insertStreamSavePoint(savePoint);
        // 创建与流任务的边关系
        if (insert > 0 && savePoint.getStreamId() != -1L){
            streamSavePointMapper.createStreamAndStreamSavePointEdge(savePoint.getStreamId(), Arrays.asList(savePoint.getId()));
        }
        return insert;
    }

    @Override
    public int update(StreamSavePoint savePoint) {
        if (streamSavePointMapper.selectById(savePoint.getId()) == null){
            throw new ServiceException(Status.STREAM_SP_DOES_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(savePoint.getCode()) && streamSavePointMapper.checkCodeIsExists(savePoint.getId(), savePoint.getCode()) != null) {
            throw new ServiceException(Status.STREAM_ALREADY_EXISTS);
        }
        return streamSavePointMapper.updateById(savePoint);
    }

    @Override
    public int delete(List<Long> ids, Long streamId) {
        // 先删除边关系
        streamSavePointMapper.deleteStreamAndStreamSavePointEdge(ids, streamId);
        return streamSavePointMapper.deleteBatchIds(ids);
    }

    @Override
    public PageDataInfo<StreamSavePoint> selectSavePointByStreamId(Long streamId, String sort, SearchVo searchVo) {
        // 获取分页参数dd
        Page<StreamSavePoint> page = PageUtils.getPage(StreamSavePoint.class);
        IPage<StreamSavePoint> streamSavePointIPage = streamSavePointMapper.selectSavePointListByStreamId(page, streamId, sort, searchVo);
        List<StreamSavePoint> records = streamSavePointIPage.getRecords();
        return PageUtils.getPageInfo(records, (int) streamSavePointIPage.getTotal());
    }

}
