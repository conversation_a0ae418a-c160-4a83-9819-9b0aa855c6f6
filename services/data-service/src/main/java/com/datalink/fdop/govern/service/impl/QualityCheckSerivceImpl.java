package com.datalink.fdop.govern.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.FieldType;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.drive.api.RemoteDriveService;
import com.datalink.fdop.drive.api.RemoteFieldRelationService;
import com.datalink.fdop.drive.api.RemoteJdbcService;
import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.drive.api.domain.dto.CreateTableDto;
import com.datalink.fdop.drive.api.domain.dto.Field;
import com.datalink.fdop.element.api.RemoteEntityStrureService;
import com.datalink.fdop.element.api.RemoteEntityTableService;
import com.datalink.fdop.element.api.domain.DataEntityTableMapping;
import com.datalink.fdop.element.api.model.vo.DataEntityStructureVo;
import com.datalink.fdop.govern.api.RemoteDolTaskService;
import com.datalink.fdop.govern.api.domain.ErrorTableField;
import com.datalink.fdop.govern.api.domain.QualityCheck;
import com.datalink.fdop.govern.mapper.QualityCheckMapper;
import com.datalink.fdop.govern.mapper.QualityCheckMenuMapper;
import com.datalink.fdop.govern.service.QualityCheckService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class QualityCheckSerivceImpl implements QualityCheckService {


    @Autowired
    private QualityCheckMapper qualityCheckMapper;

    @Autowired
    private QualityCheckMenuMapper qualityCheckMenuMapper;

    @Autowired
    private RemoteDolTaskService remoteDolTaskService;

    @Autowired
    private RemoteEntityTableService remoteEntityTableService;

    @Autowired
    private RemoteEntityStrureService remoteEntityStrureService;

    @Autowired
    private RemoteDriveService remoteDriveService;

    @Autowired
    private RemoteFieldRelationService remoteFieldRelationService;

    @Autowired
    private RemoteJdbcService remoteJdbcService;


    @Override
    public String generateCheckDataTotalSql(QualityCheck qualityCheck) {
        return null;
    }

    @Override
    public String generateCheckErrorDataSql(QualityCheck qualityCheck) {
        return null;
    }

    @Override
    public int create(QualityCheck seaTunnelCmd) {
        seaTunnelCmd.setId(IdWorker.getId());
        int insert = qualityCheckMapper.insertSeaTunnelCmd(seaTunnelCmd);
        // 创建元素边关系
        if (insert > 0 && seaTunnelCmd.getPid() != -1L) {
            // 如果修改了元素层级，并且不是置为顶级菜单，则需要添加点和菜单的边关系
            qualityCheckMapper.createSeaTunnelCmdAndMenuEdge(seaTunnelCmd.getPid(), Arrays.asList(seaTunnelCmd.getId()));
        }
        return insert;
    }

    public String GenerateSql(Long id) {
//        List<DataEntityStructureVo> entityFields = this.getRemoteDataEntityFields(id);
//        List<DataEntityTableMapping> tableMappings = this.getRemoteEntityTableMapping(id);
//        List<DataEntityStructureVo> collect = entityFields.stream().filter(field -> field.getEntityInsertType() != EntityInsertType.BUILTIN).map(field -> {
//            for (DataEntityTableMapping mapping : tableMappings) {
//                // 更新为实体映射字段
//                if (field.getCode().equals(mapping.getEntityFieldCode())) {
//                    field.setFieldName(mapping.getFieldName());
//                    break;
//                }
//            }
//            return field;
//        }).collect(Collectors.toList());
//
        StringBuilder selectBuilder = new StringBuilder("SELECT ");
//        selectBuilder.append(SeaTunnelConnectorFactory.getFieldsStr(options));
//        // 正则匹配 转义库名
//        String dataBaseName = escapeSql(options.getDatabaseName());
//        // 正则匹配 转义表名
//        String tableName = escapeSql(options.getTableName());
//        selectBuilder.append(" FROM ").append(String.format(JDBC_TABLE_FMT, dataBaseName, tableName));
//        if (StringUtils.isNotEmpty(options.getWhereSql())) {
//            selectBuilder.append(" WHERE ").append(options.getWhereSql());
//        }
        return selectBuilder.toString();
    }

    @Override
    public QualityCheck selectById(Long id) {

        VlabelItem<QualityCheck> vlabelItem = qualityCheckMapper.selectById(id);
        if (vlabelItem == null) {
            throw new ServiceException(Status.SEATUNNEL_CMD_DOES_NOT_EXIST);
        }
        return vlabelItem.getProperties();
    }

    @Override
    public Integer updateTask(QualityCheck qualityCheck) {
        List<ErrorTableField> ErrorTableFields = qualityCheck.getErrorTableFields();
        Long dataSourceId = qualityCheck.getDataSourceId();
        R<DataSource> queryDataSourceResult = remoteDriveService.queryDataSource(dataSourceId);
        if (queryDataSourceResult.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException(queryDataSourceResult.getMsg());
        }
        String dataBaseName = qualityCheck.getDataBaseName();
        R<PageDataInfo<String>> tablesResult = remoteJdbcService.getTables(dataSourceId, dataBaseName, 1, Integer.MAX_VALUE);
        if (tablesResult.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException(tablesResult.getMsg());
        }
        List<String> tableList = tablesResult.getData().getTotalList();
        if (tableList.contains(qualityCheck.getTableName())) {
            remoteJdbcService.dropTable(dataSourceId, qualityCheck.getDataBaseName(), qualityCheck.getTableName());
        }
        DataSource dataSource = queryDataSourceResult.getData();
        List<Field> fieldList = ErrorTableFields.stream()
                .map(dataEntityStructureVo -> {
                    Field field = new Field();
                    // sqlserver类型数据源区分大小写
                    if ("sqlserver".equalsIgnoreCase(dataSource.getType())) {
                        field.setFieldName(dataEntityStructureVo.getCode());
                    } else {
                        // 转小写
                        field.setFieldName(dataEntityStructureVo.getCode().toLowerCase());
                    }
                    field.setFieldDesc(dataEntityStructureVo.getName());
                    field.setBaseFieldType(dataEntityStructureVo.getFieldType());
                    field.setLength(dataEntityStructureVo.getLength());
                    field.setIsPk(dataEntityStructureVo.getIsPk());
                    field.setDecimalLength(dataEntityStructureVo.getDecimalLength());
                    field.setIsNull(!field.getIsPk());
                    return field;
                }).collect(Collectors.toList());

        //添加固定列 时间批次 uuid 以及当前检查任务名
        Field field = new Field();
        field.setFieldName("fdop_import_time");
        field.setFieldDesc("时间批次");
        field.setBaseFieldType(FieldType.时间类型);
        field.setLength(8L);
        field.setDecimalLength(0L);
        field.setIsPk(false);
        field.setIsNull(false);
        fieldList.add(field);

        Field uuidField = new Field();
        uuidField.setFieldName("_uuid");
        uuidField.setFieldDesc("UID数据");
        uuidField.setBaseFieldType(FieldType.字符类型);
        uuidField.setLength(8L);
        uuidField.setDecimalLength(0L);
        uuidField.setIsPk(false);
        uuidField.setIsNull(false);
        fieldList.add(uuidField);

        Field checkNameField = new Field();
        checkNameField.setFieldName("_check_name");
        checkNameField.setFieldDesc("当前检查任务名");
        checkNameField.setBaseFieldType(FieldType.字符类型);
        checkNameField.setLength(8L);
        checkNameField.setDecimalLength(0L);
        checkNameField.setIsPk(false);
        checkNameField.setIsNull(false);
        fieldList.add(checkNameField);

        Field countField = new Field();
        countField.setFieldName("_count");
        countField.setFieldDesc("总数");
        countField.setBaseFieldType(FieldType.长整数类型);
        countField.setLength(8L);
        countField.setDecimalLength(0L);
        countField.setIsPk(false);
        countField.setIsNull(false);
        fieldList.add(countField);
        R<List<Field>> r = remoteFieldRelationService.toSource(dataSource.getType(), fieldList);
        if (r.getCode() != Status.SUCCESS.getCode()) {
            return 0;
        }

        fieldList = r.getData();
        CreateTableDto createTable = new CreateTableDto();
        createTable.setDataSourceId(dataSourceId);
        createTable.setDatabaseName(qualityCheck.getDataBaseName());
        createTable.setTableName(qualityCheck.getTableName());
        createTable.setFieldList(fieldList);
        R tableResult = remoteJdbcService.createTable(createTable);
        if (tableResult.getCode() != Status.SUCCESS.getCode()) {
            return 0;
        }

        ErrorTableFields = fieldList.stream().map(item -> {
            ErrorTableField errorTableField = new ErrorTableField();
            errorTableField.setCode(item.getFieldName());
            errorTableField.setDescription(item.getFieldDesc());
            errorTableField.setFieldType(item.getBaseFieldType());
            return errorTableField;
        }).collect(Collectors.toList());

        String jsonString = JSON.toJSONString(ErrorTableFields);
        qualityCheck.setErrorTableJson(jsonString);

        long code = qualityCheck.getTaskCode();
        long projectCode = qualityCheck.getProjectCode();
        String taskDefinitionJsonObj = qualityCheck.getTaskDefinitionJsonObj();
        remoteDolTaskService.updateTaskDefinition(projectCode, code, taskDefinitionJsonObj);
        return qualityCheckMapper.updateById(qualityCheck);
    }


//    private static String getFieldsStr(DataModelOptions options) {
//        // seaTunnel 属性列
//        List<SeaTunnelField> seaTunnelFields = options.getSeaTunnelFields();
//        List<String> fields = new ArrayList<>();
//        for (SeaTunnelField field : seaTunnelFields) {
//            String fieldName = field.getFieldName();
//            if (StringUtils.isEmpty(fieldName)) {
//                continue;
//            }
//            // 正则匹配 转义列名
//            fieldName = escapeSql(fieldName);
//            if ("risingwave".equalsIgnoreCase(options.getSourceDbType())) {
//                // 先写死，没办法！妥协一下 目标是 rw的才需要转换一下
//                if ("DATE".equals(field.getFieldType())) {
//                    fieldName = "TO_CHAR(" + field.getFieldName() + ",'YYYY-MM-DD HH24:mi:SS') AS " + fieldName;
//                }
//            }
//            fields.add(fieldName);
//        }
//        return StringUtils.join(fields, ",");
//    }


    private List<DataEntityStructureVo> getRemoteDataEntityFields(Long id) {
        R<List<DataEntityStructureVo>> r = remoteEntityStrureService.selectStructureById(id);
        if (r.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }


    private List<DataEntityTableMapping> getRemoteEntityTableMapping(Long id) {
        R<List<DataEntityTableMapping>> r = remoteEntityTableService.selectEntityTableMapping(id);
        if (r.getCode() != Status.SUCCESS.getCode()) {
            throw new ServiceException(r.getMsg());
        }
        return r.getData();
    }

}
