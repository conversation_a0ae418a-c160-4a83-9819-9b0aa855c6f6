package com.datalink.fdop.element.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.enums.FieldType;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.DateUtils;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.split.SplitUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.datasource.annotation.SwitchDataSource;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.drive.api.RemoteDriveService;
import com.datalink.fdop.drive.api.RemoteJdbcService;
import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.drive.api.domain.dto.Field;
import com.datalink.fdop.drive.mapper.DataSourceMapper;
import com.datalink.fdop.element.api.domain.*;
import com.datalink.fdop.element.api.enums.*;
import com.datalink.fdop.element.api.enums.ImportMode;
import com.datalink.fdop.element.api.model.vo.DataEntityStructureVo;
import com.datalink.fdop.element.api.model.vo.DataEntitySynVo;
import com.datalink.fdop.element.api.model.vo.DataEntityTableNameSynVo;
import com.datalink.fdop.element.api.model.vo.DataJsonEntityVo;
import com.datalink.fdop.element.mapper.*;
import com.datalink.fdop.element.service.IDataEntityMenuService;
import com.datalink.fdop.element.service.IDataEntityService;
import com.datalink.fdop.element.service.IDataEntityStructureService;
import com.datalink.fdop.element.service.IDataEntitySynLogService;
import com.datalink.fdop.element.service.IDataEntityTableService;
import com.datalink.fdop.element.utils.GraphTableUtils;
import com.datalink.fdop.param.api.RemoteParamService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/12 13:58
 */
@Service
public class DataEntitySynLogServiceImpl implements IDataEntitySynLogService {

    private static final Logger logger = LoggerFactory.getLogger(DataEntitySynLogServiceImpl.class);

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private DataEntitytSynLogMapper dataEntitytSynLogMapper;

    @Autowired
    private IDataEntityStructureService dataEntityStructureService;

    @Autowired
    IDataEntityTableService dataEntityTableService;

    @Autowired
    private RemoteParamService remoteParamService;

    @Autowired
    private RemoteJdbcService remoteJdbcService;

    @Autowired
    private RemoteDriveService remoteDriveService;

    @Autowired
    private IDataEntityService dataEntityService;

    @Autowired
    private DataEntityMapper dataEntityMapper;

    @Autowired
    private DataEntityStructureMapper dataEntityStructureMapper;

    @Autowired
    private DataEntityTableMapper dataEntityTableMapper;

    @Autowired
    private DataEntityMenuMapper dataEntityMenuMapper;

    @Autowired
    private DataSourceMapper dataSourceMapper;

    @Autowired
    private IDataEntityMenuService dataEntityMenuService;

    @Autowired
    private ThreadPoolTaskExecutor entityImportExecutor;

    @Override
    public void synchronizeCopyAndCreateTable(DataEntitySynVo dataEntitySyn) {
        Long pid = dataEntitySyn.getPid();
        Long dataSourceId = dataEntitySyn.getDataSourceId();
        String databaseName = dataEntitySyn.getDatabaseName();
        Boolean isView = dataEntitySyn.getIsView();
        for (DataEntityTableNameSynVo dataEntityTableNameSynVo : dataEntitySyn.getTableNameList()) {
            // 表名
            String tableName = dataEntityTableNameSynVo.getTableName();
            // 实体名称
            String entityCode = dataEntityTableNameSynVo.getEntityCode();
            if (StringUtils.isEmpty(entityCode)) {
                entityCode = tableName;
            }
            EntityType entityType = dataEntityTableNameSynVo.getEntityType();
            // 记录同步日志
            DataEntitySynLog dataEntitySynLog = new DataEntitySynLog(IdWorker.getId(), pid, dataSourceId, databaseName, isView ? null : tableName, isView ? tableName : null, entityCode, true);
            dataEntitySynLog.setSinkDataSourceId(String.valueOf(dataEntitySyn.getSinkDataSourceId()));
            dataEntitySynLog.setSinkDataBaseName(dataEntitySyn.getSinkDataBaseName());
            String currentTime = DateUtils.getTime();
            Formatter formatter = new Formatter(System.out);
            formatter.format("[%s]，[实体同步开始，数据源：%s，库：%s，表名：%s]", currentTime, dataEntitySyn.getDataSourceId(), dataEntitySyn.getDatabaseName(), dataEntityTableNameSynVo.getTableName());
            dataEntitySynLog.setError(formatter.toString());
            dataEntitySynLog.setEntityType(entityType);
            this.insertSynLog(dataEntitySynLog);
            // 获取当前登录用户的租户ID
            Long tenantId = SecurityUtils.getLoginUser().getTenantId();
            applicationContext.getBean(IDataEntitySynLogService.class).buildDataEntityAndCreateTable(pid, entityCode, tenantId, dataSourceId, databaseName, tableName, isView, dataEntitySynLog, dataEntitySyn.getSinkDataSourceId(), dataEntitySyn.getSinkDataBaseName(), entityType, dataEntityTableNameSynVo.getTableDesc());
        }
    }


    @Override
    public void synchronize(DataEntitySynVo dataEntitySyn) {
        Long pid = dataEntitySyn.getPid();
        Long dataSourceId = dataEntitySyn.getDataSourceId();
        String databaseName = dataEntitySyn.getDatabaseName();
        Boolean isView = dataEntitySyn.getIsView();
        for (DataEntityTableNameSynVo dataEntityTableNameSynVo : dataEntitySyn.getTableNameList()) {
            // 表名
            String tableName = dataEntityTableNameSynVo.getTableName();
            // 实体名称
            String entityCode = dataEntityTableNameSynVo.getEntityCode();
            if (StringUtils.isEmpty(entityCode)) {
                entityCode = tableName;
            }
            EntityType entityType = dataEntityTableNameSynVo.getEntityType();
            // 记录同步日志
            DataEntitySynLog dataEntitySynLog = new DataEntitySynLog(IdWorker.getId(), pid, dataSourceId, databaseName, isView ? null : tableName, isView ? tableName : null, entityCode, false);
            dataEntitySynLog.setEntityType(entityType);
            this.insertSynLog(dataEntitySynLog);
            // 获取当前登录用户的租户ID
            Long tenantId = SecurityUtils.getLoginUser().getTenantId();
            applicationContext.getBean(IDataEntitySynLogService.class).buildDataEntity(pid, entityCode, tenantId, dataSourceId, databaseName, tableName, isView, dataEntitySynLog, entityType, dataEntityTableNameSynVo.getTableDesc());
        }
    }

    @Async
    @Transactional(rollbackFor = Exception.class)
    @Override
    @SwitchDataSource(value = "current", tenantIndex = 2)
    public void buildDataEntity(Long pid, String entityCode, Long tenantId, Long dataSourceId, String databaseName, String tableName, Boolean isView, DataEntitySynLog dataEntitySynLog, EntityType entityType, String description) {
        try {
            if (entityType == EntityType.DWD || entityType == EntityType.DIM) {
                DataEntityMenu dataEntityMenu = new DataEntityMenu();
                dataEntityMenu.setPid(pid);
                dataEntityMenu.setCode(entityCode);
                dataEntityMenu.setEntityType(entityType);
                if (dataEntityMenuMapper.selectByCode(dataEntityMenu.getCode()) != null) {
                    throw new ServiceException(Status.DATA_ENTITY_MENU_EXIST);
                }
                if (dataEntityMenu.getId() == null || dataEntityMenu.getId() == 0L) {
                    dataEntityMenu.setId(IdWorker.getId());
                }
                int insert = dataEntityMenuMapper.insertEntityMenu(dataEntityMenu);
                // 创建菜单边关系
                if (insert > 0 && dataEntityMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    dataEntityMenuMapper.createEntityMenuEdge(dataEntityMenu.getPid(), Arrays.asList(dataEntityMenu.getId()));
                }
                pid = dataEntityMenu.getId();
            }
            // 创建实体
            DataEntity dataEntity = new DataEntity(pid, entityCode);
            dataEntity.setEntityType(entityType);
            if (dataEntityMapper.selectByCode(entityCode) != null) {
                throw new ServiceException(Status.DATA_ENTITY_EXIST);
            }
            // 表描述信息
            dataEntity.setDescription(description);
            dataEntity.setId(IdWorker.getId());
            int insert = dataEntityMapper.insertEntity(dataEntity);
            // 创建元素边关系
            if (insert > 0 && pid != -1L) {
                // 如果修改了元素层级，并且不是置为顶级菜单，则需要添加点和菜单的边关系
                dataEntityMapper.createEntityAndMenuEdge(pid, Arrays.asList(dataEntity.getId()));
            }

            // 创建列
            // 获取字段信息
            R<List<Field>> fieldsR = remoteJdbcService.getFieldsByTenantId(tenantId, dataSourceId, databaseName, tableName);
            if (fieldsR.getCode() != Status.SUCCESS.getCode()) {
                throw new ServiceException(fieldsR.getMsg());
            }
            List<Field> fieldList = fieldsR.getData();

            // 获取数据源信息
            R<DataSource> sourceR = remoteDriveService.queryDataSourceByTenantId(tenantId, dataSourceId);
            if (sourceR.getCode() != Status.SUCCESS.getCode()) {
                throw new ServiceException(sourceR.getMsg());
            }
            DataSource dataSource = sourceR.getData();
            boolean flag = false;
            if ("oracle".equals(dataSource.getType())) {
                flag = true;
            }

            List<DataEntityStructure> dataEntityStructureList = new ArrayList<>(fieldList.size());
            for (int i = 0; i < fieldList.size(); i++) {
                Field field = fieldList.get(i);
                if (flag) {
                    field.setFieldName(field.getFieldName().toLowerCase());
                }
                Integer seq = i + 1;
                // 初始化长度
                GraphTableUtils.initSynLength(field);
                if (StringUtils.isNotEmpty(field.getFieldDesc()) && field.getFieldDesc().contains("\'")) {
                    field.setFieldDesc(field.getFieldDesc().replace("\'", "\\'"));
                }
                dataEntityStructureList.add(new DataEntityStructure(IdWorker.getId(), dataEntity.getId(),
                        EntityInsertType.CUSTOMIZE, field.getFieldName(), field.getFieldDesc(), field.getBaseFieldType(),
                        field.getLength(), field.getDecimalLength(), field.getIsPk(), seq));
            }
            // 每50个提交一次
            List<List<DataEntityStructure>> dataEntityStructureSplitList = SplitUtils.splitList(dataEntityStructureList, 50);
            for (List<DataEntityStructure> dataEntityStructureSplit : dataEntityStructureSplitList) {
                // 自定义添加字段
                dataEntityStructureMapper.batchInsertStructure(dataEntityStructureSplit);
                // 创建关系
                dataEntityStructureMapper.batchCreateStructureEdge(dataEntity.getId(), dataEntityStructureSplit);
            }

            // 判断是否只读
            Boolean isRead = isView ? true : false;
            if (dataSource.getIsRead() != null && dataSource.getIsRead()) {
                isRead = dataSource.getIsRead();
            }

            // 创建关联表
            DataEntityTable dataEntityTable = new DataEntityTable(IdWorker.getId(), dataEntity.getId(), dataSourceId, databaseName, tableName, isRead, isView ? CreateTableWay.VIEW : CreateTableWay.TABLE, dataSourceId, databaseName, tableName, isView);
            dataEntityTableMapper.insertTable(dataEntityTable);
            // 修改明细，添加映射
            // 获取映射字段
            List<DataEntityStructureVo> dataEntityStructureVos = dataEntityStructureService.selectStructureById(dataEntity.getId(), true);
            List<DataEntityTableMapping> dataEntityTableMappingList = fieldList.stream().map(field -> {
                        for (DataEntityStructureVo dataEntityStructureVo : dataEntityStructureVos) {
                            if (field.getFieldName().equalsIgnoreCase(dataEntityStructureVo.getCode())) {
                                return new DataEntityTableMapping(dataEntity.getId(), dataEntityTable.getId(), dataEntityStructureVo.getId(), EntityInsertType.CUSTOMIZE, null, field.getFieldName());
                            }
                        }
                        return null;
                    }).filter(map -> map != null)
                    .collect(Collectors.toList());

            dataEntityTable.setDataEntityTableMappingList(dataEntityTableMappingList);
            dataEntityTableService.saveMapping(tenantId, dataEntityTable);
            // 绑定实体和关联表
            dataEntityMapper.updateById(new DataEntity(dataEntity.getId(), dataEntityTable.getId()));

            dataEntitySynLog.setSynStatus(DataEntitySynStatus.SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage());
            e.printStackTrace();
            dataEntitySynLog.setError(e.getMessage());
            dataEntitySynLog.setSynStatus(DataEntitySynStatus.FAIL);
            throw new ServiceException(e.getMessage());
        } finally {
            applicationContext.getBean(IDataEntitySynLogService.class).updateSynLog(dataEntitySynLog);
        }
    }


    @Async
    @Transactional(rollbackFor = Exception.class)
    @Override
    @SwitchDataSource(value = "current", tenantIndex = 2)
    public void buildDataEntityAndCreateTable(Long pid, String entityCode, Long tenantId, Long dataSourceId, String databaseName, String tableName, Boolean isView, DataEntitySynLog dataEntitySynLog, long sinkDataSourceId, String sinkDataBaseName, EntityType entityType, String description) {
        String logInfo = "";
        try {
            if (entityType == EntityType.DWD || entityType == EntityType.DIM) {
                DataEntityMenu dataEntityMenu = new DataEntityMenu();
                dataEntityMenu.setPid(pid);
                dataEntityMenu.setCode(entityCode);
                dataEntityMenu.setEntityType(entityType);
                if (dataEntityMenuMapper.selectByCode(dataEntityMenu.getCode()) != null) {
                    throw new ServiceException(Status.DATA_ENTITY_MENU_EXIST);
                }
                if (dataEntityMenu.getId() == null || dataEntityMenu.getId() == 0L) {
                    dataEntityMenu.setId(IdWorker.getId());
                }
                int insert = dataEntityMenuMapper.insertEntityMenu(dataEntityMenu);
                // 创建菜单边关系
                if (insert > 0 && dataEntityMenu.getPid() != -1L) {
                    // 如果修改了菜单层级，并且不是置为顶级菜单，则需要添加边关系
                    dataEntityMenuMapper.createEntityMenuEdge(dataEntityMenu.getPid(), Arrays.asList(dataEntityMenu.getId()));
                }
                pid = dataEntityMenu.getId();
            }
            // 创建实体
            DataEntity dataEntity = new DataEntity(pid, entityCode);
            dataEntity.setEntityType(entityType);
            if (dataEntityMapper.selectByCode(entityCode) != null) {
                throw new ServiceException(Status.DATA_ENTITY_EXIST);
            }
            // 表描述信息
            dataEntity.setDescription(description);
            dataEntity.setId(IdWorker.getId());
            int insert = dataEntityMapper.insertEntity(dataEntity);
            // 创建元素边关系
            if (insert > 0 && pid != -1L) {
                // 如果修改了元素层级，并且不是置为顶级菜单，则需要添加点和菜单的边关系
                dataEntityMapper.createEntityAndMenuEdge(pid, Arrays.asList(dataEntity.getId()));
            }
            // 创建列
            // 获取源表字段信息
            R<List<Field>> fieldsR = remoteJdbcService.getFieldsByTenantId(tenantId, dataSourceId, databaseName, tableName);
            if (fieldsR.getCode() != Status.SUCCESS.getCode()) {
                throw new ServiceException(fieldsR.getMsg());
            }
            List<Field> fieldList = fieldsR.getData();
            Formatter formatter = new Formatter();
            String time = DateUtils.getTime();
            formatter.format("[%s] 读取源表[%s]结构，列数：%d个，主键列：%d个", time, tableName, fieldList.size(), fieldList.stream().filter(field -> field.getIsPk()).count());
            logInfo += formatter + "\n";
            logInfo += "[" + time + "]" + "实体生成中" + "\n";
            // 获取数据源信息
            R<DataSource> sourceR = remoteDriveService.queryDataSourceByTenantId(tenantId, dataSourceId);
            R<DataSource> sourceT = remoteDriveService.queryDataSourceByTenantId(tenantId, sinkDataSourceId);
            DataSource source = sourceT.getData();
            if (sourceR.getCode() != Status.SUCCESS.getCode()) {
                throw new ServiceException(sourceR.getMsg());
            }
            DataSource dataSource = sourceR.getData();
            boolean flag = false;
            if ("oracle".equals(dataSource.getType())) {
                flag = true;
            }
            //判断表内是否有导入字段
            boolean flag2 = false;
            Integer tmpSeq = 0;
            List<DataEntityStructure> dataEntityStructureList = new ArrayList<>(fieldList.size());
            for (int i = 0; i < fieldList.size(); i++) {
                Field field = fieldList.get(i);
                if (flag) {
                    field.setFieldName(field.getFieldName().toLowerCase());
                }
                if (field.getFieldName().equalsIgnoreCase("fdop_import_time")) {
                    flag2 = true;
                }
                Integer seq = i + 1;
                tmpSeq = seq + 1;
                // 初始化长度
                GraphTableUtils.initSynLength(field);
                if (StringUtils.isNotEmpty(field.getFieldDesc()) && field.getFieldDesc().contains("\'")) {
                    field.setFieldDesc(field.getFieldDesc().replace("\'", "\\'"));
                }
                dataEntityStructureList.add(new DataEntityStructure(IdWorker.getId(), dataEntity.getId(),
                        EntityInsertType.CUSTOMIZE, field.getFieldName(), field.getFieldDesc(), field.getBaseFieldType(),
                        field.getLength(), field.getDecimalLength(), field.getIsPk(), seq));
            }
            // 添加默认内置列
            if (!flag2) {
                dataEntityStructureList.add(new DataEntityStructure(IdWorker.getId(), dataEntity.getId(), EntityInsertType.BUILTIN, "fdop_import_time", "导入时间", FieldType.时戳类型, 0L, 0L, false, tmpSeq));
            }
            // 每50个提交一次
            List<List<DataEntityStructure>> dataEntityStructureSplitList = SplitUtils.splitList(dataEntityStructureList, 50);
            for (List<DataEntityStructure> dataEntityStructureSplit : dataEntityStructureSplitList) {
                // 自定义添加字段
                dataEntityStructureMapper.batchInsertStructure(dataEntityStructureSplit);
                // 创建关系
                dataEntityStructureMapper.batchCreateStructureEdge(dataEntity.getId(), dataEntityStructureSplit);
            }
            // 创建关联表
            DataEntityTable dataEntityTable = new DataEntityTable(IdWorker.getId(), dataEntity.getId(), sinkDataSourceId, sinkDataBaseName, entityCode, false, CreateTableWay.AUTO, dataSourceId, databaseName, tableName, isView);
            dataEntityTableMapper.insertTable(dataEntityTable);
            // Doris默认创表结构
            if ("doris".equals(source.getType())) {
                List<String> pkList = dataEntityStructureList.stream().filter(DataEntityStructure::getIsPk).map(DataEntityStructure::getCode).collect(Collectors.toList());
                String keyType = "DUPLICATE_KEY";
                List<String> bucketKeys = new ArrayList<>();
                if (!pkList.isEmpty()) {
                    keyType = "UNIQUE_KEY";
                    bucketKeys = pkList;
                } else {
                    // 无主键实体的第一个列作为DUPLICATE_KEY
                    bucketKeys.add(dataEntityStructureList.get(0).getCode());
                }
                JSONObject indexDefinition = new JSONObject();
                JSONObject dorisConfig = getDorisConfig();
                int buckets = 3;
                if (dorisConfig != null) {
                    // 分桶数量配置
                    buckets = dorisConfig.get("buckets") == null ? 3 : dorisConfig.getInteger("buckets");
                    // properties Doris额外创表属性配置
                    if (dorisConfig.getJSONObject("BValue") != null) {
                        indexDefinition.put("properties", dorisConfig.getJSONObject("BValue"));
                    }
                }
                // 创建keysType内部的对象和数组
                JSONObject keysType = new JSONObject();
                keysType.put("keyType", keyType);
                keysType.put("keysFieldList", bucketKeys);
                // 创建distributed内部的对象和数组
                JSONObject distributed = new JSONObject();
                distributed.put("distributedType", "HASH");
                distributed.put("distributedFieldList", bucketKeys);
                distributed.put("buckets", buckets);
                // 添加其他属性
                indexDefinition.put("keysType", keysType);
                indexDefinition.put("distributed", distributed);

                dataEntityTable.setCreateTableConfig(indexDefinition.toJSONString());
            }
            String EntityCreateTime = DateUtils.getDate();
            logInfo += EntityCreateTime + " 实体同步完成\n";
            logInfo += EntityCreateTime + " 实体同步完成，开始自动创表，目标数据\n";
            dataEntityTableService.createTable(tenantId, dataEntity.getId(), dataEntityTable, false);
            // 获取新建表字段信息
            // 获取源表字段信息
            fieldsR = remoteJdbcService.getFieldsByTenantId(tenantId, dataEntityTable.getDataSourceId(), dataEntityTable.getDatabaseName(), dataEntityTable.getTableName());
            if (fieldsR.getCode() != Status.SUCCESS.getCode()) {
                throw new ServiceException(fieldsR.getMsg());
            }
            List<Field> fieldListSink = fieldsR.getData();
            // 获取实体字段结构
            List<DataEntityStructureVo> dataEntityStructureVos = dataEntityStructureService.selectStructureById(dataEntity.getId(), true);
            // 不区分大小写同名匹配数据库字段列关系
            List<DataEntityTableMapping> dataEntityTableMappingList = fieldListSink.stream().map(field -> {
                        for (DataEntityStructureVo dataEntityStructureVo : dataEntityStructureVos) {
                            if (field.getFieldName().equalsIgnoreCase(dataEntityStructureVo.getCode())) {
                                return new DataEntityTableMapping(dataEntity.getId(), dataEntityTable.getId(), dataEntityStructureVo.getId(), EntityInsertType.CUSTOMIZE, null, field.getFieldName());
                            }
                        }
                        return null;
                    }).
                    filter(map -> map != null)
                    .collect(Collectors.toList());
            dataEntityTable.setDataEntityTableMappingList(dataEntityTableMappingList);
            // 保存实体字段与实体数据库表字段的映射关系
            dataEntityTableService.saveMappingOnSyncAndCreatTable(tenantId, dataEntityTable);
            // 绑定实体和关联表
            dataEntityMapper.updateById(new DataEntity(dataEntity.getId(), dataEntityTable.getId()));
            dataEntitySynLog.setSynStatus(DataEntitySynStatus.SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage());
            e.printStackTrace();
            String EntityCreateTime = DateUtils.getTime();
            logInfo += "[" + e.getMessage() + "]";
            String message = e.getMessage();
            dataEntitySynLog.setError(logInfo);
            dataEntitySynLog.setSynStatus(DataEntitySynStatus.FAIL);
            throw new ServiceException(e.getMessage());
        } finally {
            dataEntitySynLog.setError(logInfo);
            applicationContext.getBean(IDataEntitySynLogService.class).updateSynLog(dataEntitySynLog);
        }
    }

    /**
     * 异步创建实体和表方法
     * 用于批量导入时的并发控制，支持事务回滚和数据源切换
     */
    @Async("entityImportExecutor")
    @Transactional(rollbackFor = Exception.class)
    @SwitchDataSource(value = "current", tenantIndex = 2)
    public CompletableFuture<Void> buildDataEntityAndCreateTableAsync(Long globalLogId, String logInfo, Long pid, String entityCode, Long tenantId, Long dataSourceId, String databaseName, String tableName, Boolean isView, long sinkDataSourceId, String sinkDataBaseName, EntityType entityType, String description) {
        try {
            // 创建或更新实体
            DataEntity existingEntity = dataEntityMapper.selectByCode(entityCode);
            DataEntity dataEntity;
            if (existingEntity != null) {
                dataEntity = existingEntity;
                dataEntity.setPid(pid);
                dataEntity.setEntityType(entityType);
                dataEntity.setDescription(description);
                dataEntityMapper.updateById(dataEntity);
            } else {
                // 实体不存在
                dataEntity = new DataEntity(pid, entityCode);
                dataEntity.setEntityType(entityType);
                dataEntity.setDescription(description);
                dataEntity.setId(IdWorker.getId());
                int insert = dataEntityMapper.insertEntity(dataEntity);
                // 创建元素边关系
                if (insert > 0 && pid != -1L) {
                    // 如果修改了元素层级，并且不是置为顶级菜单，则需要添加点和菜单的边关系
                    dataEntityMapper.createEntityAndMenuEdge(pid, Arrays.asList(dataEntity.getId()));
                }
                logInfo += "[" + DateUtils.getTime() + "] 创建新实体\n";
            }
            // 如果实体已存在，先删除现有列
            if (existingEntity != null) {
                int deletedCount = dataEntityStructureService.deleteByEntityIdAndInsertType(dataEntity.getId());
                logInfo += "[" + DateUtils.getTime() + "] 删除现有列，删除数量：" + deletedCount + "\n";
            }

            // 创建列
            // 获取源表字段信息
            R<List<Field>> fieldsR = remoteJdbcService.getFieldsByTenantId(tenantId, dataSourceId, databaseName, tableName);
            if (fieldsR.getCode() != Status.SUCCESS.getCode()) {
                throw new ServiceException(fieldsR.getMsg());
            }
            List<Field> fieldList = fieldsR.getData();
            Formatter formatter = new Formatter();
            String time = DateUtils.getTime();
            formatter.format("[%s] 读取源表[%s]结构，列数：%d个，主键列：%d个", time, tableName, fieldList.size(), fieldList.stream().filter(field -> field.getIsPk()).count());
            logInfo += formatter + "\n";
            logInfo += "[" + time + "]" + "实体生成中" + "\n";
            // 获取数据源信息
            R<DataSource> sourceR = remoteDriveService.queryDataSourceByTenantId(tenantId, dataSourceId);
            R<DataSource> sourceT = remoteDriveService.queryDataSourceByTenantId(tenantId, sinkDataSourceId);
            DataSource source = sourceT.getData();
            if (sourceR.getCode() != Status.SUCCESS.getCode()) {
                throw new ServiceException(sourceR.getMsg());
            }
            DataSource dataSource = sourceR.getData();
            boolean flag = false;
            if ("oracle".equals(dataSource.getType())) {
                flag = true;
            }
            //判断表内是否有导入字段
            List<DataEntityStructure> dataEntityStructureList = new ArrayList<>(fieldList.size());
            for (int i = 0; i < fieldList.size(); i++) {
                Field field = fieldList.get(i);
                if (flag) {
                    field.setFieldName(field.getFieldName().toLowerCase());
                }
                Integer seq = i + 1;
                // 初始化长度
                GraphTableUtils.initSynLength(field);
                if (StringUtils.isNotEmpty(field.getFieldDesc()) && field.getFieldDesc().contains("\'")) {
                    field.setFieldDesc(field.getFieldDesc().replace("\'", "\\'"));
                }
                dataEntityStructureList.add(new DataEntityStructure(IdWorker.getId(), dataEntity.getId(),
                        EntityInsertType.CUSTOMIZE, field.getFieldName(), field.getFieldDesc(), field.getBaseFieldType(),
                        field.getLength(), field.getDecimalLength(), field.getIsPk(), seq));
            }
            // 每50个提交一次
            List<List<DataEntityStructure>> dataEntityStructureSplitList = SplitUtils.splitList(dataEntityStructureList, 50);
            for (List<DataEntityStructure> dataEntityStructureSplit : dataEntityStructureSplitList) {
                // 自定义添加字段
                dataEntityStructureMapper.batchInsertStructure(dataEntityStructureSplit);
                // 创建关系
                dataEntityStructureMapper.batchCreateStructureEdge(dataEntity.getId(), dataEntityStructureSplit);
            }
            // 创建关联表
            DataEntityTable dataEntityTable = new DataEntityTable(IdWorker.getId(), dataEntity.getId(), sinkDataSourceId, sinkDataBaseName, entityCode, false, CreateTableWay.AUTO, dataSourceId, databaseName, tableName, isView);
            dataEntityTableMapper.insertTable(dataEntityTable);
            // Doris默认创表结构
            if ("doris".equals(source.getType())) {
                List<String> pkList = dataEntityStructureList.stream().filter(DataEntityStructure::getIsPk).map(DataEntityStructure::getCode).collect(Collectors.toList());
                String keyType = "DUPLICATE_KEY";
                List<String> bucketKeys = new ArrayList<>();
                if (!pkList.isEmpty()) {
                    keyType = "UNIQUE_KEY";
                    bucketKeys = pkList;
                } else {
                    // 无主键实体的第一个列作为DUPLICATE_KEY
                    bucketKeys.add(dataEntityStructureList.get(0).getCode());
                }
                JSONObject indexDefinition = new JSONObject();
                JSONObject dorisConfig = getDorisConfig();
                int buckets = 3;
                if (dorisConfig != null) {
                    // 分桶数量配置
                    buckets = dorisConfig.get("buckets") == null ? 3 : dorisConfig.getInteger("buckets");
                    // properties Doris额外创表属性配置
                    if (dorisConfig.getJSONObject("BValue") != null) {
                        indexDefinition.put("properties", dorisConfig.getJSONObject("BValue"));
                    }
                }
                // 创建keysType内部的对象和数组
                JSONObject keysType = new JSONObject();
                keysType.put("keyType", keyType);
                keysType.put("keysFieldList", bucketKeys);
                // 创建distributed内部的对象和数组
                JSONObject distributed = new JSONObject();
                distributed.put("distributedType", "HASH");
                distributed.put("distributedFieldList", bucketKeys);
                distributed.put("buckets", buckets);
                // 添加其他属性
                indexDefinition.put("keysType", keysType);
                indexDefinition.put("distributed", distributed);

                dataEntityTable.setCreateTableConfig(indexDefinition.toJSONString());
            }
            String EntityCreateTime = DateUtils.getDate();
            logInfo += EntityCreateTime + " 实体同步完成\n";
            logInfo += EntityCreateTime + " 实体同步完成，开始自动创表，目标数据\n";
            dataEntityTableService.createTable(tenantId, dataEntity.getId(), dataEntityTable, true);
            // 获取新建表字段信息
            // 获取源表字段信息
            fieldsR = remoteJdbcService.getFieldsByTenantId(tenantId, dataEntityTable.getDataSourceId(), dataEntityTable.getDatabaseName(), dataEntityTable.getTableName());
            if (fieldsR.getCode() != Status.SUCCESS.getCode()) {
                throw new ServiceException(fieldsR.getMsg());
            }
            List<Field> fieldListSink = fieldsR.getData();
            // 获取实体字段结构
            List<DataEntityStructureVo> dataEntityStructureVos = dataEntityStructureService.selectStructureById(dataEntity.getId(), true);
            // 不区分大小写同名匹配数据库字段列关系
            List<DataEntityTableMapping> dataEntityTableMappingList = fieldListSink.stream().map(field -> {
                        for (DataEntityStructureVo dataEntityStructureVo : dataEntityStructureVos) {
                            if (field.getFieldName().equalsIgnoreCase(dataEntityStructureVo.getCode())) {
                                return new DataEntityTableMapping(dataEntity.getId(), dataEntityTable.getId(), dataEntityStructureVo.getId(), EntityInsertType.CUSTOMIZE, null, field.getFieldName());
                            }
                        }
                        return null;
                    }).
                    filter(map -> map != null)
                    .collect(Collectors.toList());
            dataEntityTable.setDataEntityTableMappingList(dataEntityTableMappingList);
            // 保存实体字段与实体数据库表字段的映射关系
            dataEntityTableService.saveMappingOnSyncAndCreatTable(tenantId, dataEntityTable);
            // 绑定实体和关联表
            dataEntityMapper.updateById(new DataEntity(dataEntity.getId(), dataEntityTable.getId()));
            updateLogInfo(globalLogId, logInfo);
            return CompletableFuture.completedFuture(null);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }


    /**
     * 获取 Doris 配置
     *
     * @return Doris 配置
     */
    private JSONObject getDorisConfig() {
        R<Object> r = remoteParamService.selectValueByCode("DORIS_PARAMS");
        if (r.getCode() == Status.SUCCESS.getCode()) {
            return JSON.parseObject((String) r.getData());
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int insertSynLog(DataEntitySynLog dataEntitySynLog) {
        try {
            // 存在图中的特殊字符需要转义
            if (StringUtils.isNotEmpty(dataEntitySynLog.getError())) {
                String error = dataEntitySynLog.getError();
                if (error.contains("\\")) {
                    error = error.replace("\\", "\\\\");
                }
                if (error.contains("'")) {
                    error = error.replace("'", "\\\'");
                }
                if (error.contains("$")) {
                    error = error.replace("$", "\\\\$");
                }
                dataEntitySynLog.setError(error);
            }
            // 插入日志
            dataEntitytSynLogMapper.insertSynLog(dataEntitySynLog);
            // 创建实体菜单和日志关系
            dataEntitytSynLogMapper.insertMenuSynLog(dataEntitySynLog.getPid(), dataEntitySynLog.getId());
            // 创建数据源和日志关系
            dataEntitytSynLogMapper.insertDataSourceSynLog(dataEntitySynLog.getDataSourceId(), dataEntitySynLog.getId());
        } catch (Exception e) {
            logger.error("插入同步日志失败 {}", e.getMessage());
        }
        return 1;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public int updateSynLog(DataEntitySynLog dataEntitySynLog) {
        // 存在图中的特殊字符需要转义
        if (StringUtils.isNotEmpty(dataEntitySynLog.getError())) {
            if (dataEntitySynLog.getError().contains("\\")) {
                dataEntitySynLog.setError(dataEntitySynLog.getError().replace("\\", "\\\\"));
            }
            if (dataEntitySynLog.getError().contains("'")) {
                dataEntitySynLog.setError(dataEntitySynLog.getError().replace("'", "\\\'"));
            }
            if (dataEntitySynLog.getError().contains("$")) {
                dataEntitySynLog.setError(dataEntitySynLog.getError().replace("$", "\\\\$"));
            }
        }
        return dataEntitytSynLogMapper.updateById(dataEntitySynLog);
    }

    @Override
    public PageDataInfo<DataEntitySynLog> getSynLog(String Code, String status) {
        // 获取分页参数
        Page<DataEntitySynLog> page = PageUtils.getPage(DataEntitySynLog.class);
        // 查询数据
        IPage<DataEntitySynLog> dataEntitySynLogIPage = dataEntitytSynLogMapper.selectList(page, Code, status);
        return PageUtils.getPageInfo(dataEntitySynLogIPage.getRecords(), (int) dataEntitySynLogIPage.getTotal());
    }

    @Override
    public void synImportJson(DataJsonEntityVo dataJsonEntityVo) {
        // 创建全局开始日志
        DataEntitySynLog globalLog = createGlobalImportLog(dataJsonEntityVo);
        Long globalLogId = globalLog.getId();

        // 获取开始日志信息
        String logInfo = globalLog.getError() + "\n";

        try {
            // 1. 验证数据源并获取导入模式
            ImportMode importMode = validateDataSourcesAndGetMode(dataJsonEntityVo);
            logInfo += String.format("[%s] 验证数据源成功，模式：%s\n", DateUtils.getTime(), importMode.name());
            updateLogInfo(globalLogId, logInfo);
            // 2. 根据模式处理实体导入
            processEntityImport(dataJsonEntityVo, importMode, globalLogId, logInfo);
            logInfo += String.format("[%s] 导入完成\n", DateUtils.getTime());
            updateGlobalImportLogSuccess(globalLogId, logInfo);
        } catch (Exception e) {
            logInfo += String.format("[%s] 导入失败：%s\n", DateUtils.getTime(), e.getMessage());
            logImportFailure(e, globalLogId, logInfo);
            logger.error("实体导入失败， {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 创建全局导入日志
     *
     * @param dataJsonEntityVo 数据实体JSON
     * @return 全局日志对象
     */
    private DataEntitySynLog createGlobalImportLog(DataJsonEntityVo dataJsonEntityVo) {
        try {
            DataEntityTable dataEntityTable = dataJsonEntityVo.getDataEntityTable();
            DataEntityMenu menuInfo = dataJsonEntityVo.getMenuInfo();

            DataEntitySynLog globalLog = new DataEntitySynLog();
            Long globalLogId = IdWorker.getId();
            globalLog.setId(globalLogId);
            globalLog.setPid(menuInfo.getId());
            globalLog.setEntityCode(
                    dataEntityTable.getDataSourceId() != null && dataEntityTable.getSyncFromDataSourceId() != null
                            ? dataEntityTable.getTableName()
                            : dataJsonEntityVo.getDataEntity().getCode()
            );
            globalLog.setSynStatus(DataEntitySynStatus.IMPORT_RUNNING);
            globalLog.setTableName(dataEntityTable.getTableName());
            globalLog.setDataSourceId(dataEntityTable.getDataSourceId());
            globalLog.setDatabaseName(dataEntityTable.getDatabaseName());
            globalLog.setSinkDataSourceId(String.valueOf(dataEntityTable.getSyncFromDataSourceId()));
            globalLog.setSinkDataBaseName(dataEntityTable.getSyncFromDataBaseName());
            globalLog.setUseCreateTable(true);

            String currentTime = DateUtils.getTime();
            String startMessage = String.format("[%s] 开始导入实体【%s】，菜单：%s，表名：%s，来源数据源ID：%s，来源库：%s，目标数据源ID：%s，目标库：%s",
                    currentTime, dataJsonEntityVo.getDataEntity().getCode(), dataJsonEntityVo.getMenuInfo().getName(), dataEntityTable.getTableName(),
                    dataEntityTable.getSyncFromDataSourceId(), dataEntityTable.getSyncFromDataBaseName(),
                    dataEntityTable.getDataSourceId(), dataEntityTable.getDatabaseName());

            globalLog.setError(startMessage);
            this.insertSynLog(globalLog);

            logger.info("创建导入日志成功，日志ID：{}", globalLogId);
            return globalLog;
        } catch (Exception e) {
            logger.error("创建全局导入日志失败：{}", e.getMessage(), e);
            throw new ServiceException("创建导入日志失败：" + e.getMessage());
        }
    }

    /**
     * 更新全局导入日志为成功状态
     *
     * @param globalLogId 全局日志ID
     */
    private void updateGlobalImportLogSuccess(Long globalLogId, String logInfo) {
        try {
            DataEntitySynLog globalLog = new DataEntitySynLog();
            globalLog.setId(globalLogId);
            globalLog.setError(logInfo);
            globalLog.setSynStatus(DataEntitySynStatus.IMPORT_SUCCESS);
            this.updateSynLog(globalLog);

            logger.info("导入日志成功，日志ID：{}", globalLogId);
        } catch (Exception e) {
            logger.error("更新导入日志失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 验证数据源是否存在并返回导入模式
     *
     * @param dataJsonEntityVo 数据实体JSON
     * @return 导入模式：SYNC(同步模式) 或 CREATE_TABLE(创表模式)
     */
    private ImportMode validateDataSourcesAndGetMode(DataJsonEntityVo dataJsonEntityVo) {
        createSingleMenuFromJson(dataJsonEntityVo.getMenuInfo());

        DataEntityTable dataEntityTable = dataJsonEntityVo.getDataEntityTable();
        if (dataEntityTable == null) {
            throw new ServiceException("缺少实体表信息");
        }

        Long dataSourceId = dataEntityTable.getDataSourceId();
        Long sinkDataSourceId = dataEntityTable.getSyncFromDataSourceId();
        String databaseName = dataEntityTable.getDatabaseName();
        String sinkDataBaseName = dataEntityTable.getSyncFromDataBaseName();

        boolean hasSourceData = false;
        boolean hasSinkData = false;

        // 检查来源数据源
        if (dataSourceId != null) {
            VlabelItem<DataSource> dataSourceVlabelItem = dataSourceMapper.selectById(dataSourceId);
            if (dataSourceVlabelItem != null && databaseName != null) {
                R<PageDataInfo> databases = remoteJdbcService.getDatabases(dataSourceId, databaseName, null, null);
                if (databases.getData() != null) {
                    hasSourceData = true;
                }
            }
        }

        // 检查目标数据源
        if (sinkDataSourceId != null) {
            VlabelItem<DataSource> dataSinkSourceVlabelItem = dataSourceMapper.selectById(sinkDataSourceId);
            if (dataSinkSourceVlabelItem != null && sinkDataBaseName != null) {
                R<PageDataInfo> sinkDatabases = remoteJdbcService.getDatabases(sinkDataSourceId, sinkDataBaseName, null, null);
                if (sinkDatabases.getData() != null) {
                    hasSinkData = true;
                }
            }
        }

        // 根据数据源情况决定导入模式
        if (hasSourceData && hasSinkData) {
            return ImportMode.SYNC;
        } else if (hasSourceData) {
            return ImportMode.CREATE_TABLE;
        } else {
            throw new ServiceException("实体数据源不存在，请先导入实体对应的数据源信息");
        }
    }

    /**
     * 处理实体导入
     *
     * @param dataJsonEntityVo 数据实体JSON
     * @param importMode       导入模式
     * @param globalLogId      全局日志ID
     */
    @Transactional
    public void processEntityImport(DataJsonEntityVo dataJsonEntityVo, ImportMode importMode, Long globalLogId, String logInfo) {
        DataEntityTable dataEntityTable = dataJsonEntityVo.getDataEntityTable();
        if (dataEntityTable == null) {
            throw new ServiceException("缺少实体表信息");
        }

        // 处理表名列表导入（同步模式）
        List<DataEntityTableNameSynVo> tableNameList = dataEntityTable.getTableNameList();

        if (importMode == ImportMode.SYNC) {
            if (tableNameList == null || tableNameList.isEmpty()) {
                throw new ServiceException("同步模式下表名列表不能为空");
            }
            processBatchTableImport(dataJsonEntityVo, globalLogId, logInfo);
        }
        // 处理实体表导入（创表模式）
        else {
            processEntityTableImport(dataJsonEntityVo, globalLogId, logInfo);
        }
    }

    /**
     * 创建菜单
     *
     * @param menuInfo 菜单信息
     */
    private void createSingleMenuFromJson(DataEntityMenu menuInfo) {
        try {
            if (menuInfo == null) {
                throw new ServiceException("菜单信息不能为空");
            }
            // 检查菜单是否已存在
            DataEntityMenu existingMenu = dataEntityMenuMapper.selectById(menuInfo.getId());
            if (existingMenu == null) {
                // 菜单不存在，创建新菜单
                dataEntityMenuService.create(menuInfo);
                logger.info("创建菜单成功: code={}, name={}", menuInfo.getCode(), menuInfo.getName());
            } else {
                // 菜单已存在，更新菜单信息
                dataEntityMenuService.update(menuInfo);
                logger.info("更新菜单成功: code={}, name={}", menuInfo.getCode(), menuInfo.getName());
            }
        } catch (Exception e) {
            logger.error("创建/更新菜单失败: code={}, error={}", menuInfo.getCode(), e.getMessage(), e);
        }
    }

    /**
     * 处理单个实体表导入（创表模式专用）
     *
     * @param dataJsonEntityVo 数据实体JSON
     * @param globalLogId      全局日志ID
     */
    private void processEntityTableImport(DataJsonEntityVo dataJsonEntityVo, Long globalLogId, String logInfo) {
        try {
            // 1. 创建实体
            createEntitiesFromJson(dataJsonEntityVo);
            logInfo += String.format("[%s] 创建实体成功\n", DateUtils.getTime());
            updateLogInfo(globalLogId, logInfo);

            // 2. 创建实体结构（自定义列）
            createEntityStructuresFromJson(dataJsonEntityVo);
            logInfo += String.format("[%s] 创建字段成功\n", DateUtils.getTime());
            updateLogInfo(globalLogId, logInfo);

            // 3. 最后为实体创建表
            createTableFromJson(dataJsonEntityVo, globalLogId);
            logInfo += String.format("[%s] 创建表成功\n", DateUtils.getTime());
            updateLogInfo(globalLogId, logInfo);

        } catch (Exception e) {
            logInfo += String.format("[%s] 创表失败：%s\n", DateUtils.getTime(), e.getMessage());
            updateLogInfo(globalLogId, logInfo);
            throw e;
        }
    }

    /**
     * 处理批量表导入（同步模式专用）
     *
     * @param dataJsonEntityVo 数据实体JSON
     * @param globalLogId      全局日志ID
     */
    private void processBatchTableImport(DataJsonEntityVo dataJsonEntityVo, Long globalLogId, String logInfo) {
        DataEntityTable dataEntityTable = dataJsonEntityVo.getDataEntityTable();
        List<DataEntityTableNameSynVo> tableNameList = dataEntityTable.getTableNameList();
        logInfo += String.format("[%s] 开始同步导入 ：", DateUtils.getTime());
        updateLogInfo(globalLogId, logInfo);
        if (tableNameList == null || tableNameList.isEmpty()) {
            logger.warn("表名列表为空，跳过处理");
            return;
        }
        logger.info("开始同步导入，数量：{}", tableNameList.size());
        for (DataEntityTableNameSynVo dataEntityTableNameSynVo : tableNameList) {
            Long pid = dataJsonEntityVo.getMenuInfo().getId();
            Long dataSourceId = dataEntityTable.getSyncFromDataSourceId();
            String databaseName = dataEntityTable.getSyncFromDataBaseName();
            Boolean isView = dataEntityTable.getSyncIsView();
            String tableName = dataEntityTable.getSyncFromTableName();
            String entityCode = dataEntityTableNameSynVo.getEntityCode();
            EntityType entityType = dataEntityTableNameSynVo.getEntityType();

            try {
                Long tenantId = SecurityUtils.getLoginUser().getTenantId();
                Long sinkDataSourceId = dataEntityTable.getDataSourceId();
                String sinkDatabaseName = dataEntityTable.getDatabaseName();
                // 执行异步导入
                CompletableFuture<Void> future = buildDataEntityAndCreateTableAsync(globalLogId, logInfo, pid, entityCode, tenantId, dataSourceId, databaseName, tableName, isView, sinkDataSourceId, sinkDatabaseName, entityType, dataEntityTableNameSynVo.getTableDesc());
                future.get();
            } catch (Exception e) {
                logger.error("表导入失败: tableName={}, error={}", dataEntityTableNameSynVo.getTableName(), e.getMessage(), e);
                throw new ServiceException(String.format("表 %s 导入失败: %s", tableName, e.getMessage()));
            }
        }

        logger.info("同步导入完成，总处理数量：{}", tableNameList.size());
    }


    /**
     * 从JSON数据创建实体
     *
     * @param dataJsonEntityVo 数据实体JSON
     */
    private void createEntitiesFromJson(DataJsonEntityVo dataJsonEntityVo) {
        DataEntity dataEntity = dataJsonEntityVo.getDataEntity();
        if (dataEntity == null) {
            throw new ServiceException("请添加实体信息，实体不存在");
        }

        try {
            // 检查实体是否已存在
            DataEntity existingEntity = dataEntityMapper.selectById(dataEntity.getId());
            if (existingEntity == null) {
                if (dataEntity.getId() == null) {
                    dataEntity.setId(IdWorker.getId());
                }
                dataEntityService.create(dataEntity);
            } else {
                dataEntityService.update(dataEntity);
            }
        } catch (Exception e) {
            throw new ServiceException("创建/更新实体失败: " + e.getMessage());
        }
    }

    /**
     * 从JSON数据创建表
     *
     * @param dataJsonEntityVo 数据实体JSON
     * @param globalLogId      全局日志ID
     */
    private void createTableFromJson(DataJsonEntityVo dataJsonEntityVo, Long globalLogId) {
        DataEntityTable dataEntityTable = dataJsonEntityVo.getDataEntityTable();
        try {
            Long dataEntityId = dataJsonEntityVo.getDataEntity().getId();
            Long tenantId = SecurityUtils.getLoginUser().getTenantId();
            List<DataEntityStructure> dataEntityStructureList = dataJsonEntityVo.getDataEntityStructureList();
            if (dataEntityId == null) {
                throw new ServiceException("缺少实体ID");
            }
            // Doris默认创表结构
            R<DataSource> sourceT = remoteDriveService.queryDataSourceByTenantId(tenantId, dataEntityTable.getDataSourceId());
            DataSource source = sourceT.getData();
            if ("doris".equals(source.getType())) {
                List<String> pkList = dataEntityStructureList.stream().filter(DataEntityStructure::getIsPk).map(DataEntityStructure::getCode).collect(Collectors.toList());
                String keyType = "DUPLICATE_KEY";
                List<String> bucketKeys = new ArrayList<>();
                if (!pkList.isEmpty()) {
                    keyType = "UNIQUE_KEY";
                    bucketKeys = pkList;
                } else {
                    // 无主键实体的第一个列作为DUPLICATE_KEY
                    bucketKeys.add(dataEntityStructureList.get(0).getCode());
                }
                JSONObject indexDefinition = new JSONObject();
                JSONObject dorisConfig = getDorisConfig();
                int buckets = 3;
                if (dorisConfig != null) {
                    // 分桶数量配置
                    buckets = dorisConfig.get("buckets") == null ? 3 : dorisConfig.getInteger("buckets");
                    // properties Doris额外创表属性配置
                    if (dorisConfig.getJSONObject("BValue") != null) {
                        indexDefinition.put("properties", dorisConfig.getJSONObject("BValue"));
                    }
                }
                // 创建keysType内部的对象和数组
                JSONObject keysType = new JSONObject();
                keysType.put("keyType", keyType);
                keysType.put("keysFieldList", bucketKeys);
                // 创建distributed内部的对象和数组
                JSONObject distributed = new JSONObject();
                distributed.put("distributedType", "HASH");
                distributed.put("distributedFieldList", bucketKeys);
                distributed.put("buckets", buckets);
                // 添加其他属性
                indexDefinition.put("keysType", keysType);
                indexDefinition.put("distributed", distributed);

                dataEntityTable.setCreateTableConfig(indexDefinition.toJSONString());
            }
            dataEntityTableService.createTable(tenantId, dataEntityId, dataEntityTable, true);
        } catch (Exception e) {
            throw new ServiceException("创建表失败: " + e.getMessage());
        }
    }

    /**
     * 从JSON数据创建实体结构（自定义列）
     *
     * @param dataJsonEntityVo 数据实体JSON
     */
    private void createEntityStructuresFromJson(DataJsonEntityVo dataJsonEntityVo) {
        List<DataEntityStructure> dataEntityStructureList = dataJsonEntityVo.getDataEntityStructureList();
        if (dataEntityStructureList == null || dataEntityStructureList.isEmpty()) {
            return;
        }

        Long dataEntityId = dataJsonEntityVo.getDataEntity().getId();

        try {
            // 删除现有列
            dataEntityStructureService.deleteByEntityIdAndInsertType(dataEntityId);

            // 字段去重处理
            Map<String, DataEntityStructure> fieldMap = new LinkedHashMap<>();
            for (DataEntityStructure structure : dataEntityStructureList) {
                if (structure != null && StringUtils.isNotEmpty(structure.getCode())
                        && dataEntityId.equals(structure.getDataEntityId())) {
                    fieldMap.putIfAbsent(structure.getCode().toLowerCase(), structure);
                }
            }

            List<DataEntityStructure> structureList = new ArrayList<>(fieldMap.values());

            // 设置ID和基本信息
            for (DataEntityStructure structure : structureList) {
                if (structure.getId() == null) {
                    structure.setId(IdWorker.getId());
                }
                if (structure.getEntityInsertType() == null) {
                    structure.setEntityInsertType(EntityInsertType.CUSTOMIZE);
                }
                structure.setDataEntityId(dataEntityId);
            }

            // 批量插入
            List<List<DataEntityStructure>> dataEntityStructureSplitList = SplitUtils.splitList(structureList, 50);
            for (List<DataEntityStructure> dataEntityStructureSplit : dataEntityStructureSplitList) {
                dataEntityStructureMapper.batchInsertStructure(dataEntityStructureSplit);
                dataEntityStructureMapper.batchCreateStructureEdge(dataEntityId, dataEntityStructureSplit);
            }

        } catch (Exception e) {
            throw new ServiceException("创建实体结构失败: " + e.getMessage());
        }
    }

    /**
     * 更新日志信息的辅助方法
     *
     * @param globalLogId 全局日志ID
     * @param logInfo     日志信息
     */
    private void updateLogInfo(Long globalLogId, String logInfo) {
        try {
            DataEntitySynLog globalLog = new DataEntitySynLog();
            globalLog.setId(globalLogId);
            globalLog.setError(logInfo);
            this.updateSynLog(globalLog);
        } catch (Exception e) {
            logger.error("更新日志信息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 记录导入失败日志的统一方法
     *
     * @param exception   异常信息
     * @param globalLogId 全局日志ID
     * @param logInfo     已有日志信息
     */
    private void logImportFailure(Exception exception, Long globalLogId, String logInfo) {
        try {
            // 更新全局日志为失败状态
            DataEntitySynLog globalLog = new DataEntitySynLog();
            globalLog.setId(globalLogId);
            globalLog.setSynStatus(DataEntitySynStatus.IMPORT_FAIL);
            globalLog.setError(logInfo);
            this.updateSynLog(globalLog);

            logger.info("导入失败，{}，错误：{}", globalLogId, exception.getMessage());

        } catch (Exception e) {
            logger.error("记录导入失败日志时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 记录导入失败日志的统一方法（兼容旧版本）
     *
     * @param exception   异常信息
     * @param globalLogId 全局日志ID
     */
    private void logImportFailure(Exception exception, Long globalLogId) {
        String errorMessage = String.format("[%s] 导入失败：%s", DateUtils.getTime(), exception.getMessage());
        logImportFailure(exception, globalLogId, errorMessage);
    }


    /**
     * 将列表分割成指定大小的子列表
     *
     * @param list      原始列表
     * @param batchSize 批次大小
     * @return 分割后的子列表集合
     */
    private <T> List<List<T>> splitList(List<T> list, int batchSize) {
        List<List<T>> result = new ArrayList<>();
        int size = list.size();
        for (int i = 0; i < size; i += batchSize) {
            int end = Math.min(i + batchSize, size);
            result.add(list.subList(i, end));
        }
        return result;
    }

    @Override
    public void batchImportWithConcurrencyControl(List<DataJsonEntityVo> dataJsonEntityList) {
        if (dataJsonEntityList == null || dataJsonEntityList.isEmpty()) {
            logger.warn("导入列表为空，跳过处理");
            return;
        }

        logger.info("开始批量导入，总数量：{}", dataJsonEntityList.size());

        // 分批处理，每批20个
        int batchSize = 20;
        List<List<DataJsonEntityVo>> batches = splitList(dataJsonEntityList, batchSize);

        for (int i = 0; i < batches.size(); i++) {
            List<DataJsonEntityVo> batch = batches.get(i);
            logger.info("处理第 {}/{} 批，数量：{}", i + 1, batches.size(), batch.size());

            // 使用CountDownLatch等待当前批次完成
            CountDownLatch latch = new CountDownLatch(batch.size());

            // 提交当前批次任务到线程池
            for (DataJsonEntityVo entityVo : batch) {
                entityImportExecutor.execute(() -> {
                    try {
                        synImportJson(entityVo);
                    } catch (Exception e) {
                        logger.error("实体导入失败: {}", e.getMessage(), e);
                    } finally {
                        latch.countDown();
                    }
                });
            }

            // 等待当前批次完成
            try {
                latch.await(10, TimeUnit.MINUTES); // 10分钟超时
                logger.info("第 {}/{} 批处理完成", i + 1, batches.size());
            } catch (InterruptedException e) {
                logger.error("批次处理被中断: {}", e.getMessage());
                Thread.currentThread().interrupt();
                break;
            }

            // 批次间等待2秒，避免系统压力过大
            if (i < batches.size() - 1) {
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        logger.info("批量导入完成，总处理数量：{}", dataJsonEntityList.size());
    }

}


