package com.datalink.fdop.element.utils;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.datalink.fdop.common.core.utils.DateUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.element.api.domain.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/5/9 21:28
 */
public class DomainAgeUtils {

    public static String getDataElementMenuAgeStr(DataElementMenu dataElementMenu) {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{")
                .append(" id: " + (dataElementMenu.getId() != null ? dataElementMenu.getId() : IdWorker.getId()))
                .append(", pid: " + (dataElementMenu.getPid() != null ? dataElementMenu.getPid() : -1))
                .append((StringUtils.isNotEmpty(dataElementMenu.getCode()) ? ", code: '" + dataElementMenu.getCode() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataElementMenu.getName()) ? ", name: '" + dataElementMenu.getName() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataElementMenu.getDescription()) ? ", description: '" + dataElementMenu.getDescription() + "'" : ""))
                .append(", createBy: '" + (StringUtils.isNotEmpty(dataElementMenu.getCreateBy()) ? dataElementMenu.getCreateBy() : SecurityUtils.getUsername()) + "'")
                .append(", createTime: '" + (dataElementMenu.getCreateTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(dataElementMenu.getCreateTime())) + "'")
                .append(" }");
        return ageString.toString();
    }

    public static String getDataElementAgeStr(DataElement dataElement) {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{")
                .append(" id: " + (dataElement.getId() != null ? dataElement.getId() : IdWorker.getId()))
                .append(", pid: " + (dataElement.getPid() != null ? dataElement.getPid() : -1))
                .append((StringUtils.isNotEmpty(dataElement.getCode()) ? ", code: '" + dataElement.getCode() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataElement.getName()) ? ", name: '" + dataElement.getName() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataElement.getDescription()) ? ", description: '" + dataElement.getDescription() + "'" : ""))
                .append((dataElement.getDataElementType() != null ? ", dataElementType: '" + dataElement.getDataElementType() + "'" : ""))
                .append((dataElement.getElementSourceType() != null ? ", elementSourceType: '" + dataElement.getElementSourceType() + "'" : ""))
                .append((dataElement.getFieldType() != null ? ", fieldType: '" + dataElement.getFieldType() + "'" : ""))
                .append((dataElement.getLength() != null ? ", length: " + dataElement.getLength() : ""))
                .append((dataElement.getDecimalLength() != null ? ", decimalLength: " + dataElement.getDecimalLength() : ""))
                .append((dataElement.getIsPk() != null ? ", isPk: " + dataElement.getIsPk() : ""))
                .append(", mapTableName: 'table_" + IdWorker.getId() + "'")
                .append(", mapFieldName: 'field_" + IdWorker.getId() + "'")
                .append(", createBy: '" + (StringUtils.isNotEmpty(dataElement.getCreateBy()) ? dataElement.getCreateBy() : SecurityUtils.getUsername()) + "'")
                .append(", createTime: '" + (dataElement.getCreateTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(dataElement.getCreateTime())) + "'")
                .append(" }");
        return ageString.toString();
    }

    public static String getStructureAgeStr(DataElementStructure dataElementStructure) {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{")
                .append(" id: " + (dataElementStructure.getId() != null ? dataElementStructure.getId() : IdWorker.getId()))
                .append((StringUtils.isNotEmpty(dataElementStructure.getCode()) ? ", code: '" + dataElementStructure.getCode() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataElementStructure.getName()) ? ", name: '" + dataElementStructure.getName() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataElementStructure.getDescription()) ? ", description: '" + dataElementStructure.getDescription() + "'" : ""))
                .append((dataElementStructure.getDataElementType() != null ? ", dataElementType: '" + dataElementStructure.getDataElementType() + "'" : ""))
                .append((dataElementStructure.getFieldType() != null ? ", fieldType: '" + dataElementStructure.getFieldType() + "'" : ""))
                .append((dataElementStructure.getLength() != null ? ", length: " + dataElementStructure.getLength() : ""))
                .append((dataElementStructure.getDecimalLength() != null ? ", decimalLength: " + dataElementStructure.getDecimalLength() : ""))
                .append((dataElementStructure.getIsPk() != null ? ", isPk: " + dataElementStructure.getIsPk() : ""))
                .append((StringUtils.isNotEmpty(dataElementStructure.getMapFieldName()) ? ", mapFieldName: 'field_" + IdWorker.getId() + "'" : ""))
                .append(", createBy: '" + (StringUtils.isNotEmpty(dataElementStructure.getCreateBy()) ? dataElementStructure.getCreateBy() : SecurityUtils.getUsername()) + "'")
                .append(", createTime: '" + (dataElementStructure.getCreateTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(dataElementStructure.getCreateTime())) + "'")
                .append(" }");
        return ageString.toString();
    }

    public static String getDataEntityMenuAgeStr(DataEntityMenu dataEntityMenu) {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{")
                .append(" id: " + (dataEntityMenu.getId() != null ? dataEntityMenu.getId() : IdWorker.getId()))
                .append(", pid: " + (dataEntityMenu.getPid() != null ? dataEntityMenu.getPid() : -1))
                .append((StringUtils.isNotEmpty(dataEntityMenu.getCode()) ? ", code: '" + dataEntityMenu.getCode() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataEntityMenu.getName()) ? ", name: '" + dataEntityMenu.getName() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataEntityMenu.getDescription()) ? ", description: '" + dataEntityMenu.getDescription() + "'" : ""))
                .append(dataEntityMenu.getEntityType() != null ? ", entityType: '" + dataEntityMenu.getEntityType() + "'" : "")
                .append(", createBy: '" + (StringUtils.isNotEmpty(dataEntityMenu.getCreateBy()) ? dataEntityMenu.getCreateBy() : SecurityUtils.getUsername()) + "'")
                .append(", createTime: '" + (dataEntityMenu.getCreateTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(dataEntityMenu.getCreateTime())) + "'")
                .append(", serialNumber: " +  dataEntityMenu.getSerialNumber())
                .append(" }");
        return ageString.toString();
    }

    public static String getDataEntityAgeStr(DataEntity dataEntity) {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{")
                .append(" id: " + (dataEntity.getId() != null ? dataEntity.getId() : IdWorker.getId()))
                .append(", pid: " + (dataEntity.getPid() != null ? dataEntity.getPid() : -1))
                .append((StringUtils.isNotEmpty(dataEntity.getCode()) ? ", code: '" + dataEntity.getCode() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataEntity.getName()) ? ", name: '" + dataEntity.getName() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataEntity.getDescription()) ? ", description: '" + dataEntity.getDescription() + "'" : ""))
                .append(dataEntity.getEntityType() != null ? ", entityType: '" + dataEntity.getEntityType() + "'" : "")
                .append(", createBy: '" + (StringUtils.isNotEmpty(dataEntity.getCreateBy()) ? dataEntity.getCreateBy() : SecurityUtils.getUsername()) + "'")
                .append(", createTime: '" + (dataEntity.getCreateTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(dataEntity.getCreateTime())) + "'")
                .append(" }");
        return ageString.toString();
    }

    public static String getDataEntitySynLogAgeStr(DataEntitySynLog dataEntitySynLog) {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{")
                .append(" id: " + (dataEntitySynLog.getId() != null ? dataEntitySynLog.getId() : IdWorker.getId()))
                .append(", pid: " + dataEntitySynLog.getPid())
                .append(", dataSourceId: " + dataEntitySynLog.getDataSourceId())
                .append(", databaseName: '" + dataEntitySynLog.getDatabaseName() + "'")
                .append((StringUtils.isNotEmpty(dataEntitySynLog.getTableName()) ? ", tableName: '" + dataEntitySynLog.getTableName() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataEntitySynLog.getViewName()) ? ", viewName: '" + dataEntitySynLog.getViewName() + "'" : ""))
                .append(", entityCode: '" + dataEntitySynLog.getEntityCode() + "'")
                .append(", synStatus: '" + dataEntitySynLog.getSynStatus() + "'")
                .append(dataEntitySynLog.getEntityType() != null ? ", entityType: '" + dataEntitySynLog.getEntityType() + "'" : "")
                .append((StringUtils.isNotEmpty(dataEntitySynLog.getError()) ? ", error: '" + dataEntitySynLog.getError() + "'" : ""))
                .append(", sinkDataSourceId: '" + dataEntitySynLog.getSinkDataSourceId() + "'")
                .append(", sinkDataBaseName: '" + dataEntitySynLog.getSinkDataBaseName() + "'")
                .append(", useCreateTable: " + (dataEntitySynLog.getUseCreateTable() != null ? dataEntitySynLog.getUseCreateTable() : false))
                .append(", createBy: '" + (StringUtils.isNotEmpty(dataEntitySynLog.getCreateBy()) ? dataEntitySynLog.getCreateBy() : SecurityUtils.getUsername()) + "'")
                .append(", createTime: '" + (dataEntitySynLog.getCreateTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(dataEntitySynLog.getCreateTime())) + "'")
                .append(" }");
        return ageString.toString();
    }

    public static String getEntityStructureAgeStr(DataEntityStructure dataEntityStructure) {
        StringBuffer ageString = new StringBuffer();
        String name = dataEntityStructure.getName();
        // 处理从源数据库的注释中同步过来的特殊字符导致报错无法新增实体表结构信息的问题
        if (name != null){
            name = name.replace("\\","\\\\").replace("'", "\\\'").replace("$", "\\\\$");
        }
        String description = dataEntityStructure.getDescription();
        if (description != null) {
            description = description.replace("\\","\\\\").replace("'", "\\\'").replace("$", "\\\\$");
        }
        ageString.append("{")
                .append(" id: " + (dataEntityStructure.getId() != null ? dataEntityStructure.getId() : IdWorker.getId()))
                .append(", dataEntityId: " + (dataEntityStructure.getDataEntityId() != null ? dataEntityStructure.getDataEntityId() : -1L))
                .append((StringUtils.isNotEmpty(dataEntityStructure.getCode()) ? ", code: '" + dataEntityStructure.getCode() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataEntityStructure.getName()) ? ", name: '" + name + "'" : ""))
                .append((StringUtils.isNotEmpty(dataEntityStructure.getDescription()) ? ", description: '" + description + "'" : ""))
                .append((dataEntityStructure.getEntityInsertType() != null ? ", entityInsertType: '" + dataEntityStructure.getEntityInsertType() + "'" : ""))
                .append((dataEntityStructure.getFieldType() != null ? ", fieldType: '" + dataEntityStructure.getFieldType() + "'" : ""))
                .append((dataEntityStructure.getLength() != null ? ", length: " + dataEntityStructure.getLength() : ""))
                .append((dataEntityStructure.getDecimalLength() != null ? ", decimalLength: " + dataEntityStructure.getDecimalLength() : ""))
                .append((dataEntityStructure.getIsPk() != null ? ", isPk: " + dataEntityStructure.getIsPk() : ""))
                .append((dataEntityStructure.getSeq() != null ? ", seq: " + dataEntityStructure.getSeq() : 0))
                .append(", createBy: '" + (StringUtils.isNotEmpty(dataEntityStructure.getCreateBy()) ? dataEntityStructure.getCreateBy() : SecurityUtils.getUsername()) + "'")
                .append(", createTime: '" + (dataEntityStructure.getCreateTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(dataEntityStructure.getCreateTime())) + "'")
                .append(" }");
        return ageString.toString();
    }



    @ApiModelProperty(value = "同步数据源")
    private long syncFromDataSourceId;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "同步库名")
    private String syncFromDataBaseName;

    @ApiModelProperty(value = "同步表名")
    private String syncFromTableName;





    public static String getEntityTableAgeStr(DataEntityTable dataEntityTable) {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{")
                .append(" id: " + (dataEntityTable.getId() != null ? dataEntityTable.getId() : IdWorker.getId()))
                .append(", dataEntityId: " + dataEntityTable.getDataEntityId())
                .append((dataEntityTable.getDataSourceId() != null ? ", dataSourceId: " + dataEntityTable.getDataSourceId() : ""))
                .append((dataEntityTable.getSyncFromDataSourceId() != null ? ", syncFromDataSourceId: " + dataEntityTable.getSyncFromDataSourceId() : ""))
                .append((StringUtils.isNotEmpty(dataEntityTable.getDatabaseName()) ? ", databaseName: '" + dataEntityTable.getDatabaseName() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataEntityTable.getTableName()) ? ", tableName: '" + dataEntityTable.getTableName() + "'" : ""))
                .append((dataEntityTable.getIsRead() != null ? ", isRead: " + dataEntityTable.getIsRead() : ""))
                .append((dataEntityTable.getCreateTableWay() != null ? ", createTableWay: '" + dataEntityTable.getCreateTableWay() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataEntityTable.getCreateTablesql()) ? ", createTablesql: '" + dataEntityTable.getCreateTablesql() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataEntityTable.getCreateTableConfig()) ? ", createTableConfig: '" + dataEntityTable.getCreateTableConfig() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataEntityTable.getSyncFromDataBaseName()) ? ", syncFromDataBaseName: '" + dataEntityTable.getSyncFromDataBaseName() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataEntityTable.getSyncFromTableName()) ? ", syncFromTableName: '" + dataEntityTable.getSyncFromTableName() + "'" : ""))
                .append(", syncIsView: " + (dataEntityTable.getSyncIsView() != null ? dataEntityTable.getSyncIsView() : "false"))
                .append(", createBy: '" + (StringUtils.isNotEmpty(dataEntityTable.getCreateBy()) ? dataEntityTable.getCreateBy() : SecurityUtils.getUsername()) + "'")
                .append(", createTime: '" + (dataEntityTable.getCreateTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(dataEntityTable.getCreateTime())) + "'")
                .append(" }");
        return ageString.toString();
    }

    public static String getEntityTableMappingAgeStr(DataEntityTableMapping dataEntityTableMapping) {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{")
                .append("dataEntityId: " + dataEntityTableMapping.getDataEntityId())
                .append(",dataTableId: " + dataEntityTableMapping.getDataTableId())
                .append(",fieldId: " + dataEntityTableMapping.getFieldId())
                .append((dataEntityTableMapping.getEntityInsertType() != null ? ", entityInsertType: '" + dataEntityTableMapping.getEntityInsertType() + "'" : ""))
                .append((dataEntityTableMapping.getDataElementType() != null ? ", dataElementType: '" + dataEntityTableMapping.getDataElementType() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataEntityTableMapping.getFieldName()) ? ", fieldName: '" + dataEntityTableMapping.getFieldName() + "'" : ""))
                .append(" }");
        return ageString.toString();
    }

}
