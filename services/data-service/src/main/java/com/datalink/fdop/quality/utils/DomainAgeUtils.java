package com.datalink.fdop.quality.utils;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.datalink.fdop.common.core.utils.DateUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.quality.api.domain.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/8/18 9:31
 */
public class DomainAgeUtils {

    public static String getQualityMenuAgeStr(DataQualityMenu dataQualityMenu) {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{")
                .append(" id: " + (dataQualityMenu.getId() != null ? dataQualityMenu.getId() : IdWorker.getId()))
                .append(", pid: " + (dataQualityMenu.getPid() != null ? dataQualityMenu.getPid() : -1))
                .append((StringUtils.isNotEmpty(dataQualityMenu.getCode()) ? ", code: '" + dataQualityMenu.getCode() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataQualityMenu.getName()) ? ", name: '" + dataQualityMenu.getName() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataQualityMenu.getDescription()) ? ", description: '" + dataQualityMenu.getDescription() + "'" : ""))
                .append(", createBy: '" + (StringUtils.isNotEmpty(dataQualityMenu.getCreateBy()) ? dataQualityMenu.getCreateBy() : SecurityUtils.getUsername()) + "'")
                .append(", createTime: '" + (dataQualityMenu.getCreateTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(dataQualityMenu.getCreateTime())) + "'")
                .append(" }");
        return ageString.toString();
    }

    public static String getQualityAgeStr(DataQuality dataQuality) {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{")
                .append(" id: " + (dataQuality.getId() != null ? dataQuality.getId() : IdWorker.getId()))
                .append(", pid: " + (dataQuality.getPid() != null ? dataQuality.getPid() : -1))
                .append((StringUtils.isNotEmpty(dataQuality.getCode()) ? ", code: '" + dataQuality.getCode() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataQuality.getName()) ? ", name: '" + dataQuality.getName() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataQuality.getDescription()) ? ", description: '" + dataQuality.getDescription() + "'" : ""))
                .append((dataQuality.getDataQualityType() != null ? ", dataQualityType: '" + dataQuality.getDataQualityType() + "'" : ""))
                .append(", isCiteStandard: " + (dataQuality.getIsCiteStandard() != null ? dataQuality.getIsCiteStandard() : "false"))
                .append(", isConfigType: " + ((dataQuality.getIsCiteStandard() == null || !dataQuality.getIsCiteStandard()) && dataQuality.getIsConfigType() != null ? dataQuality.getIsConfigType() : "false"))
                .append(((dataQuality.getIsCiteStandard() == null || !dataQuality.getIsCiteStandard()) && dataQuality.getIsConfigType() != null && dataQuality.getIsConfigType() && dataQuality.getFieldType() != null ? ", fieldType: '" + dataQuality.getFieldType() + "'" : ""))
                .append(((dataQuality.getIsCiteStandard() == null || !dataQuality.getIsCiteStandard()) && dataQuality.getIsConfigType() != null && dataQuality.getIsConfigType() && dataQuality.getLength() != null ? ", length: " + dataQuality.getLength() : ""))
                .append(((dataQuality.getIsCiteStandard() == null || !dataQuality.getIsCiteStandard()) && dataQuality.getIsConfigType() != null && dataQuality.getIsConfigType() && dataQuality.getDecimalLength() != null ? ", decimalLength: " + dataQuality.getDecimalLength() : ""))
                .append((dataQuality.getRegexRule() != null ? ", regexRule: " + dataQuality.getRegexRule().getAgeStr() : ""))
                .append(", createBy: '" + (StringUtils.isNotEmpty(dataQuality.getCreateBy()) ? dataQuality.getCreateBy() : SecurityUtils.getUsername()) + "'")
                .append(", createTime: '" + (dataQuality.getCreateTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(dataQuality.getCreateTime())) + "'")
                .append(" }");
        return ageString.toString();
    }

    public static String getQualityLogAgeStr(DataQualityLog dataQualityLog) {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{")
                .append(" id: " + (dataQualityLog.getId() != null ? dataQualityLog.getId() : IdWorker.getId()))
                .append((dataQualityLog.getDataQualityLogStatus() != null ? ", dataQualityLogStatus: '" + dataQualityLog.getDataQualityLogStatus() + "'" : ""))
                .append((dataQualityLog.getProcessInstanceId() != null ? ", processInstanceId: " + dataQualityLog.getProcessInstanceId() : ""))
                .append((dataQualityLog.getTaskInstanceId() != null ? ", taskInstanceId: " + dataQualityLog.getTaskInstanceId() : ""))
                .append(", callTime: '" + (dataQualityLog.getCallTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(dataQualityLog.getCallTime())) + "'")
                .append((dataQualityLog.getStorageType() != null ? ", storageType: '" + dataQualityLog.getStorageType() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataQualityLog.getStorageName()) ? ", storageName: '" + dataQualityLog.getStorageName() + "'" : ""))
                .append((dataQualityLog.getInspectionType() != null ? ", inspectionType: '" + dataQualityLog.getInspectionType() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataQualityLog.getMonitorId()) ? ", monitorId: '" + dataQualityLog.getMonitorId() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataQualityLog.getErrorLog()) ? ", errorLog: '" + dataQualityLog.getErrorLog() + "'" : ""))
                .append((dataQualityLog.getErrorStatus()!=null ? ", errorStatus: " + dataQualityLog.getErrorStatus() : true))
                .append(" }");
        return ageString.toString();
    }

    public static String getDataQualityMonitorMenuAgeStr(DataQualityMonitorMenu dataQualityMonitorMenu) {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{")
                .append(" id: " + (dataQualityMonitorMenu.getId() != null ? dataQualityMonitorMenu.getId() : IdWorker.getId()))
                .append(", pid: " + (dataQualityMonitorMenu.getPid() != null ? dataQualityMonitorMenu.getPid() : -1))
                .append((StringUtils.isNotEmpty(dataQualityMonitorMenu.getCode()) ? ", code: '" + dataQualityMonitorMenu.getCode() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataQualityMonitorMenu.getName()) ? ", name: '" + dataQualityMonitorMenu.getName() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataQualityMonitorMenu.getDescription()) ? ", description: '" + dataQualityMonitorMenu.getDescription() + "'" : ""))
                .append(dataQualityMonitorMenu.getSerialNumber() != null ? ", serialNumber: " + dataQualityMonitorMenu.getSerialNumber() : "")
                .append(", createBy: '" + (StringUtils.isNotEmpty(dataQualityMonitorMenu.getCreateBy()) ? dataQualityMonitorMenu.getCreateBy() : SecurityUtils.getUsername()) + "'")
                .append(", createTime: '" + (dataQualityMonitorMenu.getCreateTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(dataQualityMonitorMenu.getCreateTime())) + "'")
                .append(" }");
        return ageString.toString();
    }

    public static String getDataQualityMonitorAgeStr(DataQualityMonitor dataQualityMonitor) {
        StringBuffer ageString = new StringBuffer();
        ageString.append("{")
                .append(" id: " + (dataQualityMonitor.getId() != null ? dataQualityMonitor.getId() : IdWorker.getId()))
                .append(", pid: " + (dataQualityMonitor.getPid() != null ? dataQualityMonitor.getPid() : -1))
                .append((StringUtils.isNotEmpty(dataQualityMonitor.getCode()) ? ", code: '" + dataQualityMonitor.getCode() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataQualityMonitor.getName()) ? ", name: '" + dataQualityMonitor.getName() + "'" : ""))
                .append((StringUtils.isNotEmpty(dataQualityMonitor.getDescription()) ? ", description: '" + dataQualityMonitor.getDescription() + "'" : ""))
                .append(dataQualityMonitor.getSerialNumber() != null ? ", serialNumber: " + dataQualityMonitor.getSerialNumber() : "")
                .append(", createBy: '" + (StringUtils.isNotEmpty(dataQualityMonitor.getCreateBy()) ? dataQualityMonitor.getCreateBy() : SecurityUtils.getUsername()) + "'")
                .append(", createTime: '" + (dataQualityMonitor.getCreateTime() == null ? DateUtils.getTime(new Date()) : DateUtils.getTime(dataQualityMonitor.getCreateTime())) + "'")
                .append(" }");
        return ageString.toString();
    }

}
