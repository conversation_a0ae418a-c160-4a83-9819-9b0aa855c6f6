package com.datalink.fdop.govern.version;

import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.govern.api.domain.DataStandardTree;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

public class VersionHandle {






    public static List<DataStandardTree> standardTreesSort(List<DataStandardTree> standardTrees ){
        List<DataStandardTree> result= Lists.newArrayList();

        //取出所有的code 去重
        List<String> sTCodes = standardTrees.stream().map(DataStandardTree::getCode).distinct().collect(Collectors.toList());
        for (String sTCode : sTCodes) {
            List<DataStandardTree> stList = standardTrees.stream().filter(st->st.getCode().equals(sTCode))
                    .sorted(new Comparator<DataStandardTree>() {
                        //返回正 o1>o2 返回负 o2>o1
                        @Override
                        public int compare(DataStandardTree o1, DataStandardTree o2) {
                            return o1.getCreateTime().compareTo(o1.getCreateTime());
                        }
                    })
                    .collect(Collectors.toList());
            DataStandardTree dataStandardTree=new DataStandardTree();
            for (int i = 0; i < stList.size(); i++) {
                DataStandardTree standardTree = stList.get(i);
                if (i==0) {
                    dataStandardTree=standardTree;
                }else {
                    dataStandardTree.getChildren().add(standardTree);
                }
            }
            result.add(dataStandardTree);
        }

        return result;
    }
}
