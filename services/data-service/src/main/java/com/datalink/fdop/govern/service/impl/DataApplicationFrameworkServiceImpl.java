package com.datalink.fdop.govern.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.SelectVo;
import com.datalink.fdop.common.core.enums.Status;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.tree.TreeUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.drive.mapper.DataSourceMapper;
import com.datalink.fdop.govern.api.domain.DataApplicationFramework;
import com.datalink.fdop.govern.api.domain.DataApplicationFrameworkTree;
import com.datalink.fdop.govern.mapper.DataApplicationFrameworkMapper;
import com.datalink.fdop.govern.service.IDataApplicationFrameworkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class DataApplicationFrameworkServiceImpl implements IDataApplicationFrameworkService {

    @Autowired
    private DataApplicationFrameworkMapper applicationFrameworkMapper;

    @Autowired
    private DataSourceMapper dataSourceMapper;

    @Override
    public int create(DataApplicationFramework applicationFramework) {
        if (applicationFrameworkMapper.selectByCode(applicationFramework.getCode()) != null) {
            throw new ServiceException(Status.DATA_APP_EXIST);
        }
        // 检查字典值
        checkDictionaryValues(applicationFramework);
        applicationFramework.setId(IdWorker.getId());
        return applicationFrameworkMapper.insert(applicationFramework);
    }

    @Override
    public int update(DataApplicationFramework applicationFramework) {
        VlabelItem<DataApplicationFramework> vlabelItem = applicationFrameworkMapper.selectById(applicationFramework.getId());
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATA_APP_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(applicationFramework.getCode()) && applicationFrameworkMapper.checkCodeIsExists(applicationFramework.getId(), applicationFramework.getCode()) != null) {
            throw new ServiceException(Status.DATA_APP_EXIST);
        }
        // 检查字典值
        checkDictionaryValues(applicationFramework);
        return applicationFrameworkMapper.updateById(applicationFramework);
    }

    /**
     * 检查字典值
     * @param applicationFramework 应用框架信息
     */
    private void checkDictionaryValues(DataApplicationFramework applicationFramework) {
        if (applicationFramework.getSourceId() != null) {
            VlabelItem<DataSource> dataSourceVlabelItem = dataSourceMapper.selectById(applicationFramework.getSourceId());
            if (dataSourceVlabelItem == null) {
                throw new ServiceException(Status.DATASOURCE_NOT_EXIST);
            }
            applicationFramework.setSourceName(dataSourceVlabelItem.getProperties().getCode());
        }
    }

    @Override
    public int delete(List<Long> ids) {
        return applicationFrameworkMapper.deleteBatchIds(ids);
    }

    @Override
    public DataApplicationFramework selectById(Long id) {
        VlabelItem<DataApplicationFramework> vlabelItem = applicationFrameworkMapper.selectById(id);
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATA_APP_NOT_EXIST);
        }
        return vlabelItem.getProperties();
    }

    @Override
    public DataApplicationFramework selectByCode(String code) {
        VlabelItem<DataApplicationFramework> vlabelItem = applicationFrameworkMapper.selectByCode(code);
        if (vlabelItem == null) {
            throw new ServiceException(Status.DATA_APP_NOT_EXIST);
        }
        return vlabelItem.getProperties();
    }

    @Override
    public List<DataApplicationFrameworkTree> treeList(String sort, String code, String name, String description) {
        // 所有的数据集合
        List<DataApplicationFrameworkTree> trees = new ArrayList<>();
        List<DataApplicationFrameworkTree> treeList = applicationFrameworkMapper.selectTree(sort, code, name, description);
        trees.addAll(treeList);
        // 递归获取子节点
        List<DataApplicationFrameworkTree> newTreeList = (List<DataApplicationFrameworkTree>) TreeUtils.getTree(trees);
        // 条件查找删除空集合的菜单
        if (StringUtils.isNotEmpty(code)) {
            TreeUtils.removeEmptyChilderAndMenu(newTreeList);
        }
        return newTreeList;
    }

    @Override
    public PageDataInfo<DataApplicationFramework> overview(Long pid, String sort, SearchVo searchVo) {
        Page<DataApplicationFramework> page = PageUtils.getPage(DataApplicationFramework.class);
        // 获取分页参数
        IPage<DataApplicationFramework> pageInfoList = applicationFrameworkMapper.overview(page, pid, sort, searchVo);
        List<DataApplicationFramework> records = pageInfoList.getRecords();
        return PageUtils.getPageInfo(records, (int) pageInfoList.getTotal());
    }

    @Override
    public List<SelectVo> selectVoList(DataApplicationFramework applicationFramework) {
        return applicationFrameworkMapper.selectListAll(applicationFramework);
    }

    @Override
    public int copy(Long pid, List<DataApplicationFramework> applicationFrameworkList) {
        if (applicationFrameworkMapper.selectById(pid) == null) {
            throw new ServiceException(Status.DATA_APP_NOT_EXIST);
        }
        int count = 0;
        for (DataApplicationFramework applicationFramework : applicationFrameworkList) {
            // 复制到新目录
            applicationFramework.setId(IdWorker.getId());
            applicationFramework.setPid(pid);
            create(applicationFramework);
            count++;
        }
        return count;
    }

}
