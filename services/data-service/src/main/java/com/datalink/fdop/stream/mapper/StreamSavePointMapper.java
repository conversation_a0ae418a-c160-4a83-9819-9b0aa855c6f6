package com.datalink.fdop.stream.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.mybatis.model.VlabelItem;
import com.datalink.fdop.stream.api.domain.StreamCdc;
import com.datalink.fdop.stream.api.domain.StreamSavePoint;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface StreamSavePointMapper extends BaseMapper<StreamSavePoint> {

    int createStreamAndStreamSavePointEdge(@Param("streamId") Long streamId, @Param("ids") List<Long> ids);

    int insertStreamSavePoint(@Param("streamSavePoint") StreamSavePoint streamSavePoint);

    int updateById(StreamSavePoint streamSavePoint);

    int batchUpdatePidById(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteStreamAndStreamSavePointEdge(@Param("ids") List<Long> ids, @Param("pid") Long pid);

    int deleteBatchIds(@Param("ids") List<Long> ids);

    VlabelItem<StreamSavePoint> selectById(Long id);

    VlabelItem<StreamSavePoint> selectByCode(String code);

    VlabelItem<StreamCdc> checkCodeIsExists(@Param("id") Long id, @Param("code") String code);

    IPage<StreamSavePoint> selectSavePointListByStreamId(@Param("page") Page<StreamSavePoint> page, @Param("streamId") Long streamId, @Param("sort") String sort, @Param("searchVo") SearchVo searchVo);

    Integer querySerialNumber();

}
