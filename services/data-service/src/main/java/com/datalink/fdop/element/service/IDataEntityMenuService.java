package com.datalink.fdop.element.service;

import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.element.api.domain.DataEntity;
import com.datalink.fdop.element.api.domain.DataEntityMenu;
import com.datalink.fdop.element.api.model.DataEntityTree;
import com.datalink.fdop.element.api.model.vo.DataEntityHierarchyTreeVo;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/4/12 13:58
 */
public interface IDataEntityMenuService {

    int create(DataEntityMenu dataEntityMenu);

    int update(DataEntityMenu dataEntityMenu);

    int updateL3(DataEntityMenu dataEntityMenu, Boolean isDelete);

    int delete(List<Long> ids);

    List<DataEntityTree> tree(String sort, String code, Boolean isQueryNode, Long dataSourceId,Boolean filterReadOnly);

    List<DataEntityTree> readMarkedtree(String sort, String code, Boolean isQueryNode, Long dataSourceId,Boolean filterReadOnly,Boolean read);

    PageDataInfo<DataEntity> overview(Long pid, String sort, SearchVo searchVo);

    List<DataEntity> list(Long dataEntityId);

    List<DataEntityHierarchyTreeVo> selectEntityHierarchyTree(Long dataSourceId, String code);

    /**
     * 获取菜单树，不包含子任务节点
     * @param menuIds 菜单ID
     */
    List<DataEntityTree> getTaskTree(Set<Long> menuIds);

    PageDataInfo<DataEntity> readMarked(Long pid, String sort, SearchVo searchVo, Boolean read);
}
