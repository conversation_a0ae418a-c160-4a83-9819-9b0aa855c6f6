package com.datalink.fdop.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.engine.api.domain.DwhFinalRecoverEnt;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DwhFinalRecoverEntMapper extends BaseMapper<DwhFinalRecoverEnt> {

    IPage<DwhFinalRecoverEnt> selectAll(
            @Param("verId") String verId,
            @Param("plantId") String plantId, @Param("yearMonth") String yearMonth,
            @Param("page") IPage<DwhFinalRecoverEnt> page, @Param("sort") String sort,
            @Param("searchVo") SearchVo searchVo);

    List<DwhFinalRecoverEnt> selectNoPage(
            @Param("verId") String verId,
            @Param("plantId") String plantId, @Param("yearMonth") String yearMonth,
            @Param("sort") String sort, @Param("searchVo") SearchVo searchVo); // 新增方法定义
}