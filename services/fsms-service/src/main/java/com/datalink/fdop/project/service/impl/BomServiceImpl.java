package com.datalink.fdop.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.project.api.domain.BomHead;
import com.datalink.fdop.project.api.domain.BomRow;
import com.datalink.fdop.project.mapper.BomMapper;
import com.datalink.fdop.project.mapper.BomRowMapper;
import com.datalink.fdop.project.service.BomService;
import com.datalink.fdop.project.vo.BomSearchVo;
import com.datalink.fdop.project.vo.BomTreeVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/10/11 14:20
 */
@Service
public class BomServiceImpl extends ServiceImpl<BomMapper, BomHead> implements BomService {


    @Autowired
    private BomMapper bomMapper;
    @Autowired
    private BomRowMapper bomRowMapper;


    @Override
    public PageDataInfo<BomTreeVo> assemblyTree(BomSearchVo bomSearchVo) {
        List<String> supplierCode = null;
        List<String> status = null;
        List<String> creators = null;
        List<String> versions = null;
        List<String> materialCodes = null;
        if (bomSearchVo != null) {
            supplierCode = bomSearchVo.getSupplierCode();
            status = bomSearchVo.getStatus();
            creators = bomSearchVo.getCreators();
            versions = bomSearchVo.getVersions();
            materialCodes = bomSearchVo.getMaterialCodes();
        }
        //找出头行数据
        Page<BomHead> page = PageUtils.getPage(BomHead.class);
        Page<BomHead> headPage = bomMapper.selectPage(page, new QueryWrapper<BomHead>()
                        .lambda()
                        .in(CollectionUtils.isNotEmpty(supplierCode), BomHead::getSupplierCode, supplierCode)
                        .in(CollectionUtils.isNotEmpty(status), BomHead::getStatus, status)
                        .in(CollectionUtils.isNotEmpty(creators), BomHead::getCreator, creators)
                        .in(CollectionUtils.isNotEmpty(versions), BomHead::getVersions, versions)
                //.in(CollectionUtils.isNotEmpty(materialCodes), BomHead::getParentMaterialCode, materialCodes)
        );
        List<BomHead> bomHeads = headPage.getRecords();
        if (bomSearchVo != null && (CollectionUtils.isNotEmpty(bomSearchVo.getProjectNumbers()) || CollectionUtils.isNotEmpty(bomSearchVo.getDeviceNames()))) {
            List<String> mCodes = bomMapper.findBomHeads(bomSearchVo);
            bomHeads = bomHeads.stream().filter(bomHead -> mCodes.contains(bomHead)).collect(Collectors.toList());
        }
        //找出所有行表数据
        List<BomRow> bomRows = bomRowMapper.selectList(new QueryWrapper<BomRow>());
        //获取顶级节点
        List<String> headIds = bomRowMapper.findTopBom();
        List<BomHead> headList = bomHeads;
        if (bomSearchVo == null) {
            headList = bomHeads.stream().filter(bomHead -> headIds.contains(bomHead.getHeadId())).collect(Collectors.toList());
        }
        //转化对象
        List<BomTreeVo> headBomTreeVos = headToBomTreeVo(headList);
        List<BomTreeVo> rowBomTreeVos = rowToBomTreeVo(bomRows);
        //找树
        for (int i = headBomTreeVos.size() - 1; i >= 0; i--) {
            BomTreeVo headBomTreeVo = headBomTreeVos.get(i);
            List<Boolean> falgs = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(materialCodes)) {
                falgs.add(materialCodes.contains(headBomTreeVo.getMaterialCode()));
            }
            recursion(headBomTreeVo, rowBomTreeVos, materialCodes, falgs, bomHeads);
            if (CollectionUtils.isNotEmpty(materialCodes)) {
                if (falgs.stream().noneMatch(falg -> falg)) {
                    headBomTreeVos.remove(headBomTreeVo);
                }
            }
        }


        return PageUtils.getPageInfo(headBomTreeVos, (int) headPage.getTotal());
    }

    @Override
    public Map<String, Object> queryBom(String headId, SearchVo searchVo) {
        BomHead bomHead = bomMapper.selectOne(new QueryWrapper<BomHead>().lambda().eq(BomHead::getHeadId, headId));
        Page<BomRow> page = PageUtils.getPage(BomRow.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (searchVo != null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<BomRow> bomRowPage = bomRowMapper.selectPage(page, new QueryWrapper<BomRow>().lambda().eq(BomRow::getHeadId, headId));
        List<BomRow> records = bomRowPage.getRecords();
        for (BomRow record : records) {
            String childrenMaterialCode = record.getChildrenMaterialCode();
            BomHead head = bomMapper.selectOne(new QueryWrapper<BomHead>().lambda().eq(BomHead::getParentMaterialCode, childrenMaterialCode).eq(BomHead::getVersions, record.getChildrenMaterialVersions()));
            if (head != null) {
                record.setHeadId(head.getHeadId());
                List<BomRow> bomRows = bomRowMapper.selectList(new QueryWrapper<BomRow>().lambda()
                        .eq(BomRow::getHeadId, head.getHeadId())

                );
                if (CollectionUtils.isNotEmpty(bomRows)) {
                    record.setSubstratumFlag(true);
                }
            } else {
                record.setSubstratumFlag(false);
            }

        }
        if (searchVo != null) {
            records = SearchUtils.getByEntityFilter(searchVo, records);
            page.setTotal(records.size());
            records.stream().skip(pageSize * (current - 1)).limit(pageSize).collect(Collectors.toList());
        }
        PageDataInfo<BomRow> pageInfo = PageUtils.getPageInfo(records, (int) bomRowPage.getTotal());
        Map<String, Object> map = Maps.newHashMap();
        map.put("head", bomHead);
        map.put("row", pageInfo);
        return map;
    }

    private List<Boolean> recursion(BomTreeVo headBomTreeVo, List<BomTreeVo> rowBomTreeVos, List<String> materialCodes, List<Boolean> falgs, List<BomHead> bomHeads) {
        for (BomTreeVo rowBomTreeVo : rowBomTreeVos) {
            if (rowBomTreeVo.getHeadId().equals(headBomTreeVo.getHeadId()) && !rowBomTreeVo.getMaterialCode().equals(headBomTreeVo.getMaterialCode())) {
                headBomTreeVo.getChildren().add(rowBomTreeVo);
                if (CollectionUtils.isNotEmpty(materialCodes)) {
                    falgs.add(materialCodes.contains(rowBomTreeVo.getMaterialCode()));
                }
                for (BomHead bomHead : bomHeads) {
                    if (bomHead.getParentMaterialCode().equals(rowBomTreeVo.getMaterialCode())) {
                        List<BomTreeVo> bomTreeVos = headToBomTreeVo(Arrays.asList(bomHead));
                        rowBomTreeVo.setHeadId(bomTreeVos.get(0).getHeadId());
                        recursion(rowBomTreeVo, rowBomTreeVos, materialCodes, falgs, bomHeads);
                    }
                }

            }
        }

        return falgs;
    }

    private List<BomTreeVo> rowToBomTreeVo(List<BomRow> bomRows) {
        List<BomTreeVo> bomTreeVos = Lists.newArrayList();
        for (BomRow bomRow : bomRows) {
            BomTreeVo bomTreeVo = new BomTreeVo();
            bomTreeVo.setHeadId(bomRow.getHeadId());
            bomTreeVo.setMaterialCode(bomRow.getChildrenMaterialCode());
            bomTreeVo.setMaterialDescribe(bomRow.getChildrenMaterialDescribe());
            bomTreeVos.add(bomTreeVo);
        }
        return bomTreeVos;
    }

    private List<BomTreeVo> headToBomTreeVo(List<BomHead> headList) {
        List<BomTreeVo> bomTreeVos = Lists.newArrayList();
        for (BomHead bomHead : headList) {
            BomTreeVo bomTreeVo = new BomTreeVo();
            bomTreeVo.setHeadId(bomHead.getHeadId());
            bomTreeVo.setMaterialCode(bomHead.getParentMaterialCode());
            bomTreeVo.setMaterialDescribe(bomHead.getParentMaterialDescribe());
            bomTreeVos.add(bomTreeVo);
        }
        return bomTreeVos;
    }


}
