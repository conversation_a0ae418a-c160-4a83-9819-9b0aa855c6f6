package com.datalink.fdop.project.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.project.api.domain.OrderRow;
import com.datalink.fdop.project.mapper.OrderRowMapper;
import com.datalink.fdop.project.service.OrderRowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2022/10/24 14:24
 */
@Service
public class OrderRowServiceImpl extends ServiceImpl<OrderRowMapper, OrderRow> implements OrderRowService {


    @Autowired
    private OrderRowMapper orderRowMapper;

    @Override
    public String findDesc(String code) {
        return orderRowMapper.findDesc(code);
    }

    @Override
    public String findSPDesc(String stockPCode) {
        return orderRowMapper.findSPDesc(stockPCode);
    }
}
