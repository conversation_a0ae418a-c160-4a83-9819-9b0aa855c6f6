package com.datalink.fdop.project.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.project.api.domain.BaseMaterial;
import com.datalink.fdop.project.api.domain.CapsulationInfo;
import com.datalink.fdop.project.api.domain.ManufacturerManage;
import com.datalink.fdop.project.api.domain.TestProcedure;
import com.datalink.fdop.project.service.*;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/11/4 14:59
 */

@RestController
@RequestMapping("/mmanage")
@ApiOperation("制造商管理")
public class ManufacturerManageController {


    @Autowired
    private ManufacturerManageService manageService;
    @Autowired
    private MaterialService materialService;
    @Autowired
    private TestProcedureService testProcedureService;
    @Autowired
    private CapsulationInfoService capsulationInfoService;
    @Autowired
    private BaseMaterialService baseMaterialService;



    @ApiOperation(value = "修改")
    @Log(title = "工程资料",businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public R update(@RequestBody ManufacturerManage manufacturerManage) {
        switch (manufacturerManage.getSerialNum()){
            case "1" :
                List<BaseMaterial> list = baseMaterialService.list();
                if (CollectionUtils.isNotEmpty(list)) {
                    throw new ServiceException("已有数据，不允许修改");
                }
                break;
            case "2" :
                List<CapsulationInfo> capsulationInfos = capsulationInfoService.list();
                if (CollectionUtils.isNotEmpty(capsulationInfos)) {
                    throw new ServiceException("已有数据，不允许修改");
                }
                break;
            case "3" :
                List<TestProcedure> testProcedures = testProcedureService.list();
                if (CollectionUtils.isNotEmpty(testProcedures)) {
                    throw new ServiceException("已有数据，不允许修改");
                }
                break;
            case "4" :
                int count=manageService.findFlowCount();
                if (count>0) {
                    throw new ServiceException("已有数据，不允许修改");
                }
                break;
        }
        boolean update = manageService.update(Wrappers.lambdaUpdate(ManufacturerManage.class)
                .set(ManufacturerManage::getStatu,manufacturerManage.getStatu())
                .eq(ManufacturerManage::getSerialNum,manufacturerManage.getSerialNum()));
        if (update){
            return R.ok("成功");
        }else {
            return R.fail("失败");
        }

    }


    @ApiOperation(value = "查询")
    @Log(title = "工程资料",businessType = BusinessType.UPDATE)
    @PostMapping("/query")
    public R query() {
        List<ManufacturerManage> list = manageService.list();
        return R.ok(list);
    }
}
