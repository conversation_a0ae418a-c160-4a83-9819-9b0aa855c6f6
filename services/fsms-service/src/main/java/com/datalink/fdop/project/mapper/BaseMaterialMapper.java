package com.datalink.fdop.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.project.api.domain.BaseMaterial;
import com.datalink.fdop.project.api.model.vo.BaseMaterialShowVo;
import com.datalink.fdop.project.api.model.vo.BaseMaterialVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @Date 2022/10/31 10:50
 */
@Mapper
public interface BaseMaterialMapper extends BaseMapper<BaseMaterial> {
    Page<BaseMaterialShowVo> pageData(IPage<BaseMaterialShowVo> page, @Param("base") BaseMaterialVo baseMaterialVo);
}
