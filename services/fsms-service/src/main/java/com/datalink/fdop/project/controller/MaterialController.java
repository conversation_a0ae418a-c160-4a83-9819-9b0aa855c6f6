package com.datalink.fdop.project.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.MapUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.project.api.domain.Material;
import com.datalink.fdop.project.api.domain.MaterialProperty;
import com.datalink.fdop.project.api.model.vo.MaterialVo;
import com.datalink.fdop.project.service.ManufacturerService;
import com.datalink.fdop.project.service.MaterialPropertyService;
import com.datalink.fdop.project.service.MaterialService;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/10/9 15:48
 */
@RestController
@RequestMapping("/material")
@Transactional
public class MaterialController {


    @Autowired
    private MaterialService materialService;
    @Autowired
    private ManufacturerService manufacturerService;
    @Autowired
    private MaterialPropertyService materialPropertyService;

    @ApiOperation(value = "新增")
    @Log(title = "工程资料",businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public R create(@RequestBody List<Material> materials) {
        materials.stream().forEach(material -> {
            Material material1 = materialService.getOne(new QueryWrapper<Material>().lambda().eq(Material::getMaterialCode, material.getMaterialCode()));
            if (material1!=null) {
                throw new ServiceException("物料编码已存在");
            }
        });
        try {
            if (materialService.saveBatch(materials)) {
                return R.ok("保存成功");
            }else {
                return R.fail("保存失败");
            }
        } catch (Exception e){
            e.printStackTrace();
            throw new ServiceException("检查数据是否正确");
        }

    }


    @ApiOperation(value = "修改")
    @Log(title = "工程资料",businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public R update(@RequestBody List<Material> materials) {
        materials.stream().forEach(material -> {
            Material one = materialService.getOne(new QueryWrapper<Material>()
                    .lambda()
                    .eq(Material::getMaterialCode, material.getMaterialCode()));
            if (one!=null) {
                throw new ServiceException("数据已存在");
            }
        });
        if (materialService.updateBatchById(materials)) {
            return R.ok("修改成功");
        }else {
            return R.fail("修改失败");
        }

    }
    @ApiOperation("删除")
    @Log(title = "工程资料",businessType = BusinessType.DELETE)
    @PostMapping("/del")
    public R del(@RequestBody List<Long> materialIds) {
        if (materialService.removeBatchByIds(materialIds)) {
            return R.ok("修改成功");
        } else {
            return R.fail("修改失败");
        }


    }

    @ApiOperation(value = "查询")
    @Log(title = "工程资料",businessType = BusinessType.OTHER)
    @PostMapping("/query")
    public R query(@RequestBody(required = false) MaterialVo material) {
        Page<Material> page = PageUtils.getPage(Material.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (material.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<Material> materialPage =null;
        if (material!=null) {
            materialPage= materialService.pageData(page,material );
        }else {
            materialPage= materialService.page(page,new QueryWrapper<Material>().lambda().orderByAsc(Material::getMaterialCode));
        }
        List<Material> records = materialPage.getRecords();
        if (material.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(material.getSearchVo(), records);
            materialPage.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records,(int)materialPage.getTotal()));
    }
    @ApiOperation(value = "查询物料属性")
    @Log(title = "工程资料",businessType = BusinessType.OTHER)
    @PostMapping("/query/property")
    public R queryProperty(@RequestBody List<String> material) {
        Page<MaterialProperty> propertyPage = materialPropertyService.page(PageUtils.getPage(MaterialProperty.class),
                new QueryWrapper<MaterialProperty>().lambda().in(CollectionUtils.isNotEmpty(material),MaterialProperty::getMaterialCode,material));
        return R.ok(PageUtils.getPageInfo(propertyPage.getRecords(),(int)propertyPage.getTotal()));
    }


    @ApiOperation(value = "导出物料")
    @Log(title = "工程资料",businessType = BusinessType.OTHER)
    @PostMapping("/download")
    public void download(@RequestBody(required = false) MaterialVo material,HttpServletResponse response) throws IOException {
        Page<Material> page = PageUtils.getPage(Material.class);
        if (material.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<Material> materialPage =null;
        if (material!=null) {
            materialPage= materialService.pageData(page,material );
        }else {
            materialPage= materialService.page(page);
        }
        List<Material> records = materialPage.getRecords();
        if (material.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(material.getSearchVo(), records);
        }
        ExcelUtils.export3Excel(response,records, Material.class,"物料信息");
    }
    @ApiOperation(value = "导出物料属性")
    @Log(title = "工程资料",businessType = BusinessType.OTHER)
    @PostMapping("/download/property")
    public void download(@RequestBody List<String> material,HttpServletResponse response) throws IOException {
        Page<MaterialProperty> propertyPage = materialPropertyService.page(PageUtils.getPage(MaterialProperty.class),
                new QueryWrapper<MaterialProperty>().lambda().in(CollectionUtils.isNotEmpty(material),MaterialProperty::getMaterialCode,material));
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("物料信息属性", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 这里需要设置不关闭流
            EasyExcel.write(response.getOutputStream(), MaterialProperty.class).autoCloseStream(Boolean.FALSE).sheet("模板")
                    .doWrite(propertyPage.getRecords());
        } catch (Exception e) {
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            Map<String, String> map = MapUtils.newHashMap();
            map.put("status", "failure");
            map.put("message", "下载文件失败" + e.getMessage());
            response.getWriter().println(JSON.toJSONString(map));
        }
    }

    @ApiOperation(value = "下拉框查询")
    @Log(title = "工程资料",businessType = BusinessType.OTHER)
    @PostMapping("/combobox")
    public R combobox() {
        Map result= Maps.newHashMap();
        List<Material> list = materialService.list();
        //物料类型
        List<Material> mtart = list.stream().map(m -> {
            Material material = new Material();
            material.setMaterialTypeDesc(m.getMaterialTypeDesc());
            material.setMaterialType(m.getMaterialType());
            return material;
        }).distinct().collect(Collectors.toList());
        //物料组
        List<Material> matkl = list.stream().map(m -> {
            Material material = new Material();
            material.setMaterialGroup(m.getMaterialGroup());
            material.setMaterialGroupDesc(m.getMaterialGroupDesc());
            return material;
        }).distinct().collect(Collectors.toList());
        //物料状态
        List<Material> statu = list.stream().map(m -> {
            Material material = new Material();
            material.setMaterialStatu(m.getMaterialStatu());
            material.setMaterialStatuDesc(m.getMaterialStatuDesc());
            return material;
        }).distinct().collect(Collectors.toList());
        result.put("mtart",mtart);
        result.put("matkl",matkl);
        result.put("statu",statu);
        return R.ok(result);
    }

    @ApiOperation(value = "code查询")
    @Log(title = "工程资料",businessType = BusinessType.OTHER)
    @PostMapping("/byCode")
    public R byCode(@RequestParam String materialCode) {
        Page<Material> page = PageUtils.getPage(Material.class);
        Page<Material> materialPage = materialService.page(page, new QueryWrapper<Material>().lambda().like(Material::getMaterialCode, materialCode));
        return R.ok(PageUtils.getPageInfo(materialPage.getRecords(),(int)materialPage.getTotal()));
    }



}
