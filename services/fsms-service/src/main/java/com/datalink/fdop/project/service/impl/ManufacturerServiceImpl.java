package com.datalink.fdop.project.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.project.api.domain.Manufacturer;
import com.datalink.fdop.project.mapper.ManufacturerMapper;
import com.datalink.fdop.project.service.ManufacturerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2022/10/27 14:50
 */
@Service
public class ManufacturerServiceImpl extends ServiceImpl<ManufacturerMapper, Manufacturer> implements ManufacturerService {

    @Autowired
    private ManufacturerMapper manufacturerMapper;
}
