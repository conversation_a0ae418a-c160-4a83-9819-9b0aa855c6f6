package com.datalink.fdop.project.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.project.api.domain.OrderHead;
import com.datalink.fdop.project.api.model.vo.OrderQueryShowVo;
import com.datalink.fdop.project.api.model.vo.OrderQueryVo;

/**
 * <AUTHOR>
 * @Date 2022/10/24 14:23
 */
public interface OrderHeadService  extends IService<OrderHead> {
    Page<OrderQueryShowVo> queryData(Page<OrderQueryShowVo> page, OrderQueryVo orderQueryVo, String delFlag);

    String findDescription(String companyCode);

    String findPurchaseDesc(String ekorg);

    String findSupplierName(String supplierCode);
}
