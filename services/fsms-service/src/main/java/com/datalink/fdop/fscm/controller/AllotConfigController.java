package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.AllotConfig;
import com.datalink.fdop.fscm.service.AllotConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/12/12 16:41
 */
@RestController
@RequestMapping("/allot/config")
@Transactional
@Api(tags = "调拨配置")
public class AllotConfigController {

    @Autowired
    private AllotConfigService allotConfigService;

    @ApiOperation(value = "调拨查询")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/query")
    public R query(@RequestBody AllotConfig allotConfig) {
        Page<AllotConfig> page = PageUtils.getPage(AllotConfig.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        page.setSize(Long.MAX_VALUE);
        Page<AllotConfig> pageData= allotConfigService.page(page,new QueryWrapper<AllotConfig>()
                        .select("DISTINCT on (transfer_type) * ")
                .lambda()
        );
        List<AllotConfig> records = pageData.getRecords();
        if (allotConfig.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(allotConfig.getSearchVo(), records);

        }
        long total=records.size();
        records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        return R.ok(PageUtils.getPageInfo(records,(int)total));
    }

    @ApiOperation(value = "根据类别查询")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/select")
    public R select(@RequestBody AllotConfig allotConfig) {
        List<AllotConfig> pageData= allotConfigService.list(new QueryWrapper< AllotConfig>().lambda()
                .eq(AllotConfig::getTransferType,allotConfig.getTransferType()));

        if (allotConfig.getSearchVo()!=null) {
            pageData = SearchUtils.getByEntityFilter(allotConfig.getSearchVo(), pageData);

        }
        return R.ok(pageData);
    }
    @ApiOperation(value = "保存")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/save")
    public R save(@RequestBody List<AllotConfig> allotConfigs) {
        for (AllotConfig allotConfig : allotConfigs) {
            allotConfigService.remove(new QueryWrapper<AllotConfig>().lambda()
                    .eq(AllotConfig::getTransferType,allotConfig.getTransferType())
                    .eq(AllotConfig::getField,allotConfig.getField())
            );
        }
        allotConfigService.saveBatch(allotConfigs);
        return R.ok();
    }
    @ApiOperation(value = "删除")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/del")
    public R del(@RequestBody List<AllotConfig> allotConfigs) {
        for (AllotConfig allotConfig : allotConfigs) {
            allotConfigService.remove(new QueryWrapper<AllotConfig>().lambda()
                    .eq(AllotConfig::getTransferType,allotConfig.getTransferType())
            );
        }
        return R.ok();
    }


    @ApiOperation(value = "导出")
    @Log(title = "fscm",businessType = BusinessType.EXPORT)
    @PostMapping("/download")
    public void download( HttpServletResponse response,@RequestBody SearchVo searchVo) throws IOException {
        Page<AllotConfig> page = PageUtils.getPage(AllotConfig.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        page.setSize(Long.MAX_VALUE);
        List<AllotConfig> pageData= allotConfigService.list();

        if (searchVo!=null) {
            pageData = SearchUtils.getByEntityFilter(searchVo, pageData);

        }
        ExcelUtils.export3Excel(response,pageData, AllotConfig.class,"调拨");
    }




}
