package com.datalink.fdop.settlement.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.PieceConfig;
import com.datalink.fdop.settlement.api.domain.PriceElement;
import com.datalink.fdop.settlement.service.PriceDefinitionService;
import com.datalink.fdop.settlement.service.PriceElementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/settlement/price/element")
@Transactional
@Api(tags = "价格因子影响因素")
public class PriceElementController {

    @Autowired
    private PriceDefinitionService priceDefinitionService;

    @Autowired
    private PriceElementService priceElementService;


    @ApiOperation(value = "查询")
    @Log(title = "settlement", businessType = BusinessType.UPDATE)
    @PostMapping("/query")
    public R query(@RequestBody(required = false) PriceElement priceElement) {
        Page<PriceElement> page = PageUtils.getPage(PriceElement.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        SearchVo searchVo = priceElement.getSearchVo();
        if (searchVo != null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<PriceElement> pageData = priceElementService.page(page, new QueryWrapper<PriceElement>().lambda()
                .eq(StringUtils.isNotEmpty(priceElement.getPriceFactor()), PriceElement::getPriceFactor, priceElement.getPriceFactor())
        );
        List<PriceElement> records = pageData.getRecords();
        if (searchVo != null) {
            records = SearchUtils.getByEntityFilter(searchVo, records);
            page.setTotal(records.size());
            records = records.stream().skip(pageSize * (current - 1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records, (int) pageData.getTotal()));
    }

    @ApiOperation(value = "新增")
    @Log(title = "settlement", businessType = BusinessType.UPDATE)
    @PostMapping("/create")
    public R create(@RequestBody List<PriceElement> priceDefinitions) {
        if (priceElementService.saveBatch(priceDefinitions)) {
            return R.ok("保存成功");
        } else {
            return R.fail("保存失败");
        }
    }

    @ApiOperation(value = "修改")
    @Log(title = "settlement", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public R update(@RequestBody List<PriceElement> priceElements) {

        for (PriceElement priceElement : priceElements) {
            priceElementService.update(new UpdateWrapper<PriceElement>().lambda()
                    .set(StringUtils.isNotEmpty(priceElement.getChangingFactorDesc()), PriceElement::getChangingFactorDesc, priceElement.getChangingFactorDesc())
                    .eq(PriceElement::getPriceFactor, priceElement.getPriceFactor())
                    .eq(PriceElement::getChangingFactor, priceElement.getChangingFactor())
            );
        }
        return R.ok("修改成功");
    }

    @ApiOperation(value = "删除")
    @Log(title = "settlement", businessType = BusinessType.UPDATE)
    @PostMapping("/del")
    public R del(@RequestBody List<PriceElement> priceElements) {
        for (PriceElement priceElement : priceElements) {
            priceElementService.remove(new QueryWrapper<PriceElement>().lambda()
                    .eq(PriceElement::getPriceFactor, priceElement.getPriceFactor())
                    .eq(PriceElement::getChangingFactor, priceElement.getChangingFactor()));
        }

        return R.ok();

    }

    @ApiOperation(value = "导出")
    @Log(title = "settlement", businessType = BusinessType.OTHER)
    @PostMapping("/download")
    public void download(HttpServletResponse response, @RequestBody(required = false) PriceElement priceElement) throws IOException {
        Page<PriceElement> page = PageUtils.getPage(PriceElement.class);
        SearchVo searchVo = priceElement.getSearchVo();
        if (searchVo != null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<PriceElement> pageData = priceElementService.page(page, new QueryWrapper<PriceElement>().lambda()
                .eq(StringUtils.isNotEmpty(priceElement.getPriceFactor()), PriceElement::getPriceFactor, priceElement.getPriceFactor())
        );
        List<PriceElement> records = pageData.getRecords();
        if (searchVo != null) {
            records = SearchUtils.getByEntityFilter(searchVo, records);
        }
        ExcelUtils.export3Excel(response, records, PriceElement.class, "公式定义");
    }


}
