package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.*;
import com.datalink.fdop.fscm.api.model.vo.AllotPostCertificateVo;
import com.datalink.fdop.fscm.api.model.vo.AllotQueryVo;
import com.datalink.fdop.fscm.api.model.vo.AllotSaveVo;
import com.datalink.fdop.fscm.api.model.vo.AllotShowVo;
import com.datalink.fdop.fscm.service.*;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/12/12 16:41
 */
@RestController
@RequestMapping("/allot/head")
@Transactional
@Api(tags = "调拨头")
public class AllotHeadController {

    @Autowired
    private AllotHeadService allotHeadService;
    @Autowired
    private PostCertificateRowService postCertificateRowService;
    @Autowired
    private AllotRowService allotRowService;
    @Autowired
    private TemplateService templateService;
    @Autowired
    private AllotConfigService allotConfigService;

    @Autowired
    private ProvisionalSingleOutService provisionalSingleOutService;

    @ApiOperation(value = "调拨查询")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/query")
    public R query(@RequestBody AllotQueryVo allotQueryVo, @RequestParam(required = false) String delflag,@RequestParam(required = false) String temporary,@RequestParam(required = false) Boolean postFlag) {
        Page<AllotShowVo> page = PageUtils.getPage(AllotShowVo.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        page.setSize(Long.MAX_VALUE);
        List<AllotShowVo> pageData= allotHeadService.queryData(allotQueryVo,delflag,temporary);
        pageData.stream().forEach(allotShowVo -> {
            List<PostCertificateRow> list = postCertificateRowService.list(new QueryWrapper<PostCertificateRow>()
                    .lambda()
                    .eq(PostCertificateRow::getWriteOff,"0")
                    .in(PostCertificateRow::getMoveType, Arrays.asList("311", "312"))
                    .eq(PostCertificateRow::getTransferOrderNum, allotShowVo.getTransferOrderNum())
                    .eq(PostCertificateRow::getTransferOrderRowNum, allotShowVo.getTransferOrderRowNum())
            );
            if (CollectionUtils.isNotEmpty(list)) {
                double sum = list.stream().map(PostCertificateRow::getPostQuantity).mapToDouble(m -> m).sum();
                allotShowVo.setPostQuantity(sum);
            }
            allotShowVo.setStockPCodeReceiveDesc(templateService.findStockPDescription(allotShowVo.getStockPCodeReceive()));
            allotShowVo.setStockPCodeShipDesc(templateService.findStockPDescription(allotShowVo.getStockPCodeShip()));
        });
        if (postFlag!=null&&postFlag) {
            pageData=pageData.stream().filter(p->p.getPostQuantity()==0.0).collect(Collectors.toList());
        }
        if (allotQueryVo.getSearchVo()!=null) {
            pageData = SearchUtils.getByEntityFilter(allotQueryVo.getSearchVo(), pageData);

        }
        long total=pageData.size();
        pageData=pageData.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        return R.ok(PageUtils.getPageInfo(pageData,(int)total));
    }

    /***
     *
     * @param allotPostCertificateVo
     * @return
     */
    @ApiOperation(value = "调拨过帐")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/post/certificate")
    public R certificate(@RequestBody AllotPostCertificateVo allotPostCertificateVo) {
        //更新
        allotHeadService.updateData(allotPostCertificateVo);
        return R.ok();
    }
    @ApiOperation(value = "调拨过帐检查")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/post/certificate/check")
    public R certificateCheck(@RequestBody AllotPostCertificateVo allotPostCertificateVo) {
        //校验
        allotHeadService.checkData(allotPostCertificateVo);
        return R.ok();
    }


    @ApiOperation(value = "保存前数据检查")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/save/check")
    public R saveCheck(@RequestBody AllotSaveVo allotSaveVo) {
        //校验
        List<AllotRow> allotRows = Lists.newArrayList();
        allotRows.addAll(allotSaveVo.getAllotRowsUpdate());
        allotRows.addAll(allotSaveVo.getAllotRowsInsert());
        allotHeadService.checkSaveData(allotRows);
        return R.ok();
    }

    @ApiOperation(value = "保存或暂存")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/post/save")
    public R save(@RequestBody AllotSaveVo allotSaveVo) {
        List<AllotHead> allotHeads = allotSaveVo.getAllotHeads();
        List<String> list = allotHeads.stream().map(AllotHead::getTransferOrderNum).distinct().collect(Collectors.toList());
        allotHeadService.remove(new QueryWrapper<AllotHead>().lambda().in(AllotHead::getTransferOrderNum,list));
        if (CollectionUtils.isNotEmpty(allotHeads)) {
            for (AllotHead allotHead : allotHeads) {
                //删除挑选的缓存
                ProvisionalSingleOut provisionalSingleOut = new ProvisionalSingleOut();
                provisionalSingleOut.setTransferOrderNum(allotHead.getTransferOrderNum());
                provisionalSingleOutService.delCache(Arrays.asList(provisionalSingleOut));
                //删除头
                allotHeadService.remove(new QueryWrapper<AllotHead>().lambda().eq(AllotHead::getTransferOrderNum,allotHead.getTransferOrderNum()));
            }
            allotHeadService.saveBatch(allotSaveVo.getAllotHeads());
        }


        allotHeadService.repertoryUpdate(allotSaveVo);
        List<AllotRow> allotRowsDel =Lists.newArrayList();
        allotRowsDel.addAll(allotSaveVo.getAllotRowsDel()) ;
        allotRowsDel.addAll(allotSaveVo.getAllotRowsUpdate());
        for (AllotRow allotRow : allotRowsDel) {
            allotRowService.remove(new QueryWrapper<AllotRow>()
                    .lambda()
                    .eq(AllotRow::getTransferOrderNum,allotRow.getTransferOrderNum())
                    .eq(AllotRow::getTransferOrderRowNum,allotRow.getTransferOrderRowNum())
            );
        }
        List<AllotRow> allotRows = Lists.newArrayList();
        allotRows.addAll(allotSaveVo.getAllotRowsUpdate());
        allotRows.addAll(allotSaveVo.getAllotRowsInsert());
        allotRows.addAll(allotSaveVo.getAllotRowsDel());
        if (CollectionUtils.isNotEmpty(allotRows)) {
            allotRows.forEach(allotRow -> {
                int count =allotRowService.findMaterialCode(allotRow.getMaterialCode());
                if (count<1){
                    throw new ServiceException("填写的物料编码不存在！");
                }
            });
            allotRowService.saveBatch(allotRows);
        }
        return R.ok();
    }


    @ApiOperation(value = "导出")
    @Log(title = "fscm",businessType = BusinessType.EXPORT)
    @PostMapping("/download")
    public void download( HttpServletResponse response,@RequestBody AllotQueryVo allotQueryVo, @RequestParam(required = false) String delflag,@RequestParam(required = false) String temporary,@RequestParam(required = false) Boolean postFlag) throws IOException {
        Page<AllotShowVo> page = PageUtils.getPage(AllotShowVo.class);
        page.setSize(Long.MAX_VALUE);
        List<AllotShowVo> pageData= allotHeadService.queryData(allotQueryVo,delflag,temporary);
        pageData.stream().forEach(allotShowVo -> {
            List<PostCertificateRow> list = postCertificateRowService.list(new QueryWrapper<PostCertificateRow>()
                    .lambda()
                    .in(PostCertificateRow::getMoveType, Arrays.asList("311", "312"))
                    .eq(PostCertificateRow::getTransferOrderNum, allotShowVo.getTransferOrderNum())
                    .eq(PostCertificateRow::getTransferOrderRowNum, allotShowVo.getTransferOrderRowNum())
            );
            if (CollectionUtils.isNotEmpty(list)) {
                double sum = list.stream().map(PostCertificateRow::getPostQuantity).mapToDouble(m -> m).sum();
                allotShowVo.setPostQuantity(sum);
            }
            allotShowVo.setStockPCodeReceiveDesc(templateService.findStockPDescription(allotShowVo.getStockPCodeReceive()));
            allotShowVo.setStockPCodeShipDesc(templateService.findStockPDescription(allotShowVo.getStockPCodeShip()));
        });
        if (postFlag!=null&&postFlag) {
            pageData=pageData.stream().filter(p->p.getPostQuantity()!=0.0).collect(Collectors.toList());
        }
        if (allotQueryVo.getSearchVo()!=null) {
            pageData = SearchUtils.getByEntityFilter(allotQueryVo.getSearchVo(), pageData);

        }
        ExcelUtils.export3Excel(response,pageData, AllotShowVo.class,"调拨");
    }



    @ApiOperation(value = "调拨类型下拉")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/dropDownBox")
    public R dropDownBox( ) throws IOException {
        List<AllotConfig> list = allotConfigService.list();
        list=  list.stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(
                                Comparator.comparing(p ->
                                        p.getTransferType()
                                ))), ArrayList::new));
        return R.ok(list);
    }
}
