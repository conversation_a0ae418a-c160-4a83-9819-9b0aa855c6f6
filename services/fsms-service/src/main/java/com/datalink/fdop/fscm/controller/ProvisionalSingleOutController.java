package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.constant.Constants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.*;
import com.datalink.fdop.fscm.api.model.vo.RepertoryVo;
import com.datalink.fdop.fscm.api.model.vo.WorkOrderVo;
import com.datalink.fdop.fscm.service.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/11/24 15:32
 */
@RestController
@RequestMapping("/provisional/single/out")
@Api(tags = "临时挑选数据")
@Slf4j
public class ProvisionalSingleOutController {

    @Autowired
    private ProvisionalSingleOutService provisionalSingleOutService;
    @Autowired
    private ProvisionalSingleOutCacheService cacheService;
    @Autowired
    private RepertoryService repertoryService;

    @Autowired
    private RepertoryController repertoryController;
    @Autowired
    private TemplateService templateService;
    @Autowired
    private WorkOrderExcelService workOrderExcelService;
    @Autowired
    private WorkOrderStoreIssueService workOrderStoreIssueService;


    @ApiOperation(value = "保存临时库存")
    @Log(title = "fscm",businessType = BusinessType.INSERT)
    @PostMapping("/save")
    //@Transactional
    //@RepeatSubmit(interval = 1000)
    public R save(@RequestBody List<ProvisionalSingleOutCache> provisionalSingleOuts,@RequestParam String workOrderNum ,@RequestParam Long workOrderRowNum,@RequestParam Long workOrderChildrenNum, @RequestParam boolean dimensionFlag) {
        //校验
        List<String> failList =Lists.newArrayList();

        if (dimensionFlag) {
            List<ProvisionalSingleOutCache> provisionalSingleOutCaches=Lists.newArrayList();
            for (ProvisionalSingleOutCache singleOut : provisionalSingleOuts) {
                RepertoryVo repertoryVo=new RepertoryVo();
                repertoryVo.setFactoryCode(Arrays.asList(singleOut.getFactoryCode()));
                repertoryVo.setStockPCode(Arrays.asList(singleOut.getStockPCode()));
                //repertoryVo.setStockBPCode(Arrays.asList(singleOut.getStockBPCode()));
                repertoryVo.setMaterialCode(Arrays.asList(singleOut.getMaterialCode()));
                repertoryVo.setBatchNumber(Arrays.asList(singleOut.getBatchNumber()));
                R r = repertoryController.queryChange(repertoryVo, singleOut.getWorkOrderNum(), singleOut.getWorkOrderRowNum(), singleOut.getWorkOrderChildrenNum(), false,Long.MAX_VALUE,1l);
                if (r.getCode()!=200) {
                    throw new ServiceException("获取批次信息失败");
                }
                Map<String,Object> map = (Map<String,Object>)r.getData();
                if (map!=null) {
                    PageDataInfo<Repertory> data =(PageDataInfo<Repertory>) MapUtils.getObject(map, "data");
                    List<Repertory> totalList = data.getTotalList();
                    for (Repertory repertory : totalList) {
                        ProvisionalSingleOutCache provisionalSingleOutCache=new ProvisionalSingleOutCache();
                        provisionalSingleOutCache.setWorkOrderRowNum(workOrderRowNum);
                        provisionalSingleOutCache.setWorkOrderNum(workOrderNum);
                        provisionalSingleOutCache.setWorkOrderChildrenNum(workOrderChildrenNum);
                        provisionalSingleOutCache.setMaterialCode(repertory.getMaterialCode());
                        provisionalSingleOutCache.setMaterialDesc(repertory.getMaterialDesc());
                        provisionalSingleOutCache.setFactoryCode(repertory.getFactoryCode());
                        provisionalSingleOutCache.setStockPCode(repertory.getStockPCode());
                        provisionalSingleOutCache.setBatchNumber(repertory.getBatchNumber());
                        provisionalSingleOutCache.setPiece(repertory.getPiece());
                        provisionalSingleOutCache.setBinNum(repertory.getBinNum());
                        provisionalSingleOutCache.setQuantityConsume(repertory.getUnrestrictedStock());
                        provisionalSingleOutCache.setQuantityDelivery(repertory.getUnrestrictedStock());
                        provisionalSingleOutCache.setBasicUnit(repertory.getBasicUnit());
                        provisionalSingleOutCaches.add(provisionalSingleOutCache);
                    }
                    provisionalSingleOuts=provisionalSingleOutCaches;
                }
            }
        }


        provisionalSingleOuts.stream().forEach(provisionalSingleOut -> {
            try {
                //查重
                ProvisionalSingleOutCache singleOut = cacheService.getOne(new QueryWrapper<ProvisionalSingleOutCache>()
                        .lambda()
                        .eq(ProvisionalSingleOutCache::getWorkOrderNum, workOrderNum)
                        .eq(ProvisionalSingleOutCache::getWorkOrderRowNum, workOrderRowNum)
                        .eq(ProvisionalSingleOutCache::getWorkOrderChildrenNum, workOrderChildrenNum)
                        .eq(ProvisionalSingleOutCache::getMaterialCode, provisionalSingleOut.getMaterialCode())
                        .eq(ProvisionalSingleOutCache::getMaterialDesc, provisionalSingleOut.getMaterialDesc())
                        .eq(ProvisionalSingleOutCache::getFactoryCode, provisionalSingleOut.getFactoryCode())
                        .eq(ProvisionalSingleOutCache::getStockPCode, provisionalSingleOut.getStockPCode())
                        .eq(ProvisionalSingleOutCache::getBatchNumber, provisionalSingleOut.getBatchNumber())
                        .eq( ProvisionalSingleOutCache::getPiece, StringUtils.isNotEmpty(provisionalSingleOut.getPiece())?provisionalSingleOut.getPiece(): Constants.DATA_DEFAULT_VALUE)
                        .eq(ProvisionalSingleOutCache::getBinNum,StringUtils.isNotEmpty(provisionalSingleOut.getBinNum())? provisionalSingleOut.getBinNum(): Constants.DATA_DEFAULT_VALUE)
                );
                if (singleOut!=null) {
                    provisionalSingleOut.setQuantityDelivery(singleOut.getQuantityDelivery()+provisionalSingleOut.getQuantityDelivery());
                    provisionalSingleOut.setQuantityDeliveryVersions(singleOut.getQuantityDelivery());
                    cacheService.remove(new QueryWrapper<ProvisionalSingleOutCache>()
                            .lambda()
                            .eq(ProvisionalSingleOutCache::getWorkOrderNum, workOrderNum)
                            .eq(ProvisionalSingleOutCache::getWorkOrderRowNum, workOrderRowNum)
                            .eq(ProvisionalSingleOutCache::getWorkOrderChildrenNum, workOrderChildrenNum)
                            .eq(ProvisionalSingleOutCache::getMaterialCode, provisionalSingleOut.getMaterialCode())
                            .eq(ProvisionalSingleOutCache::getMaterialDesc, provisionalSingleOut.getMaterialDesc())
                            .eq(ProvisionalSingleOutCache::getFactoryCode, provisionalSingleOut.getFactoryCode())
                            .eq(ProvisionalSingleOutCache::getStockPCode, provisionalSingleOut.getStockPCode())
                            .eq(ProvisionalSingleOutCache::getBatchNumber, provisionalSingleOut.getBatchNumber())
                            .eq( ProvisionalSingleOutCache::getPiece, StringUtils.isNotEmpty(provisionalSingleOut.getPiece())?provisionalSingleOut.getPiece(): Constants.DATA_DEFAULT_VALUE)
                            .eq(ProvisionalSingleOutCache::getBinNum,StringUtils.isNotEmpty(provisionalSingleOut.getBinNum())? provisionalSingleOut.getBinNum(): Constants.DATA_DEFAULT_VALUE)
                    );
                }
                //临时表挑片数量
                List<ProvisionalSingleOut> list = provisionalSingleOutService.list(new QueryWrapper<ProvisionalSingleOut>()
                        .lambda()
                        .eq(ProvisionalSingleOut::getMaterialCode, provisionalSingleOut.getMaterialCode())
                        .eq(ProvisionalSingleOut::getFactoryCode, provisionalSingleOut.getFactoryCode())
                        .eq(ProvisionalSingleOut::getStockPCode, provisionalSingleOut.getStockPCode())
                        .eq(ProvisionalSingleOut::getBatchNumber, provisionalSingleOut.getBatchNumber())
                        .eq( ProvisionalSingleOut::getPiece, StringUtils.isNotEmpty(provisionalSingleOut.getPiece())?provisionalSingleOut.getPiece(): Constants.DATA_DEFAULT_VALUE)
                        .eq(ProvisionalSingleOut::getBinNum,StringUtils.isNotEmpty(provisionalSingleOut.getBinNum())? provisionalSingleOut.getBinNum(): Constants.DATA_DEFAULT_VALUE)
                        .ne(ProvisionalSingleOut::getWorkOrderNum, workOrderNum)
                        .ne(ProvisionalSingleOut::getWorkOrderRowNum, workOrderRowNum)
                        .ne(ProvisionalSingleOut::getWorkOrderChildrenNum, workOrderChildrenNum)
                );
                double sum = list.stream().map(ProvisionalSingleOut::getQuantityDelivery).mapToDouble(m->m).sum();
                //临时缓存表挑片数量
                List<ProvisionalSingleOutCache> cacheList = cacheService.list(new QueryWrapper<ProvisionalSingleOutCache>()
                        .lambda()
                        .eq(ProvisionalSingleOutCache::getMaterialCode, provisionalSingleOut.getMaterialCode())
                        .eq(ProvisionalSingleOutCache::getFactoryCode, provisionalSingleOut.getFactoryCode())
                        .eq(ProvisionalSingleOutCache::getStockPCode, provisionalSingleOut.getStockPCode())
                        .eq(ProvisionalSingleOutCache::getBatchNumber, provisionalSingleOut.getBatchNumber())
                        .eq( ProvisionalSingleOutCache::getPiece, StringUtils.isNotEmpty(provisionalSingleOut.getPiece())?provisionalSingleOut.getPiece(): Constants.DATA_DEFAULT_VALUE)
                        .eq(ProvisionalSingleOutCache::getBinNum,StringUtils.isNotEmpty(provisionalSingleOut.getBinNum())? provisionalSingleOut.getBinNum(): Constants.DATA_DEFAULT_VALUE)
                );
                double cacheSum = cacheList.stream().map(ProvisionalSingleOutCache::getQuantityDelivery).mapToDouble(m->m).sum();
                //库存表挑片数量
                List<Repertory> repertories = repertoryService.list(new QueryWrapper<Repertory>()
                        .lambda()
                        .eq(Repertory::getMaterialCode, provisionalSingleOut.getMaterialCode())
                        .eq(Repertory::getFactoryCode, provisionalSingleOut.getFactoryCode())
                        .eq(Repertory::getStockPCode, provisionalSingleOut.getStockPCode())
                        .eq( Repertory::getBatchNumber, StringUtils.isNotEmpty(provisionalSingleOut.getBatchNumber())?provisionalSingleOut.getBatchNumber(): Constants.DATA_DEFAULT_VALUE)
                        .eq( Repertory::getPiece, StringUtils.isNotEmpty(provisionalSingleOut.getPiece())?provisionalSingleOut.getPiece(): Constants.DATA_DEFAULT_VALUE)
                        .eq(Repertory::getBinNum,StringUtils.isNotEmpty(provisionalSingleOut.getBinNum())? provisionalSingleOut.getBinNum(): Constants.DATA_DEFAULT_VALUE)
                );
                double sum1 = repertories.stream().map(Repertory::getUnrestrictedStock).mapToDouble(m ->m).sum();
                if (sum1-sum-cacheSum<provisionalSingleOut.getQuantityDelivery()) {
                    throw new ServiceException("发料物料编码&"+provisionalSingleOut.getMaterialCode()+"&批次号&"+(StringUtils.isEmpty(provisionalSingleOut.getBatchNumber())?' ':provisionalSingleOut.getBatchNumber())
                            +"&片号&"+(StringUtils.isEmpty(provisionalSingleOut.getPiece())?' ':provisionalSingleOut.getPiece())
                            +"&BIN号"+(StringUtils.isEmpty(provisionalSingleOut.getBinNum())?' ':provisionalSingleOut.getBinNum())
                            +"的库存数据被其他用户先挑选，只剩"+String.valueOf(sum1-sum-cacheSum));
                }
                if (provisionalSingleOut.getQuantityDelivery()==null||provisionalSingleOut.getQuantityDelivery()==0.0) {
                    throw new ServiceException("工单行&"+provisionalSingleOut.getWorkOrderRowNum() +"&的工单子件行&"+provisionalSingleOut.getWorkOrderChildrenNum()+"&还未挑选库存，无法保存，请检查！");
                }
                cacheService.save(provisionalSingleOut);
            }catch (ServiceException e){
                e.printStackTrace();
                failList.add(e.getMessage());
            }catch (Exception e){
                e.printStackTrace();
                failList.add("发料物料编码&"+provisionalSingleOut.getMaterialCode()+"&批次号&"+(StringUtils.isEmpty(provisionalSingleOut.getBatchNumber())?' ':provisionalSingleOut.getBatchNumber())
                        +"&片号&"+(StringUtils.isEmpty(provisionalSingleOut.getPiece())?' ':provisionalSingleOut.getPiece())
                        +"&BIN号"+(StringUtils.isEmpty(provisionalSingleOut.getBinNum())?' ':provisionalSingleOut.getBinNum()));
            }

        });
        if (CollectionUtils.isNotEmpty(failList)) {
            return R.fail(failList);
        }else {
            return R.ok();
        }

    }

    @ApiOperation(value = "其他页面保存临时库存")
    @Log(title = "fscm",businessType = BusinessType.INSERT)
    @PostMapping("/other/save")
    @Transactional
    //@RepeatSubmit(interval = 1000)
    public R saveOther(@RequestBody List<ProvisionalSingleOut> provisionalSingleOuts,@RequestParam boolean dimensionFlag) {
        //校验

        if (dimensionFlag) {
            List<ProvisionalSingleOut> provisionalSingleOutCaches=Lists.newArrayList();
            for (ProvisionalSingleOut singleOut : provisionalSingleOuts) {
                RepertoryVo repertoryVo=new RepertoryVo();
                repertoryVo.setFactoryCode(Arrays.asList(singleOut.getFactoryCode()));
                repertoryVo.setStockPCode(Arrays.asList(singleOut.getStockPCode()));
                //repertoryVo.setStockBPCode(Arrays.asList(singleOut.getStockBPCode()));
                repertoryVo.setMaterialCode(Arrays.asList(singleOut.getMaterialCode()));
                repertoryVo.setBatchNumber(Arrays.asList(singleOut.getBatchNumber()));
                R r = repertoryController.querySubtract(repertoryVo, singleOut.getWorkOrderNum(), singleOut.getWorkOrderRowNum(), singleOut.getWorkOrderChildrenNum(), false,Long.MAX_VALUE,1l);
                if (r.getCode()!=200) {
                    throw new ServiceException("获取批次信息失败");
                }
                Map<String,Object> map = (Map<String,Object>)r.getData();
                if (map!=null) {
                    PageDataInfo<Repertory> data =(PageDataInfo<Repertory>) MapUtils.getObject(map, "data");
                    List<Repertory> totalList = data.getTotalList();
                    for (Repertory repertory : totalList) {
                        ProvisionalSingleOut provisionalSingleOut=new ProvisionalSingleOut();
                        BeanUtils.copyProperties(singleOut,provisionalSingleOut);
                        provisionalSingleOut.setMaterialCode(repertory.getMaterialCode());
                        provisionalSingleOut.setMaterialDesc(repertory.getMaterialDesc());
                        provisionalSingleOut.setFactoryCode(repertory.getFactoryCode());
                        provisionalSingleOut.setStockPCode(repertory.getStockPCode());
                        provisionalSingleOut.setBatchNumber(repertory.getBatchNumber());
                        provisionalSingleOut.setPiece(repertory.getPiece());
                        provisionalSingleOut.setBinNum(repertory.getBinNum());
                        provisionalSingleOut.setQuantityDelivery(repertory.getUnrestrictedStock());
                        provisionalSingleOut.setBasicUnit(repertory.getBasicUnit());
                        provisionalSingleOutCaches.add(provisionalSingleOut);
                    }
                    provisionalSingleOuts=provisionalSingleOutCaches;
                }
            }
        }
        //校验
        List<String> failList =Lists.newArrayList();
        provisionalSingleOuts.stream().forEach(provisionalSingleOut -> {
            try {

                //临时表挑片数量
                List<ProvisionalSingleOut> list = provisionalSingleOutService.list(new QueryWrapper<ProvisionalSingleOut>()
                        .lambda()
                        .eq(ProvisionalSingleOut::getMaterialCode, provisionalSingleOut.getMaterialCode())
                        .eq(ProvisionalSingleOut::getFactoryCode, provisionalSingleOut.getFactoryCode())
                        .eq(ProvisionalSingleOut::getStockPCode, provisionalSingleOut.getStockPCode())
                        .eq(ProvisionalSingleOut::getBatchNumber, provisionalSingleOut.getBatchNumber())
                        .eq(ProvisionalSingleOut::getPiece, StringUtils.isNotEmpty(provisionalSingleOut.getPiece())?provisionalSingleOut.getPiece(): Constants.DATA_DEFAULT_VALUE)
                        .eq(ProvisionalSingleOut::getBinNum,StringUtils.isNotEmpty(provisionalSingleOut.getBinNum())? provisionalSingleOut.getBinNum(): Constants.DATA_DEFAULT_VALUE)

                );
                double sum = list.stream().map(ProvisionalSingleOut::getQuantityDelivery).mapToDouble(m->m).sum();
                //库存表挑片数量
                List<Repertory> repertories = repertoryService.list(new QueryWrapper<Repertory>()
                        .lambda()
                        .eq(Repertory::getMaterialCode, provisionalSingleOut.getMaterialCode())
                        .eq(Repertory::getFactoryCode, provisionalSingleOut.getFactoryCode())
                        .eq(Repertory::getStockPCode, provisionalSingleOut.getStockPCode())
                        .eq( Repertory::getBatchNumber, StringUtils.isNotEmpty(provisionalSingleOut.getBatchNumber())?provisionalSingleOut.getBatchNumber(): Constants.DATA_DEFAULT_VALUE)
                        .eq( Repertory::getPiece, StringUtils.isNotEmpty(provisionalSingleOut.getPiece())?provisionalSingleOut.getPiece(): Constants.DATA_DEFAULT_VALUE)
                        .eq(Repertory::getBinNum,StringUtils.isNotEmpty(provisionalSingleOut.getBinNum())? provisionalSingleOut.getBinNum(): Constants.DATA_DEFAULT_VALUE)
                );
                double sum1 = repertories.stream().map(Repertory::getUnrestrictedStock).mapToDouble(m ->m).sum();
                if ((sum1-sum<=0&&provisionalSingleOut.getQuantityDelivery()>0)||provisionalSingleOut.getQuantityDelivery()>sum1-sum) {
                    throw new ServiceException("发料物料编码&"+provisionalSingleOut.getMaterialCode()+"&批次号&"+(StringUtils.isEmpty(provisionalSingleOut.getBatchNumber())?' ':provisionalSingleOut.getBatchNumber())
                            +"&片号&"+(StringUtils.isEmpty(provisionalSingleOut.getPiece())?' ':provisionalSingleOut.getPiece())
                            +"&BIN号"+(StringUtils.isEmpty(provisionalSingleOut.getBinNum())?' ':provisionalSingleOut.getBinNum())
                            +"的库存数据被其他用户先挑选，只剩"+String.valueOf(sum1-sum));
                }
                if (provisionalSingleOut.getQuantityDelivery()==null||provisionalSingleOut.getQuantityDelivery()==0.0) {
                    throw new ServiceException("还未挑选库存，无法保存，请检查！");
                }
                provisionalSingleOutService.save(provisionalSingleOut);
            }catch (ServiceException e){
                e.printStackTrace();
                failList.add(e.getMessage());
            }catch (Exception e){
                e.printStackTrace();
                failList.add("发料物料编码&"+provisionalSingleOut.getMaterialCode()+"&批次号&"+(StringUtils.isEmpty(provisionalSingleOut.getBatchNumber())?' ':provisionalSingleOut.getBatchNumber())
                        +"&片号&"+(StringUtils.isEmpty(provisionalSingleOut.getPiece())?' ':provisionalSingleOut.getPiece())
                        +"&BIN号"+(StringUtils.isEmpty(provisionalSingleOut.getBinNum())?' ':provisionalSingleOut.getBinNum())
                        +"数据有误！"
                );
            }

        });
        if (CollectionUtils.isNotEmpty(failList)) {
            return R.fail(failList);
        }else {
            return R.ok();
        }

    }


    @ApiOperation(value = "回滚子件行临时挑片")
    @Log(title = "fscm",businessType = BusinessType.INSERT)
    @PostMapping("/del")
    @Transactional
    public R del(@RequestBody ProvisionalSingleOutCache provisionalSingleOut) {
        List<ProvisionalSingleOutCache> singleOuts = cacheService.list(new QueryWrapper<ProvisionalSingleOutCache>()
                .lambda()
                .eq(ProvisionalSingleOutCache::getWorkOrderNum, provisionalSingleOut.getWorkOrderNum())
                .eq(ProvisionalSingleOutCache::getWorkOrderRowNum, provisionalSingleOut.getWorkOrderRowNum())
                .eq(ProvisionalSingleOutCache::getWorkOrderChildrenNum, provisionalSingleOut.getWorkOrderChildrenNum())
        );
        singleOuts.forEach(singleOut -> {
            if (singleOut.getQuantityDeliveryVersions()!=null) {
                singleOut.setQuantityDelivery(singleOut.getQuantityDeliveryVersions());
                singleOut.setQuantityDeliveryVersions(null);
                cacheService.update(Wrappers.lambdaUpdate(ProvisionalSingleOutCache.class)
                        .eq(ProvisionalSingleOutCache::getWorkOrderNum, singleOut.getWorkOrderNum())
                        .eq(ProvisionalSingleOutCache::getWorkOrderRowNum, singleOut.getWorkOrderRowNum())
                        .eq(ProvisionalSingleOutCache::getWorkOrderChildrenNum, singleOut.getWorkOrderChildrenNum())
                        .eq(ProvisionalSingleOutCache::getMaterialCode, singleOut.getMaterialCode())
                        .eq(ProvisionalSingleOutCache::getMaterialDesc, singleOut.getMaterialDesc())
                        .eq(ProvisionalSingleOutCache::getFactoryCode, singleOut.getFactoryCode())
                        .eq(ProvisionalSingleOutCache::getStockPCode, singleOut.getStockPCode())
                        .eq(ProvisionalSingleOutCache::getBatchNumber, singleOut.getBatchNumber())
                        .eq( ProvisionalSingleOutCache::getPiece, StringUtils.isNotEmpty(singleOut.getPiece())?singleOut.getPiece(): Constants.DATA_DEFAULT_VALUE)
                        .eq(ProvisionalSingleOutCache::getBinNum,StringUtils.isNotEmpty(singleOut.getBinNum())? singleOut.getBinNum(): Constants.DATA_DEFAULT_VALUE)
                        .set(ProvisionalSingleOutCache::getQuantityDelivery,singleOut.getQuantityDeliveryVersions())
                        .set(ProvisionalSingleOutCache::getQuantityDeliveryVersions,null)
                );
            }else {
                cacheService.remove( new QueryWrapper<ProvisionalSingleOutCache>()
                        .lambda()
                        .eq(ProvisionalSingleOutCache::getWorkOrderNum, singleOut.getWorkOrderNum())
                        .eq(ProvisionalSingleOutCache::getWorkOrderRowNum, singleOut.getWorkOrderRowNum())
                        .eq(ProvisionalSingleOutCache::getWorkOrderChildrenNum, singleOut.getWorkOrderChildrenNum())
                        .eq(ProvisionalSingleOutCache::getMaterialCode, singleOut.getMaterialCode())
                        .eq(ProvisionalSingleOutCache::getMaterialDesc, singleOut.getMaterialDesc())
                        .eq(ProvisionalSingleOutCache::getFactoryCode, singleOut.getFactoryCode())
                        .eq(ProvisionalSingleOutCache::getStockPCode, singleOut.getStockPCode())
                        .eq(ProvisionalSingleOutCache::getBatchNumber, singleOut.getBatchNumber())
                        .eq( ProvisionalSingleOutCache::getPiece, StringUtils.isNotEmpty(singleOut.getPiece())?singleOut.getPiece(): Constants.DATA_DEFAULT_VALUE)
                        .eq(ProvisionalSingleOutCache::getBinNum,StringUtils.isNotEmpty(singleOut.getBinNum())? singleOut.getBinNum(): Constants.DATA_DEFAULT_VALUE)
                );
            }
        });
        return R.ok();
    }

    @ApiOperation(value = "变更单刷新发料单至临时挑片表")
    @Log(title = "fscm",businessType = BusinessType.INSERT)
    @PostMapping("/change/refresh/cache")
    @Transactional
    public R changeRefreshCache(@RequestParam String workOrderNum) {
        provisionalSingleOutService.changeRefreshCache(workOrderNum);
        return R.ok();
    }
    @ApiOperation(value = "变更单刷新发料单至临时挑片表2")
    @Log(title = "fscm",businessType = BusinessType.INSERT)
    @PostMapping("/change/refresh/cache/2")
    @Transactional
    public R changeRefreshCache2(@RequestBody ProvisionalSingleOut provisionalSingleOut) {
        provisionalSingleOutService.changeRefreshCache2(provisionalSingleOut);
        return R.ok();
    }


    @ApiOperation(value = "单次操作回滚")
    @Log(title = "fscm",businessType = BusinessType.INSERT)
    @PostMapping("/once/del")
    @Transactional
    public R onceDel(@RequestParam String workOrderNum,@RequestBody List<Long> workOrderRowNums) {
        cacheService.remove(new QueryWrapper<ProvisionalSingleOutCache>()
                .lambda()
                .eq(ProvisionalSingleOutCache::getWorkOrderNum,workOrderNum)
                .in(ProvisionalSingleOutCache::getWorkOrderRowNum, workOrderRowNums)
        );
        return R.ok();
    }

    @ApiOperation(value = "取消临时挑片")
    @Log(title = "fscm",businessType = BusinessType.INSERT)
    @PostMapping("/cancel/del")
    @Transactional
    public R del(@RequestBody List<ProvisionalSingleOutCache> provisionalSingleOuts) {
        provisionalSingleOuts.forEach(provisionalSingleOut -> {
            cacheService.remove( new QueryWrapper<ProvisionalSingleOutCache>()
                    .lambda()
                    .eq(ProvisionalSingleOutCache::getWorkOrderNum, provisionalSingleOut.getWorkOrderNum())
                    .eq(ProvisionalSingleOutCache::getWorkOrderRowNum, provisionalSingleOut.getWorkOrderRowNum())
                    .eq(ProvisionalSingleOutCache::getWorkOrderChildrenNum, provisionalSingleOut.getWorkOrderChildrenNum())
                    .eq(ProvisionalSingleOutCache::getMaterialCode, provisionalSingleOut.getMaterialCode())
                    .eq(ProvisionalSingleOutCache::getMaterialDesc, provisionalSingleOut.getMaterialDesc())
                    .eq(ProvisionalSingleOutCache::getFactoryCode, provisionalSingleOut.getFactoryCode())
                    .eq(ProvisionalSingleOutCache::getStockPCode, provisionalSingleOut.getStockPCode())
                    .eq(ProvisionalSingleOutCache::getBatchNumber, provisionalSingleOut.getBatchNumber())
                    .eq(ProvisionalSingleOutCache::getPiece, StringUtils.isNotEmpty(provisionalSingleOut.getPiece())?provisionalSingleOut.getPiece(): Constants.DATA_DEFAULT_VALUE)
                    .eq(ProvisionalSingleOutCache::getBinNum,StringUtils.isNotEmpty(provisionalSingleOut.getBinNum())? provisionalSingleOut.getBinNum(): Constants.DATA_DEFAULT_VALUE)
            );
        });
        return R.ok();
    }

    @ApiOperation(value = "单次确认保存")
    @Log(title = "fscm",businessType = BusinessType.INSERT)
    @PostMapping("/once/save")
    @Transactional
    public R onceSave(@RequestParam String workOrderNum,@RequestBody List<Long> workOrderRowNums ) {
        workOrderRowNums.stream().forEach(row->{
            List<ProvisionalSingleOutCache> cacheList =cacheService.findCacheList(workOrderNum,row);
            List<Long> list= Lists.newArrayList();
            if (CollectionUtils.isEmpty(cacheList)) {
                provisionalSingleOutService.remove(new QueryWrapper<ProvisionalSingleOut>().lambda()
                        .eq(ProvisionalSingleOut::getWorkOrderNum,workOrderNum)
                        .eq(ProvisionalSingleOut::getWorkOrderRowNum,row)
                );
            }else {
                cacheList.stream().forEach(cache -> {
                    //校验同一工单行下是否都挑选完
                    long i = provisionalSingleOutService.getProvisionalSingleOutCacheCount(cache);
                    list.add(i);
                });
            }
            if (CollectionUtils.isNotEmpty(list)) {
                if (!list.stream().allMatch(i->i==list.get(0))) {
                    throw new ServiceException("工单行&"+row+"下的批次/片/BIN挑选不齐套！");
                }
            }
            //获取改工单行下的所有子件临时挑片
            List<ProvisionalSingleOut> singleOutList=provisionalSingleOutService.getProvisionalSingleOutByRow(workOrderNum,row);
            singleOutList.forEach(provisionalSingleOut -> {
                singleOutList.stream().forEach(pOut -> {
                    if (!provisionalSingleOut.getWorkOrderChildrenNum().equals(pOut.getWorkOrderChildrenNum())&&provisionalSingleOut.getMaterialCode().equals(pOut.getMaterialCode())&&provisionalSingleOut.getBatchNumber().equals(pOut.getBatchNumber())&&provisionalSingleOut.getStockPCode().equals(pOut.getStockPCode())){
                        if (!provisionalSingleOut.getQuantityDelivery().equals(pOut.getQuantityDelivery())) {
                            throw new ServiceException("工单行&"+pOut.getWorkOrderRowNum()+"&各子件行挑选的批次数量不一致，无法保存，请检查！");
                        }
                    }
                });
            });
        });
        provisionalSingleOutService.synchronizationProvisional(workOrderNum,workOrderRowNums);
        return R.ok();
    }
    @ApiOperation(value = "清除缓存")
    @Log(title = "fscm",businessType = BusinessType.INSERT)
    @PostMapping("/del/cache")
    @Transactional
    //@RepeatSubmit(interval = 1000)
    public R delCache(@RequestParam String workOrderNum) {
        cacheService.remove(new QueryWrapper<ProvisionalSingleOutCache>().lambda().eq(ProvisionalSingleOutCache::getWorkOrderNum,workOrderNum));
        return R.ok();
    }
    @ApiOperation(value = "同步缓存")
    @Log(title = "fscm",businessType = BusinessType.INSERT)
    @PostMapping("/synchronization/cache")
    @Transactional
    //@RepeatSubmit(interval = 1000)
    public R delCache(@RequestParam String workOrderNum,@RequestParam Long workOrderRowNum, @RequestParam Long workOrderChildrenNum) {
        //同步缓存表
        provisionalSingleOutService.synchronizationCache(workOrderNum,workOrderRowNum,workOrderChildrenNum);
        return R.ok();
    }



    @ApiOperation(value = "查询临时库存")
    @Log(title = "fscm",businessType = BusinessType.INSERT)
    @PostMapping("/query")
    @Transactional
    //@RepeatSubmit(interval = 1000)
    public R query(@RequestBody ProvisionalSingleOutCache provisionalSingleOut) {

        Page<ProvisionalSingleOutCache> page = PageUtils.getPage(ProvisionalSingleOutCache.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (provisionalSingleOut.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<ProvisionalSingleOutCache> pageData = cacheService.page(page, new QueryWrapper<ProvisionalSingleOutCache>()
                .lambda()
                .eq(StringUtils.isNotEmpty(provisionalSingleOut.getWorkOrderNum()), ProvisionalSingleOutCache::getWorkOrderNum, provisionalSingleOut.getWorkOrderNum())
                .eq(provisionalSingleOut.getWorkOrderRowNum()!=null, ProvisionalSingleOutCache::getWorkOrderRowNum, provisionalSingleOut.getWorkOrderRowNum())
                .eq(provisionalSingleOut.getWorkOrderChildrenNum()!=null, ProvisionalSingleOutCache::getWorkOrderChildrenNum, provisionalSingleOut.getWorkOrderChildrenNum())
                .orderByAsc(ProvisionalSingleOutCache::getStockPCode)
                .orderByAsc(ProvisionalSingleOutCache::getBatchNumber)
                .orderByAsc(ProvisionalSingleOutCache::getPiece)
                .orderByAsc(ProvisionalSingleOutCache::getBinNum)
        );
        Map<String, Object> map = Maps.newHashMap();
        List<ProvisionalSingleOutCache> data = pageData.getRecords();
        double sum = data.stream().map(ProvisionalSingleOutCache::getQuantityDelivery).mapToDouble(m -> m).sum();
        data.forEach(provisionalSingleOutCache -> {
            provisionalSingleOutCache.setStockPDescription(repertoryService.findSPDesc(provisionalSingleOutCache.getStockPCode()));
            provisionalSingleOutCache.setStockPDescription(templateService.findStockPDescription(provisionalSingleOutCache.getStockPCode()));
            provisionalSingleOutCache.setMaterialDesc(workOrderExcelService.findMaterialdesc(provisionalSingleOutCache.getMaterialCode()));
            List<WorkOrderStoreIssue> workOrderStoreIssues = workOrderStoreIssueService.list(new QueryWrapper<WorkOrderStoreIssue>()
                    .lambda()
                    .eq(WorkOrderStoreIssue::getWorkOrderNum, provisionalSingleOutCache.getWorkOrderNum())
                    .eq(WorkOrderStoreIssue::getWorkOrderRowNum, provisionalSingleOutCache.getWorkOrderRowNum())
                    .eq(WorkOrderStoreIssue::getWorkOrderChildrenNum, provisionalSingleOutCache.getWorkOrderChildrenNum())
                    .eq(WorkOrderStoreIssue::getMaterialCode, provisionalSingleOutCache.getMaterialCode())
                    .eq(WorkOrderStoreIssue::getStockPCode, provisionalSingleOutCache.getStockPCode())
                    .eq(WorkOrderStoreIssue::getBatchNumber, provisionalSingleOutCache.getBatchNumber())
                    .eq(WorkOrderStoreIssue::getPiece, StringUtils.isNotEmpty(provisionalSingleOutCache.getPiece())?provisionalSingleOutCache.getPiece(): Constants.DATA_DEFAULT_VALUE)
                    .eq(WorkOrderStoreIssue::getBinNum,StringUtils.isNotEmpty(provisionalSingleOutCache.getBinNum())? provisionalSingleOutCache.getBinNum(): Constants.DATA_DEFAULT_VALUE)
            );
            double sum1 = workOrderStoreIssues.stream().map(WorkOrderStoreIssue::getQuantityConsume).mapToDouble(m -> m).sum();
            provisionalSingleOutCache.setQuantityConsume(sum1);
        });
        if (provisionalSingleOut.getSearchVo()!=null) {
            data = SearchUtils.getByEntityFilter(provisionalSingleOut.getSearchVo(), data );
            data=data.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        map.put("data",PageUtils.getPageInfo(data,(int)pageData.getTotal()));
        map.put("count",sum);
        return R.ok(map);
    }

    @ApiOperation(value = "确认挑选完成")
    @Log(title = "fscm",businessType = BusinessType.INSERT)
    @PostMapping("/complete/selection")
    @Transactional
    public R completeSelection(@RequestBody WorkOrderVo workOrderVo) {

        //校验确认
        List<WorkOrderRow> rowList = workOrderVo.getWorkOrderRows();
        List<WorkOrderChild> childList = workOrderVo.getWorkOrderChildList();
        List<WorkOrderTest> workOrderTests = workOrderVo.getWorkOrderTests();
        //校验所有行是否挑选
        if (CollectionUtils.isNotEmpty(rowList)) {
            rowList.stream().forEach(row -> {
                List<Long> list = Lists.newArrayList();

                childList.stream().filter(workOrderChild -> workOrderChild.getWorkOrderRowNum().equals(row.getWorkOrderRowNum())).forEach(workOrderChild -> {
                    //校验同一工单行下是否都挑选完
                    long i = provisionalSingleOutService.getProvisionalSingleOutCount(workOrderChild);
                    list.add(i);
                });
                if (CollectionUtils.isNotEmpty(list)) {
                    if (!list.stream().allMatch(i -> i == list.get(0))) {
                        throw new ServiceException("工单行&" + row.getWorkOrderRowNum() + "下的批次/片/BIN挑选不齐套！");
                    }
                }
                //获取改工单行下的所有子件临时挑片
                List<ProvisionalSingleOut> singleOutList = provisionalSingleOutService.getProvisionalSingleOutByRow(row.getWorkOrderNum(), row.getWorkOrderRowNum());
                singleOutList.forEach(provisionalSingleOut -> {
                    singleOutList.stream().forEach(pOut -> {
                        if (!provisionalSingleOut.getWorkOrderChildrenNum().equals(pOut.getWorkOrderChildrenNum()) && provisionalSingleOut.getMaterialCode().equals(pOut.getMaterialCode()) && provisionalSingleOut.getBatchNumber().equals(pOut.getBatchNumber()) && provisionalSingleOut.getStockPCode().equals(pOut.getStockPCode())) {
                            if (!provisionalSingleOut.getQuantityDelivery() .equals( pOut.getQuantityDelivery())) {
                                throw new ServiceException("工单行&" + pOut.getWorkOrderRowNum() + "&各子件行挑选的批次数量不一致，无法保存，请检查！");
                            }
                        }
                    });
                });
            });


            //校验完成以后拆行
            List<WorkOrderRow> rowNewList = Lists.newArrayList();
            List<WorkOrderChild> childNewList = Lists.newArrayList();
            List<WorkOrderTest> testNewList = Lists.newArrayList();
            //算出行号的最大值
            long maxRowNum = rowList.stream().map(WorkOrderRow::getWorkOrderRowNum).mapToLong(m -> m).max().getAsLong();
            if (CollectionUtils.isNotEmpty(rowList)) {
                for (WorkOrderRow row : rowList) {
                    //计算出要拆多少行
                    List<ProvisionalSingleOut> singleOutList = provisionalSingleOutService.getSingleOutByRowGroupBy(row);
                    //去重子件号
                    List<Long> childrenNums = singleOutList.stream().map(ProvisionalSingleOut::getWorkOrderChildrenNum).distinct().collect(Collectors.toList());
                    LinkedList<Long> linkedList= Lists.newLinkedList();
                    Map<String, Long> childrenNumMap = Maps.newHashMap();
                    for (int i = 0; i < childrenNums.size(); i++) {
                        Long childrenNum = childrenNums.get(i);
                        long count = singleOutList.stream().map(ProvisionalSingleOut::getWorkOrderChildrenNum).filter(p -> p == childrenNum).count();
                        int flag=0;
                        for (long l = 0; l < count; l++) {

                            if (l > 0) {
                                if (i==0) {
                                    //拆工单行
                                    WorkOrderRow orderRow = new WorkOrderRow();
                                    BeanUtils.copyProperties(row, orderRow);
                                    maxRowNum = maxRowNum + 1L;
                                    orderRow.setWorkOrderRowNum(maxRowNum);
                                    rowNewList.add(orderRow);
                                    linkedList.add(Integer.valueOf(String.valueOf(l))-1,maxRowNum);
                                    //拆测试程序
                                    List<WorkOrderTest> workOrderTestList = workOrderTests.stream().filter(workOrderTest -> workOrderTest.getWorkOrderRowNum() == row.getWorkOrderRowNum()).collect(Collectors.toList());
                                    for (int z = 0; z < workOrderTestList.size(); z++) {
                                        WorkOrderTest workOrderTest = workOrderTestList.get(z);
                                        WorkOrderTest orderTest = new WorkOrderTest();
                                        BeanUtils.copyProperties(workOrderTest, orderTest);
                                        orderTest.setWorkOrderRowNum(maxRowNum);
                                        orderTest.setItemNum(Long.valueOf(z + 1));
                                        testNewList.add(orderTest);
                                    }
                                }else {
                                    linkedList.get(Integer.valueOf(String.valueOf(l))-1);
                                }
                                log.info("拆的第几行:"+l+",行号："+maxRowNum);



                                for (int o = 0; o < childrenNums.size(); o++) {
                                    Long cNum = childrenNums.get(o);
                                    //子件 根据编码，批次，发料 分组
                                    List<ProvisionalSingleOut> singleOutsGroupBy = provisionalSingleOutService.getProvisionalSingleOutByRowGroupBy(row.getWorkOrderNum(), row.getWorkOrderRowNum(), cNum);
                                    log.info("子件分组："+singleOutsGroupBy);
                                    if (CollectionUtils.isNotEmpty(singleOutsGroupBy)) {
                                        ProvisionalSingleOut singleOut = singleOutsGroupBy.get(0);
                                        //获取子件信息
                                        WorkOrderChild orderChild = childList.stream().filter(workOrderChild -> workOrderChild.getWorkOrderRowNum() == singleOut.getWorkOrderRowNum() && workOrderChild.getWorkOrderChildrenNum() == singleOut.getWorkOrderChildrenNum() && workOrderChild.getMaterialCode().equals(singleOut.getMaterialCode())).collect(Collectors.toList()).get(0);
                                        for (int ol = 0; ol < singleOutsGroupBy.size(); ol++) {
                                            if (ol==(l-flag)) {
                                                flag=flag+1;
                                                ProvisionalSingleOut provisionalSingleOut = singleOutsGroupBy.get(ol);
                                                log.info("修改挑片信息："+provisionalSingleOut);
                                                if (provisionalSingleOut != null) {

                                                    if (StringUtils.isEmpty(row.getWorkOrderNum())) {
                                                        throw new ServiceException("行项目工单号不能为空！");
                                                    }


                                                    //修改片的子项目号
                                                    List<ProvisionalSingleOut> provisionalSingleOutByChilds = provisionalSingleOutService.list(new QueryWrapper<ProvisionalSingleOut>()
                                                            .lambda()
                                                            .eq(ProvisionalSingleOut::getWorkOrderNum, provisionalSingleOut.getWorkOrderNum())
                                                            .eq(ProvisionalSingleOut::getWorkOrderRowNum, provisionalSingleOut.getWorkOrderRowNum())
                                                            .eq(ProvisionalSingleOut::getWorkOrderChildrenNum, provisionalSingleOut.getWorkOrderChildrenNum())
                                                            .eq(ProvisionalSingleOut::getMaterialCode, provisionalSingleOut.getMaterialCode())
                                                            .eq(ProvisionalSingleOut::getStockPCode, provisionalSingleOut.getStockPCode())
                                                            .eq(ProvisionalSingleOut::getBatchNumber, provisionalSingleOut.getBatchNumber())
                                                    );
                                                    WorkOrderChild child = new WorkOrderChild();
                                                    //拆子件行
                                                    BeanUtils.copyProperties(orderChild, child);
                                                    child.setWorkOrderRowNum(maxRowNum);
                                                    log.info("修改挑片信息行号："+maxRowNum);
                                                    if (childrenNumMap.get(String.valueOf(maxRowNum))==null) {
                                                        childrenNumMap.put(String.valueOf(maxRowNum),1l);
                                                    }else {
                                                        childrenNumMap.put(String.valueOf(maxRowNum),childrenNumMap.get(String.valueOf(maxRowNum))+1);
                                                    }
                                                    child.setWorkOrderChildrenNum(childrenNumMap.get(String.valueOf(maxRowNum)));

                                                    for (int j = 0; j < provisionalSingleOutByChilds.size(); j++) {
                                                        ProvisionalSingleOut oldSingleOut = provisionalSingleOutByChilds.get(j);
                                                        provisionalSingleOutService.update(Wrappers.lambdaUpdate(ProvisionalSingleOut.class)
                                                                .set(ProvisionalSingleOut::getWorkOrderRowNum, maxRowNum)
                                                                .set(ProvisionalSingleOut::getWorkOrderChildrenNum, child.getWorkOrderChildrenNum())
                                                                .eq(ProvisionalSingleOut::getWorkOrderNum, oldSingleOut.getWorkOrderNum())
                                                                .eq(ProvisionalSingleOut::getWorkOrderRowNum, oldSingleOut.getWorkOrderRowNum())
                                                                .eq(ProvisionalSingleOut::getWorkOrderChildrenNum, oldSingleOut.getWorkOrderChildrenNum())
                                                                .eq(ProvisionalSingleOut::getMaterialCode, oldSingleOut.getMaterialCode())
                                                                .eq(ProvisionalSingleOut::getMaterialDesc, oldSingleOut.getMaterialDesc())
                                                                .eq(ProvisionalSingleOut::getFactoryCode, oldSingleOut.getFactoryCode())
                                                                .eq(ProvisionalSingleOut::getStockPCode, oldSingleOut.getStockPCode())
                                                                .eq(ProvisionalSingleOut::getBatchNumber, oldSingleOut.getBatchNumber())
                                                                .eq(ProvisionalSingleOut::getPiece, oldSingleOut.getPiece())
                                                                .eq(ProvisionalSingleOut::getBinNum, oldSingleOut.getBinNum())
                                                        );
                                                        //将挑片信息塞入子件
                                                        provisionalSingleOutService.setVal(provisionalSingleOut, child,oldSingleOut);
                                                    }
                                                    childNewList.add(child);
                                                }
                                            }
                                        }
                                    }
                                }

                            } else {
                                List<ProvisionalSingleOut> singleOutsGroupBy = provisionalSingleOutService.getProvisionalSingleOutByRowGroupBy(row.getWorkOrderNum(), row.getWorkOrderRowNum(), childrenNum);
                                ProvisionalSingleOut singleOut = singleOutsGroupBy.get(0);
                                if (CollectionUtils.isNotEmpty(childList)) {
                                    List<WorkOrderChild> orderChildren = childList.stream().filter(workOrderChild -> workOrderChild.getWorkOrderRowNum() == singleOut.getWorkOrderRowNum() && workOrderChild.getWorkOrderChildrenNum() == singleOut.getWorkOrderChildrenNum() && workOrderChild.getMaterialCode().equals(singleOut.getMaterialCode())).collect(Collectors.toList());
                                    if (CollectionUtils.isNotEmpty(orderChildren)) {
                                        WorkOrderChild orderChild = orderChildren.get(0);
                                        provisionalSingleOutService.setVal(singleOutsGroupBy.get(0), orderChild, singleOutsGroupBy.get(0));
                                    }
                                }
                            }
                        }

                    }

                }
                rowList.addAll(rowNewList);
                childList.addAll(childNewList);
                workOrderTests.addAll(testNewList);
                //塞bin 片
                childList.stream().forEach(workOrderChild -> {
                    //查发料表
                    List<WorkOrderStoreIssue> workOrderStoreIssues = workOrderStoreIssueService.list(new QueryWrapper<WorkOrderStoreIssue>()
                            .lambda()
                            .eq(WorkOrderStoreIssue::getWorkOrderNum,workOrderChild.getWorkOrderNum())
                            .eq(WorkOrderStoreIssue::getWorkOrderRowNum,workOrderChild.getWorkOrderRowNum())
                            .eq(WorkOrderStoreIssue::getWorkOrderChildrenNum,workOrderChild.getWorkOrderChildrenNum())
                    );
                    List<ProvisionalSingleOut> singleOuts = provisionalSingleOutService.list(new QueryWrapper<ProvisionalSingleOut>()
                            .lambda()
                            .eq(ProvisionalSingleOut::getWorkOrderNum, workOrderChild.getWorkOrderNum())
                            .eq(ProvisionalSingleOut::getWorkOrderRowNum, workOrderChild.getWorkOrderRowNum())
                            .eq(ProvisionalSingleOut::getWorkOrderChildrenNum, workOrderChild.getWorkOrderChildrenNum())
                    );
                    List<ProvisionalSingleOutCache> outCaches = cacheService.list(new QueryWrapper<ProvisionalSingleOutCache>()
                            .lambda()
                            .eq(ProvisionalSingleOutCache::getWorkOrderNum, workOrderChild.getWorkOrderNum())
                            .eq(ProvisionalSingleOutCache::getWorkOrderRowNum, workOrderChild.getWorkOrderRowNum())
                            .eq(ProvisionalSingleOutCache::getWorkOrderChildrenNum, workOrderChild.getWorkOrderChildrenNum())
                    );
                    if (CollectionUtils.isNotEmpty(workOrderStoreIssues)||CollectionUtils.isNotEmpty(singleOuts)||CollectionUtils.isNotEmpty(outCaches)) {
                        workOrderChild.setPickFlag("1");
                    }
                    provisionalSingleOutService.setPieBin(workOrderChild);
                });
            }
            rowList.forEach(row->{

                row.setMaterialDesc(workOrderExcelService.findMaterialdesc(row.getMaterialCode()));
            });
        }
        return R.ok(workOrderVo);
    }




    @ApiOperation(value = "删除子件挑片缓存")
    @Log(title = "fscm",businessType = BusinessType.INSERT)
    @PostMapping("/del/child/cache")
    @Transactional
    public R delChildCache(@RequestBody List<ProvisionalSingleOut> provisionalSingleOuts) {
        provisionalSingleOuts.stream().forEach(provisionalSingleOut -> {
            //删除挑片
            provisionalSingleOutService.remove(new QueryWrapper<ProvisionalSingleOut>().lambda()
                    .eq(ProvisionalSingleOut::getWorkOrderNum,provisionalSingleOut.getWorkOrderNum())
                    .eq(ProvisionalSingleOut::getWorkOrderRowNum,provisionalSingleOut.getWorkOrderRowNum())
                    .eq(provisionalSingleOut.getWorkOrderChildrenNum()!=null,ProvisionalSingleOut::getWorkOrderChildrenNum,provisionalSingleOut.getWorkOrderChildrenNum())
            );
            //删除缓存挑片
            cacheService.remove(new QueryWrapper<ProvisionalSingleOutCache>().lambda()
                    .eq(ProvisionalSingleOutCache::getWorkOrderNum,provisionalSingleOut.getWorkOrderNum())
                    .eq(ProvisionalSingleOutCache::getWorkOrderRowNum,provisionalSingleOut.getWorkOrderRowNum())
                    .eq(provisionalSingleOut.getWorkOrderChildrenNum()!=null,ProvisionalSingleOutCache::getWorkOrderChildrenNum,provisionalSingleOut.getWorkOrderChildrenNum())
            );
        });
        return R.ok();
    }

    @ApiOperation(value = "删除挑片调拨，报废，领退，出货需求缓存盘盈亏过账")
    @Log(title = "fscm",businessType = BusinessType.INSERT)
    @PostMapping("/del/other/cache")
    @Transactional
    public R delCache(@RequestBody List<ProvisionalSingleOut> provisionalSingleOuts) {
        provisionalSingleOutService.delCache(provisionalSingleOuts);

        return R.ok();
    }

    @ApiOperation(value = "删除挑片调拨，报废，领退，出货需求缓存，支持批量删除")
    @Log(title = "fscm",businessType = BusinessType.INSERT)
    @PostMapping("/del/other/cache/batch")
    @Transactional
    public R delCacheBatch(@RequestBody List<ProvisionalSingleOut> provisionalSingleOuts,@RequestParam(required = false) Boolean delFlag,@RequestParam boolean dimensionFlag) {
        provisionalSingleOutService.delCache(provisionalSingleOuts);
        if (delFlag==null||!delFlag) {
            return this.saveOther(provisionalSingleOuts,dimensionFlag);
        }
        return R.ok();
    }


}
