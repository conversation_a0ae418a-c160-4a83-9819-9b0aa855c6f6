package com.datalink.fdop.fscm.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.model.vo.PostCertificateVo;
import com.datalink.fdop.fscm.service.PostCertificateHeadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2022/11/29 10:39
 */
@RestController
@RequestMapping("/post/certificate/head")
@Transactional
@Api(tags = "过账凭证头项目")
public class PostCertificateHeadController {

    @Autowired
    private PostCertificateHeadService postCertificateHeadService;
    @ApiOperation(value = "获取最大过帐凭证号")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/max/num")
    public R getMaxNum() {
        return R.ok(postCertificateHeadService.getMaxNum());
    }


    @ApiOperation(value = "过张凭证单张显示过账数据检查")
    @Log(title = "fscm")
    @PostMapping("/check/data")
    public R checkData(@RequestBody PostCertificateVo postCertificateVo)  {
        postCertificateHeadService.checkData(postCertificateVo);
        return R.ok();
    }

    @ApiOperation(value = "过张凭证单张显示过账数据过账")
    @Log(title = "fscm")
    @PostMapping("/post")
    public R post(@RequestBody PostCertificateVo postCertificateVo)  {
        postCertificateHeadService.post(postCertificateVo);
        return R.ok();
    }
}
