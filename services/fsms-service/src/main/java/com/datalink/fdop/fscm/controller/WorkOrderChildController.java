package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.WorkOrderChild;
import com.datalink.fdop.fscm.service.WorkOrderChildService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/11/24 15:32
 */
@RestController
@RequestMapping("/work/order/child")
@Transactional
@Api(tags = "工单子件")
public class WorkOrderChildController {

    @Autowired
    private  WorkOrderChildService workOrderChildService;


    @ApiOperation(value = "查询子件信息")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/query")
    public R query(@RequestBody WorkOrderChild workOrderChild) {
        Page<WorkOrderChild> page = PageUtils.getPage(WorkOrderChild.class);
        Page pageData = workOrderChildService.page(page,
                new QueryWrapper<WorkOrderChild>()
                        .lambda()
                        .eq(StringUtils.isNotEmpty(workOrderChild.getWorkOrderNum()),WorkOrderChild::getWorkOrderNum,workOrderChild.getWorkOrderNum())
                        .eq(workOrderChild.getWorkOrderRowNum()!=null,WorkOrderChild::getWorkOrderRowNum,workOrderChild.getWorkOrderRowNum())
                        .eq(workOrderChild.getWorkOrderChildrenNum()!=null,WorkOrderChild::getWorkOrderChildrenNum,workOrderChild.getWorkOrderChildrenNum())
        );

        return R.ok(PageUtils.getPageInfo(pageData.getRecords(),(int) pageData.getTotal()));
    }


    @ApiOperation(value = "获取工单子件项目号")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/get/child/num")
    public R getWorkChildNum(@RequestParam String workOrderNum,@RequestParam Long workOrderRowNum) {
        return R.ok(workOrderChildService.getWorkChildNum(workOrderNum,workOrderRowNum));
    }


    @ApiOperation(value = "工单行下的子件查询")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/row/query")
    public R workOrderHeadQuery(@RequestBody(required = false) WorkOrderChild workOrderChild) {
        Page<WorkOrderChild> page = PageUtils.getPage(WorkOrderChild.class);
        Page<WorkOrderChild> dataPage= workOrderChildService.page(page,new QueryWrapper<WorkOrderChild>()
                .lambda()
                .eq(StringUtils.isNotEmpty(workOrderChild.getWorkOrderNum()),WorkOrderChild::getWorkOrderNum,workOrderChild.getWorkOrderNum())
                .eq(workOrderChild.getWorkOrderRowNum()!=null,WorkOrderChild::getWorkOrderRowNum,workOrderChild.getWorkOrderRowNum())
        );
        List<WorkOrderChild> records = dataPage.getRecords();
        records.stream().forEach(workOrderChild1 -> {
            workOrderChild1.setWorkOrderChildrenNum(workOrderChildService.getWorkChildNum(workOrderChild1.getWorkOrderNum(),workOrderChild1.getWorkOrderRowNum()));

        });

        return R.ok(PageUtils.getPageInfo(records,(int)dataPage.getTotal()));
    }

}
