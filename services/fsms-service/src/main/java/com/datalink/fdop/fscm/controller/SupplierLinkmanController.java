package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.SupplierLinkman;
import com.datalink.fdop.fscm.mapper.FSCMTemplateMapper;
import com.datalink.fdop.fscm.mapper.WorkOrderExcelMapper;
import com.datalink.fdop.fscm.service.SupplierLinkmanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/2/15 14:33
 */
@RestController
@RequestMapping("/supplier/linkman")
@Transactional
@Api(tags = "供应商联系人")
public class SupplierLinkmanController {

    @Autowired
    private SupplierLinkmanService supplierLinkmanService;
    @Autowired
    FSCMTemplateMapper fscmTemplateMapper;
    @Autowired
    WorkOrderExcelMapper workOrderExcelMapper;


    @ApiOperation(value = "查询")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/query")
    public R query(@RequestBody(required = false) SearchVo searchVo,@RequestParam(required = false) String supplierCode,@RequestParam(required = false) String manufCode,@RequestParam(required = false) String manufType) throws Exception {
        Page<SupplierLinkman> page = PageUtils.getPage(SupplierLinkman.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (searchVo!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<SupplierLinkman> pageData=supplierLinkmanService.page(page,new QueryWrapper<SupplierLinkman>()
                .lambda()
                .eq(StringUtils.isNotEmpty(supplierCode),SupplierLinkman::getSupplierCode,supplierCode)
                .eq(StringUtils.isNotEmpty(manufCode),SupplierLinkman::getManufCode,manufCode)
                .eq(StringUtils.isNotEmpty(manufType),SupplierLinkman::getManufType,manufType)
        );
        List<SupplierLinkman> records = pageData.getRecords();
        for (SupplierLinkman record : records) {
            record.setSupplierName(fscmTemplateMapper.findSupplierName(record.getSupplierCode()));
            record.setManufName(workOrderExcelMapper.findManufName(record.getManufCode(), record.getManufType()));

        }
        if (searchVo!=null) {
            records = SearchUtils.getByEntityFilter(searchVo, records);
            pageData.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records, (int) pageData.getTotal()));
    }


    @ApiOperation(value = "新增")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/create")
    @Transactional
    public R create(@RequestBody List<SupplierLinkman> supplierLinkmens) {
        for (SupplierLinkman supplierLinkmen : supplierLinkmens) {
            SupplierLinkman one = supplierLinkmanService.getOne(new QueryWrapper<SupplierLinkman>().lambda()
                    .eq(SupplierLinkman::getSupplierCode,supplierLinkmen.getSupplierCode())
                    .eq(SupplierLinkman::getManufCode,supplierLinkmen.getManufCode())
                    .eq(SupplierLinkman::getManufType,supplierLinkmen.getManufType())
                    .eq(SupplierLinkman::getSerialNum,supplierLinkmen.getSerialNum())
            );
            if (one!=null) {
                throw new ServiceException("主键重复，请检查");
            }
        }
        supplierLinkmanService.saveBatch(supplierLinkmens);
        return R.ok("保存成功");
    }


    @ApiOperation(value = "修改")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    @Transactional
    public R update(@RequestBody List<SupplierLinkman> supplierLinkmens) {
        for (SupplierLinkman supplierLinkmen : supplierLinkmens) {
            supplierLinkmanService.update(Wrappers.lambdaUpdate(SupplierLinkman.class)
                    .eq(SupplierLinkman::getSupplierCode,supplierLinkmen.getSupplierCode())
                    .eq(SupplierLinkman::getManufCode,supplierLinkmen.getManufCode())
                    .eq(SupplierLinkman::getManufType,supplierLinkmen.getManufType())
                    .eq(SupplierLinkman::getSerialNum,supplierLinkmen.getSerialNum())
                    .set(SupplierLinkman::getName,supplierLinkmen.getName())
                    .set(SupplierLinkman::getPhone,supplierLinkmen.getPhone())
                    .set(SupplierLinkman::getEmail,supplierLinkmen.getEmail())
                    .set(SupplierLinkman::getDepartment,supplierLinkmen.getDepartment())
                    .set(SupplierLinkman::getPost,supplierLinkmen.getPost())
                    .set(SupplierLinkman::getDefaultLinkman,supplierLinkmen.getDefaultLinkman())
            );
        }

        return R.ok("修改成功");
    }



    @ApiOperation(value = "删除")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/del")
    @Transactional
    public R del(@RequestBody List<SupplierLinkman> supplierLinkmens) {
        for (SupplierLinkman supplierLinkmen : supplierLinkmens) {
            supplierLinkmanService.remove(new QueryWrapper<SupplierLinkman>().lambda()
                    .eq(SupplierLinkman::getSupplierCode,supplierLinkmen.getSupplierCode())
                    .eq(SupplierLinkman::getManufCode,supplierLinkmen.getManufCode())
                    .eq(SupplierLinkman::getManufType,supplierLinkmen.getManufType())
                    .eq(SupplierLinkman::getSerialNum,supplierLinkmen.getSerialNum())
            );
        }

        return R.ok("修改成功");
    }

    @ApiOperation(value = "导出")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/download")
    public void download(HttpServletResponse response, @RequestBody(required = false) SearchVo searchVo,@RequestParam(required = false) String supplierCode,@RequestParam(required = false) String manufCode,@RequestParam(required = false) String manufType) throws IOException {
        Page<SupplierLinkman> page = PageUtils.getPage(SupplierLinkman.class);
        if (searchVo!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<SupplierLinkman> pageData=supplierLinkmanService.page(page,new QueryWrapper<SupplierLinkman>()
                .lambda()
                .eq(StringUtils.isNotEmpty(supplierCode),SupplierLinkman::getSupplierCode,supplierCode)
                .eq(StringUtils.isNotEmpty(manufCode),SupplierLinkman::getManufCode,manufCode)
                .eq(StringUtils.isNotEmpty(manufType),SupplierLinkman::getManufType,manufType)
        );
        List<SupplierLinkman> records = pageData.getRecords();
        for (SupplierLinkman record : records) {
            record.setSupplierName(fscmTemplateMapper.findSupplierName(record.getSupplierCode()));
            record.setManufName(workOrderExcelMapper.findManufName(record.getManufCode(), record.getManufType()));

        }
        if (searchVo!=null) {
            records = SearchUtils.getByEntityFilter(searchVo, records);
        }
        ExcelUtils.export3Excel(response,records, SupplierLinkman.class,"供应商联系人");
    }

}
