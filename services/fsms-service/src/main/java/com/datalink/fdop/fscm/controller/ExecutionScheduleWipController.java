package com.datalink.fdop.fscm.controller;

import io.swagger.annotations.Api;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2023/2/15 16:30
 */
@RestController
@RequestMapping("/execution/schedule/wip")
@Transactional
@Api(tags = "工单执行进度WIP数据")
public class ExecutionScheduleWipController {
}
