package com.datalink.fdop.settlement.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.idempotent.annotation.RepeatSubmit;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.drive.api.RemoteJdbcService;
import com.datalink.fdop.settlement.api.domain.PriceDefinition;
import com.datalink.fdop.settlement.api.domain.PriceElement;
import com.datalink.fdop.settlement.api.model.vo.PriceSelectVo;
import com.datalink.fdop.settlement.listener.NoModelDataListener;
import com.datalink.fdop.settlement.service.PriceDefinitionService;
import com.datalink.fdop.settlement.service.PriceElementService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/settlement/price/definition")
@Transactional
@Api(tags = "价格因子")
public class PriceDefinitionController {

    @Autowired
    private PriceDefinitionService priceDefinitionService;
    @Autowired
    private PriceElementService priceElementService;

    @Autowired
    private RemoteJdbcService remoteJdbcService;

    private static final String realPath = "/settlement/admin/resources";

    @ApiOperation(value = "查询价格因子")
    @Log(title = "settlement", businessType = BusinessType.UPDATE)
    @PostMapping("/query")
    public R query(@RequestBody(required = false) PriceDefinition priceDefinition) {
        Page<PriceDefinition> page = PageUtils.getPage(PriceDefinition.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        SearchVo searchVo = priceDefinition.getSearchVo();
        if (searchVo != null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<PriceDefinition> pageData = priceDefinitionService.page(page);
        List<PriceDefinition> records = pageData.getRecords();
        if (searchVo != null) {
            records = SearchUtils.getByEntityFilter(searchVo, records);
            page.setTotal(records.size());
            records = records.stream().skip(pageSize * (current - 1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records, (int) pageData.getTotal()));
    }

    @ApiOperation(value = "新增价格因子")
    @Log(title = "settlement", businessType = BusinessType.UPDATE)
    @PostMapping("/create")
    public R create(@RequestBody List<PriceDefinition> priceDefinitions) {
        for (PriceDefinition priceDefinition : priceDefinitions) {
            if (priceDefinition.getPriceFactorType().equals("2")) {
                List<PriceElement> priceElements = priceDefinition.getPriceElements();
                for (PriceElement priceElement : priceElements) {
                    priceElementService.remove(new QueryWrapper<PriceElement>().lambda()
                            .eq(PriceElement::getPriceFactor, priceDefinition.getPriceFactor())
                    );
                }
                priceDefinitionService.remove(new QueryWrapper<PriceDefinition>().lambda()
                        .eq(PriceDefinition::getPriceFactor, priceDefinition.getPriceFactor())
                );
                priceElementService.saveBatch(priceElements);
            }
        }
        if (priceDefinitionService.saveBatch(priceDefinitions)) {
            //创建表
            priceDefinitionService.createTable(priceDefinitions);
            return R.ok("保存成功");
        } else {
            return R.fail("保存失败");
        }
    }

    @ApiOperation(value = "修改")
    @Log(title = "settlement", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public R update(@RequestBody List<PriceDefinition> priceDefinitions) {
        for (PriceDefinition priceDefinition : priceDefinitions) {
            List<PriceElement> priceElements = priceDefinition.getPriceElements();
            if (CollectionUtils.isNotEmpty(priceElements)) {
                for (PriceElement priceElement : priceElements) {
                    priceElementService.update(new UpdateWrapper<PriceElement>().lambda()
                            .set(StringUtils.isNotEmpty(priceElement.getChangingFactorDesc()), PriceElement::getChangingFactorDesc, priceElement.getChangingFactorDesc())
                            .eq(PriceElement::getPriceFactor, priceElement.getPriceFactor())
                            .eq(PriceElement::getChangingFactor, priceElement.getChangingFactor())
                    );
                }
            }
            priceDefinitionService.update(new UpdateWrapper<PriceDefinition>().lambda()
                    .set(StringUtils.isNotEmpty(priceDefinition.getPriceFactorDesc()), PriceDefinition::getPriceFactorDesc, priceDefinition.getPriceFactorDesc())
                    .set(StringUtils.isNotEmpty(priceDefinition.getPriceFactorType()), PriceDefinition::getPriceFactorType, priceDefinition.getPriceFactorType())
                    .set(StringUtils.isNotEmpty(priceDefinition.getTable()), PriceDefinition::getTable, priceDefinition.getTable())
                    .eq(PriceDefinition::getPriceFactor, priceDefinition.getPriceFactor())
            );
        }
        return R.ok("修改成功");
    }

    @ApiOperation(value = "删除")
    @Log(title = "settlement", businessType = BusinessType.UPDATE)
    @PostMapping("/del")
    public R del(@RequestBody List<PriceDefinition> priceDefinitions) {
        for (PriceDefinition priceDefinition : priceDefinitions) {
            priceElementService.remove(new QueryWrapper<PriceElement>().lambda()
                    .eq(PriceElement::getPriceFactor, priceDefinition.getPriceFactor()));
            priceDefinitionService.remove(new QueryWrapper<PriceDefinition>().lambda()
                    .eq(PriceDefinition::getPriceFactor, priceDefinition.getPriceFactor())
            );
            //删表
            if (priceDefinition.getPriceFactorType().equals("2")) {
                priceDefinitionService.delTable(priceDefinition);
            }

        }

        return R.ok();

    }

    @ApiOperation(value = "导出")
    @Log(title = "settlement", businessType = BusinessType.OTHER)
    @PostMapping("/download")
    public void download(HttpServletResponse response, @RequestBody(required = false) PriceDefinition priceDefinition) throws IOException {
        Page<PriceDefinition> page = PageUtils.getPage(PriceDefinition.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        SearchVo searchVo = priceDefinition.getSearchVo();
        if (searchVo != null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<PriceDefinition> pageData = priceDefinitionService.page(page);
        List<PriceDefinition> records = pageData.getRecords();
        if (searchVo != null) {
            records = SearchUtils.getByEntityFilter(searchVo, records);
            page.setTotal(records.size());
            records = records.stream().skip(pageSize * (current - 1)).limit(pageSize).collect(Collectors.toList());
        }
        ExcelUtils.export3Excel(response, records, PriceDefinition.class, "公式定义");
    }


    @ApiOperation("模板文件下载")
    @Log(title = "settlement")
    @PostMapping("/template/download")
    @ResponseBody
    @RepeatSubmit(interval = 1000)
    public void templateDownload(@RequestParam String table,@RequestParam String priceFactor, HttpServletResponse response) throws IOException {
        priceDefinitionService.templateDownload(table,priceFactor,response);

    }

    @ApiOperation("文件上传")
    @Log(title = "settlement")
    @PostMapping("/upload")
    @ResponseBody
    //@RepeatSubmit(interval = 1000)
    public R upload(MultipartFile file,@RequestParam String priceFactor) throws IOException{
        //priceFactor=new String(priceFactor.getBytes("ISO-8859-1"), "UTF-8");
        List<Map<String, Object>> map = Lists.newArrayList();
       // priceDefinitionService.getField(table);
        EasyExcel.read(file.getInputStream())
                .registerReadListener(new NoModelDataListener(map,priceFactor))
                .sheet()
                .doRead();
        return R.ok(map);
    }


    @ApiOperation("导入数据")
    @Log(title = "settlement")
    @PostMapping("/import/data")
    @ResponseBody
    //@RepeatSubmit(interval = 1000)
    public R importData(@RequestBody List<Map<String,Object>> data,@RequestParam String table) {
        priceDefinitionService.insertData(data,table);
        return R.ok("插入成功");
    }
    @ApiOperation("导出数据")
    @Log(title = "settlement")
    @PostMapping("/down/data")
    @ResponseBody
    //@RepeatSubmit(interval = 1000)
    public R downData(@RequestParam String table,@RequestParam String priceFactor, @RequestBody(required = false)PriceSelectVo priceSelectVo, HttpServletResponse response) throws IOException {
        priceDefinitionService.downData(table,response,priceFactor,priceSelectVo);
        return R.ok("插入成功");
    }

    @ApiOperation("查维护数据")
    @Log(title = "settlement")
    @PostMapping("/select/data")
    @ResponseBody
    //@RepeatSubmit(interval = 1000)
    public R selectData(@RequestParam String table, @RequestBody(required = false)PriceSelectVo priceSelectVo){
        Map<String, Object> map = Maps.newHashMap();
        Page<Map> page = PageUtils.getPage(Map.class);
        //获取字段
        Page<Map<String,Object>> datas=priceDefinitionService.selectData(table,priceSelectVo,page);
        return R.ok(PageUtils.getPageInfo(datas.getRecords(), (int) datas.getTotal()));
    }
}
