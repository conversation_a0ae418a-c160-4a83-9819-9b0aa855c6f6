package com.datalink.fdop.project.config;

import com.datalink.fdop.common.core.domain.R;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import javax.servlet.http.HttpServletRequest;
import java.sql.SQLException;


/**
 * <AUTHOR>
 * @Date 2023/2/20 11:38
 */
@ControllerAdvice
@ResponseBody
public class ExceptionConfiguration extends ResponseEntityExceptionHandler {


    public static final String CODE_ERROR = "500";

    protected Logger logger = LoggerFactory.getLogger(getClass());

    @ExceptionHandler(value = SQLException.class)
    public ResponseEntity<R> sqlExceptionHandel(HttpServletRequest request, SQLException e) {
        logger.error(e.getMessage(), e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(R.fail(CODE_ERROR,"数据异常，请检查字段长度或主键是否重复！")) ;
    }



}