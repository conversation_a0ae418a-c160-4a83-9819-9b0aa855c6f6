package com.datalink.fdop.project.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.project.api.domain.OrderHead;
import com.datalink.fdop.project.api.model.vo.OrderQueryShowVo;
import com.datalink.fdop.project.api.model.vo.OrderQueryVo;
import com.datalink.fdop.project.service.OrderHeadService;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/10/24 14:18
 */
@RestController
@RequestMapping("/head")
public class OrderHeadController {
    @Autowired
    private OrderHeadService orderHeadService;



    @ApiOperation(value = "采购订单查询")
    @Log(title = "工程资料",businessType = BusinessType.OTHER)
    @PostMapping("/query")
    public R query(@RequestBody OrderQueryVo orderQueryVo,@RequestParam(required = false) String delFlag) {
        Page<OrderQueryShowVo> page = PageUtils.getPage(OrderQueryShowVo.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (orderQueryVo.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<OrderQueryShowVo> dataPage= orderHeadService.queryData(page,orderQueryVo,delFlag);
        List<OrderQueryShowVo> records = dataPage.getRecords();
        if (orderQueryVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(orderQueryVo.getSearchVo(), records);
            dataPage.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records,(int)dataPage.getTotal()));
    }


    @ApiOperation(value = "导出")
    @Log(title = "工程资料",businessType = BusinessType.OTHER)
    @PostMapping("/download")
    public void download(@RequestBody OrderQueryVo orderQueryVo ,@RequestParam(required = false) String delFlag, HttpServletResponse response) throws IOException {
        Page<OrderQueryShowVo> page = PageUtils.getPage(OrderQueryShowVo.class);
        if (orderQueryVo.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<OrderQueryShowVo> dataPage= orderHeadService.queryData(page,orderQueryVo, delFlag);
        List<OrderQueryShowVo> records = dataPage.getRecords();
        if (orderQueryVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(orderQueryVo.getSearchVo(), records);
        }
        ExcelUtils.export3Excel(response,records, OrderQueryShowVo.class,"采购订单");
    }




    @ApiOperation(value = "头查询")
    @Log(title = "工程资料",businessType = BusinessType.OTHER)
    @PostMapping("/find")
    public R find(@RequestBody OrderHead orderHead) {
        Page<OrderHead> page = PageUtils.getPage(OrderHead.class);
        Page<OrderHead> headPage = orderHeadService.page(page, new QueryWrapper<OrderHead>()
                .lambda()
                .eq(StringUtils.isNotEmpty(orderHead.getOrderNum()), OrderHead::getOrderNum, orderHead.getOrderNum()));
        List<OrderHead> list =headPage.getRecords();
        list.stream().forEach(orderHead1 -> {
            //供应商
            String companyCode = orderHead1.getCompanyCode();
            orderHead1.setCompanyDesc(orderHeadService.findDescription(companyCode));
            orderHead1.setPurchaseDesc(orderHeadService.findPurchaseDesc(orderHead1.getPurchaseCode()));
            orderHead1.setSupplierName(orderHeadService.findSupplierName(orderHead1.getSupplierCode()));
        });
        return R.ok(PageUtils.getPageInfo(list,(int)headPage.getTotal()));
    }

    @ApiOperation(value = "头下拉框查询")
    @Log(title = "工程资料",businessType = BusinessType.OTHER)
    @PostMapping("/condition/find")
    public R conditionFind() {
        Map<String, Object> result = Maps.newHashMap();
        List<OrderHead> list = orderHeadService.list(new QueryWrapper<OrderHead>());
        List<String> status = list.stream().map(OrderHead::getStatu).distinct().collect(Collectors.toList());
        List<OrderHead> bsarts = list.stream().map(o -> {
            OrderHead orderHead = new OrderHead();
            orderHead.setOrderType(o.getOrderType());
            orderHead.setOrderTypeDesc(o.getOrderTypeDesc());
            return orderHead;
        }).distinct().collect(Collectors.toList());
        List<OrderHead> bukrs = list.stream().map(
                o -> {
                    OrderHead orderHead = new OrderHead();
                    orderHead.setCompanyCode(o.getCompanyCode());
                    orderHead.setCompanyDesc(orderHeadService.findDescription(o.getCompanyCode()));
                    return orderHead;
                }
        ).distinct().collect(Collectors.toList());
        List<OrderHead> ekorg = list.stream().map(
                o -> {
                    OrderHead orderHead = new OrderHead();
                    orderHead.setPurchaseCode(o.getPurchaseCode());
                    orderHead.setPurchaseDesc(orderHeadService.findPurchaseDesc(o.getPurchaseDesc()));
                    return orderHead;
                }
        ).distinct().collect(Collectors.toList());
        result.put("status",status);
        result.put("orderType",bsarts);
        result.put("companyCode",bukrs);
        result.put("purchaseCode",ekorg);
        return R.ok(result);
    }

}
