package com.datalink.fdop;

import com.datalink.fdop.common.security.annotation.EnableCustomConfig;
import com.datalink.fdop.common.security.annotation.EnableRyFeignClients;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 *
 */
@EnableAsync
@EnableCustomConfig
@EnableRyFeignClients
@SpringBootApplication
public class FSmsApplication {
    public static void main(String[] args) {
        SpringApplication.run(FSmsApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  FSMS启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                "  _____ ______ __  __ _____ \n" +
                " / ____|  ____|  \\/  |_   _|\n" +
                "| (___ | |__  | \\  / | | |  \n" +
                " \\___ \\|  __| | |\\/| | | |  \n" +
                " ____) | |____| |  | |_| |_ \n" +
                "|_____/|______|_|  |_|_____|\n");
    }
}
