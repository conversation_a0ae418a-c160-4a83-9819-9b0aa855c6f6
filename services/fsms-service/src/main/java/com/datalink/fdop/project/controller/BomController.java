package com.datalink.fdop.project.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.project.api.domain.BomHead;
import com.datalink.fdop.project.api.domain.BomRow;
import com.datalink.fdop.project.api.model.vo.BomHeadVo;
import com.datalink.fdop.project.service.BomRowService;
import com.datalink.fdop.project.service.BomService;
import com.datalink.fdop.project.vo.BomSearchVo;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/10/11 14:18
 */
@RestController
@RequestMapping("/bom")
public class BomController {

    @Autowired
    private BomService bomService;
    @Autowired
    private BomRowService bomRowService;

    @ApiOperation(value = "查询bom树")
    @Log(title = "工程资料", businessType = BusinessType.OTHER)
    @PostMapping("/queryTree")
    public R queryTree(@RequestBody(required = false) BomSearchVo bomSearchVo) {
        //查询头行表数据
        return R.ok(bomService.assemblyTree(bomSearchVo));
    }


    @ApiOperation(value = "查询层级")
    @Log(title = "工程资料", businessType = BusinessType.OTHER)
    @PostMapping("/query")
    public R query(@RequestParam String headId, @RequestBody(required = false) SearchVo searchVo) {
        Map<String, Object> map = bomService.queryBom(headId, searchVo);
        return R.ok(map);
    }


    @ApiOperation(value = "导出")
    @Log(title = "工程资料", businessType = BusinessType.OTHER)
    @PostMapping("/download")
    public void download(@RequestParam String headId, @RequestBody(required = false) SearchVo searchVo, HttpServletResponse response) throws IOException {
        Map<String, Object> map = bomService.queryBom(headId, searchVo);
        PageDataInfo<BomRow> row = (PageDataInfo<BomRow>) map.get("row");
        List<BomRow> totalList = row.getTotalList();
        ExcelUtils.export3Excel(response, totalList, BomRow.class, "bom");
    }

    @ApiOperation(value = "查询bom头")
    @Log(title = "工程资料", businessType = BusinessType.OTHER)
    @PostMapping("/query/head")
    public R queryHead() {
        Page<BomHead> page = PageUtils.getPage(BomHead.class);
        Page<BomHead> headPage = bomService.page(page);
        return R.ok(PageUtils.getPageInfo(headPage.getRecords(), (int) headPage.getTotal()));
    }


    @ApiOperation(value = "多值查询bom头")
    @Log(title = "工程资料", businessType = BusinessType.OTHER)
    @PostMapping("/query/more/head")
    public R queryHead(@RequestBody BomHeadVo bomHeadVo) {
        Page<BomHead> page = PageUtils.getPage(BomHead.class);
        Page<BomHead> headPage = bomService.page(page, new QueryWrapper<BomHead>()
                .lambda()
                .in(CollectionUtils.isNotEmpty(bomHeadVo.getSupplierCode()), BomHead::getSupplierCode, bomHeadVo.getSupplierCode())
                .in(CollectionUtils.isNotEmpty(bomHeadVo.getParentMaterialCode()), BomHead::getParentMaterialCode, bomHeadVo.getParentMaterialCode())
                .apply("valid_deadline>now()")
                .orderByAsc(BomHead::getParentMaterialCode)
                .orderByAsc(BomHead::getVersions)
        );
        return R.ok(PageUtils.getPageInfo(headPage.getRecords(), (int) headPage.getTotal()));
    }


    @ApiOperation(value = "根据头id查行bom")
    @Log(title = "工程资料", businessType = BusinessType.OTHER)
    @PostMapping("/query/row")
    public R queryRow(@RequestParam String headId) {
        Page<BomRow> page = PageUtils.getPage(BomRow.class);
        Page<BomRow> rowPage = bomRowService.page(page, new QueryWrapper<BomRow>().lambda().eq(BomRow::getHeadId, headId));
        return R.ok(PageUtils.getPageInfo(rowPage.getRecords(), (int) rowPage.getTotal()));
    }

}
