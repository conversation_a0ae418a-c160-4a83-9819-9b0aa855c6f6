package com.datalink.fdop.settlement.listener;

import com.alibaba.druid.sql.visitor.functions.If;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.util.ConverterUtils;
import com.alibaba.excel.util.ListUtils;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;


import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date 2022/7/6 19:58
 */
@Slf4j
public class NoModelDataListener extends AnalysisEventListener<Map<Integer, String>> implements Converter<String> {

    private List<Map<String, Object>> map;

    private Map<Integer, String> headMap;

    private String priceFactor;

    private int batchCount;

    private List<Map<String, Object>> cachedDataList = ListUtils.newArrayListWithExpectedSize(batchCount);

    public NoModelDataListener( List<Map<String, Object>> map , String priceFactor) {
        this.priceFactor=priceFactor;
        this.map=map;
    }

    @Override
    public void invoke(Map<Integer, String> integerStringMap, AnalysisContext analysisContext) {
        try {
            Map<String, Object> dataMap = new HashMap<>();
            for (Map.Entry<Integer, String> entry : headMap.entrySet()) {
                // key是下标
                Integer index = entry.getKey();
                // 列文本
                String fieldText = entry.getValue();
                // 值
                String value = integerStringMap.get(index);
                // 转换值类型
                /*value = FieldUtils.convertField(value, templateField.getFieldType());*/
                if (StringUtils.isNotEmpty(fieldText)) {
                    this.valueCheck(fieldText,value,dataMap);
                }
                // 重新赋值
                if (StringUtils.isEmpty(value)) {
                    value="empty";
                }
                dataMap.put(fieldText, value);
            }
            map.add(dataMap);
        }catch (Exception e){
            e.printStackTrace();
            throw new ServiceException("文件解析失败，仍有数据有误，不能导入！且数据导入不成功");
        }

    }

    private void valueCheck(String fieldText,String value, Map<String, Object> dataMap) {
        String msg="";
        String path="\\d{4}-\\d{2}-\\d{2}";//定义匹配规则
        Pattern p=Pattern.compile(path);//实例化Pattern
        if (fieldText.equals("supplier_code")&&(StringUtils.isEmpty(value)|| value.length()>10) ){
            msg="供应商编码长度不超过10,且不能为空";
        }else if (fieldText.equals("valid_from")&&(StringUtils.isEmpty(value)||!p.matcher(value).matches())) {
            msg="有效起始日期,非法格式,且不能为空";
        }else if (fieldText.equals("valid_to")&&(StringUtils.isEmpty(value)||!p.matcher(value).matches())) {
            msg="有效终止日期,非法格式,且不能为空";
        }else if (fieldText.equals("quotation_num")&&(StringUtils.isEmpty(value)|| value.length()>20)) {
            msg="报价单长度不超过20,且不能为空";
        }else if (fieldText.equals(priceFactor)&&(StringUtils.isEmpty(value)|| value.length()>13)){
            msg="价格因子长度不超过13,且不能为空";
        }else {
            if (StringUtils.isNotEmpty(value)&&value.length()>200){
                msg="动态列长度不超过200";
            }
        }
        String msg1 = String.valueOf(dataMap.get("msg")) ;
        if (StringUtils.isNotEmpty(msg)) {
            if (StringUtils.isNotEmpty(msg1)&&!"null".equals(msg1)) {
                dataMap.put("msg",msg1+";"+msg);
            }else {
                dataMap.put("msg",msg);
            }
        }

    }

    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        Map<Integer, String> integerStringMap = ConverterUtils.convertToStringMap(headMap, context);
        this.headMap = integerStringMap;
        //log.info("解析到一条头数据:{}", JSON.toJSONString(integerStringMap));
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
    }


    /**
     * 这里读的时候会调用
     *
     * @param context
     * @return
     */
    @Override
    public String convertToJavaData(ReadConverterContext<?> context) {
        String string = context.getReadCellData().toString();
        log.info("读取到得值" + string);
        return context.getReadCellData().toString();
    }

}
