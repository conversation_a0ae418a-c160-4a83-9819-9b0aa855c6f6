package com.datalink.fdop.project.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.project.api.domain.TestProcedure;
import com.datalink.fdop.project.api.model.vo.TestProcedureVo;
import com.datalink.fdop.project.mapper.TestProcedureMapper;
import com.datalink.fdop.project.service.TestProcedureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2022/11/2 13:39
 */
@Service
public class TestProcedureServiceImpl extends ServiceImpl<TestProcedureMapper, TestProcedure> implements TestProcedureService {

    @Autowired
    private TestProcedureMapper testProcedureMapper;

    @Override
    public Page<TestProcedure> pageData(Page<TestProcedure> page, TestProcedureVo testProcedure) {
        return testProcedureMapper.pageData(page,testProcedure);
    }
}
