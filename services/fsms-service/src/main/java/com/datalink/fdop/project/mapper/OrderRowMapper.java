package com.datalink.fdop.project.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datalink.fdop.project.api.domain.OrderRow;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @Date 2022/10/24 14:25
 */
@Mapper
public interface OrderRowMapper extends BaseMapper<OrderRow> {


    @Select("SELECT material_desc FROM zjdata.p_d_material where material_code =#{code}")
    String findDesc(String code);

    @Select("SELECT stock_p_description FROM zjdata.org_stock_place where stock_p_code =#{stockPCode}")
    String findSPDesc(String stockPCode);
}
