package com.datalink.fdop.project.controller;



import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.project.api.domain.ProcessingSingle;
import com.datalink.fdop.project.service.ProcessingSingleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/processing/single")
@Api(tags = "单道制程")
public class ProcessingSingleController {


    @Autowired
    private ProcessingSingleService processingSingleService;


    @ApiOperation(value = "新增")
    @Log(title = "工程资料",businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public R create(@RequestBody List<ProcessingSingle> processingSingles) {
        List<ProcessingSingle> datas=processingSingleService.list(new QueryWrapper<ProcessingSingle>()
                .lambda()
                .eq(ProcessingSingle::getTurnkeyProcess,processingSingles.get(0).getTurnkeyProcess())
        );
        int max=0;
        if (CollectionUtils.isNotEmpty(datas)) {
            max = datas.stream().map(ProcessingSingle::getSerialNum).mapToInt(m -> Integer.valueOf(m)).max().getAsInt();
        }
        List<ProcessingSingle> processingSingleList=new ArrayList<>();
        for (int i = 0; i < processingSingles.size(); i++) {
            ProcessingSingle processingSingle = processingSingles.get(i);
            processingSingle.setSerialNum(String.valueOf(max+i+1));
            processingSingleList.add(processingSingle);
        }
        if (processingSingleService.saveBatch(processingSingleList)) {
            return R.ok("保存成功");
        }else {
            return R.fail("保存失败");
        }
    }

    @ApiOperation(value = "修改")
    @Log(title = "工程资料",businessType = BusinessType.INSERT)
    @PostMapping("/update")
    public R update(@RequestBody List<ProcessingSingle> processingSingles) {
        if (processingSingleService.updateBatchById(processingSingles)) {
            return R.ok("修改成功");
        }else {
            return R.fail("修改失败");
        }

    }

    @ApiOperation("删除")
    @Log(title = "工程资料",businessType = BusinessType.DELETE)
    @PostMapping("/del")
    public R del(@RequestBody List<Long> ids,@RequestParam  String turnkeyProcess) {
        processingSingleService.removeBatchByIds(ids);
        List<ProcessingSingle> datas=processingSingleService.list(new QueryWrapper<ProcessingSingle>()
                .lambda()
                .eq(ProcessingSingle::getTurnkeyProcess,turnkeyProcess)
                .orderByAsc(ProcessingSingle::getCreateTime)
        );
        if (CollectionUtils.isNotEmpty(datas)) {
            for (int i = 0; i < datas.size(); i++) {
                ProcessingSingle processingSingle = datas.get(i);
                processingSingle.setSerialNum(String.valueOf(i+1));
                processingSingleService.updateById(processingSingle);
            }
        }
        return R.ok("删除成功");
    }

    @ApiOperation(value = "查询")
    @Log(title = "工程资料",businessType = BusinessType.UPDATE)
    @PostMapping("/query")
    public R query(@RequestBody(required = false)ProcessingSingle processingSingle) {
        Page<ProcessingSingle> page = PageUtils.getPage(ProcessingSingle.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (processingSingle.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<ProcessingSingle> pageData=processingSingleService.page(page,new QueryWrapper<ProcessingSingle>()
                .lambda()
                .eq(StringUtils.isNotEmpty(processingSingle.getProcess()),ProcessingSingle::getProcess,processingSingle.getProcess())
                .eq(StringUtils.isNotEmpty(processingSingle.getTurnkeyProcess()),ProcessingSingle::getTurnkeyProcess,processingSingle.getTurnkeyProcess())
                .eq(StringUtils.isNotEmpty(processingSingle.getSerialNum()),ProcessingSingle::getSerialNum,processingSingle.getSerialNum())
                .orderByAsc(ProcessingSingle::getCreateTime)
        );
        List<ProcessingSingle> records = pageData.getRecords();
        if (processingSingle.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(processingSingle.getSearchVo(), records);
            pageData.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records, (int) pageData.getTotal()));
    }

    @ApiOperation(value = "导出测试数据")
    @Log(title = "工程资料",businessType = BusinessType.OTHER)
    @PostMapping("/download")
    public void download(@RequestBody(required = false)ProcessingSingle processingSingle, HttpServletResponse response) throws IOException {
        Page<ProcessingSingle> page = PageUtils.getPage(ProcessingSingle.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (processingSingle.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<ProcessingSingle> pageData=processingSingleService.page(page,new QueryWrapper<ProcessingSingle>()
                .lambda()
                .eq(StringUtils.isNotEmpty(processingSingle.getProcess()),ProcessingSingle::getProcess,processingSingle.getProcess())
                .eq(StringUtils.isNotEmpty(processingSingle.getTurnkeyProcess()),ProcessingSingle::getTurnkeyProcess,processingSingle.getTurnkeyProcess())
                .eq(StringUtils.isNotEmpty(processingSingle.getSerialNum()),ProcessingSingle::getSerialNum,processingSingle.getSerialNum())
                .orderByAsc(ProcessingSingle::getCreateTime)
        );
        List<ProcessingSingle> records = pageData.getRecords();
        if (processingSingle.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(processingSingle.getSearchVo(), records);
            pageData.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        ExcelUtils.export3Excel(response,records, ProcessingSingle.class,"委外制程单道制程");
    }
}
