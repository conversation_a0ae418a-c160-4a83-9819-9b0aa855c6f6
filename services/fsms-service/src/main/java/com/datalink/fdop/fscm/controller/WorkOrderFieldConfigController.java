package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.WorkOrderConfig;
import com.datalink.fdop.fscm.api.domain.WorkOrderFieldConfig;
import com.datalink.fdop.fscm.api.domain.WorkOrderProductionConfig;
import com.datalink.fdop.fscm.service.WorkOrderConfigService;
import com.datalink.fdop.fscm.service.WorkOrderFieldConfigService;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/11/22 10:12
 */
@RestController
@RequestMapping("/work/order/field/config")
@Transactional
@ApiOperation("工单字段配置")
public class WorkOrderFieldConfigController {

    @Autowired
    private WorkOrderFieldConfigService workOrderFieldConfigService;
    @Autowired
    private WorkOrderConfigService workOrderConfigService;

    @ApiOperation(value = "上半查询")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/query")
    public R query(@RequestBody(required = false) SearchVo searchVo) {
        Page<WorkOrderFieldConfig> page = PageUtils.getPage(WorkOrderFieldConfig.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (searchVo!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<WorkOrderFieldConfig> pageData=workOrderFieldConfigService.page(page,new QueryWrapper<WorkOrderFieldConfig>()
                .lambda()
                .select(WorkOrderFieldConfig::getWorkOrderType,WorkOrderFieldConfig::getProcess,WorkOrderFieldConfig::getScreenMode,WorkOrderFieldConfig::getStatu)
                .orderByAsc(WorkOrderFieldConfig::getWorkOrderType)
                .orderByAsc(WorkOrderFieldConfig::getProcess)
                .orderByAsc(WorkOrderFieldConfig::getScreenMode)
                .orderByAsc(WorkOrderFieldConfig::getStatu)
                .groupBy(WorkOrderFieldConfig::getWorkOrderType)
                .groupBy(WorkOrderFieldConfig::getProcess)
                .groupBy(WorkOrderFieldConfig::getScreenMode)
                .groupBy(WorkOrderFieldConfig::getStatu)
        );
        List<WorkOrderFieldConfig> records = pageData.getRecords();
        records.forEach(record->{
            WorkOrderConfig config = workOrderConfigService.getOne(new QueryWrapper<WorkOrderConfig>()
                    .lambda()
                    .eq(WorkOrderConfig::getWorkOrderType, record.getWorkOrderType()));
            if (config!=null) {
                record.setWorkOrderTypeDesc(config.getWorkOrderTypeDesc());
            }
        });
        if (searchVo!=null) {
            records = SearchUtils.getByEntityFilter(searchVo, records );
            page.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records, (int) pageData.getTotal()));
    }


    @ApiOperation(value = "下拉查询")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/down/query")
    public R downQuery(@RequestBody WorkOrderFieldConfig workOrderFieldConfig) {
        SearchVo searchVo = workOrderFieldConfig.getSearchVo();
        Page<WorkOrderFieldConfig> page = PageUtils.getPage(WorkOrderFieldConfig.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (searchVo!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<WorkOrderFieldConfig> pageData=workOrderFieldConfigService.page(page,new QueryWrapper<WorkOrderFieldConfig>()
                .lambda()
                .eq(WorkOrderFieldConfig::getWorkOrderType,workOrderFieldConfig.getWorkOrderType())
                .eq(WorkOrderFieldConfig::getProcess,workOrderFieldConfig.getProcess())
                .eq(WorkOrderFieldConfig::getScreenMode,workOrderFieldConfig.getScreenMode())
                .eq(WorkOrderFieldConfig::getStatu,workOrderFieldConfig.getStatu())
                .orderByAsc(WorkOrderFieldConfig::getLocation)
                .orderByAsc(WorkOrderFieldConfig::getField)
        );
        List<WorkOrderFieldConfig> records = pageData.getRecords();
        if (searchVo!=null) {
            records = SearchUtils.getByEntityFilter(searchVo, records );
            page.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records, (int) pageData.getTotal()));
    }


    @ApiOperation(value = "去重查询")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/repetition/query")
    public R repetitionQuery() {
        List<WorkOrderFieldConfig> list = workOrderFieldConfigService.list(new QueryWrapper<WorkOrderFieldConfig>()
                .lambda()
                .select(WorkOrderFieldConfig::getWorkOrderType, WorkOrderFieldConfig::getProcess, WorkOrderFieldConfig::getScreenMode, WorkOrderFieldConfig::getStatu)
                .groupBy(WorkOrderFieldConfig::getWorkOrderType)
                .groupBy(WorkOrderFieldConfig::getProcess)
                .groupBy(WorkOrderFieldConfig::getScreenMode)
                .groupBy(WorkOrderFieldConfig::getStatu)
        );
        return R.ok(list.size());

    }

    @ApiOperation(value = "新增")
    @Log(title = "fscm",businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public R create(@RequestBody List<WorkOrderFieldConfig> workOrderFieldConfigs) {
        try {
            workOrderFieldConfigs.forEach(workOrderFieldConfig -> {
                List<WorkOrderFieldConfig> list = null;

                list = workOrderFieldConfigService.list(new QueryWrapper<WorkOrderFieldConfig>()
                        .lambda()
                        .eq(WorkOrderFieldConfig::getWorkOrderType, workOrderFieldConfig.getWorkOrderType())
                        .eq(WorkOrderFieldConfig::getProcess, workOrderFieldConfig.getProcess())
                        .eq(WorkOrderFieldConfig::getScreenMode, workOrderFieldConfig.getScreenMode())
                        .eq(WorkOrderFieldConfig::getStatu, workOrderFieldConfig.getStatu())
                        .eq(WorkOrderFieldConfig::getField, workOrderFieldConfig.getField())
                        .eq(WorkOrderFieldConfig::getLocation, workOrderFieldConfig.getLocation())
                );

                if (CollectionUtils.isNotEmpty(list)) {
                    throw new ServiceException("新增行已存在，请检查输入");
                }
            });
            if (workOrderFieldConfigService.saveBatch(workOrderFieldConfigs)) {
                return R.ok();
            }else {
                return R.fail("失败");
            }
        }catch (Exception e){
            e.printStackTrace();
            throw new ServiceException("检查数据是否正确!");
        }
    }


    @ApiOperation(value = "修改")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public R update(@RequestBody List<WorkOrderFieldConfig> workOrderFieldConfigs) {
        try {
            workOrderFieldConfigs.forEach(workOrderFieldConfig -> {
                 workOrderFieldConfigService.update(Wrappers.lambdaUpdate(WorkOrderFieldConfig.class)
                        .set(WorkOrderFieldConfig::getFieldDesc, workOrderFieldConfig.getFieldDesc())
                        .set(WorkOrderFieldConfig::getControl, workOrderFieldConfig.getControl())
                        .set(WorkOrderFieldConfig::getLocationXy, workOrderFieldConfig.getLocationXy())
                        .eq(WorkOrderFieldConfig::getWorkOrderType, workOrderFieldConfig.getWorkOrderType())
                        .eq(WorkOrderFieldConfig::getProcess, workOrderFieldConfig.getProcess())
                        .eq(WorkOrderFieldConfig::getScreenMode, workOrderFieldConfig.getScreenMode())
                        .eq(WorkOrderFieldConfig::getStatu, workOrderFieldConfig.getStatu())
                        .eq(WorkOrderFieldConfig::getField, workOrderFieldConfig.getField())
                        .eq(WorkOrderFieldConfig::getLocation, workOrderFieldConfig.getLocation())
                );
            });
            return R.ok();
        }catch (Exception e){
            e.printStackTrace();
            throw new ServiceException("检查数据是否正确!");
        }
    }

    @ApiOperation(value = "删除")
    @Log(title = "fscm",businessType = BusinessType.DELETE)
    @PostMapping("/del")
    public R del(@RequestBody List<WorkOrderFieldConfig> workOrderFieldConfigs) {
        for (WorkOrderFieldConfig workOrderFieldConfig : workOrderFieldConfigs) {
            workOrderFieldConfigService.remove(new QueryWrapper<WorkOrderFieldConfig>()
                    .lambda()
                    .eq(WorkOrderFieldConfig::getWorkOrderType,workOrderFieldConfig.getWorkOrderType())
                    .eq(WorkOrderFieldConfig::getProcess,workOrderFieldConfig.getProcess())
                    .eq(WorkOrderFieldConfig::getScreenMode,workOrderFieldConfig.getScreenMode())
                    .eq(WorkOrderFieldConfig::getStatu,workOrderFieldConfig.getStatu()));
        }
        return R.ok();

    }

    @ApiOperation(value = "导出")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/download")
    public void download( HttpServletResponse response,@RequestBody(required = false) SearchVo searchVo) throws IOException {
        Page<WorkOrderFieldConfig> page = PageUtils.getPage(WorkOrderFieldConfig.class);
        if (searchVo!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<WorkOrderFieldConfig> pageData=workOrderFieldConfigService.page(page,new QueryWrapper<WorkOrderFieldConfig>()
                .lambda()
                .select(WorkOrderFieldConfig::getWorkOrderType,WorkOrderFieldConfig::getProcess,WorkOrderFieldConfig::getScreenMode,WorkOrderFieldConfig::getStatu)
                .orderByAsc(WorkOrderFieldConfig::getWorkOrderType)
                .orderByAsc(WorkOrderFieldConfig::getProcess)
                .orderByAsc(WorkOrderFieldConfig::getScreenMode)
                .orderByAsc(WorkOrderFieldConfig::getStatu)
                .groupBy(WorkOrderFieldConfig::getWorkOrderType)
                .groupBy(WorkOrderFieldConfig::getProcess)
                .groupBy(WorkOrderFieldConfig::getScreenMode)
                .groupBy(WorkOrderFieldConfig::getStatu)
        );
        List<WorkOrderFieldConfig> records = pageData.getRecords();
        records.forEach(record->{
            WorkOrderConfig config = workOrderConfigService.getOne(new QueryWrapper<WorkOrderConfig>()
                    .lambda()
                    .eq(WorkOrderConfig::getWorkOrderType, record.getWorkOrderType()));
            if (config!=null) {
                record.setWorkOrderTypeDesc(config.getWorkOrderTypeDesc());
            }
        });
        if (searchVo!=null) {
            records = SearchUtils.getByEntityFilter(searchVo, records );
        }
        ExcelUtils.export3Excel(response,records, WorkOrderProductionConfig.class,"工单生产状态配置");
    }




    @ApiOperation(value = "条件查询")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/work/order/query")
    public R workOrderQuery(@RequestBody WorkOrderFieldConfig workOrderFieldConfig) {
        Page<WorkOrderFieldConfig> page = PageUtils.getPage(WorkOrderFieldConfig.class);
        Page<WorkOrderFieldConfig> pageData=workOrderFieldConfigService.page(page,new QueryWrapper<WorkOrderFieldConfig>()
                .lambda()
                .eq(WorkOrderFieldConfig::getWorkOrderType,workOrderFieldConfig.getWorkOrderType())
                .eq(WorkOrderFieldConfig::getProcess,workOrderFieldConfig.getProcess())
                .eq(WorkOrderFieldConfig::getScreenMode,workOrderFieldConfig.getScreenMode())
                .eq(WorkOrderFieldConfig::getStatu,workOrderFieldConfig.getStatu())
        );
        List<WorkOrderFieldConfig> records = pageData.getRecords();
        records.forEach(record->{
            WorkOrderConfig config = workOrderConfigService.getOne(new QueryWrapper<WorkOrderConfig>()
                    .lambda()
                    .eq(WorkOrderConfig::getWorkOrderType, record.getWorkOrderType()));
            record.setWorkOrderTypeDesc(config.getWorkOrderTypeDesc());
        });
        return R.ok(PageUtils.getPageInfo(records, (int) pageData.getTotal()));
    }




}
