package com.datalink.fdop.project.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.project.api.domain.BinMainData;
import com.datalink.fdop.project.service.BinMainDataService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/main")
@ApiOperation("BIN主数据")
@Transactional
public class BinMainDataController {


    @Autowired
    private BinMainDataService binMainDataService;

    @ApiOperation(value = "查询")
    @Log(title = "工程资料")
    @PostMapping("/query")
    public R query(@RequestBody BinMainData binMainData) {
        Page<BinMainData> page = PageUtils.getPage(BinMainData.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (binMainData.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<BinMainData> pageData=binMainDataService.page(page);
        List<BinMainData> records = pageData.getRecords();
        if (binMainData.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(binMainData.getSearchVo(), records);
            pageData.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records, (int) pageData.getTotal()));
    }
    @ApiOperation(value = "导出")
    @Log(title = "工程资料")
    @PostMapping("/download")
    public void download(@RequestBody  BinMainData binMainData, HttpServletResponse response) throws IOException {
        Page<BinMainData> page = PageUtils.getPage(BinMainData.class);
        if (binMainData.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<BinMainData> pageData=binMainDataService.page(page);
        List<BinMainData> records = pageData.getRecords();
        if (binMainData.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(binMainData.getSearchVo(), records);
        }
        ExcelUtils.export3Excel(response,records, BinMainData.class,"BIN主数据数据");

    }

    @ApiOperation(value = "新增")
    @Log(title = "工程资料")
    @PostMapping("/save")
    public R save(@RequestBody List<BinMainData> binMainDataList) {
        binMainDataService.saveBatch(binMainDataList);
        return R.ok();
    }

    @ApiOperation(value = "删除")
    @Log(title = "工程资料")
    @PostMapping("/del")
    public R del(@RequestBody List<BinMainData> binMainDataList) {
        for (BinMainData binMainData : binMainDataList) {
            binMainDataService.remove(new QueryWrapper<BinMainData>().lambda()
                    .eq(BinMainData::getBinNum,binMainData.getBinNum()));
        }
        return R.ok();
    }


    @ApiOperation(value = "修改")
    @Log(title = "工程资料")
    @PostMapping("/update")
    public R update(@RequestBody List<BinMainData> binMainDataList) {
        for (BinMainData binMainData : binMainDataList) {
            binMainDataService.update(new UpdateWrapper<BinMainData>().lambda()
                    .set(BinMainData::getBinAttribute,binMainData.getBinAttribute())
                    .eq(BinMainData::getBinNum,binMainData.getBinNum())
            );
        }
        return R.ok();
    }
}