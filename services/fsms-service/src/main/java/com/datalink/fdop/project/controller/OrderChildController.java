package com.datalink.fdop.project.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.project.api.domain.OrderChild;
import com.datalink.fdop.project.service.OrderChildService;
import com.datalink.fdop.project.service.OrderRowService;
import com.datalink.fdop.project.vo.OrderChildVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/10/24 14:19
 */
@RestController
@RequestMapping("/child")
public class OrderChildController {


    @Autowired
    private OrderChildService orderChildService;
    @Autowired
    private OrderRowService orderRowService;


    @ApiOperation(value = "行查询")
    @Log(title = "工程资料",businessType = BusinessType.OTHER)
    @PostMapping("/find")
    public R find(@RequestParam String orderNum, @RequestParam Long orderRowNum, @RequestBody OrderChildVo orderChildVo) {
        List<String> rsposes = orderChildVo.getRsposes();
        SearchVo searchVo = orderChildVo.getSearchVo();
        Page<OrderChild> page = PageUtils.getPage(OrderChild.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (searchVo!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<OrderChild> data = orderChildService.page(page, new QueryWrapper<OrderChild>()
                .lambda()
                .eq(StringUtils.isNotEmpty(orderNum), OrderChild::getOrderNum, orderNum)
                .eq(orderRowNum!=null, OrderChild::getOrderRowNum, orderRowNum)
                .in(CollectionUtils.isNotEmpty(rsposes), OrderChild::getSubprojectNum, rsposes)
        );
        List<OrderChild> records = data.getRecords();
        records.stream().forEach(child -> {
            String code = child.getMaterialCode();
            child.setMaterialDesc(orderRowService.findDesc(code));
        });
        if (searchVo!=null) {
            records = SearchUtils.getByEntityFilter(searchVo, records);
            data.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records,(int) data.getTotal()));
    }
}
