package com.datalink.fdop.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.project.api.domain.TestProcedure;
import com.datalink.fdop.project.api.model.vo.TestProcedureVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @Date 2022/11/2 13:40
 */
@Mapper
public interface TestProcedureMapper extends BaseMapper<TestProcedure>  {
    Page<TestProcedure> pageData(IPage<TestProcedure> page,@Param("testProcedure") TestProcedureVo testProcedure);
}
