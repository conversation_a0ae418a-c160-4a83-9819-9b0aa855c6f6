package com.datalink.fdop.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.project.api.domain.Material;
import com.datalink.fdop.project.api.model.vo.MaterialVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Date 2022/10/9 15:51
 */
@Mapper
@Transactional
public interface MaterialMapper extends BaseMapper<Material> {
    Page<Material> pageData(IPage<Material> page, @Param("material") MaterialVo material);
}
