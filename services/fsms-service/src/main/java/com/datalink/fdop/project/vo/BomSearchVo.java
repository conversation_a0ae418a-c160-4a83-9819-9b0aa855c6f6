package com.datalink.fdop.project.vo;

import com.datalink.fdop.common.core.search.vo.SearchVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/10/11 15:42
 */
@Data
public class BomSearchVo implements Serializable {

    private static final long serialVersionUID = 1088091711979189878L;

    @ApiModelProperty(value = "供应商代码")
    private List<String> supplierCode;
    @ApiModelProperty(value = "物料编码")
    private List<String> materialCodes;
    @ApiModelProperty(value = "BOM状态")
    private List<String> status;
    @ApiModelProperty(value = "创建人")
    private List<String> creators;
    @ApiModelProperty(value = "BOM版本")
    private List<String> versions;
    @ApiModelProperty(value = "项目号")
    private List<String> projectNumbers;
    @ApiModelProperty(value = "产品名")
    private List<String> deviceNames;
    @ApiModelProperty("高级搜索传参")
    private SearchVo searchVo;
}
