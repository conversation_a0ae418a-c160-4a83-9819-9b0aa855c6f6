package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.PieceConfig;
import com.datalink.fdop.fscm.service.PieceConfigService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/11/14 16:03
 */
@RestController
@RequestMapping("/piece")
@Transactional
public class PieceConfigController {
    @Autowired
    private PieceConfigService pieceConfigService;

    @ApiOperation(value = "查询")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/query")
    public R query(@RequestBody(required = false) SearchVo searchVo) {
        Page<PieceConfig> page = PageUtils.getPage(PieceConfig.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (searchVo!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<PieceConfig> pageData=pieceConfigService.page(page,new QueryWrapper<PieceConfig>()
                .lambda()
                .orderByAsc(PieceConfig::getAttributeName));
        List<PieceConfig> records = pageData.getRecords();
        if (searchVo!=null) {
            records = SearchUtils.getByEntityFilter(searchVo, records );
            page.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records, (int) pageData.getTotal()));
    }



    @ApiOperation(value = "新增修改")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/createOrUpdate")
    public R create(@RequestBody List<PieceConfig> pieceConfigs) {
        pieceConfigs.forEach(pieceConfig -> {
            PieceConfig one=null;
            if (pieceConfig.getId()!=null) {
                one= pieceConfigService.getOne(new QueryWrapper<PieceConfig>().lambda()
                        .eq(PieceConfig::getAttributeName,pieceConfig.getAttributeName())
                        .ne(PieceConfig::getId,pieceConfig.getId())
                );
            }else {
                one= pieceConfigService.getOne(new QueryWrapper<PieceConfig>().lambda()
                        .eq(PieceConfig::getAttributeName,pieceConfig.getAttributeName())
                );
            }
            if (one!=null) {
                throw new ServiceException("数据已存在");
            }
        });
        if (pieceConfigService.saveOrUpdateBatch(pieceConfigs)) {
            return R.ok();
        }else {
            return R.fail("失败");
        }

    }

    @ApiOperation(value = "删除")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/del")
    public R del(@RequestBody List<Long> ids) {
        if (pieceConfigService.removeBatchByIds(ids)) {
            return R.ok();
        }else {
            return R.fail("失败");
        }

    }

    @ApiOperation(value = "导出")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/download")
    public void download( HttpServletResponse response,@RequestBody(required = false) SearchVo searchVo) throws IOException {
        Page<PieceConfig> page = PageUtils.getPage(PieceConfig.class);
        if (searchVo!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<PieceConfig> pageData=pieceConfigService.page(page,new QueryWrapper<PieceConfig>()
                .lambda()
                .orderByAsc(PieceConfig::getAttributeName));
        List<PieceConfig> records = pageData.getRecords();
        if (searchVo!=null) {
            records = SearchUtils.getByEntityFilter(searchVo, records );

        }
        ExcelUtils.export3Excel(response,records,PieceConfig.class,"片属性配置");
    }
}
