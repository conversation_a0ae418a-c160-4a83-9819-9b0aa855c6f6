package com.datalink.fdop.project.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.project.api.domain.Manufacturer;
import com.datalink.fdop.project.api.domain.Supplier;
import com.datalink.fdop.project.api.model.vo.SupplierVo;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @Date 2022/10/26 11:32
 */
public interface SupplierService extends IService<Supplier> {
    Page<Supplier> queryData(Page<Supplier> page,@Param("supplierVo") SupplierVo supplierVo);

    Page<Manufacturer> queryMData(Page<Manufacturer> page, @Param("supplierVo")SupplierVo supplierVo);
}
