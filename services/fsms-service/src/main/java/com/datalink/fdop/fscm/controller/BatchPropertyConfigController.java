package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.BatchPropertyConfig;
import com.datalink.fdop.fscm.service.BatchPropertyConfigService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/11/14 16:03
 */
@RestController
@RequestMapping("/batch/property")
@Transactional
public class BatchPropertyConfigController {
    @Autowired
    private BatchPropertyConfigService batchPropertyConfigService;

    @ApiOperation(value = "查询")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/query")
    public R query(@RequestBody(required = false) SearchVo searchVo) {
        Page<BatchPropertyConfig> page = PageUtils.getPage(BatchPropertyConfig.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (searchVo!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<BatchPropertyConfig> pageData=batchPropertyConfigService.page(page,new QueryWrapper<BatchPropertyConfig>()
                .lambda()
                .orderByAsc(BatchPropertyConfig::getAttributeName));
        List<BatchPropertyConfig> records = pageData.getRecords();
        if (searchVo!=null) {
            records = SearchUtils.getByEntityFilter(searchVo, records );
            page.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records, (int) pageData.getTotal()));
    }



    @ApiOperation(value = "新增修改")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/createOrUpdate")
    public R create(@RequestBody List<BatchPropertyConfig> batchPropertyConfigs) {
        batchPropertyConfigs.forEach(data -> {
            BatchPropertyConfig one=null;
            if (data.getId()!=null) {
                one= batchPropertyConfigService.getOne(new QueryWrapper<BatchPropertyConfig>().lambda()
                        .eq(BatchPropertyConfig::getAttributeName,data.getAttributeName())
                        .ne(BatchPropertyConfig::getId,data.getId())
                );
            }else {
                one= batchPropertyConfigService.getOne(new QueryWrapper<BatchPropertyConfig>().lambda()
                        .eq(BatchPropertyConfig::getAttributeName,data.getAttributeName())
                );
            }
            if (one!=null) {
                throw new ServiceException("数据已存在");
            }
        });
        if (batchPropertyConfigService.saveOrUpdateBatch(batchPropertyConfigs)) {
            return R.ok();
        }else {
            return R.fail("失败");
        }

    }

    @ApiOperation(value = "删除")
    @Log(title = "fscm",businessType = BusinessType.DELETE)
    @PostMapping("/del")
    public R del(@RequestBody List<Long> ids) {
        if (batchPropertyConfigService.removeBatchByIds(ids)) {
            return R.ok();
        }else {
            return R.fail("失败");
        }

    }

    @ApiOperation(value = "导出")
    @Log(title = "fscm",businessType = BusinessType.EXPORT)
    @PostMapping("/download")
    public void download( HttpServletResponse response,@RequestBody(required = false) SearchVo searchVo) throws IOException {
        Page<BatchPropertyConfig> page = PageUtils.getPage(BatchPropertyConfig.class);
        if (searchVo!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<BatchPropertyConfig> pageData=batchPropertyConfigService.page(page,new QueryWrapper<BatchPropertyConfig>()
                .lambda()
                .orderByAsc(BatchPropertyConfig::getAttributeName));
        List<BatchPropertyConfig> records = pageData.getRecords();
        if (searchVo!=null) {
            records = SearchUtils.getByEntityFilter(searchVo, records );
        }
        ExcelUtils.export3Excel(response,records,BatchPropertyConfig.class,"批量属性配置");
    }
}
