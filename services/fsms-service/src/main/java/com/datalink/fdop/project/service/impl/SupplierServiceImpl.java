package com.datalink.fdop.project.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.project.api.domain.Manufacturer;
import com.datalink.fdop.project.api.domain.Supplier;
import com.datalink.fdop.project.api.model.vo.SupplierVo;
import com.datalink.fdop.project.mapper.SupplierMapper;
import com.datalink.fdop.project.service.SupplierService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2022/10/26 11:33
 */
@Service
public class SupplierServiceImpl extends ServiceImpl<SupplierMapper,Supplier> implements SupplierService {

    @Autowired
    private SupplierMapper supplierMapper;

    @Override
    public Page<Supplier> queryData(Page<Supplier> page, SupplierVo supplierVo) {
        return supplierMapper.queryData(page,supplierVo);
    }

    @Override
    public Page<Manufacturer> queryMData(Page<Manufacturer> page, SupplierVo supplierVo) {
        return supplierMapper.queryMData(page,supplierVo);
    }
}
