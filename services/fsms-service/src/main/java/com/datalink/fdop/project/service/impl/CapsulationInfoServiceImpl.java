package com.datalink.fdop.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.project.api.domain.*;
import com.datalink.fdop.project.api.model.vo.CapsulationInfoShowVo;
import com.datalink.fdop.project.api.model.vo.CapsulationInfoVo;
import com.datalink.fdop.project.mapper.CapsulationInfoMapper;
import com.datalink.fdop.project.service.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/10/10 16:40
 */
@Service
@Transactional
public class CapsulationInfoServiceImpl extends ServiceImpl<CapsulationInfoMapper, CapsulationInfo> implements CapsulationInfoService {

    @Autowired
    private CapsulationInfoMapper capsulationInfoMapper;
    @Autowired
    private ManufacturerService manufacturerService;
    @Autowired
    private MaterialService materialService;
    @Autowired
    private ManufacturerManageService manufacturerManageService;
    @Autowired
    private SupplierService supplierService;

    @Override
    public void checkObject(CapsulationInfo capsulationInfo,String serialNum) {
        String matnr = capsulationInfo.getMaterialCode();//内部物料编码
        String lifnr = capsulationInfo.getSupplierCode();//供应商编码
        String versi = capsulationInfo.getVersions();//封裝信息版本
        String statu = capsulationInfo.getStatu();//封裝信息状态
        if (StringUtils.isEmpty(matnr)){
            throw new ServiceException("内部物料编码为空");
        }
        if (StringUtils.isEmpty(lifnr)){
            throw new  ServiceException("供应商编码为空");
        }
        if (StringUtils.isEmpty(versi)){
            throw new  ServiceException("封裝信息版本为空");
        }
        if (StringUtils.isEmpty(statu)){
            throw new  ServiceException("封裝信息状态为空");
        }
        ManufacturerManage one = manufacturerManageService.getOne(new QueryWrapper<ManufacturerManage>().lambda().eq(ManufacturerManage::getSerialNum, serialNum));
        if (one.getStatu().equals("0")) {
            List<Supplier> list = supplierService.list(new QueryWrapper<Supplier>()
                    .lambda()
                    .eq(Supplier::getSupplierCode, capsulationInfo.getSupplierCode())
            );
            if (CollectionUtils.isEmpty(list)) {
                throw new ServiceException("供应商编码不存在，请先维护！");
            }
        }else {
            if (StringUtils.isEmpty(capsulationInfo.getManufCode())){
                throw new  ServiceException("制造商代码为空！");
            }
            if (StringUtils.isEmpty(capsulationInfo.getManufType())){
                throw new  ServiceException("制造商类别为空！");
            }
            List<Manufacturer> list = manufacturerService.list(new QueryWrapper<Manufacturer>()
                    .lambda()
                    .eq(Manufacturer::getSupplierCode, capsulationInfo.getSupplierCode())
                    .eq(StringUtils.isNotEmpty(capsulationInfo.getManufCode()),Manufacturer::getManufCode, capsulationInfo.getManufCode())
                    .eq(StringUtils.isNotEmpty(capsulationInfo.getManufType()),Manufacturer::getManufType, capsulationInfo.getManufType()));
            if (CollectionUtils.isEmpty(list)) {
                throw new ServiceException("供应商编码&制造商代码&制造商类别组合不存在，请先维护！");
            }
        }
        List<Material> list1 = materialService.list(new QueryWrapper<Material>()
                .lambda()
                .eq(Material::getMaterialCode, capsulationInfo.getMaterialCode()));
        if (CollectionUtils.isEmpty(list1)) {
            throw new ServiceException("内部物料编码不存在，请先维护！");
        }
    }

    @Override
    public Page<CapsulationInfoShowVo> pageData(Page<CapsulationInfoShowVo> page, CapsulationInfoVo infoVo) {
        return capsulationInfoMapper.pageData(page,infoVo);
    }
}
