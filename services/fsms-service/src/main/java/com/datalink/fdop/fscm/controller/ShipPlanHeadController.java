package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.ShipRequestHead;
import com.datalink.fdop.fscm.api.model.vo.ShipPlanQueryVo;
import com.datalink.fdop.fscm.api.model.vo.ShipPlanShowVo;
import com.datalink.fdop.fscm.service.ShipPlanHeadService;
import com.datalink.fdop.fscm.service.ShipRequestHeadService;
import com.datalink.fdop.fscm.service.ShipRequestRowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/12/15 10:07
 */
@RestController
@RequestMapping("/ship/plan/head")
@Transactional
@Api(tags = "出货计划头")
public class ShipPlanHeadController {

    @Autowired
    private ShipPlanHeadService shipPlanHeadService;
    @Autowired
    private ShipRequestHeadService requestHeadService;
    @Autowired
    private ShipRequestRowService requestRowService;


    @ApiOperation(value = "出货计划查询")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/query")
    public R query(@RequestBody ShipPlanQueryVo shipPlanQueryVo, @RequestParam(required = false) String delFlag) {

        Page<ShipPlanShowVo> page = PageUtils.getPage(ShipPlanShowVo.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (shipPlanQueryVo.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<ShipPlanShowVo> pageData= shipPlanHeadService.queryData(page,shipPlanQueryVo,delFlag);
        pageData.getRecords().stream().forEach(shipPlanShowVo -> {
            List<ShipRequestHead> list = requestHeadService.list(new QueryWrapper<ShipRequestHead>().lambda()
                    .eq(ShipRequestHead::getShipPlanOrderNum, shipPlanShowVo.getShipPlanOrderNum()));
            List<String> shipRequestOrderNums = list.stream().map(ShipRequestHead::getShipRequestOrderNum).collect(Collectors.toList());
           /* double quantityDelivery =0.0;
            if (CollectionUtils.isNotEmpty(shipRequestOrderNums)) {
                List<ShipRequestRow> shipRequestRows = requestRowService.list(new QueryWrapper<ShipRequestRow>().lambda()
                        .eq(ShipRequestRow::getShipPlanOrderRowNum, shipPlanShowVo.getShipPlanOrderRowNum())
                        .in(ShipRequestRow::getShipRequestOrderNum, shipRequestOrderNums)
                );
                quantityDelivery = shipRequestRows.stream().map(ShipRequestRow::getQuantityDelivery).mapToDouble(m -> m).sum();
            }*/
            if (shipPlanShowVo.getForwardedRequisitionForm()==null) {
                shipPlanShowVo.setForwardedRequisitionForm(0.0);
            }
            //shipPlanShowVo.setQuantityDelivery(quantityDelivery);
            shipPlanShowVo.setRequisitionFormCount( shipPlanShowVo.getQuantityDelivery()-(shipPlanShowVo.getForwardedRequisitionForm()==null?0.00:shipPlanShowVo.getForwardedRequisitionForm()));

        });
        List<ShipPlanShowVo> records = pageData.getRecords();
        if (shipPlanQueryVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(shipPlanQueryVo.getSearchVo(), records);
            pageData.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records,(int)pageData.getTotal()));
    }
    @ApiOperation(value = "导出")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/download")
    public void download(HttpServletResponse response, @RequestBody ShipPlanQueryVo shipPlanQueryVo, @RequestParam(required = false) String delFlag) throws IOException {
        Page<ShipPlanShowVo> page = PageUtils.getPage(ShipPlanShowVo.class);
        if (shipPlanQueryVo.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<ShipPlanShowVo> pageData= shipPlanHeadService.queryData(page,shipPlanQueryVo,delFlag);
        pageData.getRecords().stream().forEach(shipPlanShowVo -> {
            List<ShipRequestHead> list = requestHeadService.list(new QueryWrapper<ShipRequestHead>().lambda()
                    .eq(ShipRequestHead::getShipPlanOrderNum, shipPlanShowVo.getShipPlanOrderNum()));
            List<String> shipRequestOrderNums = list.stream().map(ShipRequestHead::getShipRequestOrderNum).collect(Collectors.toList());
            /*double quantityDelivery =0.0;
            if (CollectionUtils.isNotEmpty(shipRequestOrderNums)) {
                List<ShipRequestRow> shipRequestRows = requestRowService.list(new QueryWrapper<ShipRequestRow>().lambda()
                        .eq(ShipRequestRow::getShipPlanOrderRowNum, shipPlanShowVo.getShipPlanOrderRowNum())
                        .in(ShipRequestRow::getShipRequestOrderNum, shipRequestOrderNums)
                );
                quantityDelivery = shipRequestRows.stream().map(ShipRequestRow::getQuantityDelivery).mapToDouble(m -> m).sum();
            }*/
            if (shipPlanShowVo.getForwardedRequisitionForm()==null) {
                shipPlanShowVo.setForwardedRequisitionForm(0.0);
            }
            //shipPlanShowVo.setQuantityDelivery(quantityDelivery);
            shipPlanShowVo.setRequisitionFormCount( shipPlanShowVo.getQuantityDelivery()-(shipPlanShowVo.getForwardedRequisitionForm()==null?0.00:shipPlanShowVo.getForwardedRequisitionForm()));

        });
        List<ShipPlanShowVo> records = pageData.getRecords();
        if (shipPlanQueryVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(shipPlanQueryVo.getSearchVo(), records);
        }
        ExcelUtils.export3Excel(response,records, ShipPlanShowVo.class,"出货计划");
    }

}
