package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.PostCertificateRow;
import com.datalink.fdop.fscm.api.domain.ProvisionalSingleOut;
import com.datalink.fdop.fscm.api.domain.ScrapHead;
import com.datalink.fdop.fscm.api.domain.ScrapRow;
import com.datalink.fdop.fscm.api.model.vo.PostCertificateVo;
import com.datalink.fdop.fscm.api.model.vo.ScrapHeadQueryVo;
import com.datalink.fdop.fscm.api.model.vo.ScrapHeadShowVo;
import com.datalink.fdop.fscm.api.model.vo.ScrapSaveVo;
import com.datalink.fdop.fscm.service.PostCertificateRowService;
import com.datalink.fdop.fscm.service.ProvisionalSingleOutService;
import com.datalink.fdop.fscm.service.ScrapHeadService;
import com.datalink.fdop.fscm.service.ScrapRowService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/1/30 10:17
 */

@RestController
@RequestMapping("/scrap/head")
@Api(tags = "报废管理头")
public class ScrapHeadController {

    @Autowired
    private ScrapHeadService scrapHeadService;
    @Autowired
    private ScrapRowService scrapRowService;
    @Autowired
    private PostCertificateRowService postCertificateRowService;

    @Autowired
    private ProvisionalSingleOutService provisionalSingleOutService;


    @ApiOperation(value = "查询")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/query")
    public R query(@RequestBody ScrapHeadQueryVo scrapHeadQueryVo, @RequestParam(required = false) String delFlag,@RequestParam(required = false) String temporary,@RequestParam(required = false) Boolean postFlag) {

        Page<ScrapHeadShowVo> page = PageUtils.getPage(ScrapHeadShowVo.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        List<ScrapHeadShowVo> records= scrapHeadService.queryData(scrapHeadQueryVo,delFlag,temporary);
        for (ScrapHeadShowVo record : records) {
            List<PostCertificateRow> rows = postCertificateRowService.list(new QueryWrapper<PostCertificateRow>()
                    .lambda()
                    .eq(PostCertificateRow::getScrapOrderNum,record.getScrapOrderNum())
                    .eq(PostCertificateRow::getScrapOrderRowNum,record.getScrapOrderRowNum())
                    .in(PostCertificateRow::getMoveType, Arrays.asList("551","552"))
            );
            double sum = rows.stream().map(PostCertificateRow::getPostQuantity).mapToDouble(m -> m).sum();
            record.setPostedQuantity(sum*-1);
        }
        if (postFlag!=null&&postFlag) {
            records=records.stream().filter(p->p.getPostedQuantity()==0.0).collect(Collectors.toList());
        }
        if (scrapHeadQueryVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(scrapHeadQueryVo.getSearchVo(), records);

        }
        long total=records.size();
        records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        return R.ok(PageUtils.getPageInfo(records,(int)total));
    }


    @ApiOperation(value = "导出")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/download")
    public void download( HttpServletResponse response,@RequestBody ScrapHeadQueryVo scrapHeadQueryVo, @RequestParam(required = false) String delFlag,@RequestParam(required = false) String temporary,@RequestParam(required = false) Boolean postFlag) throws IOException {
        List<ScrapHeadShowVo> records= scrapHeadService.queryData(scrapHeadQueryVo,delFlag,temporary);
        for (ScrapHeadShowVo record : records) {
            List<PostCertificateRow> rows = postCertificateRowService.list(new QueryWrapper<PostCertificateRow>()
                    .lambda()
                    .eq(PostCertificateRow::getScrapOrderNum,record.getScrapOrderNum())
                    .eq(PostCertificateRow::getScrapOrderRowNum,record.getScrapOrderRowNum())
                    .in(PostCertificateRow::getMoveType, Arrays.asList("551","552"))
            );
            double sum = rows.stream().map(PostCertificateRow::getPostQuantity).mapToDouble(m -> m).sum();
            record.setPostedQuantity(sum*-1);
        }
        if (postFlag!=null&&postFlag) {
            records=records.stream().filter(p->!Double.isNaN(p.getPostedQuantity())).collect(Collectors.toList());
        }
        if (scrapHeadQueryVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(scrapHeadQueryVo.getSearchVo(), records);

        }
        ExcelUtils.export3Excel(response,records, ScrapHeadShowVo.class,"报废管理");
    }



    @ApiOperation(value = "保存")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/save")
    public R save(@RequestBody ScrapSaveVo scrapSaveVo) {
        ScrapHead scrapHead = scrapSaveVo.getScrapHead();

        //删除挑选的缓存
        ProvisionalSingleOut provisionalSingleOut = new ProvisionalSingleOut();
        provisionalSingleOutService.remove(new QueryWrapper<ProvisionalSingleOut>().lambda().eq(ProvisionalSingleOut::getScrapOrderNum,scrapHead.getScrapOrderNum()));
        scrapHeadService.remove(new QueryWrapper<ScrapHead>().lambda().eq(ScrapHead::getScrapOrderNum, scrapHead.getScrapOrderNum()));

        List<ScrapRow> scrapRows = Lists.newArrayList();
        scrapRows.addAll(scrapSaveVo.getScrapRowsUpdate());
        scrapRows.addAll(scrapSaveVo.getScrapRowsDel());
        for (ScrapRow scrapRow : scrapRows) {
            scrapRowService.remove(new QueryWrapper<ScrapRow>()
                    .lambda()
                    .eq(ScrapRow::getScrapOrderNum, scrapRow.getScrapOrderNum())
                    .eq(ScrapRow::getScrapOrderRowNum, scrapRow.getScrapOrderRowNum())
            );

        }
        scrapHeadService.save(scrapHead);
        scrapRows.addAll(scrapSaveVo.getScrapRowsInsert());
        scrapRowService.saveBatch(scrapRows);
        scrapHeadService.updateRepertory(scrapSaveVo);
        return R.ok();
    }
    @ApiOperation(value = "审批")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/examine")
    public R examine(@RequestBody List<ScrapHead> scrapHeads) {
        for (ScrapHead scrapHead : scrapHeads) {
            scrapHeadService.update(Wrappers.lambdaUpdate(ScrapHead.class)
                    .set(ScrapHead::getStatu,scrapHead.getStatu())
                    .eq(ScrapHead::getScrapOrderNum,scrapHead.getScrapOrderNum())
            );
        }
        return R.ok();
    }

    @ApiOperation(value = "保存数据检查")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/save/check")
    public R saveCheck(@RequestBody ScrapSaveVo scrapSaveVo) {
        //检查数据
        List<ScrapRow> scrapRowsDel = scrapSaveVo.getScrapRowsDel();
        scrapRowsDel.addAll(scrapSaveVo.getScrapRowsUpdate());
        for (ScrapRow scrapRow : scrapRowsDel) {
            scrapRowService.remove(new QueryWrapper<ScrapRow>().lambda()
                    .eq(ScrapRow::getScrapOrderNum,scrapRow.getScrapOrderNum())
                    .eq(ScrapRow::getScrapOrderRowNum,scrapRow.getScrapOrderRowNum())
            );
        }
        List<ScrapRow> list = Lists.newArrayList();
        list.addAll(scrapSaveVo.getScrapRowsInsert());
        list.addAll(scrapSaveVo.getScrapRowsUpdate());
        scrapHeadService.saveCheck(list);
        return R.ok();
    }

    @ApiOperation(value = "过帐数据检查")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/post/certificate/check")
    public R certificateCheck(@RequestBody PostCertificateVo postCertificateVo) {
        //检查数据
        scrapHeadService.checkData(postCertificateVo.getPostCertificateRows());
        return R.ok();
    }


    @ApiOperation(value = "过帐")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/post/certificate")
    public R certificate(@RequestBody PostCertificateVo postCertificateVo) {
        //保存数据
        scrapHeadService.saveData(postCertificateVo);

        return R.ok();
    }
}
