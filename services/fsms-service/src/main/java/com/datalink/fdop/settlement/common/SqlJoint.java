package com.datalink.fdop.settlement.common;


import cn.hutool.core.util.ObjectUtil;
import com.datalink.fdop.drive.api.domain.dto.Field;

import java.util.List;
import java.util.Map;

public class SqlJoint {




    public static String priceDefinitionSql(String s, String table, List<Field> fields, List<Map<String,Object>> datas){
        String resultSql="";
        for (Map<String, Object> data : datas) {
            String sql="insert into " +s+"."+table.toLowerCase()+"(";
            String valueString="";
            for (Field field : fields) {
                sql+="\""+field.getFieldName()+"\",";
                Object o = data.get(field.getFieldName());
                if (ObjectUtil.isEmpty(o)) {
                    o="empty";
                }
                valueString+="'"+o+"',";
            }
            sql =sql.substring(0,sql.length()-1);
            valueString=valueString.substring(0,valueString.length()-1);
            sql +=") VALUES ("+valueString+");";
            resultSql+=sql;
        }

        resultSql=resultSql.substring(0,resultSql.length()-1);
        return resultSql;
    }


}
