package com.datalink.fdop.settlement.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.common.core.constant.Constants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.drive.api.RemoteDriveService;
import com.datalink.fdop.drive.api.RemoteJdbcService;
import com.datalink.fdop.drive.api.domain.DataSource;
import com.datalink.fdop.drive.api.domain.dto.CreateTableDto;
import com.datalink.fdop.drive.api.domain.dto.Field;
import com.datalink.fdop.settlement.api.domain.PriceDefinition;
import com.datalink.fdop.settlement.api.domain.PriceElement;
import com.datalink.fdop.settlement.api.model.vo.PriceSelectVo;
import com.datalink.fdop.settlement.common.SqlJoint;
import com.datalink.fdop.settlement.service.PriceDefinitionService;
import com.datalink.fdop.settlement.mapper.PriceDefinitionMapper;
import com.datalink.fdop.settlement.service.PriceElementService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【f_s_price_definition】的数据库操作Service实现
 * @createDate 2023-03-30 15:49:27
 */
@Service
@Slf4j
@Transactional
public class PriceDefinitionServiceImpl extends ServiceImpl<PriceDefinitionMapper, PriceDefinition>
        implements PriceDefinitionService {

    @Autowired
    private PriceElementService priceElementService;
    @Autowired
    private RemoteDriveService remoteDriveService;
    @Autowired
    private RemoteJdbcService remoteJdbcService;
    @Autowired
    private PriceDefinitionMapper priceDefinitionMapper;

    @Override
    public void createTable(List<PriceDefinition> priceDefinitions) {
        //获取数据源
        R<DataSource> dataSourceR = remoteDriveService.queryDataSource(Constants.FSES_DATASOURCE_CODE);
        if (dataSourceR.getCode()!=200) {
            throw new ServiceException("获取数据源失败");
        }
        DataSource data = dataSourceR.getData();
        for (PriceDefinition priceDefinition : priceDefinitions) {
            if (priceDefinition.getPriceFactorType().equals("2")) {
                R<Boolean> existTableR = remoteJdbcService.isExistTable(data.getId(), Constants.FSES_DATASOURCE, priceDefinition.getTable().toLowerCase());
                if (existTableR.getCode()!=200) {
                    throw new ServiceException("查询表是否存在失败");
                }
                if (existTableR.getData()) {
                    remoteJdbcService.dropTable(data.getId(), Constants.FSES_DATASOURCE, priceDefinition.getTable().toLowerCase());
                }
                //填写表名字段信息
                CreateTableDto createTableDto =new CreateTableDto();
                createTableDto.setTableName(priceDefinition.getTable());
                createTableDto.setDatabaseName(Constants.FSES_DATASOURCE);
                createTableDto.setDataSourceId(data.getId());
                List<PriceElement> elements = priceElementService.list(new QueryWrapper<PriceElement>().lambda()
                        .eq(PriceElement::getPriceFactor, priceDefinition.getPriceFactor())
                );
                List<Field> fields= Lists.newArrayList();
                for (PriceElement element : elements) {
                    Field field=new Field();
                    field.setFieldName(element.getChangingFactor());
                    field.setFieldType("VARCHAR");
                    field.setLength(200L);
                    //field.setFieldDesc(element.getChangingFactorDesc());
                    field.setIsPk(true);
                    field.setIsNull(false);
                    field.setFieldDefault("empty");
                    fields.add(field);
                }
                Field field2=new Field();
                field2.setFieldName("supplier_code");
                field2.setFieldType("VARCHAR");
                field2.setLength(10L);
                field2.setIsPk(true);
                field2.setIsNull(false);
                fields.add(field2);
                Field field3=new Field();
                field3.setFieldName("valid_from");
                field3.setFieldType("date");
                field3.setLength(0L);
                field3.setIsPk(true);
                field3.setIsNull(false);
                fields.add(field3);
                Field field4=new Field();
                field4.setFieldName("valid_to");
                field4.setFieldType("date");
                field4.setLength(0L);
                field4.setIsPk(true);
                field4.setIsNull(false);
                fields.add(field4);
                Field field5=new Field();
                field5.setFieldName("quotation_num");
                field5.setFieldType("VARCHAR");
                field5.setLength(20l);
                field5.setFieldDesc("供应商编码");
                field5.setIsPk(true);
                field5.setIsNull(false);
                fields.add(field5);
                Field field6=new Field();
                field6.setFieldName(priceDefinition.getPriceFactor());
                field6.setFieldType("numeric");
                field6.setLength(13l);
                field6.setDecimalLength(3l);
                field6.setFieldDesc("价格因子描述");
                field6.setIsPk(false);
                field6.setIsNull(false);
                fields.add(field6);
                createTableDto.setFieldList(fields);
                //执行
                R table = remoteJdbcService.createTable(createTableDto);
                if (table.getCode()!=200) {
                    throw new ServiceException("创表失败");
                }
                log.info("创表："+table.toString());
            }
        }
    }

    @Override
    public void templateDownload(String table, String priceFactor, HttpServletResponse response) throws IOException {
        List<List<Object>> dataList = Lists.newArrayList();
        List<List<String>> headList = Lists.newArrayList();
        //获取数据源
        R<DataSource> dataSourceR = remoteDriveService.queryDataSource(Constants.FSES_DATASOURCE_CODE);
        if (dataSourceR.getCode()!=200) {
            throw new ServiceException("获取数据源失败");
        }
        DataSource data = dataSourceR.getData();
        R<List<Field>> fieldsR = remoteJdbcService.getFields(data.getId(), Constants.FSES_DATASOURCE, table.toLowerCase());
        if (fieldsR.getCode()!=200) {
            throw new ServiceException("获取表字段失败");
        }
        List<Field> fields = fieldsR.getData();
        for (Field field : fields) {
            List<String> head = Lists.newArrayList();
            head.add(field.getFieldName());
            headList.add(head);
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileName = URLEncoder.encode(priceFactor, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream()).head(headList).sheet("模板").doWrite(dataList);

    }

    @Override
    public List<Field> getField(String table) {
        //获取数据源
        R<DataSource> dataSourceR = remoteDriveService.queryDataSource(Constants.FSES_DATASOURCE_CODE);
        if (dataSourceR.getCode()!=200) {
            throw new ServiceException("获取数据源失败");
        }
        DataSource data = dataSourceR.getData();
        R<List<Field>> fieldsR = remoteJdbcService.getFields(data.getId(), Constants.FSES_DATASOURCE, table.toLowerCase());
        if (fieldsR.getCode()!=200) {
            throw new ServiceException("获取表字段失败");
        }
        List<Field> fields = fieldsR.getData();
        return fields;
    }

    @Override
    public void delTable(PriceDefinition priceDefinition) {
        //获取数据源
        R<DataSource> dataSourceR = remoteDriveService.queryDataSource(Constants.FSES_DATASOURCE_CODE);
        if (dataSourceR.getCode()!=200) {
            throw new ServiceException("获取数据源失败");
        }
        DataSource data = dataSourceR.getData();
        R<Boolean> existTableR = remoteJdbcService.isExistTable(data.getId(), Constants.FSES_DATASOURCE, priceDefinition.getTable().toLowerCase());
        if (existTableR.getCode()!=200) {
            throw new ServiceException("查询表是否存在失败");
        }
        if (existTableR.getData()) {
            remoteJdbcService.dropTable(data.getId(), Constants.FSES_DATASOURCE, priceDefinition.getTable().toLowerCase());
        }
    }

    @Override
    public void insertData(List<Map<String, Object>> data, String table) {
        //生成sql
        if (CollectionUtils.isNotEmpty(data)&&data.size()>0) {
            List<Field> fields = this.getField(table);
            String sql = SqlJoint.priceDefinitionSql(Constants.FSES_DATASOURCE, table, fields, data);
            try {
                priceDefinitionMapper.execSql(sql);
            }catch (Exception e){
                e.printStackTrace();
                throw new ServiceException("仍有数据有误，不能导入！且数据导入不成功");
            }
        }

    }

    @Override
    public Page<Map<String, Object>> selectData(String table, PriceSelectVo priceSelectVo, Page<Map> page) {
        return priceDefinitionMapper.selectData(table, priceSelectVo,page);
    }

    @Override
    public void downData(String table, HttpServletResponse response, String priceFactor, PriceSelectVo priceSelectVo) throws IOException {
        List<List<String>> dataList = Lists.newArrayList();
        List<List<String>> headList = Lists.newArrayList();
        //获取数据源
        R<DataSource> dataSourceR = remoteDriveService.queryDataSource(Constants.FSES_DATASOURCE_CODE);
        if (dataSourceR.getCode()!=200) {
            throw new ServiceException("获取数据源失败");
        }
        DataSource data = dataSourceR.getData();
        R<List<Field>> fieldsR = remoteJdbcService.getFields(data.getId(), Constants.FSES_DATASOURCE, table);
        if (fieldsR.getCode()!=200) {
            throw new ServiceException("获取表字段失败");
        }
        Page<Map> page = PageUtils.getPage(Map.class);
        Page<Map<String, Object>> mapList = priceDefinitionMapper.selectData(table, priceSelectVo, page);
        List<Field> fields = fieldsR.getData();
        for (Field field : fields) {
            List<String> head = Lists.newArrayList();
            head.add(field.getFieldName());
            headList.add(head);
        }
        for (Map<String, Object> map : mapList.getRecords()) {
            List<String> dataVal = Lists.newArrayList();
            for (Field field : fields) {
                Object o = map.get(field.getFieldName());
                String val="";
                if (ObjectUtil.isNotEmpty(o)) {
                    val=map.get(field.getFieldName()).toString();
                }
                dataVal.add(val);
            }
            dataList.add(dataVal);
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileName = URLEncoder.encode(priceFactor, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream()).head(headList).sheet("模板").doWrite(dataList);
    }
}




