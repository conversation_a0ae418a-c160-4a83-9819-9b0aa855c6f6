package com.datalink.fdop.project.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.project.api.domain.Material;
import com.datalink.fdop.project.api.model.vo.MaterialVo;
import com.datalink.fdop.project.mapper.MaterialMapper;
import com.datalink.fdop.project.service.MaterialService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Date 2022/10/9 15:50
 */
@Service
@Transactional
public class MaterialServiceImpl extends ServiceImpl<MaterialMapper,Material> implements MaterialService {

    @Autowired
    private MaterialMapper materialMapper;

    @Override
    public Page<Material> pageData(Page<Material> page, MaterialVo material) {
        return materialMapper.pageData(page,material);
    }

}
