package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.WorkOrderTest;
import com.datalink.fdop.fscm.service.WorkOrderTestService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/11/24 15:32
 */
@RestController
@RequestMapping("/work/order/test")
@Transactional
@Api(tags = "工单测试")
public class WorkOrderTestController {


    @Autowired
    private WorkOrderTestService workOrderTestService;

    @ApiOperation(value = "工单子件查询")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/test/query")
    public R workOrderTestQuery(@RequestBody(required = false) WorkOrderTest workOrderTest) {
        Page<WorkOrderTest> page = PageUtils.getPage(WorkOrderTest.class);
        Page<WorkOrderTest> dataPage= workOrderTestService.page(page,new QueryWrapper<WorkOrderTest>()
                .lambda()
                .eq(StringUtils.isNotEmpty(workOrderTest.getWorkOrderNum()),WorkOrderTest::getWorkOrderNum,workOrderTest.getWorkOrderNum())
                .eq(workOrderTest.getWorkOrderRowNum()!=null,WorkOrderTest::getWorkOrderRowNum,workOrderTest.getWorkOrderRowNum())
        );
        List<WorkOrderTest> records = dataPage.getRecords();

        return R.ok(PageUtils.getPageInfo(records,(int)dataPage.getTotal()));
    }
}
