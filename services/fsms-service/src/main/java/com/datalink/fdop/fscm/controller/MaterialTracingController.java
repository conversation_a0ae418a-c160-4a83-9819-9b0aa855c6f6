package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.model.vo.MaterialTracingQueryVo;
import com.datalink.fdop.fscm.api.model.vo.MaterialTracingShowVo;
import com.datalink.fdop.fscm.service.MaterialTracingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/2/10 9:55
 */
@RestController
@RequestMapping("/material/tracing")
@Transactional
@Api(tags = "材料使用追溯")
public class MaterialTracingController {

    @Autowired
    private MaterialTracingService materialTracingService;

    @ApiOperation(value = "追溯")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/query")
    public R query(@RequestBody MaterialTracingQueryVo materialTracingQueryVo) {
        //根据条件查出层级数据
        List<MaterialTracingShowVo> records = materialTracingService.queryData(materialTracingQueryVo);
        String tracingType = materialTracingQueryVo.getTracingType();

        records= records.stream().map(record -> {
            if (StringUtils.isNotEmpty(record.getUsePiece())&&!record.getUsePiece().equals("empty")&&(StringUtils.isEmpty(record.getUseBinNum())||record.getUseBinNum().equals("empty"))) {
                if (record.getUsePiece().equals(record.getOutputPiece())||StringUtils.isEmpty(record.getOutputPiece())) {
                    return record;
                }
            }else if((StringUtils.isEmpty(record.getUsePiece())||record.getUsePiece().equals("empty"))&&StringUtils.isNotEmpty(record.getUseBinNum())&&!record.getUseBinNum().equals("empty")){
                if (record.getUseBinNum().equals(record.getOutputBinNum())||StringUtils.isEmpty(record.getOutputBinNum())) {
                    return record;
                }
            } else if(StringUtils.isNotEmpty(record.getUsePiece())&&!record.getUsePiece().equals("empty")&&StringUtils.isNotEmpty(record.getUseBinNum())&&!record.getUseBinNum().equals("empty")){
                if ((record.getUseBinNum().equals(record.getOutputBinNum())||StringUtils.isEmpty(record.getOutputBinNum()))&&(record.getUsePiece().equals(record.getOutputPiece())||StringUtils.isEmpty(record.getOutputPiece()))) {
                    return record;
                }
            }else {
                return record;
            }
            return null;
        }).filter(record->record!=null).collect(Collectors.toList());


        if (tracingType.equals("1")) {
            records = records.stream().collect(
                    Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(
                                    Comparator.comparing(p -> p.getWorkOrderNum() + ";"
                                            + p.getWorkOrderRowNum() + ";"
                                            + p.getOutputMaterialCode() + ";"
                                            + p.getOutputBatchNumber()
                                    ))), ArrayList::new));
        } else if (tracingType.equals("2")) {
            records = records.stream().collect(
                    Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(
                                    Comparator.comparing(p ->
                                            p.getWorkOrderNum() + ";"
                                                    + p.getWorkOrderRowNum() + ";"
                                                    + p.getOutputMaterialCode() + ";"
                                                    + p.getOutputBatchNumber() + ";"
                                                    + p.getOutputPiece() + ";"
                                                    + p.getOutputBinNum() + ";"
                                                    + p.getUsePiece() + ";"
                                                    + p.getUseBinNum() + ";"
                                                    + p.getUseBatchNumber()
                                    ))), ArrayList::new));
        }
        materialTracingService.setValueMaterialTracingShow(records, tracingType);
        if (materialTracingQueryVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(materialTracingQueryVo.getSearchVo(), records);
        }
        Page<MaterialTracingShowVo> page = PageUtils.getPage(MaterialTracingShowVo.class);
        List<MaterialTracingShowVo> collect = records.stream().skip((page.getCurrent() - 1) * page.getSize()).limit(page.getSize()).collect(Collectors.toList());
        return R.ok(PageUtils.getPageInfo(collect,records.size()));
    }





    @ApiOperation(value = "导出")
    @Log(title = "fscm",businessType = BusinessType.EXPORT)
    @PostMapping("/download")
    public void download(HttpServletResponse response,@RequestBody MaterialTracingQueryVo materialTracingQueryVo) throws IOException {
        List<MaterialTracingShowVo> records = materialTracingService.queryData(materialTracingQueryVo);
        String tracingType = materialTracingQueryVo.getTracingType();

        records= records.stream().map(record -> {
            if (StringUtils.isNotEmpty(record.getUsePiece())&&!record.getUsePiece().equals("empty")&&(StringUtils.isEmpty(record.getUseBinNum())||record.getUseBinNum().equals("empty"))) {
                if (record.getUsePiece().equals(record.getOutputPiece())||StringUtils.isEmpty(record.getOutputPiece())) {
                    return record;
                }
            }else if((StringUtils.isEmpty(record.getUsePiece())||record.getUsePiece().equals("empty"))&&StringUtils.isNotEmpty(record.getUseBinNum())&&!record.getUseBinNum().equals("empty")){
                if (record.getUseBinNum().equals(record.getOutputBinNum())||StringUtils.isEmpty(record.getOutputBinNum())) {
                    return record;
                }
            } else if(StringUtils.isNotEmpty(record.getUsePiece())&&!record.getUsePiece().equals("empty")&&StringUtils.isNotEmpty(record.getUseBinNum())&&!record.getUseBinNum().equals("empty")){
                if ((record.getUseBinNum().equals(record.getOutputBinNum())||StringUtils.isEmpty(record.getOutputBinNum()))&&(record.getUsePiece().equals(record.getOutputPiece())||StringUtils.isEmpty(record.getOutputPiece()))) {
                    return record;
                }
            }else {
                return record;
            }
            return null;
        }).filter(record->record!=null).collect(Collectors.toList());


        if (tracingType.equals("1")) {
            records = records.stream().collect(
                    Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(
                                    Comparator.comparing(p -> p.getWorkOrderNum() + ";"
                                            + p.getWorkOrderRowNum() + ";"
                                            + p.getOutputMaterialCode() + ";"
                                            + p.getOutputBatchNumber()
                                    ))), ArrayList::new));
        } else if (tracingType.equals("2")) {
            records = records.stream().collect(
                    Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(
                                    Comparator.comparing(p ->
                                            p.getWorkOrderNum() + ";"
                                                    + p.getWorkOrderRowNum() + ";"
                                                    + p.getOutputMaterialCode() + ";"
                                                    + p.getOutputBatchNumber() + ";"
                                                    + p.getOutputPiece() + ";"
                                                    + p.getOutputBinNum() + ";"
                                                    + p.getUsePiece() + ";"
                                                    + p.getUseBinNum() + ";"
                                                    + p.getUseBatchNumber()
                                    ))), ArrayList::new));
        }
        materialTracingService.setValueMaterialTracingShow(records, tracingType);
        if (materialTracingQueryVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(materialTracingQueryVo.getSearchVo(), records);
        }
        //将数据平铺成二维
        List<MaterialTracingShowVo> list=materialTracingService.toTable(records,false,"");
        ExcelUtils.export3Excel(response, list, MaterialTracingShowVo.class, "材料使用追溯");
    }
}
