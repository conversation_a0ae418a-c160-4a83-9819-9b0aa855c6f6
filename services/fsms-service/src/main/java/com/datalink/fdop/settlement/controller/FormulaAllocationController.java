package com.datalink.fdop.settlement.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.PieceConfig;
import com.datalink.fdop.settlement.api.domain.FormulaAllocation;
import com.datalink.fdop.settlement.api.domain.FormulaDefinition;
import com.datalink.fdop.settlement.api.model.vo.FormulaAllocationVo;
import com.datalink.fdop.settlement.service.FormulaAllocationService;
import com.datalink.fdop.settlement.service.FormulaDefinitionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/settlement/formula/allocation")
@Transactional
@Api(tags = "公式分配")
public class FormulaAllocationController {

    @Autowired
    private FormulaAllocationService formulaAllocationService;

    @Autowired
    private FormulaDefinitionService formulaDefinitionService;

    @ApiOperation(value = "查询")
    @Log(title = "settlement", businessType = BusinessType.UPDATE)
    @PostMapping("/query")
    public R query(@RequestBody(required = false) FormulaAllocation formulaAllocation) {
        Page<FormulaAllocation> page = PageUtils.getPage(FormulaAllocation.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        SearchVo searchVo = formulaAllocation.getSearchVo();
        if (searchVo != null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<FormulaAllocation> pageData = formulaAllocationService.page(page);
        List<FormulaAllocation> records = pageData.getRecords();
        records.stream().forEach(f -> {
            FormulaDefinition definition = formulaDefinitionService.getOne(new QueryWrapper<FormulaDefinition>().lambda()
                    .eq(FormulaDefinition::getFormula, f.getFormula())
                    .eq(FormulaDefinition::getProcess, f.getProcess())
            );
            f.setFormulaDesc(definition != null ? definition.getFormulaDesc() : null);
        });
        if (searchVo != null) {
            records = SearchUtils.getByEntityFilter(searchVo, records);
            page.setTotal(records.size());
            records = records.stream().skip(pageSize * (current - 1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records, (int) pageData.getTotal()));
    }


    @ApiOperation(value = "新增")
    @Log(title = "settlement", businessType = BusinessType.UPDATE)
    @PostMapping("/create")
    public R create(@RequestBody List<FormulaAllocation> formulaAllocations) {
        if (formulaAllocationService.saveBatch(formulaAllocations)) {
            return R.ok("保存成功");
        } else {
            return R.fail("保存失败");
        }
    }

    @ApiOperation(value = "修改")
    @Log(title = "settlement", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public R update(@RequestBody List<FormulaAllocation> formulaAllocations) {
        for (FormulaAllocation formulaAllocation : formulaAllocations) {
            if (!formulaAllocationService.update(new UpdateWrapper<FormulaAllocation>().lambda()
                    .set(FormulaAllocation::getFormula, formulaAllocation.getFormula())
                    .set(FormulaAllocation::getProcess, formulaAllocation.getProcess())
                    .set(FormulaAllocation::getSupplierCode, formulaAllocation.getSupplierCode())
                    .eq(FormulaAllocation::getFormula, formulaAllocation.getOldFormula())
                    .eq(FormulaAllocation::getProcess, formulaAllocation.getOldProcess())
                    .eq(FormulaAllocation::getSupplierCode, formulaAllocation.getOldSupplierCode())
            )) {
                throw new ServiceException("修改失败");
            }
        }

        return R.ok("修改成功");

    }

    @ApiOperation(value = "删除")
    @Log(title = "settlement", businessType = BusinessType.UPDATE)
    @PostMapping("/del")
    public R del(@RequestBody List<FormulaAllocation> formulaAllocations) {
        for (FormulaAllocation formulaAllocation : formulaAllocations) {
            formulaAllocationService.remove(new QueryWrapper<FormulaAllocation>().lambda()
                    .eq(FormulaAllocation::getFormula, formulaAllocation.getFormula())
                    .eq(FormulaAllocation::getProcess, formulaAllocation.getProcess())
                    .eq(FormulaAllocation::getSupplierCode, formulaAllocation.getSupplierCode())
            );
        }

        return R.ok();

    }

    @ApiOperation(value = "导出")
    @Log(title = "settlement", businessType = BusinessType.OTHER)
    @PostMapping("/download")
    public void download(HttpServletResponse response, @RequestBody(required = false) FormulaAllocation formulaAllocation) throws IOException {
        Page<FormulaAllocation> page = PageUtils.getPage(FormulaAllocation.class);
        SearchVo searchVo = formulaAllocation.getSearchVo();
        if (searchVo != null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<FormulaAllocation> pageData = formulaAllocationService.page(page);
        List<FormulaAllocation> records = pageData.getRecords();
        records.stream().forEach(f -> {
            FormulaDefinition definition = formulaDefinitionService.getOne(new QueryWrapper<FormulaDefinition>().lambda()
                    .eq(FormulaDefinition::getFormula, f.getFormula())
                    .eq(FormulaDefinition::getProcess, f.getProcess())
            );
            f.setFormulaDesc(definition != null ? definition.getFormulaDesc() : null);
        });
        if (searchVo != null) {
            records = SearchUtils.getByEntityFilter(searchVo, records);
        }
        ExcelUtils.export3Excel(response, records, FormulaAllocation.class, "公式分配");
    }
}
