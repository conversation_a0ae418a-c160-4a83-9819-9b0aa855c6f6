package com.datalink.fdop.fscm.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.service.SegmentConfigService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2022/11/21 15:25
 */
@RestController
@RequestMapping("/segment/config")
@Transactional
@ApiOperation("号段配置")
public class SegmentConfigController {


    @Autowired
    private SegmentConfigService segmentConfigService;

    @ApiOperation(value = "根据类型获取号段")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/get")
    public R get(@RequestParam String type) {

        Integer segment=segmentConfigService.getSegment(type);
        return R.ok(segment);
    }


}
