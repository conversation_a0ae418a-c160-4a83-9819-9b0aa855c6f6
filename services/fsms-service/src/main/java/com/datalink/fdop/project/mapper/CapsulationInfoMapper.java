package com.datalink.fdop.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.project.api.domain.CapsulationInfo;
import com.datalink.fdop.project.api.model.vo.CapsulationInfoShowVo;
import com.datalink.fdop.project.api.model.vo.CapsulationInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Date 2022/10/10 16:41
 */
@Mapper
@Transactional
public interface CapsulationInfoMapper extends BaseMapper<CapsulationInfo> {

    Page<CapsulationInfoShowVo> pageData(IPage<CapsulationInfoShowVo> page,@Param("infoVo") CapsulationInfoVo infoVo);
}
