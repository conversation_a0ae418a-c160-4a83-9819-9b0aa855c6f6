package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.utils.DateUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.BatchDimensionality;
import com.datalink.fdop.fscm.api.domain.BatchSerialNumber;
import com.datalink.fdop.fscm.service.BatchDimensionalityService;
import com.datalink.fdop.fscm.service.BatchSerialNumberService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/12/9 10:21
 */
@RestController
@RequestMapping("batch/serial/number")
@Transactional
@Api(tags = "批次流水")
public class BatchSerialNumberController {
    @Autowired
    private BatchSerialNumberService batchSerialNumberService;
    @Autowired
    private BatchDimensionalityService batchDimensionalityService;

    @ApiOperation(value = "生成批次流水")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/produce")
    public R produce(@RequestBody BatchSerialNumber batchSerialNumber) {
        String dateStr = DateUtils.parseDateToStr("yyyy-MM-dd", new Date());
        Date date = DateUtils.dateTime(DateUtils.YYYY_MM_DD, dateStr);
        BatchDimensionality one = batchDimensionalityService.getOne(new QueryWrapper<BatchDimensionality>());
        int count=0;
        int maxNum=batchSerialNumberService.getMaxNum(batchSerialNumber,one,date,count);
        return R.ok(maxNum);

    }


}
