package com.datalink.fdop.project.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.project.api.domain.Manufacturer;
import com.datalink.fdop.project.api.domain.Supplier;
import com.datalink.fdop.project.api.model.vo.SupplierVo;
import com.datalink.fdop.project.service.ManufacturerService;
import com.datalink.fdop.project.service.SupplierService;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/10/26 10:54
 */
@RestController
@RequestMapping("/supplier")
public class SupplierController {

    @Autowired
    private SupplierService supplierService;
    @Autowired
    private ManufacturerService manufacturerService;

    @ApiOperation(value = "供应商查询")
    @Log(title = "工程资料",businessType = BusinessType.OTHER)
    @PostMapping("/query")
    public R query(@RequestBody SupplierVo supplierVo) {
        Page<Supplier> page = PageUtils.getPage(Supplier.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (supplierVo.getSearchVoS()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<Supplier> dataPage= supplierService.queryData(page,supplierVo);
        Page<Manufacturer> page2 = PageUtils.getPage(Manufacturer.class);
        Long pageSize2 = page2.getSize();
        Long current2 = page2.getCurrent();
        if (supplierVo.getSearchVoM()!=null) {
            page2.setSize(Long.MAX_VALUE);
        }
        Page<Manufacturer> dataMPage= supplierService.queryMData(page2,supplierVo);
        List<Supplier> records = dataPage.getRecords();
        if (supplierVo.getSearchVoS()!=null) {
            records = SearchUtils.getByEntityFilter(supplierVo.getSearchVoS(), records);
            dataPage.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        PageDataInfo<Supplier> pageInfo = PageUtils.getPageInfo(records, (int) dataPage.getTotal());
        List<Manufacturer> dataMPageRecords = dataMPage.getRecords();
        if (supplierVo.getSearchVoM()!=null) {
            dataMPageRecords = SearchUtils.getByEntityFilter(supplierVo.getSearchVoM(), dataMPageRecords);
            dataMPage.setTotal(dataMPageRecords.size());
            dataMPageRecords=dataMPageRecords.stream().skip(pageSize2*(current2-1)).limit(pageSize2).collect(Collectors.toList());
        }
        PageDataInfo<Manufacturer> pageInfo1 = PageUtils.getPageInfo(dataMPageRecords, (int) dataMPage.getTotal());
        HashMap<Object, Object> map = Maps.newHashMap();
        map.put("s",pageInfo);
        map.put("m",pageInfo1);
        return R.ok(map);
    }

    @ApiOperation(value = "制造商下拉框查询")
    @Log(title = "工程资料",businessType = BusinessType.OTHER)
    @PostMapping("/find/m")
    public R find() {
        Map<String, Object> result = Maps.newHashMap();
        List<Manufacturer> list = manufacturerService.list(new QueryWrapper<Manufacturer>()
        );
        List<String> collect = list.stream().map(Manufacturer::getManufType).distinct().collect(Collectors.toList());
        result.put("type",collect);
        return R.ok(result);
    }


    @ApiOperation(value = "供应商条件查询")
    @Log(title = "工程资料",businessType = BusinessType.OTHER)
    @PostMapping("/find/s")
    public R findM() {
        Map<String, Object> result = Maps.newHashMap();
        List<Supplier> list = supplierService.list(new QueryWrapper<Supplier>()
        );
        List<Supplier> clas1s = list.stream().map(s -> {
            Supplier supplier = new Supplier();
            supplier.setOneClassify(s.getOneClassify());
            supplier.setOneClassifyDesc(s.getOneClassifyDesc());
            return supplier;
        }).distinct().collect(Collectors.toList());
        List<Supplier> clas2s = list.stream().map(s -> {
            Supplier supplier = new Supplier();
            supplier.setTwoClassify(s.getTwoClassify());
            supplier.setTwoClassifyDesc(s.getTwoClassifyDesc());
            return supplier;
        }).distinct().collect(Collectors.toList());
        List<Supplier> clas3s = list.stream().map(s -> {
            Supplier supplier = new Supplier();
            supplier.setThreeClassify(s.getThreeClassify());
            supplier.setThreeClassifyDesc(s.getThreeClassifyDesc());
            return supplier;
        }).distinct().collect(Collectors.toList());
        result.put("oneClassify",clas1s);
        result.put("twoClassify",clas2s);
        result.put("threeClassify",clas3s);
        return R.ok(result);
    }

    @ApiOperation(value = "导出供应商")
    @Log(title = "工程资料",businessType = BusinessType.OTHER)
    @PostMapping("/download/s")
    public void download(@RequestBody SupplierVo supplierVo , HttpServletResponse response) throws IOException {
        Page<Supplier> page = PageUtils.getPage(Supplier.class);
        Long pageSize = page.getSize();
        if (supplierVo.getSearchVoS()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<Supplier> dataPage= supplierService.queryData(page,supplierVo);
        List<Supplier> records = dataPage.getRecords();
        if (supplierVo.getSearchVoS()!=null) {
            records = SearchUtils.getByEntityFilter(supplierVo.getSearchVoS(), records);
        }
        ExcelUtils.export3Excel(response,records, Supplier.class,"供应商");
    }


    @ApiOperation(value = "导出制造商")
    @Log(title = "工程资料",businessType = BusinessType.OTHER)
    @PostMapping("/download/m")
    public void downloadM(@RequestBody SupplierVo supplierVo , HttpServletResponse response) throws IOException {
        Page<Manufacturer> page2 = PageUtils.getPage(Manufacturer.class);
        Long pageSize = page2.getSize();
        if (supplierVo.getSearchVoM()!=null) {
            page2.setSize(Long.MAX_VALUE);
        }
        Page<Manufacturer> dataPage= supplierService.queryMData(page2,supplierVo);
        List<Manufacturer> dataMPageRecords = dataPage.getRecords();
        if (supplierVo.getSearchVoM()!=null) {
            dataMPageRecords = SearchUtils.getByEntityFilter(supplierVo.getSearchVoM(), dataMPageRecords);
        }
        ExcelUtils.export3Excel(response,dataMPageRecords, Manufacturer.class,"制造商");
    }

    @ApiOperation(value = "code查询")
    @Log(title = "工程资料",businessType = BusinessType.OTHER)
    @PostMapping("/byCode")
    public R byCode(@RequestParam  String supplierCode) {
        Page<Supplier> page = PageUtils.getPage(Supplier.class);
        Page<Supplier> supplierPage = supplierService.page(page, new QueryWrapper<Supplier>().lambda().like(Supplier::getSupplierCode, supplierCode));
        return R.ok(PageUtils.getPageInfo(supplierPage.getRecords(),(int)supplierPage.getTotal()));
    }

}
