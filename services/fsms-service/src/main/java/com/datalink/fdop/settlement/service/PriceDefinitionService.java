package com.datalink.fdop.settlement.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.drive.api.domain.dto.Field;
import com.datalink.fdop.settlement.api.domain.PriceDefinition;
import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.settlement.api.model.vo.PriceSelectVo;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【f_s_price_definition】的数据库操作Service
 * @createDate 2023-03-30 15:49:27
 */
public interface PriceDefinitionService extends IService<PriceDefinition> {

    void createTable(List<PriceDefinition> priceDefinitions);

    void templateDownload(String table, String priceFactor, HttpServletResponse response) throws IOException;

    List<Field> getField(String table);

    void delTable(PriceDefinition priceDefinition);

    void insertData(List<Map<String, Object>> data, String table);

    Page<Map<String, Object>> selectData(String table, PriceSelectVo priceSelectVo,  Page<Map> page);

    void downData(String table, HttpServletResponse response, String priceFactor, PriceSelectVo priceSelectVo) throws IOException;


}
