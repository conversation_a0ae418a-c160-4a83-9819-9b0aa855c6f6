package com.datalink.fdop.project.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.MapUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.project.api.domain.Processing;
import com.datalink.fdop.project.api.domain.ProcessingSingle;
import com.datalink.fdop.project.service.ProcessingService;
import com.datalink.fdop.project.service.ProcessingSingleService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/11/7 11:31
 */
@RestController
@RequestMapping("/processing")
@ApiOperation("制程")
public class ProcessingController {


    @Autowired
    private ProcessingService processingService;

    @Autowired
    private ProcessingSingleService processingSingleService;


    @ApiOperation(value = "新增")
    @Log(title = "工程资料",businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public R create(@RequestBody List<Processing> processings) {

        if (processingService.saveBatch(processings)) {
            return R.ok("保存成功");
        }else {
            return R.fail("保存失败");
        }
    }

    @ApiOperation(value = "修改")
    @Log(title = "工程资料",businessType = BusinessType.INSERT)
    @PostMapping("/update")
    public R update(@RequestBody List<Processing> processings) {

        processings.stream().forEach(processing -> {
            processingService.update(Wrappers.lambdaUpdate(Processing.class)
                    .set(StringUtils.isNotEmpty(processing.getProcessDesc()),Processing::getProcessDesc,processing.getProcessDesc())
                    .set(StringUtils.isNotEmpty(processing.getIsTurnkey()),Processing::getIsTurnkey,processing.getIsTurnkey())
                    .eq(Processing::getProcess,processing.getProcess()));
        });
        return R.ok("修改成功");

    }

    @ApiOperation(value = "查询")
    @Log(title = "工程资料",businessType = BusinessType.UPDATE)
    @PostMapping("/query")
    public R query(@RequestBody(required = false)Processing processing) {
        Page<Processing> page = PageUtils.getPage(Processing.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (processing.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<Processing> pageData=processingService.page(page,new QueryWrapper<Processing>()
                .lambda()
                .eq(StringUtils.isNotEmpty(processing.getProcess()),Processing::getProcess,processing.getProcess())
                .eq(StringUtils.isNotEmpty(processing.getProcessDesc()),Processing::getProcessDesc,processing.getProcessDesc())
        );
        List<Processing> records = pageData.getRecords();
        if (processing.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(processing.getSearchVo(), records);
            pageData.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records, (int) pageData.getTotal()));
    }


    @ApiOperation(value = "导出测试数据")
    @Log(title = "工程资料",businessType = BusinessType.OTHER)
    @PostMapping("/download")
    public void download(@RequestBody(required = false)Processing processing, HttpServletResponse response) throws IOException {
        Page<Processing> page = PageUtils.getPage(Processing.class);
        if (processing.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<Processing> pageData=processingService.page(page,new QueryWrapper<Processing>()
                .lambda()
                .eq(StringUtils.isNotEmpty(processing.getProcess()),Processing::getProcess,processing.getProcess())
                .eq(StringUtils.isNotEmpty(processing.getProcessDesc()),Processing::getProcessDesc,processing.getProcessDesc())
        );
        List<Processing> records = pageData.getRecords();
        if (processing.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(processing.getSearchVo(), records);
        }
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("制程数据", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 这里需要设置不关闭流
            EasyExcel.write(response.getOutputStream(), Processing.class).autoCloseStream(Boolean.FALSE).sheet("模板")
                    .doWrite(records);
        } catch (Exception e) {
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            Map<String, String> map = MapUtils.newHashMap();
            map.put("status", "failure");
            map.put("message", "下载文件失败" + e.getMessage());
            response.getWriter().println(JSON.toJSONString(map));
        }
    }

    @ApiOperation("删除")
    @Log(title = "工程资料",businessType = BusinessType.DELETE)
    @PostMapping("/del")
    public R del(@RequestBody List<String> ids) {
        ids.stream().forEach(id->{
            processingSingleService.remove(new QueryWrapper<ProcessingSingle>().lambda().eq(ProcessingSingle::getTurnkeyProcess, id));
            if (!processingService.remove(new QueryWrapper<Processing>().lambda().eq(Processing::getProcess,id))) {
                throw new ServiceException("删除失败");
            }
        });
        return R.ok("删除成功");
    }


    @ApiOperation(value = "下拉查询")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/combobox/query")
    public R combobox() {
        Page<Processing> page = PageUtils.getPage(Processing.class);
        Page<Processing> pageData=processingService.page(page,new QueryWrapper<Processing>()
                .lambda()
                .orderByAsc(Processing::getProcess)
                .groupBy(Processing::getProcess)
        );
        return R.ok(PageUtils.getPageInfo(pageData.getRecords(), (int) pageData.getTotal()));
    }
}
