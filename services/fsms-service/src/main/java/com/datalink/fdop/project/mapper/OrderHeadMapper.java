package com.datalink.fdop.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.project.api.domain.OrderHead;
import com.datalink.fdop.project.api.model.vo.OrderQueryShowVo;
import com.datalink.fdop.project.api.model.vo.OrderQueryVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @Date 2022/10/24 14:25
 */
@Mapper
public interface OrderHeadMapper extends BaseMapper<OrderHead> {

    Page<OrderQueryShowVo> queryData(@Param("page") Page<OrderQueryShowVo> page, @Param("object") OrderQueryVo orderQueryVo,@Param("delFlag") String delFlag);


    @Select("SELECT company_description FROM zjdata.org_company WHERE company_code =#{companyCode}")
    String findDescription(String companyCode);


    @Select("SELECT purchase_description FROM zjdata.org_purchase WHERE purchase_code =#{ekorg}")
    String findPurchaseDesc(String ekorg);

    @Select("SELECT supplier_name FROM zjdata.p_d_supplier WHERE supplier_code =#{supplierCode}")
    String findSupplierName(String supplierCode);
}
