package com.datalink.fdop.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datalink.fdop.project.api.domain.BomHead;
import com.datalink.fdop.project.vo.BomSearchVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/10/11 14:21
 */
@Mapper
public interface BomMapper  extends BaseMapper<BomHead> {


    List<String> findBomHeads(@Param("bomSearchVo") BomSearchVo bomSearchVo);
}
