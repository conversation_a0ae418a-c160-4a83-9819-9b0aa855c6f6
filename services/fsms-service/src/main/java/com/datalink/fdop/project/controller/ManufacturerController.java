package com.datalink.fdop.project.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.project.api.domain.Manufacturer;
import com.datalink.fdop.project.service.ManufacturerService;
import com.datalink.fdop.project.service.SupplierService;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/10/26 10:54
 */
@RestController
@RequestMapping("/manufacturer")
public class ManufacturerController {

    @Autowired
    private SupplierService supplierService;
    @Autowired
    private ManufacturerService manufacturerService;

    @ApiOperation(value = "制造商下拉框")
    @Log(title = "工程资料",businessType = BusinessType.OTHER)
    @PostMapping("/combobox")
    public R combobox(@RequestParam(required = false) String supplierCode) {
        Map<String,Object> result= Maps.newHashMap();
        List<Manufacturer> list = manufacturerService.list(new QueryWrapper<Manufacturer>().lambda().eq(StringUtils.isNotEmpty(supplierCode),Manufacturer::getSupplierCode,supplierCode));
        List<Manufacturer> manfa = list.stream().map(m -> {
            Manufacturer manufacturer = new Manufacturer();
            manufacturer.setManufCode(m.getManufCode());
            manufacturer.setManufName(m.getManufName());
            return manufacturer;
        }).distinct().collect(Collectors.toList());
        List<String> type = list.stream().map(Manufacturer::getManufType).distinct().collect(Collectors.toList());
        result.put("manfa",manfa);
        result.put("type",type);
        return R.ok(result);
    }



}
