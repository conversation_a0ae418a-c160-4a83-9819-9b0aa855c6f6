package com.datalink.fdop.project.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.project.api.domain.ProcessingSingle;
import com.datalink.fdop.project.mapper.ProcessingSingleMapper;
import com.datalink.fdop.project.service.ProcessingSingleService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【p_d_processing_single】的数据库操作Service实现
* @createDate 2024-01-10 13:57:53
*/
@Service
public class ProcessingSingleServiceImpl extends ServiceImpl<ProcessingSingleMapper, ProcessingSingle>
    implements ProcessingSingleService{

}




