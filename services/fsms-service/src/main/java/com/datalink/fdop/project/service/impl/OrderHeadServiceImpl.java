package com.datalink.fdop.project.service.impl;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.project.api.domain.OrderHead;
import com.datalink.fdop.project.api.model.vo.OrderQueryShowVo;
import com.datalink.fdop.project.api.model.vo.OrderQueryVo;
import com.datalink.fdop.project.mapper.OrderHeadMapper;
import com.datalink.fdop.project.service.OrderHeadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2022/10/24 14:24
 */
@Service
public class OrderHeadServiceImpl extends ServiceImpl<OrderHeadMapper, OrderHead> implements OrderHeadService {

    @Autowired
    private OrderHeadMapper orderHeadMapper;

    @Override
    public Page<OrderQueryShowVo> queryData(Page<OrderQueryShowVo> page, OrderQueryVo orderQueryVo, String delFlag) {
        return orderHeadMapper.queryData(page,orderQueryVo,delFlag);
    }

    @Override
    public String findDescription(String companyCode) {
        return orderHeadMapper.findDescription(companyCode);
    }

    @Override
    public String findPurchaseDesc(String ekorg) {
        return orderHeadMapper.findPurchaseDesc(ekorg);
    }

    @Override
    public String findSupplierName(String supplierCode) {
        return orderHeadMapper.findSupplierName(supplierCode);
    }
}
