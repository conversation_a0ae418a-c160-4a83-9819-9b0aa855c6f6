package com.datalink.fdop.fscm.controller;

/**
 * <AUTHOR>
 * @Date 2022/11/14 14:39
 */

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.BatchDimensionality;
import com.datalink.fdop.fscm.service.BatchDimensionalityService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
@RestController
@RequestMapping("/batch/dime")
@Transactional
public class BatchDimensionalityController {

    @Autowired
    private BatchDimensionalityService batchDimensionalityService;

    @ApiOperation(value = "查询")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/query")
    public R query() {
        Page<BatchDimensionality> page = PageUtils.getPage(BatchDimensionality.class);
        Page<BatchDimensionality> pageData=batchDimensionalityService.page(page);
        return R.ok(PageUtils.getPageInfo(pageData.getRecords(), (int) pageData.getTotal()));
    }


    @ApiOperation(value = "修改")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public R update(@RequestBody BatchDimensionality batchDimensionality) {
        //获取表中批次
        int count=batchDimensionalityService.findBatch();
        if (count>0) {
            throw new ServiceException("已有数据，不允许再被修改");
        }
        if (batchDimensionalityService.update(Wrappers.lambdaUpdate(BatchDimensionality.class).set(BatchDimensionality::getManageDimension,batchDimensionality.getManageDimension()))) {
            return R.ok();
        }else {
            return R.fail("失败");
        }

    }
}
