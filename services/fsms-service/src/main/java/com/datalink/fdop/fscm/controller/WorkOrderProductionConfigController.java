package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.WorkOrderConfig;
import com.datalink.fdop.fscm.api.domain.WorkOrderProductionConfig;
import com.datalink.fdop.fscm.service.WorkOrderConfigService;
import com.datalink.fdop.fscm.service.WorkOrderProductionConfigService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/11/21 16:21
 */
@RestController
@RequestMapping("/work/order/production/config")
@Transactional
@ApiOperation("工单生产状态配置")
public class WorkOrderProductionConfigController {

    @Autowired
    private WorkOrderProductionConfigService workOrderProductionConfigService;
    @Autowired
    private WorkOrderConfigService workOrderConfigService;

    @ApiOperation(value = "查询")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/query")
    public R query(@RequestBody(required = false) SearchVo searchVo) {
        Page<WorkOrderProductionConfig> page = PageUtils.getPage(WorkOrderProductionConfig.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (searchVo!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<WorkOrderProductionConfig> pageData=workOrderProductionConfigService.page(page,new QueryWrapper<WorkOrderProductionConfig>()
                .lambda()
                .orderByAsc(WorkOrderProductionConfig::getWorkOrderType)
                .orderByAsc(WorkOrderProductionConfig::getProduceStatu)
        );
        List<WorkOrderProductionConfig> records = pageData.getRecords();
        records.forEach(record->{
            WorkOrderConfig config = workOrderConfigService.getOne(new QueryWrapper<WorkOrderConfig>()
                    .lambda()
                    .eq(WorkOrderConfig::getWorkOrderType, record.getWorkOrderType()));
            if (config!=null) {
                record.setWorkOrderTypeDesc(config.getWorkOrderTypeDesc());
            }
        });
        if (searchVo!=null) {
            records = SearchUtils.getByEntityFilter(searchVo, records );
            page.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records, (int) pageData.getTotal()));
    }


    @ApiOperation(value = "查询生产状态下拉框")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/query/produceStatu")
    public R queryProduceStatu(@RequestBody(required = false) WorkOrderProductionConfig Config ) {
        List<WorkOrderProductionConfig> list=workOrderProductionConfigService.list(new QueryWrapper<WorkOrderProductionConfig>()
                .lambda()
                .eq(StringUtils.isNotEmpty(Config.getWorkOrderType()),WorkOrderProductionConfig::getWorkOrderType ,Config.getWorkOrderType())
        );
        List<WorkOrderProductionConfig> collect = list.stream().map(data -> {
            WorkOrderProductionConfig workOrderProductionConfig = new WorkOrderProductionConfig();
            workOrderProductionConfig.setProduceStatu(data.getProduceStatu());
            workOrderProductionConfig.setProduceStatuDesc(data.getProduceStatuDesc());
            return workOrderProductionConfig;
        }).distinct().collect(Collectors.toList());
        return R.ok(collect);
    }


    @ApiOperation(value = "新增修改")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/createOrUpdate")
    public R create(@RequestBody List<WorkOrderProductionConfig> workOrderProductionConfigs) {
        try {
            if (workOrderProductionConfigService.saveOrUpdateBatch(workOrderProductionConfigs)) {
                return R.ok();
            }else {
                return R.fail("失败");
            }
        }catch (Exception e){
            e.printStackTrace();
            throw new ServiceException("检查数据是否正确!");
        }
    }

    @ApiOperation(value = "删除")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/del")
    public R del(@RequestBody List<Long> ids) {
        if (workOrderProductionConfigService.removeBatchByIds(ids)) {
            return R.ok();
        }else {
            return R.fail("失败");
        }

    }

    @ApiOperation(value = "导出")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/download")
    public void download( HttpServletResponse response,@RequestBody(required = false) SearchVo searchVo) throws IOException {
        Page<WorkOrderProductionConfig> page = PageUtils.getPage(WorkOrderProductionConfig.class);
        if (searchVo!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<WorkOrderProductionConfig> pageData=workOrderProductionConfigService.page(page,new QueryWrapper<WorkOrderProductionConfig>()
                .lambda()
                .orderByAsc(WorkOrderProductionConfig::getWorkOrderType)
                .orderByAsc(WorkOrderProductionConfig::getProduceStatu)
        );
        List<WorkOrderProductionConfig> records = pageData.getRecords();
        records.forEach(record->{
            WorkOrderConfig config = workOrderConfigService.getOne(new QueryWrapper<WorkOrderConfig>()
                    .lambda()
                    .eq(WorkOrderConfig::getWorkOrderType, record.getWorkOrderType()));
            if (config!=null) {
                record.setWorkOrderTypeDesc(config.getWorkOrderTypeDesc());
            }
        });
        if (searchVo!=null) {
            records = SearchUtils.getByEntityFilter(searchVo, records );
        }
        ExcelUtils.export3Excel(response,records, WorkOrderProductionConfig.class,"工单生产状态配置");
    }




}
