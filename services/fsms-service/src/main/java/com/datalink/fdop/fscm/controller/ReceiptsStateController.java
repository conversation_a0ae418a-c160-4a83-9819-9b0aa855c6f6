package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.ReceiptsState;
import com.datalink.fdop.fscm.service.ReceiptsStateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/11/21 14:18
 */
@RestController
@RequestMapping("/receipts/state")
@Transactional
@Api(tags = "单据状态")
public class ReceiptsStateController {


    @Autowired
    private ReceiptsStateService receiptsStateService;

    @ApiOperation(value = "查询")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/query")
    public R query(@RequestBody(required = false) SearchVo searchVo) {
        Page<ReceiptsState> page = PageUtils.getPage(ReceiptsState.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (searchVo!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<ReceiptsState> pageData=receiptsStateService.page(page,new QueryWrapper<ReceiptsState>()
                .lambda()
                .orderByAsc(ReceiptsState::getPaperType)
                .orderByAsc(ReceiptsState::getStatu)
        );
        List<ReceiptsState> records = pageData.getRecords();
        if (searchVo!=null) {
            records = SearchUtils.getByEntityFilter(searchVo, records );
            page.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records, (int) pageData.getTotal()));
    }



    @ApiOperation(value = "新增修改")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/createOrUpdate")
    public R create(@RequestBody List<ReceiptsState> receiptsStates) {
        try {
            if (receiptsStateService.saveOrUpdateBatch(receiptsStates)) {
                return R.ok();
            }else {
                return R.fail("失败");
            }
        }catch (Exception e){
            e.printStackTrace();
            throw new ServiceException("检查数据是否正确!");
        }
    }

    @ApiOperation(value = "删除")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/del")
    public R del(@RequestBody List<Long> ids) {
        if (receiptsStateService.removeBatchByIds(ids)) {
            return R.ok();
        }else {
            return R.fail("失败");
        }

    }

    @ApiOperation(value = "导出")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/download")
    public void download( HttpServletResponse response,@RequestBody(required = false) SearchVo searchVo) throws IOException {
        Page<ReceiptsState> page = PageUtils.getPage(ReceiptsState.class);
        if (searchVo!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<ReceiptsState> pageData=receiptsStateService.page(page,new QueryWrapper<ReceiptsState>()
                .lambda()
                .orderByAsc(ReceiptsState::getPaperType)
                .orderByAsc(ReceiptsState::getStatu)
        );
        List<ReceiptsState> records = pageData.getRecords();
        if (searchVo!=null) {
            records = SearchUtils.getByEntityFilter(searchVo, records );
        }
        ExcelUtils.export3Excel(response,records, ReceiptsState.class,"单据状态配置");
    }


    @ApiOperation(value = "下拉查询")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/combobox/query")
    public R combobox(@RequestParam(required = false,defaultValue = "1") String paperType) {
        Page<ReceiptsState> page = PageUtils.getPage(ReceiptsState.class);
        Page<ReceiptsState> pageData=receiptsStateService.page(page,new QueryWrapper<ReceiptsState>()
                .lambda()
                .eq(ReceiptsState::getPaperType,paperType)
                .orderByAsc(ReceiptsState::getPaperType)
                .orderByAsc(ReceiptsState::getStatu)
                .groupBy(ReceiptsState::getPaperType)
                .groupBy(ReceiptsState::getStatu)
        );
        return R.ok(PageUtils.getPageInfo(pageData.getRecords(), (int) pageData.getTotal()));
    }
}
