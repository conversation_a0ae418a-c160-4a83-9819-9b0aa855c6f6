package com.datalink.fdop.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.project.api.domain.BomHead;
import com.datalink.fdop.project.vo.BomSearchVo;
import com.datalink.fdop.project.vo.BomTreeVo;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/10/11 14:20
 */
public interface BomService  extends IService<BomHead> {

    PageDataInfo<BomTreeVo> assemblyTree(BomSearchVo bomSearchVo);

    Map<String, Object> queryBom(String headId, SearchVo searchVo);
}
