package com.datalink.fdop.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.project.api.domain.Manufacturer;
import com.datalink.fdop.project.api.domain.Supplier;
import com.datalink.fdop.project.api.model.vo.SupplierVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @Date 2022/10/26 11:37
 */
@Mapper
public interface SupplierMapper extends BaseMapper<Supplier> {

    Page<Supplier> queryData(IPage<Supplier> page, @Param("supplierVo") SupplierVo supplierVo);

    Page<Manufacturer> queryMData(IPage<Manufacturer> page,  @Param("supplierVo")SupplierVo supplierVo);
}
