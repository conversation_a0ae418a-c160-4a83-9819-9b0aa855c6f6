package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.fscm.api.domain.ProvisionalSingleOut;
import com.datalink.fdop.fscm.api.model.vo.PostCertificateVo;
import com.datalink.fdop.fscm.service.ProfitAndLossService;
import com.datalink.fdop.fscm.service.ProvisionalSingleOutService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2023/1/31 10:20
 */
@RestController
@RequestMapping("profit/loss")
@Transactional
@Api(tags = "盘盈亏")
public class ProfitAndLossController {


    @Autowired
    private ProfitAndLossService profitAndLossService;
    @Autowired
    private ProvisionalSingleOutService provisionalSingleOutService;




    @ApiOperation(value = "过帐数据检查")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/post/certificate/check")
    public R certificateCheck(@RequestBody PostCertificateVo postCertificateVo) {
        //检查数据
        profitAndLossService.checkData(postCertificateVo.getPostCertificateRows());

        return R.ok();
    }


    @ApiOperation(value = "过帐")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/post/certificate")
    public R certificate(@RequestBody PostCertificateVo postCertificateVo) {
        String userName = SecurityUtils.getLoginUser().getSysUser().getUserName();
        provisionalSingleOutService.remove(new QueryWrapper<ProvisionalSingleOut>().lambda()
                .eq(ProvisionalSingleOut::getUserName,userName)
        );
        //保存数据
        profitAndLossService.saveData(postCertificateVo);

        return R.ok();
    }


}
