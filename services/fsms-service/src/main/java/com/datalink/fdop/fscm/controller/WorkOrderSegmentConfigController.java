package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.search.vo.SearchVo;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.WorkOrderConfig;
import com.datalink.fdop.fscm.api.domain.WorkOrderHead;
import com.datalink.fdop.fscm.api.domain.WorkOrderSegmentConfig;
import com.datalink.fdop.fscm.service.WorkOrderConfigService;
import com.datalink.fdop.fscm.service.WorkOrderHeadService;
import com.datalink.fdop.fscm.service.WorkOrderSegmentConfigService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/11/21 15:25
 */
@RestController
@RequestMapping("/work/order/segment/config")
@Transactional
@ApiOperation("工单号段配置")
public class WorkOrderSegmentConfigController {


    @Autowired
    private WorkOrderSegmentConfigService workOrderSegmentConfigService;
    @Autowired
    private WorkOrderConfigService workOrderConfigService;
    @Autowired
    private WorkOrderHeadService workOrderHeadService;

    @ApiOperation(value = "查询")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/query")
    public R query(@RequestBody(required = false) SearchVo searchVo) {
        Page<WorkOrderSegmentConfig> page = PageUtils.getPage(WorkOrderSegmentConfig.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (searchVo!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<WorkOrderSegmentConfig> pageData=workOrderSegmentConfigService.page(page,new QueryWrapper<WorkOrderSegmentConfig>()
                .lambda()
                .orderByAsc(WorkOrderSegmentConfig::getWorkOrderType)
                .orderByAsc(WorkOrderSegmentConfig::getEncode)
        );
        List<WorkOrderSegmentConfig> records = pageData.getRecords();
        records.forEach(record->{
            WorkOrderConfig config = workOrderConfigService.getOne(new QueryWrapper<WorkOrderConfig>()
                    .lambda()
                    .eq(WorkOrderConfig::getWorkOrderType, record.getWorkOrderType()));
            if (config!=null) {
                record.setWorkOrderTypeDesc(config.getWorkOrderTypeDesc());
            }
        });
        if (searchVo!=null) {
            records = SearchUtils.getByEntityFilter(searchVo, records );
            page.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records, (int) pageData.getTotal()));
    }




    @ApiOperation(value = "新增修改")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/createOrUpdate")
    public R create(@RequestBody List<WorkOrderSegmentConfig> workOrderSegmentConfigs) {
        return  workOrderSegmentConfigService.createOrUpdate(workOrderSegmentConfigs);

    }

    @ApiOperation(value = "删除")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/del")
    public R del(@RequestBody List<Long> ids) {
        for (Long id : ids) {
            WorkOrderSegmentConfig config = workOrderSegmentConfigService.getById(id);
            List<WorkOrderHead> list = workOrderHeadService.list(new QueryWrapper<WorkOrderHead>().lambda()
                    .ge(WorkOrderHead::getWorkOrderNum, config.getStartNum())
                    .le(WorkOrderHead::getWorkOrderNum, config.getEndNum())
            );
            if (CollectionUtils.isNotEmpty(list)) {
                throw new ServiceException("号段已被使用，不允许再删除！");
            }
        }
        if (workOrderSegmentConfigService.removeBatchByIds(ids)) {
            return R.ok();
        }else {
            return R.fail("失败");
        }

    }

    @ApiOperation(value = "导出")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/download")
    public void download( HttpServletResponse response,@RequestBody(required = false) SearchVo searchVo) throws IOException {
        Page<WorkOrderSegmentConfig> page = PageUtils.getPage(WorkOrderSegmentConfig.class);
        if (searchVo!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<WorkOrderSegmentConfig> pageData=workOrderSegmentConfigService.page(page,new QueryWrapper<WorkOrderSegmentConfig>()
                .lambda()
                .orderByAsc(WorkOrderSegmentConfig::getWorkOrderType)
                .orderByAsc(WorkOrderSegmentConfig::getEncode)
        );
        List<WorkOrderSegmentConfig> records = pageData.getRecords();
        records.forEach(record->{
            WorkOrderConfig config = workOrderConfigService.getOne(new QueryWrapper<WorkOrderConfig>()
                    .lambda()
                    .eq(WorkOrderConfig::getWorkOrderType, record.getWorkOrderType()));
            record.setWorkOrderTypeDesc(config.getWorkOrderTypeDesc());
        });
        if (searchVo!=null) {
            records = SearchUtils.getByEntityFilter(searchVo, records );
        }
        ExcelUtils.export3Excel(response,records, WorkOrderSegmentConfig.class,"工作订单配置");
    }
}
