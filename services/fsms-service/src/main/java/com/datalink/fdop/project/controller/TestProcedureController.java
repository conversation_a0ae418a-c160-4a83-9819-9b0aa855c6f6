package com.datalink.fdop.project.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.project.api.domain.Manufacturer;
import com.datalink.fdop.project.api.domain.ManufacturerManage;
import com.datalink.fdop.project.api.domain.Material;
import com.datalink.fdop.project.api.domain.TestProcedure;
import com.datalink.fdop.project.api.model.vo.TestProcedureVo;
import com.datalink.fdop.project.service.ManufacturerManageService;
import com.datalink.fdop.project.service.ManufacturerService;
import com.datalink.fdop.project.service.MaterialService;
import com.datalink.fdop.project.service.TestProcedureService;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/11/2 13:37
 */
@RestController
@RequestMapping("/test")
public class TestProcedureController {

    @Autowired
    private TestProcedureService testProcedureService;
    @Autowired
    private ManufacturerService manufacturerService;
    @Autowired
    private MaterialService materialService;
    @Autowired
    private ManufacturerManageService manufacturerManageService;

    @ApiOperation(value = "新增")
    @Log(title = "工程资料",businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public R create(@RequestBody List<TestProcedure> testProcedures) {
        testProcedures.stream().forEach(testProcedure -> {

            if (StringUtils.isEmpty(testProcedure.getSupplierCode())) {
                throw new ServiceException("供应商编码为空");
            }
            if (StringUtils.isEmpty(testProcedure.getMaterialCode())) {
                throw new ServiceException("内部物料编码");
            }
            if (StringUtils.isEmpty(testProcedure.getTestRoutine())) {
                throw new ServiceException("测试程序");
            }
            if (StringUtils.isEmpty(testProcedure.getTestVersions())) {
                throw new ServiceException("测试程序版本");
            }
            if (StringUtils.isEmpty(testProcedure.getTestStatu())) {
                throw new ServiceException("测试程序状态");
            }
            ManufacturerManage manufacturerManage = manufacturerManageService.getOne(new QueryWrapper<ManufacturerManage>().lambda().eq(ManufacturerManage::getSerialNum, '3'));
            if (manufacturerManage.getStatu().equals("1")) {
                List<Manufacturer> list = manufacturerService.list(new QueryWrapper<Manufacturer>()
                        .lambda()
                        .eq(Manufacturer::getSupplierCode, testProcedure.getSupplierCode())
                        .eq(Manufacturer::getManufCode, testProcedure.getManufCode())
                        .eq(Manufacturer::getManufType, testProcedure.getManufType()));
                if (CollectionUtils.isEmpty(list)) {
                    throw new ServiceException("供应商编码&制造商代码&制造商类别组合不存在，请先维护！");
                }
            }
            List<Material> list1 = materialService.list(new QueryWrapper<Material>()
                    .lambda()
                    .eq(Material::getMaterialCode, testProcedure.getMaterialCode()));
            if (CollectionUtils.isEmpty(list1)) {
                throw new ServiceException("内部物料编码不存在，请先维护！");
            }
            TestProcedure one = testProcedureService.getOne(new QueryWrapper<TestProcedure>()
                    .lambda()
                    .eq(TestProcedure::getMaterialCode, testProcedure.getMaterialCode())
                    .eq(TestProcedure::getSupplierCode, testProcedure.getSupplierCode())
                    .eq(TestProcedure::getManufCode, testProcedure.getManufCode())
                    .eq(TestProcedure::getManufType, testProcedure.getManufType())
                    .eq(TestProcedure::getTestRoutine, testProcedure.getTestRoutine())
                    .eq(TestProcedure::getTestVersions, testProcedure.getTestVersions())
            );
            if (one!=null) {
                throw new ServiceException("数据已存在");
            }
        });
        try {
            if (testProcedureService.saveBatch(testProcedures)) {
                return R.ok("保存成功");
            }else {
                return R.fail("保存失败");
            }
        }catch (Exception e){
            e.printStackTrace();
            throw new ServiceException("检查数据是否正确");
        }

    }

    @ApiOperation(value = "修改")
    @Log(title = "工程资料",businessType = BusinessType.INSERT)
    @PostMapping("/update")
    public R update(@RequestBody List<TestProcedure> testProcedures) {

        testProcedures.stream().forEach(testProcedure -> {
            if (StringUtils.isEmpty(testProcedure.getSupplierCode())) {
                throw new ServiceException("供应商编码为空");
            }
            if (StringUtils.isEmpty(testProcedure.getMaterialCode())) {
                throw new ServiceException("内部物料编码");
            }
            if (StringUtils.isEmpty(testProcedure.getTestRoutine())) {
                throw new ServiceException("测试程序");
            }
            if (StringUtils.isEmpty(testProcedure.getTestVersions())) {
                throw new ServiceException("测试程序版本");
            }
            if (StringUtils.isEmpty(testProcedure.getTestStatu())) {
                throw new ServiceException("测试程序状态");
            }
            ManufacturerManage manufacturerManage = manufacturerManageService.getOne(new QueryWrapper<ManufacturerManage>().lambda().eq(ManufacturerManage::getSerialNum, "3"));
            if (manufacturerManage.getStatu().equals("1")) {
                List<Manufacturer> list = manufacturerService.list(new QueryWrapper<Manufacturer>()
                        .lambda()
                        .eq(Manufacturer::getSupplierCode, testProcedure.getSupplierCode())
                        .eq(Manufacturer::getManufCode, testProcedure.getManufCode())
                        .eq(Manufacturer::getManufType, testProcedure.getManufType()));
                if (CollectionUtils.isEmpty(list)) {
                    throw new ServiceException("供应商编码&制造商代码&制造商类别组合不存在，请先维护！");
                }
            }
            List<Material> list1 = materialService.list(new QueryWrapper<Material>()
                    .lambda()
                    .eq(Material::getMaterialCode, testProcedure.getMaterialCode()));
            if (CollectionUtils.isEmpty(list1)) {
                throw new ServiceException("内部物料编码不存在，请先维护！");
            }
            TestProcedure one = testProcedureService.getOne(new QueryWrapper<TestProcedure>()
                    .lambda()
                    .eq(TestProcedure::getMaterialCode, testProcedure.getMaterialCode())
                    .eq(TestProcedure::getSupplierCode, testProcedure.getSupplierCode())
                    .eq(TestProcedure::getManufCode, testProcedure.getManufCode())
                    .eq(TestProcedure::getManufType, testProcedure.getManufType())
                    .eq(TestProcedure::getTestRoutine, testProcedure.getTestRoutine())
                    .eq(TestProcedure::getTestVersions, testProcedure.getTestVersions())
                    .ne(TestProcedure::getId,testProcedure.getId())
            );
            if (one!=null) {
                throw new ServiceException("数据已存在");
            }
        });

        if (testProcedureService.updateBatchById(testProcedures)) {
            return R.ok("修改成功");
        }else {
            return R.fail("修改失败");
        }
    }

    @ApiOperation(value = "查询")
    @Log(title = "工程资料",businessType = BusinessType.UPDATE)
    @PostMapping("/query")
    public R query(@RequestBody TestProcedureVo testProcedure) {
        Page<TestProcedure> page = PageUtils.getPage(TestProcedure.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (testProcedure.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<TestProcedure> pageData=testProcedureService.pageData(page,testProcedure);
        List<TestProcedure> records = pageData.getRecords();
        if (testProcedure.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(testProcedure.getSearchVo(), records);
            pageData.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records, (int) pageData.getTotal()));
    }


    @ApiOperation(value = "导出测试数据")
    @Log(title = "工程资料",businessType = BusinessType.OTHER)
    @PostMapping("/download")
    public void download(@RequestBody TestProcedureVo testProcedure, HttpServletResponse response) throws IOException {
        Page<TestProcedure> page = PageUtils.getPage(TestProcedure.class);
        if (testProcedure.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<TestProcedure> pageData=testProcedureService.pageData(page,testProcedure);
        List<TestProcedure> records = pageData.getRecords();
        if (testProcedure.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(testProcedure.getSearchVo(), records);
        }
        ExcelUtils.export3Excel(response,records, TestProcedure.class,"测试数据");
    }

    @ApiOperation("删除")
    @Log(title = "工程资料",businessType = BusinessType.DELETE)
    @PostMapping("/del")
    public R del(@RequestBody List<Long> ids) {
        if (testProcedureService.removeBatchByIds(ids)) {
            return R.ok("删除成功");
        } else {
            return R.fail("删除失败");
        }


    }
}
