package com.datalink.fdop.fscm.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.datalink.fdop.common.core.constant.Constants;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.fscm.api.domain.*;
import com.datalink.fdop.fscm.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/locked/repertory")
@Transactional
@Api(tags = "锁定库存")
public class LockedRepertoryController {

    @Autowired
    private WorkOrderStoreIssueService workOrderStoreIssueService;
    @Autowired
    private AllotRowService allotRowService;
    @Autowired
    private GetReturnMaterialRowService getReturnMaterialRowService;
    @Autowired
    private PostCertificateRowService postCertificateRowService;
    @Autowired
    private ScrapRowService scrapRowService;
    @Autowired
    private ShipRequestRowService shipRequestRowService;

    @ApiOperation(value = "加工锁定库存数量进入")
    @Log(title = "fscm")
    @PostMapping("/process")
    public R process(@RequestBody WorkOrderStoreIssue workOrderStoreIssue) {

        List<WorkOrderStoreIssue> list = workOrderStoreIssueService.list(new QueryWrapper<WorkOrderStoreIssue>()
                .lambda()
                .eq(WorkOrderStoreIssue::getMaterialCode,workOrderStoreIssue.getMaterialCode())
                .eq(WorkOrderStoreIssue::getFactoryCode,workOrderStoreIssue.getFactoryCode())
                .eq(WorkOrderStoreIssue::getStockPCode,workOrderStoreIssue.getStockPCode())
                .eq(WorkOrderStoreIssue::getBatchNumber,workOrderStoreIssue.getBatchNumber())
                .eq(WorkOrderStoreIssue::getPiece, StringUtils.isNotEmpty(workOrderStoreIssue.getPiece())?workOrderStoreIssue.getPiece(): Constants.DATA_DEFAULT_VALUE)
                .eq(WorkOrderStoreIssue::getBinNum,StringUtils.isNotEmpty(workOrderStoreIssue.getBinNum())?workOrderStoreIssue.getBinNum(): Constants.DATA_DEFAULT_VALUE)
        );

        list= list.stream().filter(f->f.getQuantityDelivery()-f.getQuantityConsume()>0).collect(Collectors.toList());
        return R.ok(list);
    }

    @ApiOperation(value = "调拨锁定库存数量进入")
    @Log(title = "fscm")
    @PostMapping("/allot")
    public R allot(@RequestBody AllotRow allotRow) {

        List<AllotRow> list = allotRowService.list(new QueryWrapper<AllotRow>()
                .lambda()
                .eq(AllotRow::getMaterialCode,allotRow.getMaterialCode())
                .eq(AllotRow::getFactoryCode,allotRow.getFactoryCode())
                .eq(AllotRow::getStockPCodeShip,allotRow.getStockPCodeShip())
                .eq(AllotRow::getBatchNumberShip,allotRow.getBatchNumberShip())
                .eq(AllotRow::getPieceShip, StringUtils.isNotEmpty(allotRow.getPieceShip())?allotRow.getPieceShip(): Constants.DATA_DEFAULT_VALUE)
                .eq(AllotRow::getBinNumShip,StringUtils.isNotEmpty(allotRow.getBinNumShip())?allotRow.getBinNumShip(): Constants.DATA_DEFAULT_VALUE)
        );

        list= list.stream().map(a->{
            List<PostCertificateRow> rows = postCertificateRowService.list(new QueryWrapper<PostCertificateRow>()
                    .lambda()
                    .eq(PostCertificateRow::getTransferOrderNum,a.getTransferOrderNum())
                    .eq(PostCertificateRow::getTransferOrderRowNum,a.getTransferOrderRowNum())
                    .in(PostCertificateRow::getMoveType, Arrays.asList("311","312"))
                    .eq(PostCertificateRow::getWriteOff,"0")
            );
            double sum = rows.stream().mapToDouble(value -> value.getPostQuantity()).sum();
            a.setPostQuantity(sum);
            return a;
        }).filter(f->f.getTransferQuantity()-f.getPostQuantity()>0).collect(Collectors.toList());
        return R.ok(list);
    }

    @ApiOperation(value = "报废锁定库存数量进入")
    @Log(title = "fscm")
    @PostMapping("/scrap")
    public R scrap(@RequestBody ScrapRow scrapRow) {

        List<ScrapRow> list = scrapRowService.list(new QueryWrapper<ScrapRow>()
                .lambda()
                .eq(ScrapRow::getMaterialCode,scrapRow.getMaterialCode())
                .eq(ScrapRow::getFactoryCode,scrapRow.getFactoryCode())
                .eq(ScrapRow::getStockPCode,scrapRow.getStockPCode())
                .eq(ScrapRow::getBatchNumber,scrapRow.getBatchNumber())
                .eq(ScrapRow::getPiece, StringUtils.isNotEmpty(scrapRow.getPiece())?scrapRow.getPiece(): Constants.DATA_DEFAULT_VALUE)
                .eq(ScrapRow::getBinNum,StringUtils.isNotEmpty(scrapRow.getBinNum())?scrapRow.getBinNum(): Constants.DATA_DEFAULT_VALUE)
        );

        list= list.stream().map(a->{
            List<PostCertificateRow> rows = postCertificateRowService.list(new QueryWrapper<PostCertificateRow>()
                    .lambda()
                    .eq(PostCertificateRow::getScrapOrderNum,a.getScrapOrderNum())
                    .eq(PostCertificateRow::getScrapOrderRowNum,a.getScrapOrderRowNum())
                    .in(PostCertificateRow::getMoveType, Arrays.asList("551","552"))
            );
            double sum = rows.stream().mapToDouble(value -> value.getPostQuantity()*-1).sum();
            a.setPostQuantity(sum);
            return a;
        }).filter(f->f.getScrapQuantity()-f.getPostQuantity()>0).collect(Collectors.toList());
        return R.ok(list);
    }
    @ApiOperation(value = "领料锁定库存数量进入")
    @Log(title = "fscm")
    @PostMapping("/getReturnMaterial")
    public R getReturnMaterial(@RequestBody GetReturnMaterialRow getReturnMaterialRow) {

        List<GetReturnMaterialRow> list = getReturnMaterialRowService.list(new QueryWrapper<GetReturnMaterialRow>()
                .lambda()
                .eq(GetReturnMaterialRow::getMaterialCode,getReturnMaterialRow.getMaterialCode())
                .eq(GetReturnMaterialRow::getFactoryCode,getReturnMaterialRow.getFactoryCode())
                .eq(GetReturnMaterialRow::getStockPCode,getReturnMaterialRow.getStockPCode())
                .eq(GetReturnMaterialRow::getBatchNumber,getReturnMaterialRow.getBatchNumber())
                .eq(GetReturnMaterialRow::getPiece, StringUtils.isNotEmpty(getReturnMaterialRow.getPiece())?getReturnMaterialRow.getPiece(): Constants.DATA_DEFAULT_VALUE)
                .eq(GetReturnMaterialRow::getBinNum,StringUtils.isNotEmpty(getReturnMaterialRow.getBinNum())?getReturnMaterialRow.getBinNum(): Constants.DATA_DEFAULT_VALUE)
        );

        list= list.stream().map(a->{
            List<PostCertificateRow> rows = postCertificateRowService.list(new QueryWrapper<PostCertificateRow>()
                    .lambda()
                    .eq(PostCertificateRow::getPickReturnOrderNum,a.getPickReturnOrderNum())
                    .eq(PostCertificateRow::getPickReturnOrderRowNum,a.getPickReturnOrderRowNum())
                    .in(PostCertificateRow::getMoveType, Arrays.asList("201","202"))
            );
            double sum = rows.stream().mapToDouble(value -> {
                if (value.getPickReturnType().equals("1")) {
                    return value.getPostQuantity()*-1;
                }else {
                    return value.getPostQuantity();
                }
            }).sum();
            a.setPostQuantity(sum);
            return a;
        }).filter(f->f.getPickReturnQuantity()-f.getPostQuantity()>0).collect(Collectors.toList());
        return R.ok(list);
    }

    @ApiOperation(value = "出货锁定库存数量进入")
    @Log(title = "fscm")
    @PostMapping("/shipRequest")
    public R shipRequest(@RequestBody ShipRequestRow shipRequestRow) {

        List<ShipRequestRow> list = shipRequestRowService.list(new QueryWrapper<ShipRequestRow>()
                .lambda()
                .eq(ShipRequestRow::getMaterialCode,shipRequestRow.getMaterialCode())
                .eq(ShipRequestRow::getFactoryCode,shipRequestRow.getFactoryCode())
                .eq(ShipRequestRow::getStockPCode,shipRequestRow.getStockPCode())
                .eq(ShipRequestRow::getBatchNumber,shipRequestRow.getBatchNumber())
                .eq(ShipRequestRow::getPiece, StringUtils.isNotEmpty(shipRequestRow.getPiece())?shipRequestRow.getPiece(): Constants.DATA_DEFAULT_VALUE)
                .eq(ShipRequestRow::getBinNum,StringUtils.isNotEmpty(shipRequestRow.getBinNum())?shipRequestRow.getBinNum(): Constants.DATA_DEFAULT_VALUE)
        );

        list= list.stream().map(a->{
            List<PostCertificateRow> rows = postCertificateRowService.list(new QueryWrapper<PostCertificateRow>()
                    .lambda()
                    .eq(PostCertificateRow::getShipRequestOrderNum,a.getShipRequestOrderNum())
                    .eq(PostCertificateRow::getShipRequestOrderRowNum,Long.valueOf(a.getShipRequestOrderRowNum()))
                    .in(PostCertificateRow::getMoveType, Arrays.asList("601","602"))
            );
            double sum = rows.stream().mapToDouble(value -> value.getPostQuantity()*-1).sum();
            a.setPostQuantity(sum);
            return a;
        }).filter(f->f.getQuantityDelivery()-f.getPostQuantity()>0).collect(Collectors.toList());
        return R.ok(list);
    }
}
