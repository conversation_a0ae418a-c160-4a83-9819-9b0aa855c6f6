package com.datalink.fdop.project.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datalink.fdop.project.api.domain.BaseMaterial;
import com.datalink.fdop.project.api.model.vo.BaseMaterialShowVo;
import com.datalink.fdop.project.api.model.vo.BaseMaterialVo;
import com.datalink.fdop.project.mapper.BaseMaterialMapper;
import com.datalink.fdop.project.service.BaseMaterialService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2022/10/31 10:49
 */
@Service
public class BaseMaterialServiceImpl extends ServiceImpl<BaseMaterialMapper, BaseMaterial> implements BaseMaterialService {

   @Autowired
   private BaseMaterialMapper baseMaterialMapper;

    @Override
    public Page<BaseMaterialShowVo> pageData(Page<BaseMaterialShowVo> page, BaseMaterialVo baseMaterialVo) {
        return baseMaterialMapper.pageData(page,baseMaterialVo);
    }
}
