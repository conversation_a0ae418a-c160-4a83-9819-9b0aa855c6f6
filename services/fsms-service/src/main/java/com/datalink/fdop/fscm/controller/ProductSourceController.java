package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.model.vo.ProductSourceQueryVo;
import com.datalink.fdop.fscm.api.model.vo.ProductSourceShowVo;
import com.datalink.fdop.fscm.service.ProductSourceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/2/10 9:55
 */
@RestController
@RequestMapping("/product/source")
@Transactional
@Api(tags = "产品来源追溯")
public class ProductSourceController {

    @Autowired
    private ProductSourceService productSourceService;

    @ApiOperation(value = "追溯")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/query")
    public R query(@RequestBody ProductSourceQueryVo productSourceQueryVo) {
        //根据条件查出层级数据
        String tracingType = productSourceQueryVo.getTracingType();
        List<ProductSourceShowVo> records=productSourceService.queryData(productSourceQueryVo,tracingType);
        records= records.stream().map(record -> {
            if (StringUtils.isNotEmpty(record.getOutputPiece())&&StringUtils.isEmpty(record.getOutputBinNum())) {
                if (StringUtils.isEmpty(record.getUsePiece())||record.getOutputPiece().equals(record.getUsePiece())||record.getUsePiece().equals("empty")) {
                    return record;
                }
            }else if(StringUtils.isEmpty(record.getOutputPiece())&&StringUtils.isNotEmpty(record.getOutputBinNum())){
                if (StringUtils.isEmpty(record.getOutputBinNum())||record.getOutputBinNum().equals(record.getUseBinNum())||record.getUseBinNum().equals("empty")) {
                    return record;
                }
            } else if(StringUtils.isNotEmpty(record.getOutputPiece())&&StringUtils.isNotEmpty(record.getOutputBinNum())){
                if ((StringUtils.isEmpty(record.getOutputBinNum())||record.getOutputBinNum().equals(record.getUseBinNum())||record.getUseBinNum().equals("empty"))&&(StringUtils.isEmpty(record.getUsePiece())||record.getUsePiece().equals(record.getOutputPiece())||record.getUsePiece().equals("empty"))) {
                    return record;
                }
            }else {
                return record;
            }
            return null;
        }).filter(record->record!=null).collect(Collectors.toList());
        productSourceService.setValueProductSourceShow(records,tracingType);
        if (productSourceQueryVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(productSourceQueryVo.getSearchVo(), records);
        }
        Page<ProductSourceShowVo> page = PageUtils.getPage(ProductSourceShowVo.class);
        List<ProductSourceShowVo> collect = records.stream().skip((page.getCurrent() - 1) * page.getSize()).limit(page.getSize()).collect(Collectors.toList());
        return R.ok(PageUtils.getPageInfo(collect,records.size()));
    }

    @ApiOperation(value = "导出")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/download")
    public void download(HttpServletResponse response, @RequestBody ProductSourceQueryVo productSourceQueryVo) throws IOException {
        //根据条件查出层级数据
        String tracingType = productSourceQueryVo.getTracingType();
        List<ProductSourceShowVo> records=productSourceService.queryData(productSourceQueryVo,tracingType);
        records= records.stream().map(record -> {
            if (StringUtils.isNotEmpty(record.getOutputPiece())&&StringUtils.isEmpty(record.getOutputBinNum())) {
                if (StringUtils.isEmpty(record.getUsePiece())||record.getOutputPiece().equals(record.getUsePiece())||record.getUsePiece().equals("empty")) {
                    return record;
                }
            }else if(StringUtils.isEmpty(record.getOutputPiece())&&StringUtils.isNotEmpty(record.getOutputBinNum())){
                if (StringUtils.isEmpty(record.getOutputBinNum())||record.getOutputBinNum().equals(record.getUseBinNum())||record.getUseBinNum().equals("empty")) {
                    return record;
                }
            } else if(StringUtils.isNotEmpty(record.getOutputPiece())&&StringUtils.isNotEmpty(record.getOutputBinNum())){
                if ((StringUtils.isEmpty(record.getOutputBinNum())||record.getOutputBinNum().equals(record.getUseBinNum())||record.getUseBinNum().equals("empty"))&&(StringUtils.isEmpty(record.getUsePiece())||record.getUsePiece().equals(record.getOutputPiece())||record.getUsePiece().equals("empty"))) {
                    return record;
                }
            }else {
                return record;
            }
            return null;
        }).filter(record->record!=null).collect(Collectors.toList());
        productSourceService.setValueProductSourceShow(records,tracingType);
        if (productSourceQueryVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(productSourceQueryVo.getSearchVo(), records);
        }
        //将数据平铺成二维
        List<ProductSourceShowVo> list=productSourceService.toTable(records,false,"");
        ExcelUtils.export3Excel(response, list, ProductSourceShowVo.class, "产品来源追溯");
    }
}
