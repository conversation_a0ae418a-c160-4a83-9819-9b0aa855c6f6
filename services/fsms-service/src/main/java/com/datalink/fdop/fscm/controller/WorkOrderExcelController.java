package com.datalink.fdop.fscm.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.DateUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.*;
import com.datalink.fdop.fscm.api.model.vo.WorkOrderChildConsumeVo;
import com.datalink.fdop.fscm.api.model.vo.WorkOrderExcelVo;
import com.datalink.fdop.fscm.api.model.vo.WorkOrderReceivingVo;
import com.datalink.fdop.fscm.api.model.vo.WorkOrderRowReceivingVo;
import com.datalink.fdop.fscm.execl.AnalysisData;
import com.datalink.fdop.fscm.listener.WorkOrderExcelListener;
import com.datalink.fdop.fscm.service.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Time;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/12/9 15:30
 */
@RestController
@RequestMapping("work/order/excel")
@Api(tags = "收货-委外工单")
@Transactional
public class WorkOrderExcelController {

    @Autowired
    private WorkOrderExcelService workOrderExcelService;
    @Autowired
    private WorkOrderHeadService workOrderHeadService;
    @Autowired
    private TemplateService templateService;
    @Autowired
    private WorkOrderChildService workOrderChildService;
    @Autowired
    private RepertoryService repertoryService;
    @Autowired
    private WorkOrderStoreIssueService workOrderStoreIssueService;
    @Autowired
    private MaterialBatchPropertyService materialBatchPropertyService;
    @Autowired
    private MaterialPiecePropertyService materialPiecePropertyService;
    @Autowired
    private PostCertificateHeadService postCertificateHeadService;
    @Autowired
    private PostCertificateRowService postCertificateRowService;
    @Autowired
    private MaterialBinPropertyService materialBinPropertyService;



    @ApiOperation(value = "模板下载")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/download")
    public void download(HttpServletResponse response) throws IOException {
        ExcelUtils.export3Excel(response,null, WorkOrderExcelVo.class,"批导模板-原材料批量收货");
    }


    @ApiOperation(value = "模板解析")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/analysis")
    public R analysis(MultipartFile file) throws IOException {
        AnalysisData analysisData = new AnalysisData();
        EasyExcel.read(file.getInputStream(), WorkOrderExcelVo.class, new WorkOrderExcelListener(analysisData)).sheet().doRead();
        List<WorkOrderExcelVo> workOrderExcelVos = analysisData.getWorkOrderExcelVos();
        List<WorkOrderExcelVo> data=workOrderExcelService.checkData(workOrderExcelVos);
        return R.ok(data);
    }


    @ApiOperation(value = "确认导入")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/specifiedata")
    public R specifiedata(@RequestBody List<WorkOrderExcelVo> voList) throws IOException {
        Map<String, Object> map = Maps.newHashMap();
        voList.stream().forEach(v->{
            WorkOrderHead workOrderHead = workOrderHeadService.getOne(new QueryWrapper<WorkOrderHead>()
                    .lambda().eq(WorkOrderHead::getWorkOrderNum, v.getWorkOrderNum()));
            v.setSupplierCode(workOrderHead.getSupplierCode());
            v.setSupplierName(templateService.findSupplierName(workOrderHead.getSupplierCode()));
            v.setManufCode(workOrderHead.getManufCode());
            v.setManufType(workOrderHead.getManufType());
            v.setManufName(workOrderExcelService.findManufName(workOrderHead.getManufCode(),workOrderHead.getManufType()));
            v.setFactoryCode(workOrderHead.getFactoryCode());
            v.setMaterialdesc(workOrderExcelService.findMaterialdesc(v.getMaterialCodeReceive()));
            v.setStockPDescription(templateService.findStockPDescription(v.getStockPCodeReceive()));
            v.setMoveType("101");
            v.setMoveTypeDesc(templateService.findMoveTypeDes("101"));
        });
        map.put("data",voList);
        if (CollectionUtils.isNotEmpty(voList)) {
            List<WorkOrderChildConsumeVo> childConsumes = Lists.newArrayList();
            List<WorkOrderRowReceivingVo> rowReceivingVos = Lists.newArrayList();
            //Long workOrderChildrenNum = voList.get(0).getWorkOrderChildrenNum();
            for (WorkOrderExcelVo workOrderExcelVo : voList) {
                String quantityConsume = workOrderExcelVo.getQuantityConsume();
                List<WorkOrderChildConsumeVo> childConsumeVos=workOrderExcelService.findChildReceiving(workOrderExcelVo.getWorkOrderNum(), workOrderExcelVo.getWorkOrderRowNum());
                if (workOrderExcelVo.getWorkOrderChildrenNum()!=null) {
                    WorkOrderChildConsumeVo workOrderChildConsumeVo=new  WorkOrderChildConsumeVo();
                    WorkOrderChild orderChild = workOrderChildService.getOne(new QueryWrapper<WorkOrderChild>()
                            .lambda()
                            .eq(WorkOrderChild::getWorkOrderNum, workOrderExcelVo.getWorkOrderNum())
                            .eq(WorkOrderChild::getWorkOrderRowNum, workOrderExcelVo.getWorkOrderRowNum())
                            .eq(WorkOrderChild::getWorkOrderChildrenNum, workOrderExcelVo.getWorkOrderChildrenNum())

                    );
                    workOrderChildConsumeVo.setWorkOrderNum(workOrderExcelVo.getWorkOrderNum());
                    workOrderChildConsumeVo.setWorkOrderRowNum( workOrderExcelVo.getWorkOrderRowNum());
                    workOrderChildConsumeVo.setWorkOrderChildrenNum(workOrderExcelVo.getWorkOrderChildrenNum());
                    workOrderChildConsumeVo.setFatherCount(orderChild.getFatherCount());
                    workOrderChildConsumeVo.setChildrenCount(orderChild.getChildrenCount());
                    if (StringUtils.isNotEmpty(workOrderExcelVo.getMaterialCodeConsume())) {
                        workOrderChildConsumeVo.setMaterialCode(workOrderExcelVo.getMaterialCodeConsume());
                    }
                    if (StringUtils.isNotEmpty(workOrderExcelVo.getMaterialdesc())) {
                        workOrderChildConsumeVo.setMaterialDesc(workOrderExcelVo.getMaterialdesc());
                    }
                    if (StringUtils.isNotEmpty(workOrderExcelVo.getStockPCodeConsume())) {
                        workOrderChildConsumeVo.setStockPCode(workOrderExcelVo.getStockPCodeConsume());
                    }
                    if (StringUtils.isNotEmpty(workOrderExcelVo.getBatchNumberConsume())) {
                        workOrderChildConsumeVo.setBatchNumber(workOrderExcelVo.getBatchNumberConsume());
                    }
                    if (StringUtils.isNotEmpty(workOrderExcelVo.getPieceConsume())) {
                        workOrderChildConsumeVo.setPiece(workOrderExcelVo.getPieceConsume());
                    }
                    if (StringUtils.isNotEmpty(workOrderExcelVo.getBinNumConsume())) {
                        workOrderChildConsumeVo.setBinNum(workOrderExcelVo.getBinNumConsume());
                    }
                    if (StringUtils.isNotEmpty(workOrderExcelVo.getBasicUnitConsume())) {
                        workOrderChildConsumeVo.setBasicUnit(workOrderExcelVo.getBasicUnitConsume());
                    }
                    workOrderChildConsumeVo.setMoveType("543");
                    workOrderChildConsumeVo.setMoveTypeDesc(templateService.findMoveTypeDes("543"));
                    workOrderChildConsumeVo.setQuantityDelivery(StringUtils.isNotEmpty(workOrderExcelVo.getQuantityConsume()) ? Double.valueOf(quantityConsume) : (Double.valueOf(workOrderExcelVo.getQuantityReceiveG()) + Double.valueOf(workOrderExcelVo.getQuantityReceiveF())) / (workOrderChildConsumeVo.getFatherCount() + workOrderChildConsumeVo.getChildrenCount()));
                    childConsumes.add(workOrderChildConsumeVo);
                }else {
                    for (WorkOrderChildConsumeVo childConsumeVo : childConsumeVos) {
                        WorkOrderChild orderChild = workOrderChildService.getOne(new QueryWrapper<WorkOrderChild>()
                                .lambda()
                                .eq(WorkOrderChild::getWorkOrderNum, childConsumeVo.getWorkOrderNum())
                                .eq(WorkOrderChild::getWorkOrderRowNum, childConsumeVo.getWorkOrderRowNum())
                                .eq(WorkOrderChild::getWorkOrderChildrenNum, childConsumeVo.getWorkOrderChildrenNum())

                        );
                        childConsumeVo.setMoveType("543");
                        childConsumeVo.setMaterialDesc(templateService.findMoveTypeDes("543"));
                        childConsumeVo.setFatherCount(orderChild.getFatherCount());
                        childConsumeVo.setChildrenCount(orderChild.getChildrenCount());
                        if (StringUtils.isEmpty(workOrderExcelVo.getQuantityConsume())) {
                            childConsumeVo.setQuantityNull(true);
                        }
                    }
                    childConsumes.addAll(childConsumeVos);
                }
                WorkOrderRowReceivingVo rowReceivingVo = new WorkOrderRowReceivingVo();
                rowReceivingVo.setWorkOrderNum(workOrderExcelVo.getWorkOrderNum());
                rowReceivingVo.setWorkOrderRowNum(workOrderExcelVo.getWorkOrderRowNum());
                rowReceivingVo.setSupplierCode(workOrderExcelVo.getSupplierCode());
                rowReceivingVo.setSupplierName(workOrderExcelVo.getSupplierName());
                rowReceivingVo.setManufCode(workOrderExcelVo.getManufCode());
                rowReceivingVo.setManufType(workOrderExcelVo.getManufType());
                rowReceivingVo.setManufName(workOrderExcelVo.getManufName());
                rowReceivingVo.setFactoryCode(workOrderExcelVo.getFactoryCode());
                rowReceivingVo.setMaterialCodeReceive(workOrderExcelVo.getMaterialCodeReceive());
                rowReceivingVo.setMaterialdesc(workOrderExcelVo.getMaterialdesc());
                rowReceivingVo.setStockPCodeReceive(workOrderExcelVo.getStockPCodeReceive());
                rowReceivingVo.setStockPDescription(workOrderExcelVo.getStockPDescription());
                rowReceivingVo.setBatchNumberReceive(workOrderExcelVo.getBatchNumberReceive());
                rowReceivingVo.setPieceReceive(workOrderExcelVo.getPieceReceive());
                rowReceivingVo.setBinNumReceive(workOrderExcelVo.getBinNumReceive());
                rowReceivingVo.setQuantityReceiveG(workOrderExcelVo.getQuantityReceiveG());
                rowReceivingVo.setQuantityReceiveF(workOrderExcelVo.getQuantityReceiveF());
                rowReceivingVo.setMoveType(workOrderExcelVo.getMoveType());
                rowReceivingVo.setMoveTypeDesc(workOrderExcelVo.getMoveTypeDesc());
                rowReceivingVo.setBasicUnit(workOrderExcelVo.getBasicUnit());
                rowReceivingVo.setBasicUnitReceive(workOrderExcelVo.getBasicUnitReceive());
                rowReceivingVo.setRowText(workOrderExcelVo.getRowText());
                rowReceivingVos.add(rowReceivingVo);
            }
            childConsumes = childConsumes.stream().distinct().collect(Collectors.toList());
            List<WorkOrderChildConsumeVo> childConsumeVos = childConsumes.stream().collect(
                    Collectors.collectingAndThen(Collectors.toCollection(
                            () -> new TreeSet<>(Comparator.comparing(childConsume -> childConsume.getWorkOrderNum()
                                    + ";" + childConsume.getWorkOrderRowNum()
                                    + ";" + childConsume.getWorkOrderChildrenNum()
                                    + ";" + childConsume.getMaterialCode()
                                    + ";" + childConsume.getStockPCode()
                                    + ";" + childConsume.getBatchNumber()
                                    + ";" + childConsume.getPiece()
                                    + ";" + childConsume.getBinNum()
                            ))
                    ), ArrayList::new)
            );

            ArrayList<WorkOrderRowReceivingVo> receivingVos = rowReceivingVos.stream().collect(
                    Collectors.collectingAndThen(Collectors.toCollection(
                            () -> new TreeSet<>(Comparator.comparing(rowReceivingVo ->
                                    rowReceivingVo.getWorkOrderNum()
                                            + ";" + rowReceivingVo.getWorkOrderRowNum()
                                            + ";" + rowReceivingVo.getMaterialCodeReceive()
                                            + ";" + rowReceivingVo.getStockPCodeReceive()
                                            + ";" + rowReceivingVo.getBatchNumberReceive()
                                            + ";" + rowReceivingVo.getPieceReceive()
                                            + ";" + rowReceivingVo.getBinNumReceive()
                            ))
                    ), ArrayList::new)
            );
            map.put("dataConsumes", childConsumeVos);
            map.put("dataReceiving", receivingVos);

        }
        return R.ok(map);
    }

    @ApiOperation(value = "过帐")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/transferItems")
    public R transferItems(@RequestBody WorkOrderReceivingVo workOrderReceivingVo) throws IOException {
        String maxNum = postCertificateHeadService.getMaxNum();
        if (StringUtils.isEmpty(maxNum)) {
            maxNum="1";
        }
        Integer value = Integer.valueOf(maxNum);
        value=value+1;
        List<Repertory> repertories = workOrderReceivingVo.getRepertories();
        List<PostCertificateRow> postCertificateRows = workOrderReceivingVo.getPostCertificateRows();
        if (CollectionUtils.isNotEmpty(repertories)) {
            repertoryService.updataExcel(repertories,postCertificateRows);
        }
        List<Repertory> repertoriesConsume = workOrderReceivingVo.getRepertoriesConsume();
        if (CollectionUtils.isNotEmpty(repertoriesConsume)) {
            repertoryService.updataConsume(repertoriesConsume);
        }
        List<WorkOrderStoreIssue> workOrderStoreIssues = workOrderReceivingVo.getWorkOrderStoreIssues();
        if (CollectionUtils.isNotEmpty(workOrderStoreIssues)) {
            workOrderStoreIssueService.updata(workOrderStoreIssues);
        }
        List<MaterialBatchProperty> materialBatchProperties = workOrderReceivingVo.getMaterialBatchProperties();
        if (CollectionUtils.isNotEmpty(materialBatchProperties)) {
            materialBatchProperties.stream().forEach(materialBatchProperty -> {
                if (materialBatchPropertyService.checkDataRepetition(materialBatchProperty)) {
                    materialBatchPropertyService.delData(materialBatchProperty);
                }
                try {
                    materialBatchPropertyService.save(materialBatchProperty);
                }catch (Exception e){
                    e.printStackTrace();
                    throw new ServiceException("检查物料批次属性数据是否正确");
                }
            });


        }
        List<MaterialPieceProperty> materialPieceProperties = workOrderReceivingVo.getMaterialPieceProperties();
        if (CollectionUtils.isNotEmpty(materialPieceProperties)) {
            materialPieceProperties.stream().forEach(materialPieceProperty -> {
                if (materialPiecePropertyService.checkDataRepetition(materialPieceProperty)) {
                    materialPiecePropertyService.delData(materialPieceProperty);
                }
                try {
                    materialPiecePropertyService.save(materialPieceProperty);
                }catch (Exception e){
                    e.printStackTrace();
                    throw new ServiceException("检查物片属性数据是否正确");
                }
            });

        }
        List<MaterialBinProperty> materialBinProperties = workOrderReceivingVo.getMaterialBinProperties();
        if (CollectionUtils.isNotEmpty(materialBinProperties)) {
            materialBinProperties.stream().forEach(materialBinProperty -> {
                if (materialBinPropertyService.checkDataRepetition(materialBinProperty)) {
                    materialBinPropertyService.delData(materialBinProperty);
                }
                try {
                    materialBinPropertyService.save(materialBinProperty);
                }catch (Exception e){
                    e.printStackTrace();
                    throw new ServiceException("检查物BIN属性数据是否正确");
                }
            });

        }
        PostCertificateHead postCertificateHead = workOrderReceivingVo.getPostCertificateHead();
        postCertificateHead.setVoucherNum(value);
        postCertificateHead.setVoucherVintage(DateUtils.dateTimeNow(DateUtils.YYYY));
        postCertificateHead.setEnteringDate(new Date());
        postCertificateHead.setEnteringTime(Time.valueOf(DateUtils.dateTimeNow(DateUtils.HH_MM_SS)));
        postCertificateHeadService.save(postCertificateHead);

        for (long i = 0; i < postCertificateRows.size(); i++) {
            PostCertificateRow postCertificateRow = postCertificateRows.get(Integer.valueOf(String.valueOf(i)));
            postCertificateRow.setVoucherRowNum(i+1);
            postCertificateRow.setVoucherNum(value);
            postCertificateRow.setVoucherVintage(DateUtils.dateTimeNow(DateUtils.YYYY));
        }
        if (CollectionUtils.isNotEmpty(postCertificateRows)) {
            try {
                postCertificateRowService.saveBatch(postCertificateRows);
            }catch (Exception e){
                e.printStackTrace();
                throw new ServiceException("过帐行数据是否正确，或主键是否重复");
            }
        }
        return R.ok();
    }

}
