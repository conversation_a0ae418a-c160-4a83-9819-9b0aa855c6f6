package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.fscm.api.domain.PostCertificateRow;
import com.datalink.fdop.fscm.api.domain.ProvisionalSingleOut;
import com.datalink.fdop.fscm.api.domain.ShipRequestHead;
import com.datalink.fdop.fscm.api.domain.ShipRequestRow;
import com.datalink.fdop.fscm.api.model.vo.SaveShipRequestVo;
import com.datalink.fdop.fscm.api.model.vo.ShipRequestPostCertificateVo;
import com.datalink.fdop.fscm.api.model.vo.ShipRequestQueryVo;
import com.datalink.fdop.fscm.api.model.vo.ShipRequestShowVo;
import com.datalink.fdop.fscm.service.PostCertificateRowService;
import com.datalink.fdop.fscm.service.ProvisionalSingleOutService;
import com.datalink.fdop.fscm.service.ShipRequestHeadService;
import com.datalink.fdop.fscm.service.ShipRequestRowService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/12/15 10:07
 */
@RestController
@RequestMapping("/ship/request/head")
@Transactional
@Api(tags = "出货需求头")
public class ShipRequestHeadController {
    @Autowired
    private ShipRequestHeadService shipRequestHeadService;
    @Autowired
    private ShipRequestRowService shipRequestRowService;
    @Autowired
    private PostCertificateRowService postCertificateRowService;

    @Autowired
    private ProvisionalSingleOutService provisionalSingleOutService;


    @ApiOperation(value = "出货需求查询")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/query")
    public R query(@RequestBody ShipRequestQueryVo shipRequestQueryVo, @RequestParam(required = false) String delFlag, @RequestParam(required = false) String temporary,@RequestParam(required = false) Boolean postFlag) {
        Page<ShipRequestShowVo> page = PageUtils.getPage(ShipRequestShowVo.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        List<ShipRequestShowVo> records= shipRequestHeadService.queryData(shipRequestQueryVo,delFlag,temporary);
        for (ShipRequestShowVo record : records) {
            List<PostCertificateRow> rows = postCertificateRowService.list(new QueryWrapper<PostCertificateRow>().lambda()
                    .eq(PostCertificateRow::getShipRequestOrderNum, record.getShipRequestOrderNum())
                    .eq(PostCertificateRow::getShipRequestOrderRowNum, record.getShipRequestOrderRowNum())
                    .in(PostCertificateRow::getMoveType, Arrays.asList("601","602"))
            );
            double sum = rows.stream().mapToDouble(p -> p.getPostQuantity()).sum();
            record.setQuantityDelivered(sum*-1);
        }
        if (postFlag!=null&&postFlag) {
            records=records.stream().filter(p->p.getQuantityDelivered()==0.0).collect(Collectors.toList());
        }
        if (shipRequestQueryVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(shipRequestQueryVo.getSearchVo(), records);
        }
        long total=records.size();
        records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        return R.ok(PageUtils.getPageInfo(records,(int)total));
    }
    @ApiOperation(value = "导出")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/download")
    public void download(HttpServletResponse response, @RequestBody ShipRequestQueryVo shipRequestQueryVo, @RequestParam(required = false) String delFlag, @RequestParam(required = false) String temporary,@RequestParam(required = false) Boolean postFlag) throws IOException {
        //Page<ShipRequestShowVo> page = PageUtils.getPage(ShipRequestShowVo.class);
        List<ShipRequestShowVo> records= shipRequestHeadService.queryData(shipRequestQueryVo,delFlag,temporary);
        for (ShipRequestShowVo record : records) {
            List<PostCertificateRow> rows = postCertificateRowService.list(new QueryWrapper<PostCertificateRow>().lambda()
                    .eq(PostCertificateRow::getShipRequestOrderNum, record.getShipRequestOrderNum())
                    .eq(PostCertificateRow::getShipRequestOrderRowNum, record.getShipRequestOrderRowNum())
                    .in(PostCertificateRow::getMoveType, Arrays.asList("601","602"))
            );
            double sum = rows.stream().mapToDouble(p -> p.getPostQuantity()).sum();
            record.setQuantityDelivered(sum*-1);
        }
        if (postFlag!=null&&postFlag) {
            records=records.stream().filter(p->p.getQuantityDelivered()==0.0).collect(Collectors.toList());
        }
        if (shipRequestQueryVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(shipRequestQueryVo.getSearchVo(), records);
        }

        ExcelUtils.export3Excel(response,records, ShipRequestShowVo.class,"出货需求");
    }

    @ApiOperation(value = "出货需求过帐检查")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/post/certificate/check")
    public R certificateCheck(@RequestBody ShipRequestPostCertificateVo requestPostCertificateVo) {
        //校验
        List<ShipRequestShowVo> shipRequestShowVos = requestPostCertificateVo.getShipRequestShowVos();
        shipRequestHeadService.checkData(shipRequestShowVos);
        return R.ok();
    }


    @ApiOperation(value = "出货需求过帐")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/post/certificate")
    public R certificate(@RequestBody ShipRequestPostCertificateVo requestPostCertificateVo) {
        //拆行保存
        shipRequestHeadService.saveData(requestPostCertificateVo);
        return R.ok();
    }
    @ApiOperation(value = "出货需求保存检查")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/save/check")
    public R saveCheck(@RequestBody SaveShipRequestVo saveShipRequestVo) {
        //校验
        List<ShipRequestRow> shipRequestRowsDel = saveShipRequestVo.getShipRequestRowsDel();
        shipRequestRowsDel.addAll(saveShipRequestVo.getShipRequestRowsUpdate());
        for (ShipRequestRow shipRequestRow : shipRequestRowsDel) {
            shipRequestRowService.remove(new QueryWrapper<ShipRequestRow>().lambda()
                    .eq(ShipRequestRow::getShipRequestOrderNum,shipRequestRow.getShipRequestOrderNum())
                    .eq(ShipRequestRow::getShipRequestOrderRowNum,shipRequestRow.getShipRequestOrderRowNum())
            );
        }
        List<ShipRequestRow> shipRequestRows = Lists.newArrayList();
        shipRequestRows.addAll(saveShipRequestVo.getShipRequestRowsInsert());
        shipRequestRows.addAll(saveShipRequestVo.getShipRequestRowsUpdate());
        shipRequestHeadService.saveCheck(shipRequestRows);
        return R.ok();
    }

    @ApiOperation(value = "保存需求头，行")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/save/head/row")
    public R save(@RequestBody SaveShipRequestVo saveShipRequestVo) {
        //保存头
        List<ShipRequestHead> shipRequestHeads = saveShipRequestVo.getShipRequestHeads();
        shipRequestHeads.forEach(shipRequestHead -> {
            //删除挑选的缓存
            ProvisionalSingleOut provisionalSingleOut = new ProvisionalSingleOut();
            provisionalSingleOut.setTransferOrderNum(shipRequestHead.getShipRequestOrderNum());
            provisionalSingleOutService.delCache(Arrays.asList(provisionalSingleOut));
            shipRequestHead.setUsername(SecurityUtils.getUsername());
        });
        for (ShipRequestHead shipRequestHead : shipRequestHeads) {
            shipRequestHeadService.remove(new QueryWrapper<ShipRequestHead>().lambda().eq(ShipRequestHead::getShipRequestOrderNum,shipRequestHead.getShipRequestOrderNum()));
        }
        List<ShipRequestRow> shipRequestRows = Lists.newArrayList();
        shipRequestRows.addAll(saveShipRequestVo.getShipRequestRowsDel());
        shipRequestRows.addAll(saveShipRequestVo.getShipRequestRowsUpdate());
        for (ShipRequestRow shipRequestRow : shipRequestRows) {
            shipRequestRowService.remove(new QueryWrapper<ShipRequestRow>()
                    .lambda()
                    .eq(ShipRequestRow::getShipRequestOrderNum,shipRequestRow.getShipRequestOrderNum())
                    .eq(ShipRequestRow::getShipRequestOrderRowNum,shipRequestRow.getShipRequestOrderRowNum())
            );
        }
        shipRequestHeadService.saveBatch(shipRequestHeads);
        shipRequestRows.addAll(saveShipRequestVo.getShipRequestRowsInsert());
        shipRequestRowService.saveBatch(shipRequestRows);
        shipRequestHeadService.updateRepertory(saveShipRequestVo);
        return R.ok();
    }



}
