package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.PostCertificateRow;
import com.datalink.fdop.fscm.service.MoveTypeService;
import com.datalink.fdop.fscm.service.PostCertificateRowService;
import com.datalink.fdop.fscm.service.WorkOrderExcelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/11/29 10:39
 */
@RestController
@RequestMapping("/post/certificate/row")
@Transactional
@Api(tags = "过账凭证行项目")
public class PostCertificateRowController {


    @Autowired
    private PostCertificateRowService postCertificateRowService;

    @Autowired
    private WorkOrderExcelService workOrderExcelService;

    @Autowired
    private MoveTypeService moveTypeService;



    @ApiOperation(value = "获取最大过帐凭证号")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/max/num")
    public R getMaxNum() {
        return R.ok(postCertificateRowService.getMaxNum());
    }


    @ApiOperation(value = "过账行查询")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/query")
    public R query(@RequestBody PostCertificateRow postCertificateRow) {
        PostCertificateRow certificateRow = postCertificateRowService.getOne(new QueryWrapper<PostCertificateRow>().lambda()
                .eq(PostCertificateRow::getVoucherNum, postCertificateRow.getVoucherNum())
                .eq(StringUtils.isNotEmpty(postCertificateRow.getVoucherVintage()),PostCertificateRow::getVoucherVintage, postCertificateRow.getVoucherVintage())
                .eq(postCertificateRow.getVoucherRowNum()!=null,PostCertificateRow::getVoucherRowNum, postCertificateRow.getVoucherRowNum())
        );
        return R.ok(certificateRow);
    }
    @ApiOperation(value = "过账行查询(多行)")
    @Log(title = "fscm",businessType = BusinessType.UPDATE)
    @PostMapping("/select")
    public R select(@RequestBody PostCertificateRow postCertificateRow) {
        Page<PostCertificateRow> page = PageUtils.getPage(PostCertificateRow.class);
        Page<PostCertificateRow> certificateRow = postCertificateRowService.page(page,new QueryWrapper<PostCertificateRow>().lambda()
                .eq(PostCertificateRow::getVoucherNum, postCertificateRow.getVoucherNum())
                .eq(StringUtils.isNotEmpty(postCertificateRow.getVoucherVintage()),PostCertificateRow::getVoucherVintage, postCertificateRow.getVoucherVintage())
                .eq(postCertificateRow.getVoucherRowNum()!=null,PostCertificateRow::getVoucherRowNum, postCertificateRow.getVoucherRowNum())
        );
        List<PostCertificateRow> records = certificateRow.getRecords();
        records.stream().forEach(p->{
            p.setStockPDescriptionReceive(workOrderExcelService.findStockPDescription(p.getStockPCodeReceive()));
            p.setStockPDescriptionShip(workOrderExcelService.findStockPDescription(p.getStockPCodeShip()));
            p.setMaterialDesc(workOrderExcelService.findMaterialdesc(p.getMaterialCode()));
            p.setMoveTypeDesc(moveTypeService.getMoveTypeDesc(p.getMoveType()));
        });
        return R.ok(PageUtils.getPageInfo(certificateRow.getRecords(),(int) certificateRow.getTotal()));
    }
}
