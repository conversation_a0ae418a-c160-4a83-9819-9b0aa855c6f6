package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.ExecutionScheduleWip;
import com.datalink.fdop.fscm.api.domain.PostCertificateRow;
import com.datalink.fdop.fscm.api.model.vo.ExecutionScheduleQueryVo;
import com.datalink.fdop.fscm.api.model.vo.ExecutionScheduleShowVo;
import com.datalink.fdop.fscm.service.ExecutionScheduleService;
import com.datalink.fdop.fscm.service.ExecutionScheduleWipService;
import com.datalink.fdop.fscm.service.PostCertificateRowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/2/15 14:57
 */
@RestController
@RequestMapping("/execution/schedule")
@Transactional
@Api(tags = "执行进度")
public class ExecutionScheduleController {

    @Autowired
    private ExecutionScheduleService executionScheduleService;

    @Autowired
    private ExecutionScheduleWipService executionScheduleWipService;
    @Autowired
    private PostCertificateRowService postCertificateRowService;

    @ApiOperation(value = "查询")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/query")
    public R query(@RequestBody ExecutionScheduleQueryVo executionScheduleQueryVo)  {
        //根据条件查出层级数据
        Page<ExecutionScheduleShowVo> page = PageUtils.getPage(ExecutionScheduleShowVo.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (executionScheduleQueryVo.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<ExecutionScheduleShowVo> executionScheduleShowVoPage=executionScheduleService.queryData(executionScheduleQueryVo,page);
        List<ExecutionScheduleShowVo> records = executionScheduleShowVoPage.getRecords();
        for (ExecutionScheduleShowVo record : records) {
            List<ExecutionScheduleWip> list = executionScheduleWipService.list(new QueryWrapper<ExecutionScheduleWip>().lambda()
                    .eq(ExecutionScheduleWip::getWorkOrderNum,record.getWorkOrderNum())
                    .eq(ExecutionScheduleWip::getWorkOrderRowNum,record.getWorkOrderRowNum())
                    .isNotNull(ExecutionScheduleWip::getQuantityPredict)
            );
            if (CollectionUtils.isNotEmpty(list)) {
                record.setQuantityPredict(list.get(0).getQuantityPredict());
            }
            List<ExecutionScheduleWip> list2 = executionScheduleWipService.list(new QueryWrapper<ExecutionScheduleWip>().lambda()
                    .eq(ExecutionScheduleWip::getWorkOrderNum,record.getWorkOrderNum())
                    .eq(ExecutionScheduleWip::getWorkOrderRowNum,record.getWorkOrderRowNum())
                    .isNotNull(ExecutionScheduleWip::getPredictOutDate)
            );
            if (CollectionUtils.isNotEmpty(list2)) {
                record.setPredictOutDate(list2.get(0).getPredictOutDate());
            }
            List<ExecutionScheduleWip> list3 = executionScheduleWipService.list(new QueryWrapper<ExecutionScheduleWip>().lambda()
                    .eq(ExecutionScheduleWip::getWorkOrderNum,record.getWorkOrderNum())
                    .eq(ExecutionScheduleWip::getWorkOrderRowNum,record.getWorkOrderRowNum())
            );
            if (CollectionUtils.isNotEmpty(list3)) {
                record.setExecutionSchedule(list3);
            }
            List<PostCertificateRow> postCertificateRows= postCertificateRowService.list(new QueryWrapper<PostCertificateRow>()
                    .lambda()
                    .eq(PostCertificateRow::getWorkOrderNum,record.getWorkOrderNum())
                    .eq(PostCertificateRow::getWorkOrderRowNum,record.getWorkOrderRowNum())
                    .in(PostCertificateRow::getMoveType, Arrays.asList("101","102"))
            );
            double sum = postCertificateRows.stream().map(PostCertificateRow::getPostQuantity).mapToDouble(p -> p).sum();
            record.setQuantityReceived(sum);
            record.setUnreceivedQuantity(record.getQuantityReceive()-record.getQuantityReceived());
        }
        if (executionScheduleQueryVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(executionScheduleQueryVo.getSearchVo(), records);
            executionScheduleShowVoPage.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records,(int) executionScheduleShowVoPage.getTotal()));
    }

    @ApiOperation(value = "导出")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/download")
    public void download(HttpServletResponse response, @RequestBody ExecutionScheduleQueryVo executionScheduleQueryVo) throws IOException {
        Page<ExecutionScheduleShowVo> page = PageUtils.getPage(ExecutionScheduleShowVo.class);
        if (executionScheduleQueryVo.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<ExecutionScheduleShowVo> executionScheduleShowVoPage=executionScheduleService.queryData(executionScheduleQueryVo,page);
        List<ExecutionScheduleShowVo> records = executionScheduleShowVoPage.getRecords();
        for (ExecutionScheduleShowVo record : records) {
            List<ExecutionScheduleWip> list = executionScheduleWipService.list(new QueryWrapper<ExecutionScheduleWip>().lambda()
                    .eq(ExecutionScheduleWip::getWorkOrderNum,record.getWorkOrderNum())
                    .eq(ExecutionScheduleWip::getWorkOrderRowNum,record.getWorkOrderRowNum())
                    .isNotNull(ExecutionScheduleWip::getQuantityPredict)
            );
            if (CollectionUtils.isNotEmpty(list)) {
                record.setQuantityPredict(list.get(0).getQuantityPredict());
            }
            List<ExecutionScheduleWip> list2 = executionScheduleWipService.list(new QueryWrapper<ExecutionScheduleWip>().lambda()
                    .eq(ExecutionScheduleWip::getWorkOrderNum,record.getWorkOrderNum())
                    .eq(ExecutionScheduleWip::getWorkOrderRowNum,record.getWorkOrderRowNum())
                    .isNotNull(ExecutionScheduleWip::getPredictOutDate)
            );
            if (CollectionUtils.isNotEmpty(list2)) {
                record.setPredictOutDate(list2.get(0).getPredictOutDate());
            }
            List<ExecutionScheduleWip> list3 = executionScheduleWipService.list(new QueryWrapper<ExecutionScheduleWip>().lambda()
                    .eq(ExecutionScheduleWip::getWorkOrderNum,record.getWorkOrderNum())
                    .eq(ExecutionScheduleWip::getWorkOrderRowNum,record.getWorkOrderRowNum())
            );
            if (CollectionUtils.isNotEmpty(list3)) {
                record.setExecutionSchedule(list3);
            }
            List<PostCertificateRow> postCertificateRows= postCertificateRowService.list(new QueryWrapper<PostCertificateRow>()
                    .lambda()
                    .eq(PostCertificateRow::getWorkOrderNum,record.getWorkOrderNum())
                    .eq(PostCertificateRow::getWorkOrderRowNum,record.getWorkOrderRowNum())
                    .in(PostCertificateRow::getMoveType, Arrays.asList("101","102"))
            );
            double sum = postCertificateRows.stream().map(PostCertificateRow::getPostQuantity).mapToDouble(p -> p).sum();
            record.setQuantityReceived(sum);
            record.setUnreceivedQuantity(record.getQuantityReceive()-record.getQuantityReceived());
        }
        if (executionScheduleQueryVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(executionScheduleQueryVo.getSearchVo(), records);
        }
        ExcelUtils.export3Excel(response, records, ExecutionScheduleShowVo.class, "执行进度");
    }



}
