package com.datalink.fdop.fscm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.fscm.api.domain.GetReturnMaterialHead;
import com.datalink.fdop.fscm.api.domain.GetReturnMaterialRow;
import com.datalink.fdop.fscm.api.domain.PostCertificateRow;
import com.datalink.fdop.fscm.api.domain.ProvisionalSingleOut;
import com.datalink.fdop.fscm.api.model.vo.GetReturnMaterialPostCertificateVo;
import com.datalink.fdop.fscm.api.model.vo.GetReturnMaterialQueryVo;
import com.datalink.fdop.fscm.api.model.vo.GetReturnMaterialSaveVo;
import com.datalink.fdop.fscm.api.model.vo.GetReturnMaterialShowVo;
import com.datalink.fdop.fscm.service.GetReturnMaterialHeadService;
import com.datalink.fdop.fscm.service.GetReturnMaterialRowService;
import com.datalink.fdop.fscm.service.PostCertificateRowService;
import com.datalink.fdop.fscm.service.ProvisionalSingleOutService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/1/4 14:15
 */
@RestController
@RequestMapping("/getReturnMaterial/head")
@Transactional
@Api(tags = "领退料头")
public class GetReturnMaterialHeadController {


    @Autowired
    private GetReturnMaterialHeadService getReturnMaterialHeadService;
    @Autowired
    private GetReturnMaterialRowService getReturnMaterialRowService;
    @Autowired
    private PostCertificateRowService postCertificateRowService;

    @Autowired
    private ProvisionalSingleOutService provisionalSingleOutService;

    @ApiOperation(value = "领退料查询")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/query")
    public R query(@RequestBody GetReturnMaterialQueryVo getReturnMaterialQueryVo, @RequestParam(required = false) String delFlag, @RequestParam(required = false) String temporary,@RequestParam(required = false) Boolean postFlag) {

        Page<GetReturnMaterialShowVo> page = PageUtils.getPage(GetReturnMaterialShowVo.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        page.setSize(Long.MAX_VALUE);
        List<GetReturnMaterialShowVo> records= getReturnMaterialHeadService.queryData(getReturnMaterialQueryVo,delFlag,temporary);
        for (GetReturnMaterialShowVo record : records) {
            List<PostCertificateRow> list = postCertificateRowService.list(new QueryWrapper<PostCertificateRow>().lambda()
                    .eq(PostCertificateRow::getPickReturnOrderNum, record.getPickReturnOrderNum())
                    .eq(PostCertificateRow::getPickReturnOrderRowNum, record.getPickReturnOrderRowNum())
                    .in(PostCertificateRow::getMoveType, Arrays.asList("201", "202"))
            );
            if (CollectionUtils.isNotEmpty(list)) {
                double sum = list.stream().map(p->{
                    GetReturnMaterialRow one = getReturnMaterialRowService.getOne(new QueryWrapper<GetReturnMaterialRow>().lambda()
                            .eq(GetReturnMaterialRow::getPickReturnOrderNum, p.getPickReturnOrderNum())
                            .eq(GetReturnMaterialRow::getPickReturnOrderRowNum, p.getPickReturnOrderRowNum())
                    );
                    if (one.getPickReturnType().equals("1")) {
                        p.setPostQuantity(p.getPostQuantity()*-1);
                    }
                    return p;
                }).filter(p->p!=null).map(PostCertificateRow::getPostQuantity).mapToDouble(m -> m).sum();
                record.setPostedQuantity(sum);
            }else {
                record.setPostedQuantity(0);
            }
        }
        if (postFlag!=null&&postFlag) {
            records=records.stream().filter(p->p.getPostedQuantity()==0.0).collect(Collectors.toList());
        }
        if (getReturnMaterialQueryVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(getReturnMaterialQueryVo.getSearchVo(), records );

        }
        long total=records.size();
        records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        return R.ok(PageUtils.getPageInfo(records,(int)total));
    }

    @ApiOperation(value = "领退料过帐数据检查")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/post/certificate/check")
    public R certificateCheck(@RequestBody GetReturnMaterialPostCertificateVo postCertificateVo) {
        //检查数据
        getReturnMaterialHeadService.checkData(postCertificateVo.getPostCertificateRows());

        return R.ok();
    }
    @ApiOperation(value = "领退料过帐")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/post/certificate")
    public R certificate(@RequestBody GetReturnMaterialPostCertificateVo postCertificateVo) {
        //保存数据
        getReturnMaterialHeadService.saveData(postCertificateVo);

        return R.ok();
    }
    @ApiOperation(value = "领退料保存数据检查")
    @Log(title = "fscm")
    @PostMapping("/save/check")
    public R saveCheck(@RequestBody GetReturnMaterialSaveVo getReturnMaterialSaveVo) {
        //检查数据
        List<GetReturnMaterialRow> materialRowList= Lists.newArrayList();
        materialRowList.addAll(getReturnMaterialSaveVo.getGetReturnMaterialRowsInsert());
        materialRowList.addAll(getReturnMaterialSaveVo.getGetReturnMaterialRowsUpdate());
        getReturnMaterialHeadService.saveCheck(materialRowList);

        return R.ok();
    }
    @ApiOperation(value = "保存，暂存")
    @Log(title = "fscm",businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public R save(@RequestBody GetReturnMaterialSaveVo getReturnMaterialSaveVo) {
        GetReturnMaterialHead getReturnMaterialHead = getReturnMaterialSaveVo.getGetReturnMaterialHead();

        //删除挑选的缓存
        ProvisionalSingleOut provisionalSingleOut = new ProvisionalSingleOut();
        provisionalSingleOut.setTransferOrderNum(getReturnMaterialHead.getPickReturnOrderNum());
        provisionalSingleOutService.delCache(Arrays.asList(provisionalSingleOut));

        getReturnMaterialHeadService.remove(new QueryWrapper<GetReturnMaterialHead>()
                .lambda()
                .eq(GetReturnMaterialHead::getPickReturnOrderNum,getReturnMaterialHead.getPickReturnOrderNum())
        );
        if (getReturnMaterialHead!=null) {
            getReturnMaterialHeadService.save(getReturnMaterialHead);
        }
        getReturnMaterialRowService.updateRepertory(getReturnMaterialSaveVo);
        List<GetReturnMaterialRow> getReturnMaterialRowsDel = Lists.newArrayList();
        getReturnMaterialRowsDel.addAll(getReturnMaterialSaveVo.getGetReturnMaterialRowsDel());
        getReturnMaterialRowsDel.addAll(getReturnMaterialSaveVo.getGetReturnMaterialRowsUpdate());
        for (GetReturnMaterialRow getReturnMaterialRow : getReturnMaterialRowsDel) {
            getReturnMaterialRowService.remove(new QueryWrapper<GetReturnMaterialRow>().lambda()
                    .eq(GetReturnMaterialRow::getPickReturnOrderNum,getReturnMaterialRow.getPickReturnOrderNum())
                    .eq(GetReturnMaterialRow::getPickReturnOrderRowNum,getReturnMaterialRow.getPickReturnOrderRowNum())
            );
        }
        List<GetReturnMaterialRow> getReturnMaterialRows = Lists.newArrayList();
        getReturnMaterialRows.addAll(getReturnMaterialSaveVo.getGetReturnMaterialRowsInsert());
        getReturnMaterialRows.addAll(getReturnMaterialSaveVo.getGetReturnMaterialRowsUpdate());
        getReturnMaterialRows.addAll(getReturnMaterialSaveVo.getGetReturnMaterialRowsDel());
        if (CollectionUtils.isNotEmpty(getReturnMaterialRows)) {
            getReturnMaterialRowService.saveBatch(getReturnMaterialRows);
        }

        return R.ok();
    }


    @ApiOperation(value = "导出")
    @Log(title = "fscm",businessType = BusinessType.EXPORT)
    @PostMapping("/download")
    public void download(HttpServletResponse response, @RequestBody GetReturnMaterialQueryVo getReturnMaterialQueryVo, @RequestParam(required = false) String delFlag, @RequestParam(required = false) String temporary,@RequestParam(required = false) Boolean postFlag) throws IOException {
        Page<GetReturnMaterialShowVo> page = PageUtils.getPage(GetReturnMaterialShowVo.class);
        page.setSize(Long.MAX_VALUE);
        List<GetReturnMaterialShowVo> records= getReturnMaterialHeadService.queryData(getReturnMaterialQueryVo,delFlag,temporary);
        for (GetReturnMaterialShowVo record : records) {
            List<PostCertificateRow> list = postCertificateRowService.list(new QueryWrapper<PostCertificateRow>().lambda()
                    .eq(PostCertificateRow::getPickReturnOrderNum, record.getPickReturnOrderNum())
                    .eq(PostCertificateRow::getPickReturnOrderRowNum, record.getPickReturnOrderRowNum())
                    .in(PostCertificateRow::getMoveType, Arrays.asList("201", "202"))
            );
            if (CollectionUtils.isNotEmpty(list)) {
                double sum = list.stream().map(p->{
                    GetReturnMaterialRow one = getReturnMaterialRowService.getOne(new QueryWrapper<GetReturnMaterialRow>().lambda()
                            .eq(GetReturnMaterialRow::getPickReturnOrderNum, p.getPickReturnOrderNum())
                            .eq(GetReturnMaterialRow::getPickReturnOrderRowNum, p.getPickReturnOrderRowNum())
                    );
                    if (one.getPickReturnType().equals("1")) {
                        p.setPostQuantity(p.getPostQuantity()*-1);
                    }
                    return p;
                }).filter(p->p!=null).map(PostCertificateRow::getPostQuantity).mapToDouble(m -> m).sum();
                record.setPostedQuantity(sum);
            }else {
                record.setPostedQuantity(0);
            }
        }
        if (postFlag!=null&&postFlag) {
            records=records.stream().filter(p->!Double.isNaN(p.getPostedQuantity())).collect(Collectors.toList());
        }
        if (getReturnMaterialQueryVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(getReturnMaterialQueryVo.getSearchVo(), records );

        }
        ExcelUtils.export3Excel(response, records, GetReturnMaterialShowVo.class, "领退料");
    }


    @ApiOperation(value = "审批")
    @Log(title = "fscm",businessType = BusinessType.OTHER)
    @PostMapping("/examine")
    public R examine(@RequestBody List<GetReturnMaterialHead> getReturnMaterialHeads) {
        for (GetReturnMaterialHead getReturnMaterialHead : getReturnMaterialHeads) {
            getReturnMaterialHeadService.update(Wrappers.lambdaUpdate(GetReturnMaterialHead.class)
                    .set(GetReturnMaterialHead::getStatu,getReturnMaterialHead.getStatu())
                    .eq(GetReturnMaterialHead::getPickReturnOrderNum,getReturnMaterialHead.getPickReturnOrderNum())
            );
        }


        return R.ok();
    }

}
