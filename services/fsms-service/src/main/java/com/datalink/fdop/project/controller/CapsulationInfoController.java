package com.datalink.fdop.project.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.exception.ServiceException;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.excel.ExcelUtils;
import com.datalink.fdop.common.core.utils.search.SearchUtils;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.project.api.domain.CapsulationInfo;
import com.datalink.fdop.project.api.model.vo.CapsulationInfoShowVo;
import com.datalink.fdop.project.api.model.vo.CapsulationInfoVo;
import com.datalink.fdop.project.service.CapsulationInfoService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/10/10 16:35
 */
@RestController
@RequestMapping("/capsulation")
@Transactional
public class CapsulationInfoController {


    @Autowired
    private CapsulationInfoService capsulationInfoService;



    @ApiOperation(value = "新增")
    @Log(title = "工程资料", businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public R create(@RequestBody List<CapsulationInfo> capsulationInfos) {
        capsulationInfos.stream().forEach(capsulationInfo -> {
            capsulationInfoService.checkObject(capsulationInfo,"2");
            CapsulationInfo one = capsulationInfoService.getOne(new QueryWrapper<CapsulationInfo>()
                    .lambda()
                    .eq(CapsulationInfo::getManufCode, capsulationInfo.getManufCode())
                    .eq(CapsulationInfo::getMaterialCode, capsulationInfo.getMaterialCode())
                    .eq(CapsulationInfo::getSupplierCode, capsulationInfo.getSupplierCode())
                    .eq(CapsulationInfo::getVersions, capsulationInfo.getVersions())
                    .eq(CapsulationInfo::getManufType, capsulationInfo.getManufType())
            );
            if (one!=null) {
                throw new ServiceException("数据已存在");
            }
        });
        try {
            if (capsulationInfoService.saveBatch(capsulationInfos)) {
                return R.ok("保存成功");
            } else {
                return R.fail("保存失败");
            }
        }catch (Exception e){
            e.printStackTrace();
            throw new ServiceException("检查数据是否正确");
        }

    }

    @ApiOperation(value = "修改")
    @Log(title = "工程资料", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public R update(@RequestBody List<CapsulationInfo> capsulationInfos) {
        capsulationInfos.stream().forEach(capsulationInfo -> {
            capsulationInfoService.checkObject(capsulationInfo,"2");
            CapsulationInfo one = capsulationInfoService.getOne(new QueryWrapper<CapsulationInfo>()
                    .lambda()
                    .eq(CapsulationInfo::getManufCode, capsulationInfo.getManufCode())
                    .eq(CapsulationInfo::getMaterialCode, capsulationInfo.getMaterialCode())
                    .eq(CapsulationInfo::getSupplierCode, capsulationInfo.getSupplierCode())
                    .eq(CapsulationInfo::getVersions, capsulationInfo.getVersions())
                    .eq(CapsulationInfo::getManufType, capsulationInfo.getManufType())
                    .ne(CapsulationInfo::getId,capsulationInfo.getId())
            );
            if (one!=null) {
                throw new ServiceException("数据已存在");
            }
        });
        if (capsulationInfoService.updateBatchById(capsulationInfos)) {
            return R.ok("修改成功");
        } else {
            return R.fail("修改失败");
        }
    }

    @ApiOperation("删除")
    @Log(title = "工程资料", businessType = BusinessType.DELETE)
    @PostMapping("/del")
    public R del(@RequestBody List<Long> materialIds) {
        if (capsulationInfoService.removeBatchByIds(materialIds)) {
            return R.ok("删除成功");
        } else {
            return R.fail("删除失败");
        }
    }

    @ApiOperation(value = "查询")
    @Log(title = "工程资料", businessType = BusinessType.OTHER)
    @PostMapping("/query")
    public R query(@RequestBody CapsulationInfoVo infoVo) {
        Page<CapsulationInfoShowVo> page = PageUtils.getPage(CapsulationInfoShowVo.class);
        Long pageSize = page.getSize();
        Long current = page.getCurrent();
        if (infoVo.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<CapsulationInfoShowVo> materialPage = capsulationInfoService.pageData(page,infoVo);
        List<CapsulationInfoShowVo> records = materialPage.getRecords();
        if (infoVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(infoVo.getSearchVo(), records);
            materialPage.setTotal(records.size());
            records=records.stream().skip(pageSize*(current-1)).limit(pageSize).collect(Collectors.toList());
        }
        return R.ok(PageUtils.getPageInfo(records, (int) materialPage.getTotal()));
    }

    @ApiOperation(value = "导出封装信息")
    @Log(title = "工程资料",businessType = BusinessType.OTHER)
    @PostMapping("/download")
    public void download(@RequestBody CapsulationInfoVo infoVo, HttpServletResponse response) throws IOException {
        Page<CapsulationInfoShowVo> page = PageUtils.getPage(CapsulationInfoShowVo.class);
        if (infoVo.getSearchVo()!=null) {
            page.setSize(Long.MAX_VALUE);
        }
        Page<CapsulationInfoShowVo> materialPage = capsulationInfoService.pageData(page,infoVo);
        List<CapsulationInfoShowVo> records = materialPage.getRecords();
        if (infoVo.getSearchVo()!=null) {
            records = SearchUtils.getByEntityFilter(infoVo.getSearchVo(), records);
        }
        ExcelUtils.export3Excel(response,records, CapsulationInfoShowVo.class,"封装信息");
    }
}
