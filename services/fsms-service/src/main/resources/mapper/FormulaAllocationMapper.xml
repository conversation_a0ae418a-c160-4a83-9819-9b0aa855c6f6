<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.settlement.mapper.FormulaAllocationMapper">

    <resultMap id="BaseResultMap" type="com.datalink.fdop.settlement.api.domain.FormulaAllocation">
        <id property="process" column="process" jdbcType="VARCHAR"/>
        <id property="formula" column="formula" jdbcType="VARCHAR"/>
        <id property="supplierCode" column="supplier_code" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        process
        ,formula,supplier_code
    </sql>
</mapper>
