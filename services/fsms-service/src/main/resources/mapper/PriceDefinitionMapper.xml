<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.settlement.mapper.PriceDefinitionMapper">

    <resultMap id="BaseResultMap" type="com.datalink.fdop.settlement.api.domain.PriceDefinition">
        <id property="priceFactor" column="price_factor" jdbcType="VARCHAR"/>
        <result property="priceFactorDesc" column="price_factor_desc" jdbcType="VARCHAR"/>
        <result property="priceFactorType" column="price_factor_type" jdbcType="VARCHAR"/>
        <result property="table" column="table" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        price_factor
        ,price_factor_desc,price_factor_type,
        table
    </sql>
    <insert id="execSql">
        ${sql}
    </insert>
    <select id="selectData" resultType="java.util.Map">
        select * from zjdata.${table} where 1=1
        <if test="priceSelectVo.supplierCode !=null and priceSelectVo.supplierCode.size() !=0" >
            AND supplier_code
            IN
            <foreach collection="priceSelectVo.supplierCode" item="supplierCode" open="(" close=")" separator="," >
                #{supplierCode}
            </foreach>
        </if>
        <if test="priceSelectVo.validFrom !=null and priceSelectVo.validFrom.size() !=0" >
            AND valid_From >= #{priceSelectVo.validFrom[0]}
        </if>
        <if test="priceSelectVo.validFrom !=null and priceSelectVo.validFrom.size() !=0" >
            AND valid_To &lt;=#{priceSelectVo.validFrom[1]}
        </if>
        <if test="priceSelectVo.quotationNum !=null and priceSelectVo.quotationNum.size() !=0" >
            AND quotation_Num
            IN
            <foreach collection="priceSelectVo.quotationNum" item="quotationNum" open="(" close=")" separator="," >
                #{quotationNum}
            </foreach>
        </if>
    </select>
</mapper>
