<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.settlement.mapper.FormulaDefinitionMapper">

    <resultMap id="BaseResultMap" type="com.datalink.fdop.settlement.api.domain.FormulaDefinition">
        <id property="process" column="process" jdbcType="VARCHAR"/>
        <result property="formula" column="formula" jdbcType="VARCHAR"/>
        <result property="formulaDesc" column="formula_desc" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        process
        ,formula,formula_desc
    </sql>
</mapper>
