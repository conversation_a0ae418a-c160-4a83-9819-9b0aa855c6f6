<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.settlement.mapper.PriceElementMapper">

    <resultMap id="BaseResultMap" type="com.datalink.fdop.settlement.api.domain.PriceElement">
        <id property="priceFactor" column="price_factor" jdbcType="VARCHAR"/>
        <result property="changingFactor" column="changing_factor" jdbcType="VARCHAR"/>
        <result property="changingFactorDesc" column="changing_factor_desc" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        price_factor
        ,changing_factor,changing_factor_desc
    </sql>
</mapper>
