<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.fscm.mapper.FlowHeadMapper">

    <select id="queryData" resultType="com.datalink.fdop.fscm.api.domain.FlowHead">
        SELECT
            T.*,
            t1.supplier_name,
            t2.manuf_name,
            t3.material_desc
        FROM
            zjdata.f_d_flow_head
            T LEFT JOIN zjdata.p_d_supplier t1 ON T.supplier_code = t1.supplier_code
            LEFT JOIN zjdata.p_d_manufacturer t2 on t2.manuf_code=T.manuf_code and t2.manuf_type=T.manuf_type
            LEFT JOIN zjdata.p_d_material t3 ON T.material_code =t3.material_code
            where 1=1
        <if test="flowHeadQueryVo.supplierCode !=null and flowHeadQueryVo.supplierCode.size() !=0" >
            AND T.supplier_code
            IN
            <foreach collection="flowHeadQueryVo.supplierCode" item="supplierCode" open="(" close=")" separator="," >
                #{supplierCode}
            </foreach>
        </if>
        <if test="flowHeadQueryVo.supplierName !=null and flowHeadQueryVo.supplierName.size() !=0" >
            AND t1.supplier_name LIKE concat('%', #{flowHeadQueryVo.supplierName[0]}, '%')
        </if>
        <if test="flowHeadQueryVo.manufCode !=null and flowHeadQueryVo.manufCode.size() !=0" >
            AND T.manuf_code
            IN
            <foreach collection="flowHeadQueryVo.manufCode" item="manufCode" open="(" close=")" separator="," >
                #{manufCode}
            </foreach>
        </if>
        <if test="flowHeadQueryVo.manufType !=null and flowHeadQueryVo.manufType.size() !=0" >
            AND T.manuf_type
            IN
            <foreach collection="flowHeadQueryVo.manufType" item="manufType" open="(" close=")" separator="," >
                #{manufType}
            </foreach>
        </if>
        <if test="flowHeadQueryVo.manufName !=null and flowHeadQueryVo.manufName.size() !=0" >
            AND t2.manuf_name LIKE concat('%', #{flowHeadQueryVo.manufName[0]}, '%')
        </if>
        <if test="flowHeadQueryVo.materialCode !=null and flowHeadQueryVo.materialCode.size() !=0" >
            AND T.material_code
            IN
            <foreach collection="flowHeadQueryVo.materialCode" item="materialCode" open="(" close=")" separator="," >
                #{materialCode}
            </foreach>
        </if>
        <if test="flowHeadQueryVo.materialDesc !=null and flowHeadQueryVo.materialDesc.size() !=0" >
            AND t3.material_desc LIKE concat('%', #{flowHeadQueryVo.materialDesc[0]}, '%')
        </if>
        <if test="flowHeadQueryVo.process !=null and flowHeadQueryVo.process.size() !=0" >
            AND T.process
            IN
            <foreach collection="flowHeadQueryVo.process" item="process" open="(" close=")" separator="," >
                #{process}
            </foreach>
        </if>
        <if test="flowHeadQueryVo.statu !=null and flowHeadQueryVo.statu.size() !=0" >
            AND T.statu
            IN
            <foreach collection="flowHeadQueryVo.statu" item="statu" open="(" close=")" separator="," >
                #{statu}
            </foreach>
        </if>
        <if test="flowHeadQueryVo.isUniversal !=null and flowHeadQueryVo.isUniversal.size() !=0" >
            AND T.is_universal
            IN
            <foreach collection="flowHeadQueryVo.isUniversal" item="isUniversal" open="(" close=")" separator="," >
                #{isUniversal}
            </foreach>
        </if>
        <if test="flowHeadQueryVo.flow !=null and flowHeadQueryVo.flow.size() !=0" >
            AND T.flow LIKE concat('%', #{flowHeadQueryVo.flow[0]}, '%')
        </if>
    </select>

</mapper>

