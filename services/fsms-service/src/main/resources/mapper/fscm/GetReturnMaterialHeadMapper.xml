<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.fscm.mapper.GetReturnMaterialHeadMapper">

    <select id="queryData" resultType="com.datalink.fdop.fscm.api.model.vo.GetReturnMaterialShowVo">
        SELECT
            t3.*,
            t4.material_desc,
            t5.stock_p_description
        FROM
            (
            SELECT
                t2.statu,
                t2."temporary",
                t2.username,
                t2.paper_date,
                t2.rise_text,
                t2.del_flag del_flag_head,
                t2.pick_return_order_num,
                t1.pick_return_order_row_num,
                t1.del_flag,
                t1.pick_return_type,
                t1.material_code,
                t1.factory_code,
                t1.stock_p_code,
                t1.batch_number,
                t1.piece,
                t1.bin_num,
                t1.reason,
                t1.pick_return_quantity,
                t1.basic_unit
            FROM
                zjdata.f_d_get_return_material_row t1 RIGHT JOIN
                zjdata.f_d_get_return_material_head t2
            ON
                t1.pick_return_order_num = t2.pick_return_order_num
            ) t3
            LEFT JOIN zjdata.p_d_material t4 ON t3.material_code = t4.material_code
            LEFT JOIN zjdata.org_stock_place t5 ON t3.stock_p_code=t5.stock_p_code
        where 1=1
        <if test="getReturnMaterialQueryVo.pickReturnOrderNum !=null and getReturnMaterialQueryVo.pickReturnOrderNum.size() !=0" >
            AND t3.pick_return_order_num
            IN
            <foreach collection="getReturnMaterialQueryVo.pickReturnOrderNum" item="pickReturnOrderNum" open="(" close=")" separator="," >
                #{pickReturnOrderNum}
            </foreach>
        </if>
        <if test="getReturnMaterialQueryVo.statu !=null and getReturnMaterialQueryVo.statu.size() !=0" >
            AND t3.statu
            IN
            <foreach collection="getReturnMaterialQueryVo.statu" item="statu" open="(" close=")" separator="," >
                #{statu}
            </foreach>
        </if>
        <if test="getReturnMaterialQueryVo.username !=null and getReturnMaterialQueryVo.username.size() !=0" >
            AND t3.username LIKE concat('%', #{getReturnMaterialQueryVo.username[0]}, '%')
        </if>
        <if test="getReturnMaterialQueryVo.pickReturnOrderNum !=null and getReturnMaterialQueryVo.pickReturnOrderNum.size() !=0" >
            AND t3.pick_return_order_num
            IN
            <foreach collection="getReturnMaterialQueryVo.pickReturnOrderNum" item="pickReturnOrderNum" open="(" close=")" separator="," >
                #{pickReturnOrderNum}
            </foreach>
        </if>
        <if test="getReturnMaterialQueryVo.paperDate !=null and getReturnMaterialQueryVo.paperDate.size() !=0" >
            AND t3.paper_date
            IN
            <foreach collection="getReturnMaterialQueryVo.paperDate" item="paperDate" open="(" close=")" separator="," >
                #{paperDate}
            </foreach>
        </if>
        <if test="getReturnMaterialQueryVo.materialCode !=null and getReturnMaterialQueryVo.materialCode.size() !=0" >
            AND t3.material_code
            IN
            <foreach collection="getReturnMaterialQueryVo.materialCode" item="materialCode" open="(" close=")" separator="," >
                #{materialCode}
            </foreach>
        </if>
        <if test="getReturnMaterialQueryVo.materialDesc !=null and getReturnMaterialQueryVo.materialDesc.size() !=0" >
            AND t4.material_desc LIKE concat('%', #{getReturnMaterialQueryVo.materialDesc[0]}, '%')
        </if>
        <if test="getReturnMaterialQueryVo.factoryCode !=null and getReturnMaterialQueryVo.factoryCode.size() !=0" >
            AND t3.factory_code
            IN
            <foreach collection="getReturnMaterialQueryVo.factoryCode" item="factoryCode" open="(" close=")" separator="," >
                #{factoryCode}
            </foreach>
        </if>
        <if test="getReturnMaterialQueryVo.stockPCode !=null and getReturnMaterialQueryVo.stockPCode.size() !=0" >
            AND t3.stock_p_code
            IN
            <foreach collection="getReturnMaterialQueryVo.stockPCode" item="stockPCode" open="(" close=")" separator="," >
                #{stockPCode}
            </foreach>
        </if>
        <if test="getReturnMaterialQueryVo.batchNumber !=null and getReturnMaterialQueryVo.batchNumber.size() !=0" >
            AND t3.batch_number
            IN
            <foreach collection="getReturnMaterialQueryVo.batchNumber" item="batchNumber" open="(" close=")" separator="," >
                #{batchNumber}
            </foreach>
        </if>
        <if test="getReturnMaterialQueryVo.piece !=null and getReturnMaterialQueryVo.piece.size() !=0" >
            AND t3.piece
            IN
            <foreach collection="getReturnMaterialQueryVo.piece" item="piece" open="(" close=")" separator="," >
                #{piece}
            </foreach>
        </if>
        <if test="getReturnMaterialQueryVo.binNum !=null and getReturnMaterialQueryVo.binNum.size() !=0" >
            AND t3.bin_num
            IN
            <foreach collection="getReturnMaterialQueryVo.binNum" item="binNum" open="(" close=")" separator="," >
                #{binNum}
            </foreach>
        </if>
        <if test="delFlag!=null and delFlag!=''">
            and t3.del_flag_head=#{delFlag}
            and t3.del_flag=#{delFlag}
        </if>
        <if test="temporary!=null and temporary!=''">
            and t3.temporary=#{temporary}
        </if>
    </select>

</mapper>

