<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.fscm.mapper.ProductSourceMapper">

    <select id="queryData" resultType="com.datalink.fdop.fscm.api.model.vo.ProductSourceShowVo">

        SELECT
        <if test="tracingType.equals('1'.toString())" >
            distinct ON(t2.work_order_num, t2.work_order_row_num,t2.output_material_code,t2.output_batch_number,t5.work_order_children_num,t5.material_code,t5.batch_number)
        </if>
        t2.*,
        t3.manuf_code,
        t3.manuf_type,
        t4.manuf_name,
        t5.work_order_children_num,
        t5.material_code  use_material_code,
        t5.batch_number  use_batch_number,
        t5.piece use_piece,
        t5.bin_num use_bin_num,
        t6.material_desc use_material_desc,
        t8.material_desc output_material_desc,
        t7.is_master_chip
        FROM
        (
        SELECT
        <if test="tracingType.equals('2'.toString())" >
            DISTINCT ON( t1.work_order_num, t1.work_order_row_num,t1.material_code, t1.batch_number_receive, t1.piece_receive, t1.bin_num_receive )
        </if>
        <if test="tracingType.equals('1'.toString())" >
            DISTINCT ON( t1.work_order_num, t1.work_order_row_num,t1.material_code, t1.batch_number_receive )
        </if>
        t1.material_code output_material_code,
        t1.batch_number_receive output_batch_number,
        t1.piece_receive output_piece,
        t1.bin_num_receive output_bin_num,
        t1.order_num,
        t1.order_row_num,
        t1.work_order_num,
        t1.work_order_row_num
        FROM
        zjdata.f_d_post_certificate_row t1 where 1 = 1  and  t1.write_off='0'
            <if test="productSourceQueryVo.materialCode !=null and productSourceQueryVo.materialCode.size() !=0" >
                AND t1.material_code
                IN
                <foreach collection="productSourceQueryVo.materialCode" item="materialCode" open="(" close=")" separator="," >
                    #{materialCode}
                </foreach>
            </if>

            <if test="productSourceQueryVo.batchNumberReceive !=null and productSourceQueryVo.batchNumberReceive.size() !=0" >
                AND t1.batch_number_receive
                IN
                <foreach collection="productSourceQueryVo.batchNumberReceive" item="batchNumberReceive" open="(" close=")" separator="," >
                    #{batchNumberReceive}
                </foreach>
            </if>
            <if test="productSourceQueryVo.pieceReceive !=null and productSourceQueryVo.pieceReceive.size() !=0" >
                AND t1.piece_receive
                IN
                <foreach collection="productSourceQueryVo.pieceReceive" item="pieceReceive" open="(" close=")" separator="," >
                    #{pieceReceive}
                </foreach>
            </if>
            <if test="productSourceQueryVo.binNumReceive !=null and productSourceQueryVo.binNumReceive.size() !=0" >
                AND t1.bin_num_receive
                IN
                <foreach collection="productSourceQueryVo.binNumReceive" item="binNumReceive" open="(" close=")" separator="," >
                    #{binNumReceive}
                </foreach>
            </if>
        and ( work_order_num IN (

        SELECT work_order_num FROM zjdata.f_d_work_order_head t12 where 1=1
        <if test="productSourceQueryVo.purchaseCode !=null and productSourceQueryVo.purchaseCode.size() !=0">
        AND
            t12.purchase_code
            IN
            <foreach collection="productSourceQueryVo.purchaseCode" item="purchaseCode" open="(" close=")" separator="," >
                    #{purchaseCode}
                </foreach>
            </if>
            <if test="productSourceQueryVo.factoryCode !=null and productSourceQueryVo.factoryCode.size()!=0" >
                AND t12.factory_code
                IN
                <foreach collection="productSourceQueryVo.factoryCode" item="factoryCode" open="(" close=")" separator="," >
                    #{factoryCode}
                </foreach>
            </if>
        )
        or order_num IN (
        select order_num from zjdata. p_d_order_head t13 where 1=1
        <if test="productSourceQueryVo.purchaseCode !=null and productSourceQueryVo.purchaseCode.size() !=0" >
            AND t13.purchase_code
            IN
            <foreach collection="productSourceQueryVo.purchaseCode" item="purchaseCode" open="(" close=")" separator="," >
                #{purchaseCode}
            </foreach>
        </if>
        )
        )
        ) t2
        LEFT JOIN zjdata.f_d_work_order_head t3 ON t3.work_order_num=t2.work_order_num
        LEFT JOIN zjdata.p_d_manufacturer t4 on t3.manuf_code=t4.manuf_code and t3.manuf_type=t4.manuf_type
        LEFT JOIN zjdata.f_d_work_order_store_issue t5 ON t5.work_order_num=t2.work_order_num and
        t2.work_order_row_num=t5.work_order_row_num
        LEFT JOIN zjdata.p_d_material t6 on t6.material_code=t5.material_code
        LEFT JOIN zjdata.f_d_work_order_child t7 on t2.work_order_num=t7.work_order_num and
        t2.work_order_row_num=t7.work_order_row_num
        LEFT JOIN zjdata.p_d_material t8 on t8.material_code=t2.output_material_code
        where 1=1
        <if test="productSourceQueryVo.materialDesc !=null and productSourceQueryVo.materialDesc.size() !=0" >
            AND t8.material_desc LIKE concat('%', #{productSourceQueryVo.materialDesc[0]}, '%')
        </if>
    </select>

    <select id="findData" resultType="com.datalink.fdop.fscm.api.model.vo.ProductSourceShowVo">

        SELECT
        <if test="tracingType.equals('1'.toString())" >
            distinct ON(t2.work_order_num, t2.work_order_row_num,t2.output_material_code,t2.output_batch_number,t5.work_order_children_num,t5.material_code,t5.batch_number)
        </if>
        t2.*,
        t3.manuf_code,
        t3.manuf_type,
        t4.manuf_name,
        t5.work_order_children_num,
        t5.material_code  use_material_code,
        t5.batch_number  use_batch_number,
        t5.piece use_piece,
        t5.bin_num use_bin_num,
        t6.material_desc use_material_desc,
        t8.material_desc output_material_desc,
        t7.is_master_chip
        FROM
        (
        SELECT
        <if test="tracingType.equals('2'.toString())" >
            DISTINCT ON( t1.work_order_num, t1.work_order_row_num,t1.material_code, t1.batch_number_receive, t1.piece_receive, t1.bin_num_receive )
        </if>
        <if test="tracingType.equals('1'.toString())" >
            DISTINCT ON( t1.work_order_num, t1.work_order_row_num,t1.material_code, t1.batch_number_receive )
        </if>
        t1.material_code output_material_code,
        t1.batch_number_receive output_batch_number,
        t1.piece_receive output_piece,
        t1.bin_num_receive output_bin_num,
        t1.order_num,
        t1.order_row_num,
        t1.work_order_num,
        t1.work_order_row_num
        FROM
        zjdata.f_d_post_certificate_row t1 where 1 = 1 and  t1.write_off='0'
        <if test="useMaterialCode !=null and useMaterialCode.equals('empty'.toString())">
            AND t1.material_code  is null
        </if>
        <if test="useMaterialCode !=null and !useMaterialCode.equals('empty'.toString())">
            AND t1.material_code=#{useMaterialCode}
        </if>
        <if test="useBatchNumber !=null and useBatchNumber.equals('empty'.toString())">
            AND t1.batch_number_receive  is null
        </if>
        <if test="useBatchNumber !=null and !useBatchNumber.equals('empty'.toString())">
            AND t1.batch_number_receive=#{useBatchNumber}
        </if>
        <if test="tracingType.equals('2'.toString())">
            <if test="usePiece !=null and usePiece.equals('empty'.toString())">
                AND t1.piece_receive  is null
            </if>
            <if test="usePiece !=null and !usePiece.equals('empty'.toString())">
                AND t1.piece_receive=#{usePiece}
            </if>
            <if test="useBinNum !=null and useBinNum.equals('empty'.toString())">
                AND t1.bin_num_receive  is null
            </if>
            <if test="useBinNum !=null and !useBinNum.equals('empty'.toString())">
                AND t1.bin_num_receive=#{useBinNum}
            </if>
        </if>
        ) t2
        LEFT JOIN zjdata.f_d_work_order_head t3 ON t3.work_order_num=t2.work_order_num
        LEFT JOIN zjdata.p_d_manufacturer t4 on t3.manuf_code=t4.manuf_code and t3.manuf_type=t4.manuf_type
        LEFT JOIN zjdata.f_d_work_order_store_issue t5 ON t5.work_order_num=t2.work_order_num and t2.work_order_row_num=t5.work_order_row_num
        LEFT JOIN zjdata.p_d_material t6 on t6.material_code=t5.material_code
        LEFT JOIN zjdata.f_d_work_order_child t7 on t2.work_order_num=t7.work_order_num and t2.work_order_row_num=t7.work_order_row_num
        LEFT JOIN zjdata.p_d_material t8 on t8.material_code=t2.output_material_code
    </select>

</mapper>

