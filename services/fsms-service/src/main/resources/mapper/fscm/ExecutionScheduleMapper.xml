<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.fscm.mapper.ExecutionScheduleMapper">

        <select id="queryData"  resultType="com.datalink.fdop.fscm.api.model.vo.ExecutionScheduleShowVo">
                SELECT
                        t1.*,
                        t2.supplier_name,
                        t3.manuf_name,
                        t4.purchase_description,
                        t5.work_order_row_num,
                        t5.material_code,
                        t5.material_desc,
                        t5.batch_number,
                        t5.quantity_receive,
                        t5.basic_unit,
                        t5.delivery_limit,
                        t5.encapsulation_mode,
                        t5.insufficient_delivery,
                        t5.delivery_date
                FROM zjdata.f_d_work_order_head t1
                LEFT JOIN zjdata.p_d_supplier t2 ON t1.supplier_code=t2.supplier_code
                LEFT JOIN zjdata.p_d_manufacturer t3 ON t3.manuf_code=t1.manuf_code and t1.manuf_type=t3.manuf_type
                LEFT JOIN zjdata.org_purchase t4 ON t4.purchase_code=t1.purchase_code
                LEFT JOIN zjdata.f_d_work_order_row t5 ON t1.work_order_num=t5.work_order_num
                where 1=1
                <if test="executionScheduleQueryVo.workOrderNum !=null and executionScheduleQueryVo.workOrderNum.size() !=0" >
                        AND t1.work_order_num IN
                        <foreach collection="executionScheduleQueryVo.workOrderNum" item="workOrderNum" open="(" close=")" separator="," >
                                #{workOrderNum}
                        </foreach>
                </if>
                <if test="executionScheduleQueryVo.workOrderRowNum !=null and executionScheduleQueryVo.workOrderRowNum.size() !=0" >
                        AND t5.work_order_row_num IN
                        <foreach collection="executionScheduleQueryVo.workOrderRowNum" item="workOrderRowNum" open="(" close=")" separator="," >
                                #{workOrderRowNum}
                        </foreach>
                </if>
                <if test="executionScheduleQueryVo.materialCode !=null and executionScheduleQueryVo.materialCode.size() !=0" >
                        AND t5.material_code IN
                        <foreach collection="executionScheduleQueryVo.materialCode" item="materialCode" open="(" close=")" separator="," >
                                #{materialCode}
                        </foreach>
                </if>
                <if test="executionScheduleQueryVo.materialDesc !=null and executionScheduleQueryVo.materialDesc.size() !=0" >
                        AND t5.material_desc LIKE concat('%', #{executionScheduleQueryVo.materialDesc[0]}, '%')
                </if>
                <if test="executionScheduleQueryVo.purchaseCode !=null and executionScheduleQueryVo.purchaseCode.size() !=0" >
                        AND t1.purchase_code IN
                        <foreach collection="executionScheduleQueryVo.purchaseCode" item="purchaseCode" open="(" close=")" separator="," >
                                #{purchaseCode}
                        </foreach>
                </if>
                <if test="executionScheduleQueryVo.factoryCode !=null and executionScheduleQueryVo.factoryCode.size() !=0" >
                        AND t1.factory_code IN
                        <foreach collection="executionScheduleQueryVo.factoryCode" item="factoryCode" open="(" close=")" separator="," >
                                #{factoryCode}
                        </foreach>
                </if>
                <if test="executionScheduleQueryVo.workOrderType !=null and executionScheduleQueryVo.workOrderType.size() !=0" >
                        AND t1.work_order_type IN
                        <foreach collection="executionScheduleQueryVo.workOrderType" item="workOrderType" open="(" close=")" separator="," >
                                #{workOrderType}
                        </foreach>
                </if>
                <if test="executionScheduleQueryVo.username !=null and executionScheduleQueryVo.username.size() !=0" >
                        AND t1.username LIKE concat('%', #{executionScheduleQueryVo.username[0]}, '%')
                </if>
                <if test="executionScheduleQueryVo.paperDate !=null and executionScheduleQueryVo.paperDate.size() !=0" >
                        AND t1.paper_date IN
                        <foreach collection="executionScheduleQueryVo.paperDate" item="paperDate" open="(" close=")" separator="," >
                                #{paperDate}
                        </foreach>
                </if>
                <if test="executionScheduleQueryVo.supplierCode !=null and executionScheduleQueryVo.supplierCode.size() !=0" >
                        AND t1.supplier_code IN
                        <foreach collection="executionScheduleQueryVo.supplierCode" item="supplierCode" open="(" close=")" separator="," >
                                #{supplierCode}
                        </foreach>
                </if>
                <if test="executionScheduleQueryVo.supplierName !=null and executionScheduleQueryVo.supplierName.size() !=0" >
                        AND t2.supplier_name LIKE concat('%', #{executionScheduleQueryVo.supplierName[0]}, '%')
                </if>
                <if test="executionScheduleQueryVo.manufCode !=null and executionScheduleQueryVo.manufCode.size() !=0" >
                        AND t1.manuf_code IN
                        <foreach collection="executionScheduleQueryVo.manufCode" item="manufCode" open="(" close=")" separator="," >
                                #{manufCode}
                        </foreach>
                </if>
                <if test="executionScheduleQueryVo.manufType !=null and executionScheduleQueryVo.manufType.size() !=0" >
                        AND t1.manuf_type IN
                        <foreach collection="executionScheduleQueryVo.manufType" item="manufType" open="(" close=")" separator="," >
                                #{manufType}
                        </foreach>
                </if>
                <if test="executionScheduleQueryVo.manufName !=null and executionScheduleQueryVo.manufName.size() !=0" >
                        AND  t3.manuf_name LIKE concat('%', #{executionScheduleQueryVo.manufName[0]}, '%')
                </if>
                <if test="executionScheduleQueryVo.process !=null and executionScheduleQueryVo.process.size() !=0" >
                        AND t1.process IN
                        <foreach collection="executionScheduleQueryVo.process" item="process" open="(" close=")" separator="," >
                                #{process}
                        </foreach>
                </if>
                <if test="executionScheduleQueryVo.rework !=null and executionScheduleQueryVo.rework.size() !=0" >
                        AND t1.rework IN
                        <foreach collection="executionScheduleQueryVo.rework" item="rework" open="(" close=")" separator="," >
                                #{rework}
                        </foreach>
                </if>
                <if test="executionScheduleQueryVo.produceStatu !=null and executionScheduleQueryVo.produceStatu.size() !=0" >
                        AND t1.produce_statu IN
                        <foreach collection="executionScheduleQueryVo.produceStatu" item="produceStatu" open="(" close=")" separator="," >
                                #{produceStatu}
                        </foreach>
                </if>
                and t1.del_flag !='1' and t5.del_flag!='1' and t1."temporary" !='1'
        </select>

</mapper>

