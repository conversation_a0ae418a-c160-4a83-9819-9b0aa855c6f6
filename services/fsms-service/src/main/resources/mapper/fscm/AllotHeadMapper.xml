<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.fscm.mapper.AllotHeadMapper">

    <select id="queryData" resultType="com.datalink.fdop.fscm.api.model.vo.AllotShowVo">
                SELECT
                    t7.*,
                    t6.stock_p_description AS stock_p_code_receive_desc,
                    t8.stock_p_description AS stock_p_code_ship_desc
                FROM
                    (
                    SELECT
                        t3.*,
                        t4.material_desc,
                        t5.stock_p_description AS stock_p_description_ship
                    FROM
                        (
                        SELECT
                            t1.statu,
                            t1.del_flag AS del_flag_h,
                            t1.TEMPORARY,
                            t1.username,
                            t1.paper_date,
                            t1.rise_text,
                            t1.transfer_order_num,
                            t1.transfer_type,
                            t2.transfer_order_row_num,
                            t2.del_flag,
                            t2.material_code,
                            t2.factory_code,
                            t2.stock_p_code_ship,
                            t2.stock_p_code_receive,
                            t2.batch_number_ship,
                            t2.batch_number_receive,
                            t2.piece_ship,
                            t2.piece_receive,
                            t2.bin_num_ship,
                            t2.bin_num_receive,
                            t2.stock_statu_ship,
                            t2.stock_statu_receive,
                            t2.reason,
                            t2.transfer_quantity,
                            t2.basic_unit
                        FROM
                            zjdata.f_d_allot_head t1 LEFT JOIN
                            zjdata.f_d_allot_row t2
                        ON
                            t1.transfer_order_num = t2.transfer_order_num
                        ) t3
                        LEFT JOIN zjdata.p_d_material t4 ON t3.material_code = t4.material_code
                        LEFT JOIN zjdata.org_stock_place t5 ON t5.stock_p_code = t3.stock_p_code_ship
                    ) t7
                    LEFT JOIN zjdata.org_stock_place t6 ON t6.stock_p_code = t7.stock_p_code_receive
                    LEFT JOIN zjdata.org_stock_place t8 ON t8.stock_p_code = t7.stock_p_code_ship
                WHERE
                    1 = 1
                    <if test="allotQueryVo.transferOrderNum !=null and allotQueryVo.transferOrderNum.size() !=0" >
                        AND t7.transfer_order_num IN
                        <foreach collection="allotQueryVo.transferOrderNum" item="transferOrderNum" open="(" close=")" separator="," >
                            #{transferOrderNum}
                        </foreach>
                    </if>
                    <if test="allotQueryVo.statu !=null and allotQueryVo.statu.size() !=0" >
                        AND t7.statu IN
                        <foreach collection="allotQueryVo.statu" item="statu" open="(" close=")" separator="," >
                            #{statu}
                        </foreach>
                    </if>
                    <if test="allotQueryVo.username !=null and allotQueryVo.username.size() !=0" >
                        AND t7.username LIKE concat('%', #{allotQueryVo.username[0]}, '%')
                    </if>
                    <if test="allotQueryVo.paperDate !=null and allotQueryVo.paperDate.size() !=0" >
                        AND t7.paper_date
                        IN
                        <foreach collection="allotQueryVo.paperDate" item="paperDate" open="(" close=")" separator="," >
                            #{paperDate}
                        </foreach>
                    </if>
                    <if test="allotQueryVo.materialCode !=null and allotQueryVo.materialCode.size() !=0" >
                        AND t7.material_code IN
                        <foreach collection="allotQueryVo.materialCode" item="materialCode" open="(" close=")" separator="," >
                            #{materialCode}
                        </foreach>
                    </if>
                    <if test="allotQueryVo.materialDesc !=null and allotQueryVo.materialDesc.size() !=0" >
                        AND t7.material_desc LIKE concat('%', #{allotQueryVo.materialDesc[0]}, '%')
                    </if>

                    <if test="allotQueryVo.factoryCode !=null and allotQueryVo.factoryCode.size() !=0" >
                        AND t7.factory_code IN
                        <foreach collection="allotQueryVo.factoryCode" item="factoryCode" open="(" close=")" separator="," >
                            #{factoryCode}
                        </foreach>
                    </if>
                    <if test="allotQueryVo.stockPCodeShip !=null and allotQueryVo.stockPCodeShip.size() !=0" >
                        AND t7.stock_p_code_ship IN
                        <foreach collection="allotQueryVo.stockPCodeShip" item="stockPCodeShip" open="(" close=")" separator="," >
                            #{stockPCodeShip}
                        </foreach>
                    </if>
                    <if test="allotQueryVo.stockPCodeReceive !=null and allotQueryVo.stockPCodeReceive.size() !=0" >
                        AND t7.stock_p_code_receive IN
                        <foreach collection="allotQueryVo.stockPCodeReceive" item="stockPCodeReceive" open="(" close=")" separator="," >
                            #{stockPCodeReceive}
                        </foreach>
                    </if>
                    <if test="allotQueryVo.batchNumberShip !=null and allotQueryVo.batchNumberShip.size() !=0" >
                        AND t7.batch_number_ship IN
                        <foreach collection="allotQueryVo.batchNumberShip" item="batchNumberShip" open="(" close=")" separator="," >
                            #{batchNumberShip}
                        </foreach>
                    </if>
                    <if test="allotQueryVo.batchNumberReceive !=null and allotQueryVo.batchNumberReceive.size() !=0" >
                        AND t7.batch_number_receive IN
                        <foreach collection="allotQueryVo.batchNumberReceive" item="batchNumberReceive" open="(" close=")" separator="," >
                            #{batchNumberReceive}
                        </foreach>
                    </if>
                    <if test="allotQueryVo.pieceShip !=null and allotQueryVo.pieceShip.size() !=0" >
                        AND t7.piece_ship IN
                        <foreach collection="allotQueryVo.pieceShip" item="pieceShip" open="(" close=")" separator="," >
                            #{pieceShip}
                        </foreach>
                    </if>
                    <if test="allotQueryVo.pieceReceive !=null and allotQueryVo.pieceReceive.size() !=0" >
                        AND t7.piece_receive IN
                        <foreach collection="allotQueryVo.pieceReceive" item="pieceReceive" open="(" close=")" separator="," >
                            #{pieceReceive}
                        </foreach>
                    </if>
                    <if test="allotQueryVo.pieceReceive !=null and allotQueryVo.pieceReceive.size() !=0" >
                        AND t7.piece_receive IN
                        <foreach collection="allotQueryVo.pieceReceive" item="pieceReceive" open="(" close=")" separator="," >
                            #{pieceReceive}
                        </foreach>
                    </if>
                    <if test="allotQueryVo.binNumShip !=null and allotQueryVo.binNumShip.size() !=0" >
                        AND t7.bin_num_ship IN
                        <foreach collection="allotQueryVo.binNumShip" item="binNumShip" open="(" close=")" separator="," >
                            #{binNumShip}
                        </foreach>
                    </if>
                    <if test="allotQueryVo.binNumReceive !=null and allotQueryVo.binNumReceive.size() !=0" >
                        AND t7.bin_num_receive IN
                        <foreach collection="allotQueryVo.binNumReceive" item="binNumReceive" open="(" close=")" separator="," >
                            #{binNumReceive}
                        </foreach>
                    </if>
                    <if test="temporary!=null and temporary!=''">
                        and t7.temporary=#{temporary}
                    </if>
                    <if test="delflag!=null and delflag!=''">
                        and t7.del_flag=#{delflag}
                    </if>
    </select>

</mapper>

