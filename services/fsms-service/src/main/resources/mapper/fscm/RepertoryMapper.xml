<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.fscm.mapper.RepertoryMapper">

    <select id="queryMove" resultType="com.datalink.fdop.fscm.api.domain.RepertoryMove">
        SELECT
        t5.rise_text,
        t5.entering_time,
            T.*,
            t5.post_date,
            t5.entering_date,
            t5.username,
            t1.move_type_desc,
            t2.material_desc,
            t3.stock_p_description stock_p_description_ship,
            t4.stock_p_description	stock_p_description_receive
        FROM
            zjdata.f_d_post_certificate_row T
        RIGHT JOIN zjdata.f_d_post_certificate_head t5 on t5.voucher_num=T.voucher_num and t5.voucher_vintage =T.voucher_vintage
             LEFT JOIN zjdata.f_d_move_type t1 ON T.move_type = t1.move_type
            LEFT JOIN  zjdata.p_d_material t2 on t2.material_code=T.material_code
            LEFT JOIN  zjdata.org_stock_place t3 on t3.stock_p_code =T.stock_p_code_ship
            LEFT JOIN	zjdata.org_stock_place t4 on t4.stock_p_code =T.stock_p_code_receive
        where 1=1
        <if test="repertoryMoveQueryVo.voucherNum !=null and repertoryMoveQueryVo.voucherNum.size() !=0" >
            AND T.voucher_num   IN
            <foreach collection="repertoryMoveQueryVo.voucherNum" item="voucherNum" open="(" close=")" separator="," >
                #{voucherNum}
            </foreach>
        </if>
        <if test="repertoryMoveQueryVo.postDate !=null and repertoryMoveQueryVo.postDate.size() !=0" >
            AND t5.post_date            BETWEEN
            <foreach collection="repertoryMoveQueryVo.postDate" item="postDate"  separator="and" >
                #{postDate}
            </foreach>
        </if>
        <if test="repertoryMoveQueryVo.enteringDate !=null and repertoryMoveQueryVo.enteringDate.size() !=0" >
            AND t5.entering_date           BETWEEN
            <foreach collection="repertoryMoveQueryVo.enteringDate" item="enteringDate"  separator="and" >
                #{enteringDate}
            </foreach>
        </if>
        <if test="repertoryMoveQueryVo.username !=null and repertoryMoveQueryVo.username.size() !=0" >
            AND t5.username   IN
            <foreach collection="repertoryMoveQueryVo.username" item="username" open="(" close=")" separator="," >
                #{username}
            </foreach>
        </if>
        <if test="repertoryMoveQueryVo.moveType !=null and repertoryMoveQueryVo.moveType.size() !=0" >
            AND T.move_type            IN
            <foreach collection="repertoryMoveQueryVo.moveType" item="moveType" open="(" close=")" separator="," >
                #{moveType}
            </foreach>
        </if>
        <if test="repertoryMoveQueryVo.materialCode !=null and repertoryMoveQueryVo.materialCode.size() !=0" >
            AND T.material_code            IN
            <foreach collection="repertoryMoveQueryVo.materialCode" item="materialCode" open="(" close=")" separator="," >
                #{materialCode}
            </foreach>
        </if>
        <if test="repertoryMoveQueryVo.factoryCodeShip !=null and repertoryMoveQueryVo.factoryCodeShip.size() !=0" >
            AND ( T.factory_code_ship IN
            <foreach collection="repertoryMoveQueryVo.factoryCodeShip" item="factoryCodeShip" open="(" close=")"
                     separator=",">
                #{factoryCodeShip}
            </foreach>
            OR T.factory_code_receive IN
            <foreach collection="repertoryMoveQueryVo.factoryCodeShip" item="factoryCodeShip" open="(" close=")"
                     separator=",">
                #{factoryCodeShip}
            </foreach>
            )
        </if>
        <if test="repertoryMoveQueryVo.stockPCodeShip !=null and repertoryMoveQueryVo.stockPCodeShip.size() !=0" >
            AND (T.stock_p_code_ship IN
            <foreach collection="repertoryMoveQueryVo.stockPCodeShip" item="stockPCodeShip" open="(" close=")"
                     separator=",">
                #{stockPCodeShip}
            </foreach>
            OR T.stock_p_code_receive IN
            <foreach collection="repertoryMoveQueryVo.stockPCodeShip" item="stockPCodeShip" open="(" close=")"
                     separator=",">
                #{stockPCodeShip}
            </foreach>
            )
        </if>
        <if test="repertoryMoveQueryVo.batchNumberShip !=null and repertoryMoveQueryVo.batchNumberShip.size() !=0" >
            AND (T.batch_number_ship IN
            <foreach collection="repertoryMoveQueryVo.batchNumberShip" item="batchNumberShip" open="(" close=")"
                     separator=",">
                #{batchNumberShip}
            </foreach>
            OR T.batch_number_receive IN
            <foreach collection="repertoryMoveQueryVo.batchNumberShip" item="batchNumberShip" open="(" close=")"
                     separator=",">
                #{batchNumberShip}
            </foreach>
            )
        </if>
        <if test="repertoryMoveQueryVo.pieceShip !=null and repertoryMoveQueryVo.pieceShip.size() !=0" >
            AND (T.piece_ship IN
            <foreach collection="repertoryMoveQueryVo.pieceShip" item="pieceShip" open="(" close=")" separator=",">
                #{pieceShip}
            </foreach>
            OR T.piece_receive IN
            <foreach collection="repertoryMoveQueryVo.pieceShip" item="pieceShip" open="(" close=")" separator=",">
                #{pieceShip}
            </foreach>
            )
        </if>
        <if test="repertoryMoveQueryVo.binNumShip !=null and repertoryMoveQueryVo.binNumShip.size() !=0" >
            AND ( T.bin_num_ship            IN
            <foreach collection="repertoryMoveQueryVo.binNumShip" item="binNumShip" open="(" close=")" separator="," >
                #{binNumShip}
            </foreach>
            OR T.bin_num_receive            IN
            <foreach collection="repertoryMoveQueryVo.binNumShip" item="binNumShip" open="(" close=")" separator="," >
                #{binNumShip}
            </foreach>
            )
        </if>
        <if test="repertoryMoveQueryVo.orderNum !=null and repertoryMoveQueryVo.orderNum.size() !=0" >
            AND T.order_num            IN
            <foreach collection="repertoryMoveQueryVo.orderNum" item="orderNum" open="(" close=")" separator="," >
                #{orderNum}
            </foreach>
        </if>
        <if test="repertoryMoveQueryVo.workOrderNum !=null and repertoryMoveQueryVo.workOrderNum.size() !=0" >
            AND T.work_order_num            IN
            <foreach collection="repertoryMoveQueryVo.workOrderNum" item="workOrderNum" open="(" close=")" separator="," >
                #{workOrderNum}
            </foreach>
        </if>
        <if test="repertoryMoveQueryVo.transferOrderNum !=null and repertoryMoveQueryVo.transferOrderNum.size() !=0" >
            AND T.transfer_order_num            IN
            <foreach collection="repertoryMoveQueryVo.transferOrderNum" item="transferOrderNum" open="(" close=")" separator="," >
                #{transferOrderNum}
            </foreach>
        </if>
        <if test="repertoryMoveQueryVo.pickReturnOrderNum !=null and repertoryMoveQueryVo.pickReturnOrderNum.size() !=0" >
            AND T.pick_return_order_num
            IN
            <foreach collection="repertoryMoveQueryVo.pickReturnOrderNum" item="pickReturnOrderNum" open="(" close=")" separator="," >
                #{pickReturnOrderNum}
            </foreach>
        </if>
        <if test="repertoryMoveQueryVo.scrapOrderNum !=null and repertoryMoveQueryVo.scrapOrderNum.size() !=0" >
            AND T.scrap_order_num
            IN
            <foreach collection="repertoryMoveQueryVo.scrapOrderNum" item="scrapOrderNum" open="(" close=")" separator="," >
                #{scrapOrderNum}
            </foreach>
        </if>
        <if test="repertoryMoveQueryVo.shipRequestOrderNum !=null and repertoryMoveQueryVo.shipRequestOrderNum.size() !=0" >
            AND T.ship_request_order_num
            IN
            <foreach collection="repertoryMoveQueryVo.shipRequestOrderNum" item="shipRequestOrderNum" open="(" close=")" separator="," >
                #{shipRequestOrderNum}
            </foreach>
        </if>
        ORDER BY t5.voucher_num,t5.voucher_vintage
    </select>

</mapper>

