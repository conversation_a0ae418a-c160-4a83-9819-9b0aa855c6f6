<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.fscm.mapper.WorkOrderHeadMapper">

        <select id="queryData" resultType="com.datalink.fdop.project.api.model.vo.OrderQueryShowVo">
            SELECT
            t3.order_num,
            t3.statu,
            t3.order_type,
            t3.order_type_desc,
            t3.del_flag,
            t3.update_date,
            t3.create_by,
            t3.supplier_code,
            t9.supplier_name,
            t3.purchase_code,
            t7.purchase_description,
            t3.company_code,
            t4.company_description,
            t3.order_row_num,
            t3.row_del_flag,
            t3.factory,
            t3.stock_p_code,
            t8.stock_p_description,
            t6.material_code,
            t6.material_desc,
            t3.order_quantity_receive,
            t3.order_unit,
            t3.basic_unit,
            t3.order_unit_num,
            t3.basic_unit_num,
            t3.delivery_control,
            t3.excessive_tolerance,
            t3.deficiency_tolerance,
            t3.project_type,
            t3.project_type_desc,
            t3.delivery_date,
            t3.batch_number,
            t3.return_flag,
            t3.comment,
            t3.update_time,
            t3.bom_versions,
            t3.status
            FROM
            (
            SELECT
            t1.order_num,
            t2.material_code,
            t1.order_type_desc,
            t1.company_code,
            t2.factory,
            t1.purchase_code,
            t1.del_flag,
            t1.order_type,
            t1.update_date,
            t1.create_by,
            t1.supplier_code,
            t2.order_row_num,
            t2.del_flag row_del_flag,
            t2.stock_p_code,
            t2.order_quantity_receive,
            t2.order_unit,
            t2.basic_unit,
            t2.order_unit_num,
            t2.basic_unit_num,
            t2.delivery_control,
            t2.excessive_tolerance,
            t2.project_type,
            t2.project_type_desc,
            t2.delivery_date,
            t2.batch_number,
            t2.return_flag ,
            t1.statu,
            t1.comment,
            t2.deficiency_tolerance,
            t1.update_time,
            t2.bom_versions,
            t2.status
            FROM
            zjdata.p_d_order_row t2
            LEFT JOIN zjdata.p_d_order_head t1 ON t1.order_num = t2.order_num
            ) t3
            LEFT JOIN zjdata.org_company t4 ON t3.company_code = t4.company_code
            --LEFT JOIN zjdata.org_factory t5 ON t3.factory = t5.factory_code
            LEFT JOIN zjdata.p_d_material t6 ON t3.material_code = t6.material_code
            LEFT JOIN zjdata.org_purchase t7 ON t3.purchase_code = t7.purchase_code
            LEFT JOIN zjdata.org_stock_place t8 ON t3.stock_p_code = t8.stock_p_code
            LEFT JOIN zjdata.p_d_supplier t9 ON t3.supplier_code = t9.supplier_code
            WHERE
            1 =1
                    <if test="object.orderNum !=null and object.orderNum.size() !=0" >
                        AND t3.order_num IN
                        <foreach collection="object.orderNum" item="orderNum" open="(" close=")" separator="," >
                            #{orderNum}
                        </foreach>
                    </if>
                    <if test="object.statu!=null and object.statu.size() !=0" >
                        AND t3.statu IN
                        <foreach collection="object.statu" item="statu" open="(" close=")" separator="," >
                            #{statu}
                        </foreach>
                    </if>
                    <if test="object.orderType!=null and object.orderType.size() !=0" >
                        AND t3.order_type IN
                        <foreach collection="object.orderType" item="orderType" open="(" close=")" separator="," >
                            #{orderType}
                        </foreach>
                    </if>
                    <if test="object.createBy!=null and object.createBy.size() !=0" >
                        AND t3.create_by
                        LIKE concat('%', #{object.createBy[0]}, '%')
                    </if>
                    <if test="object.purchaseCode!=null and object.purchaseCode.size() !=0" >
                        AND t3.purchase_code IN
                        <foreach collection="object.purchaseCode" item="purchaseCode" open="(" close=")" separator="," >
                            #{purchaseCode}
                        </foreach>
                    </if>
                    <if test="object.companyCode!=null and object.companyCode.size() !=0" >
                        AND t4.company_code IN
                        <foreach collection="object.companyCode" item="companyCode" open="(" close=")" separator="," >
                            #{companyCode}
                        </foreach>
                    </if>
                    <if test="object.materialCode!=null and object.materialCode.size() !=0" >
                        AND t3.material_code IN
                        <foreach collection="object.materialCode" item="materialCode" open="(" close=")" separator="," >
                            #{materialCode}
                        </foreach>
                    </if>
                    <if test="object.supplierCode!=null and object.supplierCode.size() !=0" >
                        AND t9.supplier_code LIKE concat('%', #{object.supplierCode[0]}, '%')
                    </if>
                    <if test="object.supplierName!=null and object.supplierName.size() !=0" >
                        AND t9.supplier_name LIKE concat('%', #{object.supplierName[0]}, '%')
                    </if>
                    <if test="object.materialDesc!=null and object.materialDesc.size() !=0" >
                        AND t6.material_desc LIKE concat('%', #{object.materialDesc[0]}, '%')
                    </if>
                    <if test="flag!=null and flag!='' ">
                        and t3.row_del_flag=#{flag}
                    </if>
                order by t3.order_num ,t3.order_row_num
        </select>
        <select id="queryWorkOrderData" resultType="com.datalink.fdop.fscm.api.model.vo.WorkOrderQueryShowVo">
            SELECT
                t3.work_order_num,
                t3.statu,
                t3.work_order_type,
                t3.username,
                t3.purchase_linkman,
                t3.purchase_phone,
                t3.purchase_email,
                t3.paper_date,
                t3.supplier_code,
                t9.supplier_name,
                t3.manuf_code,
                t3.manuf_type,
                t4.manuf_name,
                t3.purchase_code,
                t7.purchase_description,
                t3.factory_code,
                t3.process,
                t3.rework,
                t3.produce_statu,
                t3.head_note,
                t3.work_order_row_num,
                t3.order_num,
                t3.order_row_num,
                t3.del_flag,
                t3.material_code,
                t3.material_desc,
                t3.stock_p_code,
                t3.stock_p_description,
                t3.quantity_receive,
                t3.basic_unit,
                t3.package_way,
                t3.mark,
                t3.delivery_limit,
                t3.excessive_delivery,
                t3.insufficient_delivery,
                t3.delivery_date,
                t3.encapsulation_versions,
                t3.wire_rod,
                t3.bd_graph,
                t3.bd_graph_versions,
                t3.wire_size,
                t3."number",
                t3.slices_way,
                t3.dedicated,
                t3.sticker_way,
                t3.encapsulation_mode,
                t3.test_routines,
                t3.bom_versions,
                t3.status,
                t3.batch_number,
                t3.row_note,
                t3."temporary",
                t3.del_flag_head,
                t3.is_close
            FROM
                (
                SELECT
                    t1."temporary",
                    t1.work_order_num,
                    t1.statu,
                    t1.work_order_type,
                    t1.username,
                    t1.purchase_linkman,
                    t1.purchase_phone,
                    t1.purchase_email,
                    t1.paper_date,
                    t1.supplier_code,
                    t1.manuf_code,
                    t1.manuf_type,
                    t1.purchase_code,
                    t1.factory_code,
                    t1.process,
                    t1.rework,
                    t1.produce_statu,
                    t1.head_note,
                    t1.del_flag as del_flag_head,
                    t2.work_order_row_num,
                    t2.order_num,
                    t2.order_row_num,
                    t2.del_flag,
                    t2.material_code,
                    t2.material_desc,
                    t2.stock_p_code,
                    t2.stock_p_description,
                    t2.quantity_receive,
                    t2.basic_unit,
                    t2.package_way,
                    t2.mark,
                    t2.delivery_limit,
                    t2.excessive_delivery,
                    t2.insufficient_delivery,
                    t2.delivery_date,
                    t2.encapsulation_versions,
                    t2.wire_rod,
                    t2.bd_graph,
                    t2.bd_graph_versions,
                    t2.wire_size,
                    t2."number",
                    t2.slices_way,
                    t2.dedicated,
                    t2.sticker_way,
                    t2.encapsulation_mode,
                    t2.test_routines,
                    t2.bom_versions,
                    t2.status,
                    t2.batch_number,
                    t2.row_note,
                    t2.is_close
                FROM
                    zjdata.f_d_work_order_row t2
                    RIGHT JOIN zjdata.f_d_work_order_head t1 ON t1.work_order_num = t2.work_order_num
                ) t3
                LEFT JOIN zjdata.p_d_manufacturer t4 ON t3.manuf_code = t4.manuf_code
                AND t3.manuf_type = t4.manuf_type
                LEFT JOIN zjdata.p_d_material t6 ON t3.material_code = t6.material_code
                LEFT JOIN zjdata.org_purchase t7 ON t3.purchase_code = t7.purchase_code
                LEFT JOIN zjdata.org_stock_place t8 ON t3.stock_p_code = t8.stock_p_code
                LEFT JOIN zjdata.p_d_supplier t9 ON t3.supplier_code = t9.supplier_code
            WHERE
            1 =1
            <if test="workOrderQueryVo.workOrderNum !=null and workOrderQueryVo.workOrderNum.size() !=0" >
                AND t3.work_order_num  IN
                <foreach collection="workOrderQueryVo.workOrderNum" item="workOrderNum" open="(" close=")" separator="," >
                    #{workOrderNum}
                </foreach>
            </if>
            <if test="workOrderQueryVo.statu !=null and workOrderQueryVo.statu.size() !=0" >
                AND t3.statu  IN
                <foreach collection="workOrderQueryVo.statu" item="statu" open="(" close=")" separator="," >
                    #{statu}
                </foreach>
            </if>
            <if test="workOrderQueryVo.workOrderType !=null and workOrderQueryVo.workOrderType.size() !=0" >
                AND t3.work_order_type
                IN
                <foreach collection="workOrderQueryVo.workOrderType" item="workOrderType" open="(" close=")" separator="," >
                    #{workOrderType}
                </foreach>
            </if>
            <if test="workOrderQueryVo.username!=null and workOrderQueryVo.username.size() !=0" >
                AND t3.username LIKE concat('%', #{workOrderQueryVo.username[0]}, '%')
            </if>
            <if test="workOrderQueryVo.supplierCode !=null and workOrderQueryVo.supplierCode.size() !=0" >
                AND t3.supplier_code  IN
                <foreach collection="workOrderQueryVo.supplierCode" item="supplierCode" open="(" close=")" separator="," >
                    #{supplierCode}
                </foreach>
            </if>
            <if test="workOrderQueryVo.supplierName!=null and workOrderQueryVo.supplierName.size() !=0" >
                AND t9.supplier_name LIKE concat('%', #{workOrderQueryVo.supplierName[0]}, '%')
            </if>
            <if test="workOrderQueryVo.manufCode !=null and workOrderQueryVo.manufCode.size() !=0" >
                AND t3.manuf_code                IN
                <foreach collection="workOrderQueryVo.manufCode" item="manufCode" open="(" close=")" separator="," >
                    #{manufCode}
                </foreach>
            </if>
            <if test="workOrderQueryVo.manufType !=null and workOrderQueryVo.manufType.size() !=0" >
                AND t3.manuf_type
                IN
                <foreach collection="workOrderQueryVo.manufType" item="manufType" open="(" close=")" separator="," >
                    #{manufType}
                </foreach>
            </if>
            <if test="workOrderQueryVo.manufName!=null and workOrderQueryVo.manufName.size() !=0" >
                AND t4.manuf_name LIKE concat('%', #{workOrderQueryVo.manufName[0]}, '%')
            </if>
            <if test="workOrderQueryVo.process !=null and workOrderQueryVo.process.size() !=0" >
                AND t3.process
                IN
                <foreach collection="workOrderQueryVo.process" item="process" open="(" close=")" separator="," >
                    #{process}
                </foreach>
            </if>
            <if test="workOrderQueryVo.rework !=null and workOrderQueryVo.rework.size() !=0" >
                AND t3.rework
                IN
                <foreach collection="workOrderQueryVo.rework" item="rework" open="(" close=")" separator="," >
                    #{rework}
                </foreach>
            </if>
            <if test="workOrderQueryVo.produceStatu !=null and workOrderQueryVo.produceStatu.size() !=0" >
                AND t3.produce_statu
                IN
                <foreach collection="workOrderQueryVo.produceStatu" item="produceStatu" open="(" close=")" separator="," >
                    #{produceStatu}
                </foreach>
            </if>
            <if test="workOrderQueryVo.materialCode !=null and workOrderQueryVo.materialCode.size() !=0" >
                AND t3.material_code
                IN
                <foreach collection="workOrderQueryVo.materialCode" item="materialCode" open="(" close=")" separator="," >
                    #{materialCode}
                </foreach>
            </if>
            <if test="workOrderQueryVo.materialDesc !=null and workOrderQueryVo.materialDesc.size() !=0" >
                AND t3.material_desc
                LIKE concat('%', #{workOrderQueryVo.materialDesc[0]}, '%')
            </if>
            <if test="delFlag !=null and delFlag !=''">
                and t3.del_flag=#{delFlag}
            </if>
            <if test="temporary !=null and temporary !=''">
                and t3."temporary"=#{temporary}
            </if>
            order by t3.work_order_num ,t3.work_order_row_num
        </select>

</mapper>

