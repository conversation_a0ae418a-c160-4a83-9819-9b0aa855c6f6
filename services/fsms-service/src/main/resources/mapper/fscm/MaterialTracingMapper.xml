<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.fscm.mapper.MaterialTracingMapper">

    <select id="queryData" resultType="com.datalink.fdop.fscm.api.model.vo.MaterialTracingShowVo">
        SELECT
        t2.*,
        t3.bin_num_receive output_bin_num,
        t3.batch_number_receive output_batch_number,
        t3.piece_receive output_piece,
        t4.ship_request_order_num,
        t4.ship_request_order_row_num,
        t1.material_code output_material_code,
        t5.supplier_code,
        t6.supplier_name,
        t7.manuf_code,
        t7.manuf_type,
        t8.manuf_name,
        t9.is_master_chip,
        t10.material_desc output_material_desc,
        t11.sale_to,
        t11.ship_to
        FROM
        (
        SELECT T
        .material_code use_material_code,
        T.material_desc use_material_desc,
        T.batch_number use_batch_number,
        T.piece use_piece,
        T.bin_num use_bin_num,
        T.work_order_num,
        T.work_order_row_num,
        T.work_order_children_num
        FROM
        zjdata.f_d_work_order_store_issue T
        WHERE
        1 = 1
        <if test="materialTracingQueryVo.materialCode !=null and materialTracingQueryVo.materialCode.size() !=0" >
            AND T.material_code
            IN
            <foreach collection="materialTracingQueryVo.materialCode" item="materialCode" open="(" close=")" separator="," >
                #{materialCode}
            </foreach>
        </if>
        <if test="materialTracingQueryVo.materialDesc !=null and materialTracingQueryVo.materialDesc.size() !=0" >
            AND T.material_desc LIKE concat('%', #{materialTracingQueryVo.materialDesc[0]}, '%')
        </if>
        <if test="materialTracingQueryVo.batchNumber !=null and materialTracingQueryVo.batchNumber.size() !=0" >
            AND T.batch_number
            IN
            <foreach collection="materialTracingQueryVo.batchNumber" item="batchNumber" open="(" close=")" separator="," >
                #{batchNumber}
            </foreach>
        </if>
        <if test="materialTracingQueryVo.piece !=null and materialTracingQueryVo.piece.size() !=0" >
            AND T.piece
            IN
            <foreach collection="materialTracingQueryVo.piece" item="piece" open="(" close=")" separator="," >
                #{piece}
            </foreach>
        </if>
        <if test="materialTracingQueryVo.binNum !=null and materialTracingQueryVo.binNum.size() !=0" >
            AND T.bin_num
            IN
            <foreach collection="materialTracingQueryVo.binNum" item="binNum" open="(" close=")" separator="," >
                #{binNum}
            </foreach>
        </if>
        and    work_order_num IN (

            SELECT work_order_num FROM zjdata.f_d_work_order_head t12 where 1=1
            <if test="materialTracingQueryVo.purchaseCode !=null and materialTracingQueryVo.purchaseCode.size() !=0" >
                AND t12.purchase_code
                IN
                <foreach collection="materialTracingQueryVo.purchaseCode" item="purchaseCode" open="(" close=")" separator="," >
                    #{purchaseCode}
                </foreach>
            </if>
            <if test="materialTracingQueryVo.factoryCode !=null and materialTracingQueryVo.factoryCode.size() !=0" >
                AND t12.factory_code
                IN
                <foreach collection="materialTracingQueryVo.factoryCode" item="factoryCode" open="(" close=")" separator="," >
                    #{factoryCode}
                </foreach>
            </if>
            )
        ) t2
        LEFT JOIN zjdata.f_d_work_order_row t1 ON t2.work_order_num = t1.work_order_num
        AND t2.work_order_row_num = t1.work_order_row_num
        LEFT JOIN zjdata.f_d_post_certificate_row t3 ON t2.work_order_num = t3.work_order_num
        AND t2.work_order_row_num = t3.work_order_row_num and t1.material_code=t3.material_code
        LEFT JOIN zjdata.f_d_ship_request_row t4 ON t1.material_code = t4.material_code
        AND t4.piece = t3.piece_receive
        AND t4.batch_number = t3.batch_number_receive
        AND t4.bin_num = t3.bin_num_receive
        LEFT JOIN zjdata.f_d_work_order_head t5 on t5.work_order_num=t2.work_order_num
        LEFT JOIN zjdata.p_d_supplier t6 on t5.supplier_code=t6.supplier_code
        LEFT JOIN zjdata.f_d_work_order_head t7 ON t7.work_order_num=t5.work_order_num
        LEFT JOIN zjdata.p_d_manufacturer t8 on t7.manuf_code=t8.manuf_code and t7.manuf_type=t8.manuf_type
        LEFT JOIN zjdata.f_d_work_order_child t9  ON t2.work_order_num= t9.work_order_num and t2.work_order_row_num =t9.work_order_row_num and t2.work_order_children_num =t9.work_order_children_num
        LEFT JOIN zjdata.p_d_material t10 on t10.material_code=t1.material_code
        LEFT JOIN zjdata.f_d_ship_request_head  t11 ON  t4.ship_request_order_num=t11.ship_request_order_num
        where t3.write_off='0'
        ORDER BY
        t2.use_material_code ,
        t2.use_batch_number ,
        t2.use_piece ,
        t2.use_bin_num,
        t2.work_order_num,
        t2.work_order_row_num,
        t2.work_order_children_num
        ,t3.bin_num_receive
        ,t3.batch_number_receive
        ,t3.piece_receive
    </select>

    <select id="findData" resultType="com.datalink.fdop.fscm.api.model.vo.MaterialTracingShowVo">
        SELECT
            t2.*,
            t3.bin_num_receive output_bin_num,
            t3.batch_number_receive output_batch_number,
            t3.piece_receive output_piece,
            t4.ship_request_order_num,
            t4.ship_request_order_row_num,
            t1.material_code output_material_code,
            t5.supplier_code,
            t6.supplier_name,
            t7.manuf_code,
            t7.manuf_type,
            t8.manuf_name,
            t9.is_master_chip,
            t10.material_desc output_material_desc,
            t11.sale_to,
            t11.ship_to
            FROM
                (
                SELECT T
                    .material_code use_material_code,
                    T.material_desc use_material_desc,
                    T.batch_number use_batch_number,
                    T.piece use_piece,
                    T.bin_num use_bin_num,
                    T.work_order_num,
                    T.work_order_row_num,
                    T.work_order_children_num
                FROM
                    zjdata.f_d_work_order_store_issue T
                WHERE
                    1 = 1
                    <if test="useMaterialCode ==null or useMaterialCode==''">
                        AND t.material_code ='empty'
                    </if>
                    <if test="useMaterialCode !=null and useMaterialCode!=''">
                        AND t.material_code=#{useMaterialCode}
                    </if>
                    <if test="tracingType.equals('2'.toString())">
                        <if test="usePiece ==null or usePiece==''">
                            AND t.piece ='empty'
                        </if>
                        <if test="usePiece !=null and usePiece!=''">
                            AND t.piece=#{usePiece}
                        </if>
                        <if test="useBinNum ==null or useBinNum==''">
                            AND t.bin_num ='empty'
                        </if>
                        <if test="useBinNum !=null and useBinNum!=''">
                            AND t.bin_num=#{useBinNum}
                        </if>
                    </if>
                    <if test="useBatchNumber ==null or useBatchNumber==''">
                        AND t.batch_number ='empty'
                    </if>
                    <if test="useBatchNumber !=null and useBatchNumber!=''">
                        AND t.batch_number=#{useBatchNumber}
                    </if>

                ) t2
                LEFT JOIN zjdata.f_d_work_order_row t1 ON t2.work_order_num = t1.work_order_num
                AND t2.work_order_row_num = t1.work_order_row_num
                LEFT JOIN zjdata.p_d_material t10 ON t10.material_code = t1.material_code
                LEFT JOIN zjdata.f_d_post_certificate_row t3 ON t2.work_order_num = t3.work_order_num
                AND t2.work_order_row_num = t3.work_order_row_num and t3.material_code != t2.use_material_code
                LEFT JOIN zjdata.f_d_ship_request_row t4 ON t2.use_material_code = t10.material_code
                AND t4.piece = t3.piece_receive
                AND t4.batch_number = t3.batch_number_receive
                AND t4.bin_num = t3.bin_num_receive
                LEFT JOIN zjdata.f_d_work_order_head t5 ON t5.work_order_num = t2.work_order_num
                LEFT JOIN zjdata.p_d_supplier t6 ON t5.supplier_code = t6.supplier_code
                LEFT JOIN zjdata.f_d_work_order_head t7 ON t7.work_order_num = t5.work_order_num
                LEFT JOIN zjdata.p_d_manufacturer t8 ON t7.manuf_code = t8.manuf_code
                AND t7.manuf_type = t8.manuf_type
                LEFT JOIN zjdata.f_d_work_order_child t9 ON t2.work_order_num = t9.work_order_num
                AND t2.work_order_row_num = t9.work_order_row_num
                AND t2.work_order_children_num = t9.work_order_children_num
                LEFT JOIN zjdata.f_d_ship_request_head t11 ON t4.ship_request_order_num = t11.ship_request_order_num
                where t3.write_off='0'
        ORDER BY
        t2.use_material_code ,
        t2.use_batch_number ,
        t2.use_piece ,
        t2.use_bin_num,
        t2.work_order_num,
        t2.work_order_row_num,
        t2.work_order_children_num
        ,t3.bin_num_receive
        ,t3.batch_number_receive
        ,t3.piece_receive
    </select>

</mapper>

