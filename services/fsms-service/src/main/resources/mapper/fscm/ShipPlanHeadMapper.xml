<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.fscm.mapper.ShipPlanHeadMapper">

    <select id="queryData" resultType="com.datalink.fdop.fscm.api.model.vo.ShipPlanShowVo">
            SELECT
                t3.*,
                t4.material_desc,
                t5.stock_p_description
            FROM
                (
                SELECT
                    t1.sale_order_num,
                    t1.customer_purchase_order_num,
                    t1.sale_to,
                    t1.ship_to,
                    t1.ship_to_addr,
                    t1.ship_to_linkman,
                    t1.ship_to_phone,
                    t1.incoterms,
                    t1.incoterms_position,
                    t1.COMMENT,
                    t1.statu,
                    t1.del_flag as del_flag_head,
                    t1.create_by,
                    t1.update_date,
                    t1.update_time,
                    t2.*
                FROM
                    zjdata.f_d_ship_plan_head t1,
                    zjdata.f_d_ship_plan_row t2
                WHERE
                    t1.ship_plan_order_num = t2.ship_plan_order_num
                ) t3
                LEFT JOIN zjdata.p_d_material t4 ON t3.material_code = t4.material_code
                LEFT JOIN zjdata.org_stock_place t5 ON t3.stock_p_code=t5.stock_p_code
                where 1=1
        <if test="shipPlanQueryVo.shipPlanOrderNum !=null and shipPlanQueryVo.shipPlanOrderNum.size() !=0" >
            AND t3.ship_plan_order_num  IN
            <foreach collection="shipPlanQueryVo.shipPlanOrderNum" item="shipPlanOrderNum" open="(" close=")" separator="," >
                #{shipPlanOrderNum}
            </foreach>
        </if>
        <if test="shipPlanQueryVo.saleOrderNum !=null and shipPlanQueryVo.saleOrderNum.size() !=0" >
            AND t3.sale_order_num  IN
            <foreach collection="shipPlanQueryVo.saleOrderNum" item="saleOrderNum" open="(" close=")" separator="," >
                #{saleOrderNum}
            </foreach>
        </if>
        <if test="shipPlanQueryVo.customerPurchaseOrderNum !=null and shipPlanQueryVo.customerPurchaseOrderNum.size() !=0" >
            AND t3.customer_purchase_order_num            IN
            <foreach collection="shipPlanQueryVo.customerPurchaseOrderNum" item="customerPurchaseOrderNum" open="(" close=")" separator="," >
                #{customerPurchaseOrderNum}
            </foreach>
        </if>
        <if test="shipPlanQueryVo.saleTo !=null and shipPlanQueryVo.saleTo.size() !=0" >
            AND t3.sale_to LIKE concat('%', #{shipPlanQueryVo.saleTo[0]}, '%')
        </if>
        <if test="shipPlanQueryVo.shipTo !=null and shipPlanQueryVo.shipTo.size() !=0" >
            AND t3.ship_to            LIKE concat('%', #{shipPlanQueryVo.shipTo[0]}, '%')
        </if>
        <if test="shipPlanQueryVo.statu !=null and shipPlanQueryVo.statu.size() !=0" >
            AND t3.statu  IN
            <foreach collection="shipPlanQueryVo.statu" item="statu" open="(" close=")" separator="," >
                #{statu}
            </foreach>
        </if>
        <if test="shipPlanQueryVo.createBy !=null and shipPlanQueryVo.createBy.size() !=0" >
            AND t3.create_by    LIKE concat('%', #{shipPlanQueryVo.createBy[0]}, '%')
        </if>
        <if test="shipPlanQueryVo.isReturn !=null and shipPlanQueryVo.isReturn.size() !=0" >
            AND t3.is_return            IN
            <foreach collection="shipPlanQueryVo.isReturn" item="isReturn" open="(" close=")" separator="," >
                #{isReturn}
            </foreach>
        </if>
        <if test="shipPlanQueryVo.materialCode !=null and shipPlanQueryVo.materialCode.size() !=0" >
            AND t3.material_code            IN
            <foreach collection="shipPlanQueryVo.materialCode" item="materialCode" open="(" close=")" separator="," >
                #{materialCode}
            </foreach>
        </if>
        <if test="shipPlanQueryVo.materialDesc !=null and shipPlanQueryVo.materialDesc.size() !=0" >
            AND t4.material_desc            LIKE concat('%', #{shipPlanQueryVo.materialDesc[0]}, '%')
        </if>
        <if test="shipPlanQueryVo.deviceName !=null and shipPlanQueryVo.deviceName.size() !=0" >
            AND t3.device_name            LIKE concat('%', #{shipPlanQueryVo.deviceName[0]}, '%')
        </if>
        <if test="delFlag!=null and delFlag!=''">
            and t3.del_flag_head=#{delFlag}
            and t3.del_flag=#{delFlag}
        </if>

    </select>
</mapper>

