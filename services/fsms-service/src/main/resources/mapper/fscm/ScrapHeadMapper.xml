<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.fscm.mapper.ScrapHeadMapper">
    <select id="queryData" resultType="com.datalink.fdop.fscm.api.model.vo.ScrapHeadShowVo">
        SELECT
            t3.*,
            t4.material_desc,
            t5.stock_p_description
        FROM
            (
            SELECT
                t1.scrap_order_num,
                t1.statu,
                t1.temporary,
                t1.username,
                t1.paper_date,
                t1.rise_text,
                t2.scrap_order_row_num,
                t2.del_flag,
                t2.material_code,
                t2.factory_code,
                t2.stock_p_code,
                t2.batch_number,
                t2.piece,
                t2.bin_num,
                t2.reason,
                t2.scrap_quantity,
                t2.basic_unit
            FROM
                zjdata.f_d_scrap_head t1
                LEFT JOIN zjdata.f_d_scrap_row t2 ON t1.scrap_order_num = t2.scrap_order_num
            ) t3
            LEFT JOIN zjdata.p_d_material t4 ON t3.material_code = t4.material_code
            LEFT JOIN zjdata.org_stock_place t5 ON t3.stock_p_code = t5.stock_p_code
        where 1=1
        <if test="scrapHeadQueryVo.scrapOrderNum !=null and scrapHeadQueryVo.scrapOrderNum.size() !=0" >
            AND t3.scrap_order_num
            IN
            <foreach collection="scrapHeadQueryVo.scrapOrderNum" item="scrapOrderNum" open="(" close=")" separator="," >
                #{scrapOrderNum}
            </foreach>
        </if>
        <if test="scrapHeadQueryVo.statu !=null and scrapHeadQueryVo.statu.size() !=0" >
            AND t3.statu
            IN
            <foreach collection="scrapHeadQueryVo.statu" item="statu" open="(" close=")" separator="," >
                #{statu}
            </foreach>
        </if>
        <if test="scrapHeadQueryVo.username !=null and scrapHeadQueryVo.username.size() !=0" >
            AND t3.username LIKE concat('%', #{scrapHeadQueryVo.username[0]}, '%')
        </if>
        <if test="scrapHeadQueryVo.paperDate !=null and scrapHeadQueryVo.paperDate.size() !=0" >
            AND t3.paper_date
            IN
            <foreach collection="scrapHeadQueryVo.paperDate" item="paperDate" open="(" close=")" separator="," >
                #{paperDate}
            </foreach>
        </if>
        <if test="scrapHeadQueryVo.materialCode !=null and scrapHeadQueryVo.materialCode.size() !=0" >
            AND t3.material_code
            IN
            <foreach collection="scrapHeadQueryVo.materialCode" item="materialCode" open="(" close=")" separator="," >
                #{materialCode}
            </foreach>
        </if>
        <if test="scrapHeadQueryVo.materialDesc !=null and scrapHeadQueryVo.materialDesc.size() !=0" >
            AND t4.material_desc LIKE concat('%', #{scrapHeadQueryVo.materialDesc[0]}, '%')
        </if>
        <if test="scrapHeadQueryVo.factoryCode !=null and scrapHeadQueryVo.factoryCode.size() !=0" >
            AND t3.factory_code
            IN
            <foreach collection="scrapHeadQueryVo.factoryCode" item="factoryCode" open="(" close=")" separator="," >
                #{factoryCode}
            </foreach>
        </if>
        <if test="scrapHeadQueryVo.stockPCode !=null and scrapHeadQueryVo.stockPCode.size() !=0" >
            AND t3.stock_p_code
            IN
            <foreach collection="scrapHeadQueryVo.stockPCode" item="stockPCode" open="(" close=")" separator="," >
                #{stockPCode}
            </foreach>
        </if>
        <if test="scrapHeadQueryVo.batchNumber !=null and scrapHeadQueryVo.batchNumber.size() !=0" >
            AND t3.batch_number
            IN
            <foreach collection="scrapHeadQueryVo.batchNumber" item="batchNumber" open="(" close=")" separator="," >
                #{batchNumber}
            </foreach>
        </if>
        <if test="scrapHeadQueryVo.piece !=null and scrapHeadQueryVo.piece.size() !=0" >
            AND t3.piece
            IN
            <foreach collection="scrapHeadQueryVo.piece" item="piece" open="(" close=")" separator="," >
                #{piece}
            </foreach>
        </if>
        <if test="scrapHeadQueryVo.binNum !=null and scrapHeadQueryVo.binNum.size() !=0" >
            AND t3.bin_num
            IN
            <foreach collection="scrapHeadQueryVo.binNum" item="binNum" open="(" close=")" separator="," >
                #{binNum}
            </foreach>
        </if>
        <if test="delFlag!=null and delFlag!=''">
            and t3.del_flag=#{delFlag}
        </if>
        <if test="temporary!=null and temporary!=''">
            and t3.temporary=#{temporary}
        </if>
        ORDER BY t3.scrap_order_num,t3.scrap_order_row_num
    </select>
</mapper>

