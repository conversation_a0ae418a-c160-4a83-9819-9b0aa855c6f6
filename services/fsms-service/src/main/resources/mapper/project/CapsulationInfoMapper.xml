<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.project.mapper.CapsulationInfoMapper">

    <select id="pageData" resultType="com.datalink.fdop.project.api.model.vo.CapsulationInfoShowVo">
            select
                DISTINCT t1.id,
                t1.material_code,
                t2.material_desc,
                t1.supplier_code,
                t4.supplier_name,
                t1.manuf_code,
                t1.manuf_type,
                t3.manuf_name,
                t1.versions,
                t1.statu,
                t1.wire_rod,
                t1.bd_graph,
                t1.bd_graph_versions,
                t1.wire_size,
                t1.number,
                t1.slices_way,
                t1.dedicated,
                t1.sticker_way,
                t1.encapsulation_mode
        from zjdata.p_d_capsulation_info t1
                     left join zjdata.p_d_material t2
            on t1.material_code =t2.material_code
            left join zjdata.p_d_manufacturer t3
            on t1.manuf_code =t3.manuf_code and t1.manuf_type=t3.manuf_type
            left join zjdata.p_d_supplier t4
            on t1.supplier_code=t4.supplier_code
            where 1=1
                <if test="infoVo.supplierCode !=null and infoVo.supplierCode.size() !=0">
                    and t1.supplier_code in
                    <foreach collection="infoVo.supplierCode" item="supplierCode" open="(" close=")" separator="," >
                        #{supplierCode}
                    </foreach>
                </if>
                <if test="infoVo.supplierName !=null and infoVo.supplierName.size() !=0">
                    and t4.supplier_name like concat('%', #{infoVo.supplierName[0]}, '%')
                </if>
                <if test="infoVo.manufCode !=null and infoVo.manufCode.size() !=0">
                    and t1.manuf_code in
                    <foreach collection="infoVo.manufCode" item="manufCode" open="(" close=")" separator="," >
                        #{manufCode}
                    </foreach>
                </if>
                <if test="infoVo.manufType !=null and infoVo.manufType.size() !=0">
                    and    t1.manuf_type in
                    <foreach collection="infoVo.manufType" item="manufType" open="(" close=")" separator="," >
                        #{manufType}
                    </foreach>
                </if>
                <if test="infoVo.manufName !=null and infoVo.manufName.size() !=0">
                    and    t3.manuf_name like concat('%', #{infoVo.manufName[0]}, '%')
                </if>
                <if test="infoVo.materialCode !=null and infoVo.materialCode.size() !=0">
                    and   t1.material_code in
                    <foreach collection="infoVo.materialCode" item="materialCode" open="(" close=")" separator="," >
                        #{materialCode}
                    </foreach>
                </if>
                <if test="infoVo.materialDesc !=null and infoVo.materialDesc.size() !=0">
                    and    t2.material_desc  like concat('%', #{infoVo.materialDesc[0]}, '%')
                </if>
                <if test="infoVo.wireRod !=null and infoVo.wireRod.size() !=0">
                    and    t1.wire_rod like concat('%', #{infoVo.wireRod[0]}, '%')
                </if>
                <if test="infoVo.bdGraph !=null and infoVo.bdGraph.size() !=0">
                    and    t1.bd_graph like concat('%', #{infoVo.bdGraph[0]}, '%')
                </if>
                <if test="infoVo.wireSize !=null and infoVo.wireSize.size() !=0">
                    and    t1.wire_size like concat('%', #{infoVo.wireSize[0]}, '%')
                </if>
                <if test="infoVo.versions !=null and infoVo.versions.size() !=0">
                    and    t1.versions in
                    <foreach collection="infoVo.versions" item="versions" open="(" close=")" separator="," >
                        #{versions}
                    </foreach>
                </if>
                <if test="infoVo.statu !=null and infoVo.statu.size() !=0">
                    and    t1.statu in
                    <foreach collection="infoVo.statu" item="statu" open="(" close=")" separator="," >
                        #{statu}
                    </foreach>
                </if>
                order by t1.material_code,t1.supplier_code,t1.manuf_code,t1.manuf_type,t1.versions
    </select>

</mapper>

