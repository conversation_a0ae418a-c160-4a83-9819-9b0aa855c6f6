<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.project.mapper.BomMapper">

        <select id="findBomHeads" resultType="java.lang.String">
            SELECT a.parent_material_code FROM zjdata."p_d_bom_head" a ,zjdata.p_d_material b where a.parent_material_code=b.material_code
            <if test="bomSearchVo.projectNumbers !=null and bomSearchVo.projectNumbers.size !=0">
                and b.project_number in
                <foreach collection="bomSearchVo.projectNumbers" item="projectNumber" separator="," open="(" close=")">
                    #{projectNumber}
                </foreach>
            </if>
            <if test="bomSearchVo.deviceNames !=null and bomSearchVo.deviceNames.size !=0">
             and b.device_name in
                <foreach collection="bomSearchVo.deviceNames" item="deviceName" separator="," open="(" close=")">
                    #{deviceName}
                </foreach>
            </if>
        </select>

</mapper>

