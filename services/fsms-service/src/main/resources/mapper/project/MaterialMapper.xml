<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.project.mapper.MaterialMapper">

    <select id="pageData" parameterType="com.datalink.fdop.project.api.model.vo.MaterialVo" resultType="com.datalink.fdop.project.api.domain.Material">
            select
                *
            from zjdata.p_d_material t1
            where 1=1
                <if test="material.materialCode !=null and material.materialCode.size() !=0">
                    and t1.material_code in
                    <foreach collection="material.materialCode" item="materialCode" open="(" close=")" separator="," >
                        #{materialCode}
                    </foreach>
                </if>
                <if test="material.materialType !=null and material.materialType.size() !=0">
                    and t1.material_type in
                    <foreach collection="material.materialType" item="materialType" open="(" close=")" separator="," >
                        #{materialType}
                    </foreach>
                </if>
                <if test="material.materialGroup !=null and material.materialGroup.size() !=0">
                    and t1.material_group in
                    <foreach collection="material.materialGroup" item="materialGroup" open="(" close=")" separator="," >
                        #{materialGroup}
                    </foreach>
                </if>
                <if test="material.unit !=null and material.unit.size() !=0">
                    and t1.unit in
                    <foreach collection="material.unit" item="unit" open="(" close=")" separator="," >
                        #{unit}
                    </foreach>
                </if>
                <if test="material.effectiveDate !=null and material.effectiveDate.size() !=0">
                    and t1.effective_date in
                    <foreach collection="material.effectiveDate" item="effectiveDate" open="(" close=")" separator="," >
                        #{effectiveDate}
                    </foreach>
                </if>
                <if test="material.materialStatu !=null and material.materialStatu.size() !=0">
                    and t1.material_statu in
                    <foreach collection="material.materialStatu" item="materialStatu" open="(" close=")" separator="," >
                        #{materialStatu}
                    </foreach>
                </if>
            order by t1.material_code
    </select>

</mapper>

