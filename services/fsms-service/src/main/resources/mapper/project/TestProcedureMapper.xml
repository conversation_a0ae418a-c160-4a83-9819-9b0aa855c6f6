<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.project.mapper.TestProcedureMapper">

    <select id="pageData" parameterType="com.datalink.fdop.project.api.model.vo.TestProcedureVo" resultType="com.datalink.fdop.project.api.domain.TestProcedure">
                SELECT
                    DISTINCT    t1.*,
                    t4.material_desc,
                    t2.supplier_name,
                    t3.manuf_name
                FROM
                zjdata.p_d_test_procedure t1
                LEFT JOIN zjdata.p_d_supplier t2 ON t1.supplier_code = t2.supplier_code
                LEFT JOIN zjdata.p_d_manufacturer t3 ON t3.manuf_code = t1.manuf_code and  t3.manuf_type = t1.manuf_type
                LEFT JOIN zjdata.p_d_material t4 ON t4.material_code = t1.material_code
                where 1=1
                <if test="testProcedure.supplierCode !=null and testProcedure.supplierCode.size() !=0">
                    and t1.supplier_code in
                    <foreach collection="testProcedure.supplierCode" item="supplierCode" open="(" close=")" separator="," >
                        #{supplierCode}
                    </foreach>
                </if>
                <if test="testProcedure.supplierName !=null and testProcedure.supplierName.size() !=0">
                    and t2.supplier_name LIKE concat('%', #{testProcedure.supplierName[0]}, '%')
                </if>
                <if test="testProcedure.manufCode !=null and testProcedure.manufCode.size() !=0">
                    and t1.manuf_code in
                    <foreach collection="testProcedure.manufCode" item="manufCode" open="(" close=")" separator="," >
                        #{manufCode}
                    </foreach>
                </if>
                <if test="testProcedure.manufType !=null and testProcedure.manufType.size() !=0">
                    and t1."manuf_type" in
                    <foreach collection="testProcedure.manufType" item="manufType" open="(" close=")" separator="," >
                        #{manufType}
                    </foreach>
                </if>
                <if test="testProcedure.manufName !=null and testProcedure.manufName.size() !=0">
                    and t3.manuf_name LIKE concat('%', #{testProcedure.manufName[0]}, '%')
                </if>
                <if test="testProcedure.materialCode !=null and testProcedure.materialCode.size() !=0">
                    and t1.material_code in
                    <foreach collection="testProcedure.materialCode" item="materialCode" open="(" close=")" separator="," >
                        #{materialCode}
                    </foreach>
                </if>
                <if test="testProcedure.materialDesc !=null and testProcedure.materialDesc.size() !=0">
                    and t4.material_desc LIKE concat('%', #{testProcedure.materialDesc[0]}, '%')
                </if>
                <if test="testProcedure.testRoutine !=null and testProcedure.testRoutine.size() !=0">
                    and t1.test_routine LIKE concat('%', #{testProcedure.testRoutine[0]}, '%')
                </if>
                <if test="testProcedure.testVersions !=null and testProcedure.testVersions.size() !=0">
                    and t1.test_versions in
                    <foreach collection="testProcedure.testVersions" item="testVersions" open="(" close=")" separator="," >
                        #{testVersions}
                    </foreach>
                </if>
                <if test="testProcedure.testStatu !=null and testProcedure.testStatu.size() !=0">
                    and t1.test_statu in
                    <foreach collection="testProcedure.testStatu" item="testStatu" open="(" close=")" separator="," >
                        #{testStatu}
                    </foreach>
                </if>
                order by t1.material_code, t1.supplier_code,t1.manuf_code,t1.manuf_type,t1.test_routine,t1.test_versions
    </select>

</mapper>

