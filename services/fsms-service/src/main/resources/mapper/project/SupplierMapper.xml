<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.project.mapper.SupplierMapper">

        <select id="queryData" parameterType="com.datalink.fdop.project.api.model.vo.SupplierVo" resultType="com.datalink.fdop.project.api.domain.Supplier">
                SELECT
                    DISTINCT t1.*
                FROM
                    zjdata.p_d_supplier t1
            left join
                zjdata.p_d_manufacturer t2
              on
                    t1."supplier_code" = t2."supplier_code"
               where 1=1
                    <if test="supplierVo.supplierCode !=null and supplierVo.supplierCode.size() !=0">
                            AND t1.supplier_code IN
                        <foreach collection="supplierVo.supplierCode" item="supplierCode" open="(" close=")" separator="," >
                            #{supplierCode}
                        </foreach>
                    </if>
                    <if test="supplierVo.supplierName !=null and supplierVo.supplierName.size() !=0">
                        AND t1.supplier_name LIKE concat('%', #{supplierVo.supplierName[0]}, '%')
                    </if>
                    <if test="supplierVo.supplierAbbreviation !=null and supplierVo.supplierAbbreviation.size() !=0">
                        AND t1.supplier_abbreviation LIKE concat('%', #{supplierVo.supplierAbbreviation[0]}, '%')
                    </if>
                    <if test="supplierVo.oneClassify !=null and supplierVo.oneClassify.size() !=0">
                        AND t1."one_classify" IN
                        <foreach collection="supplierVo.oneClassify" item="oneClassify" open="(" close=")" separator="," >
                            #{oneClassify}
                        </foreach>
                    </if>
                    <if test="supplierVo.twoClassify !=null and supplierVo.twoClassify.size() !=0">
                        AND t1."two_classify" IN
                        <foreach collection="supplierVo.twoClassify" item="twoClassify" open="(" close=")" separator="," >
                            #{twoClassify}
                        </foreach>
                    </if>
                    <if test="supplierVo.threeClassify !=null and supplierVo.threeClassify.size() !=0">
                        AND t1."three_classify" IN
                        <foreach collection="supplierVo.threeClassify" item="threeClassify" open="(" close=")" separator="," >
                            #{threeClassify}
                        </foreach>
                    </if>
                    <if test="supplierVo.manufCode !=null and supplierVo.manufCode.size() !=0">
                        AND t2."manuf_code" IN
                        <foreach collection="supplierVo.manufCode" item="manufCode" open="(" close=")" separator="," >
                            #{manufCode}
                        </foreach>
                    </if>
                    <if test="supplierVo.manufType !=null and supplierVo.manufType.size() !=0">
                        AND t2."manuf_type" IN
                        <foreach collection="supplierVo.manufType" item="manufType" open="(" close=")" separator="," >
                            #{manufType}
                        </foreach>
                    </if>
                    <if test="supplierVo.manufName !=null and supplierVo.manufName.size() !=0">
                        AND t2.manuf_name IN
                        <foreach collection="supplierVo.manufName" item="manufName" open="(" close=")" separator="," >
                            #{manufName}
                        </foreach>
                    </if>

                    order by t1.supplier_code
        </select>

    <select id="queryMData" resultType="com.datalink.fdop.project.api.domain.Manufacturer">
        SELECT
        DISTINCT t2.*
        FROM
        zjdata.p_d_supplier t1
        left join
        zjdata.p_d_manufacturer t2
        on
        t1."supplier_code" = t2."supplier_code"
        where 1=1 and t2.manuf_code is not null
        <if test="supplierVo.supplierCode !=null and supplierVo.supplierCode.size() !=0">
            AND t1.supplier_code IN
            <foreach collection="supplierVo.supplierCode" item="supplierCode" open="(" close=")" separator="," >
                #{supplierCode}
            </foreach>
        </if>
        <if test="supplierVo.supplierName !=null and supplierVo.supplierName.size() !=0">
            AND t1.supplier_name LIKE concat('%', #{supplierVo.supplierName[0]}, '%')
        </if>
        <if test="supplierVo.supplierAbbreviation !=null and supplierVo.supplierAbbreviation.size() !=0">
            AND t1.supplier_abbreviation LIKE concat('%', #{supplierVo.supplierAbbreviation[0]}, '%')
        </if>
        <if test="supplierVo.oneClassify !=null and supplierVo.oneClassify.size() !=0">
            AND t1."one_classify" IN
            <foreach collection="supplierVo.oneClassify" item="oneClassify" open="(" close=")" separator="," >
                #{oneClassify}
            </foreach>
        </if>
        <if test="supplierVo.twoClassify !=null and supplierVo.twoClassify.size() !=0">
            AND t1."two_classify" IN
            <foreach collection="supplierVo.twoClassify" item="twoClassify" open="(" close=")" separator="," >
                #{twoClassify}
            </foreach>
        </if>
        <if test="supplierVo.threeClassify !=null and supplierVo.threeClassify.size() !=0">
            AND t1."three_classify" IN
            <foreach collection="supplierVo.threeClassify" item="threeClassify" open="(" close=")" separator="," >
                #{threeClassify}
            </foreach>
        </if>
        <if test="supplierVo.manufCode !=null and supplierVo.manufCode.size() !=0">
            AND t2."manuf_code" IN
            <foreach collection="supplierVo.manufCode" item="manufCode" open="(" close=")" separator="," >
                #{manufCode}
            </foreach>
        </if>
        <if test="supplierVo.manufType !=null and supplierVo.manufType.size() !=0">
            AND t2."manuf_type" IN
            <foreach collection="supplierVo.manufType" item="manufType" open="(" close=")" separator="," >
                #{manufType}
            </foreach>
        </if>
        <if test="supplierVo.manufName !=null and supplierVo.manufName.size() !=0">
            AND t2.manuf_name IN
            <foreach collection="supplierVo.manufName" item="manufName" open="(" close=")" separator="," >
                #{manufName}
            </foreach>
        </if>
        order by t2.supplier_code
    </select>

</mapper>

