<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.project.mapper.BaseMaterialMapper">

    <select id="pageData" parameterType="com.datalink.fdop.project.api.model.vo.BaseMaterialVo" resultType="com.datalink.fdop.project.api.model.vo.BaseMaterialShowVo">
            SELECT
                t1.id,
                t1.supplier_code,
                t4.supplier_name,
                t1.manuf_code,
                t1."manuf_type",
                t2.manuf_name,
                t1.material_code,
                t3.material_desc,
                t1.supplier_material_code,
                t1.supplier_material_desc
            FROM
                zjdata.p_d_base_material t1
                LEFT JOIN zjdata.p_d_manufacturer t2 ON t1.manuf_code = t2.manuf_code and t1.manuf_type=t2.manuf_type
                LEFT JOIN zjdata.p_d_material t3 ON t1.material_code = t3.material_code
                LEFT JOIN zjdata.p_d_supplier t4 ON t1.supplier_code = t4.supplier_code
            where 1=1
                <if test="base.supplierCode !=null and base.supplierCode.size() !=0" >
                    and t1.supplier_code in
                    <foreach collection="base.supplierCode" item="supplierCode" open="(" close=")" separator="," >
                        #{supplierCode}
                    </foreach>
                </if>
                <if test="base.supplierName !=null and base.supplierName.size() !=0" >
                    and t4.supplier_name like concat('%', #{base.supplierName[0]}, '%')
                </if>
                <if test="base.manufCode !=null and base.manufCode.size() !=0" >
                    and t1.manuf_code in
                    <foreach collection="base.manufCode" item="manufCode" open="(" close=")" separator="," >
                        #{manufCode}
                    </foreach>
                </if>
                <if test="base.manufType !=null and base.manufType.size() !=0" >
                    and t1."manuf_type" in
                    <foreach collection="base.manufType" item="manufType" open="(" close=")" separator="," >
                        #{manufType}
                    </foreach>
                </if>
                <if test="base.manufName !=null and base.manufName.size() !=0" >
                    and t2.manuf_name like concat('%', #{base.manufName[0]}, '%')
                </if>
                <if test="base.materialCode !=null and base.materialCode.size() !=0" >
                    and t1."material_code" in
                    <foreach collection="base.materialCode" item="materialCode" open="(" close=")" separator="," >
                        #{materialCode}
                    </foreach>
                </if>
                <if test="base.materialDesc !=null and base.materialDesc.size() !=0" >
                    and t3.material_desc like concat('%', #{base.materialDesc[0]}, '%')
                </if>
                group by
                    t1."id",
                    t1.supplier_code,
                    t4.supplier_name,
                    t1.manuf_code,
                    t1."manuf_type",
                    t2.manuf_name,
                    t1.material_code,
                    t3.material_desc,
                    t1.supplier_material_code,
                    t1.supplier_material_desc
            ORDER BY t1.manuf_code,t1.manuf_type
    </select>

</mapper>

