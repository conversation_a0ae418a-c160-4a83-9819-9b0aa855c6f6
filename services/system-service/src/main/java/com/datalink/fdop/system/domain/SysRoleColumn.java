package com.datalink.fdop.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.datalink.fdop.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 角色权限列关联表 sys_role_column
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_role_column")
@ApiModel("角色权限列")
public class SysRoleColumn extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键ID")
    private Long id;

    /**
     * 角色ID
     */
    @ApiModelProperty("角色ID")
    private Long roleId;

    /**
     * 模块名称
     */
    @ApiModelProperty("模块名称")
    private String moduleName;

    /**
     * 列名
     */
    @ApiModelProperty("列名")
    private String columnName;

    /**
     * 列描述
     */
    @ApiModelProperty("列描述")
    private String columnDesc;

    /**
     * 是否显示
     */
    @ApiModelProperty("是否显示")
    private Boolean isVisible;

}
