package com.datalink.fdop.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datalink.fdop.common.core.annotation.PermissionColumn;
import com.datalink.fdop.common.core.utils.PageUtils;
import com.datalink.fdop.common.core.utils.StringUtils;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.security.utils.SecurityUtils;
import com.datalink.fdop.system.domain.SysRoleColumn;

import com.datalink.fdop.system.mapper.SysRoleColumnMapper;
import com.datalink.fdop.system.service.ISysRoleColumnService;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.core.type.classreading.CachingMetadataReaderFactory;
import org.springframework.core.type.classreading.MetadataReader;
import org.springframework.core.type.classreading.MetadataReaderFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 角色权限列Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SysRoleColumnServiceImpl implements ISysRoleColumnService {

    @Autowired
    private SysRoleColumnMapper roleColumnMapper;

    /**
     * 扫描指定模块的所有实体列
     */
    @Override
    public List<SysRoleColumn> scanEntityColumns(String module) {
        List<SysRoleColumn> columnInfos = new ArrayList<>();
        
        try {
            // 根据模块名确定扫描包路径
            String packagePath = getPackagePathByModule(module);
            
            ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            MetadataReaderFactory metadataReaderFactory = new CachingMetadataReaderFactory();
            
            String pattern = "classpath*:" + packagePath.replace(".", "/") + "/**/*.class";
            Resource[] resources = resolver.getResources(pattern);
            
            for (Resource resource : resources) {
                try {
                    MetadataReader metadataReader = metadataReaderFactory.getMetadataReader(resource);
                    String className = metadataReader.getClassMetadata().getClassName();
                    
                    Class<?> clazz = Class.forName(className);
                    
                    // 检查是否有PermissionColumn注解
                    PermissionColumn annotation = clazz.getAnnotation(PermissionColumn.class);
                    if (annotation != null && module.equals(annotation.module())) {
                        // 获取所有字段
                        Field[] fields = clazz.getDeclaredFields();
                        for (Field field : fields) {
                            // 跳过静态字段和常量
                            if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) ||
                                java.lang.reflect.Modifier.isFinal(field.getModifiers())) {
                                continue;
                            }
                            
                            String columnName = field.getName();
                            String columnDesc = columnName;

                            // 获取ApiModelProperty注解中的描述
                            ApiModelProperty apiProperty = field.getAnnotation(ApiModelProperty.class);
                            if (apiProperty != null && StringUtils.isNotEmpty(apiProperty.value())) {
                                columnDesc = apiProperty.value();
                            }
                            
                            SysRoleColumn columnInfo = new SysRoleColumn();
                            columnInfo.setColumnName(columnName);
                            columnInfo.setColumnDesc(columnDesc);
                            columnInfos.add(columnInfo);
                        }
                    }
                } catch (Exception e) {
                    log.warn("扫描类失败: {}", resource.getFilename(), e);
                }
            }
        } catch (Exception e) {
            log.error("扫描实体列失败", e);
        }
        
        // 按列名去重并排序
        return columnInfos.stream()
                .collect(Collectors.toMap(
                    SysRoleColumn::getColumnName,
                    dto -> dto,
                    (existing, replacement) -> existing))
                .values()
                .stream()
                .sorted(Comparator.comparing(SysRoleColumn::getColumnName))
                .collect(Collectors.toList());
    }

    /**
     * 根据模块名获取包路径
     */
    private String getPackagePathByModule(String module) {
        switch (module.toLowerCase()) {
            case "ccms":
                return "com.datalink.fdop.engine.api.domain";
            case "system":
                return "com.datalink.fdop.system.api.domain";
            default:
                return "com.datalink.fdop." + module + ".api.domain";
        }
    }

    /**
     * 获取角色权限列（智能同步字段变更）
     */
    @Override
    public List<SysRoleColumn> getRoleColumnPermissions(Long roleId, String module) {
        // 1. 扫描当前实体的所有字段
        List<SysRoleColumn> currentColumns = scanEntityColumns(module);
        Set<String> currentColumnNames = currentColumns.stream()
                .map(SysRoleColumn::getColumnName)
                .collect(Collectors.toSet());

        // 2. 获取数据库中已存在的字段
        List<SysRoleColumn> existingColumns = roleColumnMapper.selectRoleColumnList(roleId, module);

        if (existingColumns.isEmpty()) {
            // 首次初始化：直接插入所有字段
            log.info("角色{}模块{}首次初始化权限列", roleId, module);
            initRoleColumnPermissions(roleId, module);
            existingColumns = roleColumnMapper.selectRoleColumnList(roleId, module);
        } else {
            // 检查字段变更并同步
            Set<String> existingColumnNames = existingColumns.stream()
                    .map(SysRoleColumn::getColumnName)
                    .collect(Collectors.toSet());

            // 找出新增的字段
            Set<String> newColumns = new HashSet<>(currentColumnNames);
            newColumns.removeAll(existingColumnNames);

            // 找出删除的字段
            Set<String> removedColumns = new HashSet<>(existingColumnNames);
            removedColumns.removeAll(currentColumnNames);

            // 处理字段变更
            if (!newColumns.isEmpty() || !removedColumns.isEmpty()) {
                log.info("角色{}模块{}检测到字段变更，新增：{}，删除：{}", roleId, module, newColumns, removedColumns);
                syncColumnChanges(roleId, module, currentColumns, newColumns, removedColumns);
                // 重新查询更新后的数据
                existingColumns = roleColumnMapper.selectRoleColumnList(roleId, module);
            }
        }

        return existingColumns.stream()
                .sorted(Comparator.comparing(SysRoleColumn::getColumnName))
                .collect(Collectors.toList());
    }

    /**
     * 获取角色权限列（分页查询）
     */
    @Override
    public PageDataInfo<SysRoleColumn> getRoleColumnPermissionsPage(Long roleId, String module, SysRoleColumn queryParam) {
        // 1. 先确保字段同步（调用原方法，但不使用返回值）
        getRoleColumnPermissions(roleId, module);

        // 2. 执行分页查询
        Page<SysRoleColumn> page = PageUtils.getPage(SysRoleColumn.class);
        IPage<SysRoleColumn> pageResult = roleColumnMapper.selectRoleColumnListPage(page, roleId, module, queryParam);

        return PageUtils.getPageInfo(pageResult.getRecords(), (int) pageResult.getTotal());
    }

    /**
     * 更新角色权限列
     */
    @Override
    @Transactional
    public boolean updateRoleColumnPermissions(Long roleId, String module, List<SysRoleColumn> permissions) {
        if (permissions == null || permissions.isEmpty()) {
            return false;
        }

        // 先确保权限列已初始化
        List<SysRoleColumn> existingColumns = roleColumnMapper.selectRoleColumnList(roleId, module);
        if (existingColumns.isEmpty()) {
            initRoleColumnPermissions(roleId, module);
        }

        // 只更新显示状态，不删除重新插入
        List<SysRoleColumn> roleColumns = permissions.stream()
                .map(permission -> {
                    permission.setRoleId(roleId);
                    permission.setModuleName(module);
                    permission.setUpdateBy(SecurityUtils.getUsername());
                    permission.setUpdateTime(new Date());
                    return permission;
                })
                .collect(Collectors.toList());

        return roleColumnMapper.batchUpdateVisible(roleColumns) > 0;
    }

    /**
     * 初始化角色权限列
     */
    @Override
    @Transactional
    public boolean initRoleColumnPermissions(Long roleId, String module) {
        // 扫描模块实体列
        List<SysRoleColumn> columnInfos = scanEntityColumns(module);
        if (columnInfos.isEmpty()) {
            return false;
        }

        // 转换为角色权限列实体
        List<SysRoleColumn> roleColumns = columnInfos.stream()
                .map(columnInfo -> {
                    SysRoleColumn roleColumn = new SysRoleColumn();
                    roleColumn.setRoleId(roleId);
                    roleColumn.setModuleName(module);
                    roleColumn.setColumnName(columnInfo.getColumnName());
                    roleColumn.setColumnDesc(columnInfo.getColumnDesc());
                    roleColumn.setIsVisible("1"); // 默认显示
                    roleColumn.setCreateBy(SecurityUtils.getUsername());
                    roleColumn.setCreateTime(new Date());
                    return roleColumn;
                })
                .collect(Collectors.toList());

        return roleColumnMapper.batchInsert(roleColumns) > 0;
    }

    /**
     * 查询角色权限列列表
     */
    @Override
    public List<SysRoleColumn> selectRoleColumnList(SysRoleColumn roleColumn) {
        LambdaQueryWrapper<SysRoleColumn> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(roleColumn.getRoleId() != null, SysRoleColumn::getRoleId, roleColumn.getRoleId())
               .eq(StringUtils.isNotEmpty(roleColumn.getModuleName()), SysRoleColumn::getModuleName, roleColumn.getModuleName())
               .eq(StringUtils.isNotEmpty(roleColumn.getIsVisible()), SysRoleColumn::getIsVisible, roleColumn.getIsVisible())
               .orderByAsc(SysRoleColumn::getColumnName);

        return roleColumnMapper.selectList(wrapper);
    }

    /**
     * 同步字段变更
     *
     * @param roleId 角色ID
     * @param module 模块名称
     * @param currentColumns 当前扫描到的所有字段
     * @param newColumns 新增的字段名集合
     * @param removedColumns 删除的字段名集合
     */
    @Transactional
    public void syncColumnChanges(Long roleId, String module, List<SysRoleColumn> currentColumns,
                                  Set<String> newColumns, Set<String> removedColumns) {

        // 1. 处理新增字段：插入新字段，默认显示
        if (!newColumns.isEmpty()) {
            log.info("为角色{}模块{}添加新字段：{}", roleId, module, newColumns);

            List<SysRoleColumn> newRecords = currentColumns.stream()
                    .filter(col -> newColumns.contains(col.getColumnName()))
                    .map(col -> {
                        SysRoleColumn newRecord = new SysRoleColumn();
                        newRecord.setRoleId(roleId);
                        newRecord.setModuleName(module);
                        newRecord.setColumnName(col.getColumnName());
                        newRecord.setColumnDesc(col.getColumnDesc());
                        newRecord.setIsVisible("1"); // 新字段默认显示
                        newRecord.setCreateBy(SecurityUtils.getUsername());
                        newRecord.setCreateTime(new Date());
                        newRecord.setRemark("系统自动添加");
                        return newRecord;
                    })
                    .collect(Collectors.toList());

            if (!newRecords.isEmpty()) {
                int insertCount = roleColumnMapper.batchInsert(newRecords);
                log.info("成功为角色{}模块{}添加{}个新字段", roleId, module, insertCount);
            }
        }

        // 2. 处理删除字段：从数据库中删除不存在的字段
        if (!removedColumns.isEmpty()) {
            log.info("为角色{}模块{}删除废弃字段：{}", roleId, module, removedColumns);

            int deleteCount = roleColumnMapper.deleteByColumns(roleId, module, new ArrayList<>(removedColumns));
            log.info("成功为角色{}模块{}删除{}个废弃字段", roleId, module, deleteCount);
        }
    }



}
