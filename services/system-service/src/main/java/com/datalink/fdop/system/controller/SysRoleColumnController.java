package com.datalink.fdop.system.controller;

import com.datalink.fdop.common.core.domain.R;
import com.datalink.fdop.common.core.web.controller.BaseController;
import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.common.log.annotation.Log;
import com.datalink.fdop.common.log.enums.BusinessType;
import com.datalink.fdop.system.domain.SysRoleColumn;
import com.datalink.fdop.system.service.ISysRoleColumnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 角色权限列Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/role-column")
@Api(tags = "角色权限列API")
public class SysRoleColumnController extends BaseController {

    @Autowired
    private ISysRoleColumnService roleColumnService;

    @ApiOperation(value = "扫描模块实体列")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "module", value = "模块名称", required = true, dataType = "String", paramType = "path")
    })
    @Log(title = "角色权限列", businessType = BusinessType.OTHER)
    @GetMapping("/scan/{module}")
    public R<List<SysRoleColumn>> scanEntityColumns(@PathVariable("module") String module) {
        List<SysRoleColumn> columnInfos = roleColumnService.scanEntityColumns(module);
        return R.ok(columnInfos);
    }

    @ApiOperation(value = "获取角色权限列")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleId", value = "角色ID", required = true, dataType = "Long", paramType = "path"),
            @ApiImplicitParam(name = "module", value = "模块名称", dataType = "String", paramType = "query")
    })
    @Log(title = "角色权限列", businessType = BusinessType.OTHER)
    @GetMapping("/{roleId}")
    public R<List<SysRoleColumn>> getRoleColumnPermissions(
            @PathVariable("roleId") Long roleId,
            @RequestParam(value = "module", defaultValue = "ccms") String module) {
        List<SysRoleColumn> permissions = roleColumnService.getRoleColumnPermissions(roleId, module);
        return R.ok(permissions);
    }

    @ApiOperation(value = "分页获取角色权限列")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleId", value = "角色ID", required = true, dataType = "Long", paramType = "path"),
            @ApiImplicitParam(name = "module", value = "模块名称", dataType = "String", paramType = "query")
    })
    @Log(title = "角色权限列", businessType = BusinessType.OTHER)
    @PostMapping("/{roleId}/page")
    public R<PageDataInfo<SysRoleColumn>> getRoleColumnPermissionsPage(
            @PathVariable("roleId") Long roleId,
            @RequestParam(value = "module", defaultValue = "ccms") String module,
            @RequestBody(required = false) SysRoleColumn queryParam) {
        PageDataInfo<SysRoleColumn> pageInfo = roleColumnService.getRoleColumnPermissionsPage(roleId, module, queryParam);
        return R.ok(pageInfo);
    }

    @ApiOperation(value = "修改角色权限列")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleId", value = "角色ID", required = true, dataType = "Long", paramType = "path"),
            @ApiImplicitParam(name = "module", value = "模块名称", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "permissions", value = "权限列表", required = true, dataType = "List", paramType = "body")
    })
    @Log(title = "角色权限列", businessType = BusinessType.UPDATE)
    @PostMapping("/{roleId}")
    public R updateRoleColumnPermissions(
            @PathVariable("roleId") Long roleId,
            @RequestParam("module") String module,
            @Validated @RequestBody List<SysRoleColumn> permissions) {
        boolean result = roleColumnService.updateRoleColumnPermissions(roleId, module, permissions);
        return result ? R.ok("更新成功") : R.fail("更新失败");
    }

    @ApiOperation(value = "初始化角色权限列")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleId", value = "角色ID", required = true, dataType = "Long", paramType = "path"),
            @ApiImplicitParam(name = "module", value = "模块名称", required = true, dataType = "String", paramType = "query")
    })
    @Log(title = "角色权限列", businessType = BusinessType.INSERT)
    @PostMapping("/init/{roleId}")
    public R initRoleColumnPermissions(
            @PathVariable("roleId") Long roleId,
            @RequestParam("module") String module) {
        boolean result = roleColumnService.initRoleColumnPermissions(roleId, module);
        return result ? R.ok("初始化成功") : R.fail("初始化失败");
    }

    @ApiOperation(value = "批量设置所有列显示状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleId", value = "角色ID", required = true, dataType = "Long", paramType = "path"),
            @ApiImplicitParam(name = "module", value = "模块名称", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "visible", value = "是否显示", required = true, dataType = "Boolean", paramType = "query")
    })
    @Log(title = "角色权限列", businessType = BusinessType.UPDATE)
    @PutMapping("/{roleId}/batch-visible")
    public R batchSetVisible(
            @PathVariable("roleId") Long roleId,
            @RequestParam("module") String module,
            @RequestParam("visible") Boolean visible) {

        // 获取当前权限列表
        List<SysRoleColumn> permissions = roleColumnService.getRoleColumnPermissions(roleId, module);

        // 更新所有列显示状态
        permissions.forEach(p -> p.setIsVisible(visible));

        boolean result = roleColumnService.updateRoleColumnPermissions(roleId, module, permissions);
        return result ? R.ok("批量设置成功") : R.fail("批量设置失败");
    }
}
