package com.datalink.fdop.system.service;

import com.datalink.fdop.system.domain.SysRoleColumn;

import java.util.List;

/**
 * 角色权限列Service接口
 *
 * <AUTHOR>
 */
public interface ISysRoleColumnService {

    /**
     * 扫描指定模块的所有实体列
     *
     * @param module 模块名称
     * @return 列信息集合
     */
    List<SysRoleColumn> scanEntityColumns(String module);

    /**
     * 获取角色权限列
     *
     * @param roleId 角色ID
     * @param module 模块名称
     * @return 角色权限列集合
     */
    List<SysRoleColumn> getRoleColumnPermissions(Long roleId, String module);

    /**
     * 更新角色权限列
     *
     * @param roleId 角色ID
     * @param module 模块名称
     * @param permissions 权限列表
     * @return 结果
     */
    boolean updateRoleColumnPermissions(Long roleId, String module, List<SysRoleColumn> permissions);

    /**
     * 初始化角色权限列
     *
     * @param roleId 角色ID
     * @param module 模块名称
     * @return 结果
     */
    boolean initRoleColumnPermissions(Long roleId, String module);



    /**
     * 查询角色权限列列表
     *
     * @param roleColumn 角色权限列
     * @return 角色权限列集合
     */
    List<SysRoleColumn> selectRoleColumnList(SysRoleColumn roleColumn);


}
