package com.datalink.fdop.system.service;

import com.datalink.fdop.common.core.web.page.PageDataInfo;
import com.datalink.fdop.system.domain.SysRoleColumn;

import java.util.List;

/**
 * 角色权限列Service接口
 *
 * <AUTHOR>
 */
public interface ISysRoleColumnService {

    /**
     * 扫描指定模块的所有实体列
     *
     * @param module 模块名称
     * @return 列信息集合
     */
    List<SysRoleColumn> scanEntityColumns(String module);

    /**
     * 获取角色权限列
     *
     * @param roleId 角色ID
     * @param module 模块名称
     * @return 角色权限列集合
     */
    List<SysRoleColumn> getRoleColumnPermissions(Long roleId, String module);

    /**
     * 获取角色权限列（分页查询）
     *
     * @param roleId 角色ID
     * @param module 模块名称
     * @param queryParam 查询参数
     * @return 分页结果
     */
    PageDataInfo<SysRoleColumn> getRoleColumnPermissionsPage(Long roleId, String module, SysRoleColumn queryParam);

    /**
     * 更新角色权限列
     *
     * @param roleId 角色ID
     * @param module 模块名称
     * @param permissions 权限列表
     * @return 结果
     */
    boolean updateRoleColumnPermissions(Long roleId, String module, List<SysRoleColumn> permissions);

    /**
     * 初始化角色权限列
     *
     * @param roleId 角色ID
     * @param module 模块名称
     * @return 结果
     */
    boolean initRoleColumnPermissions(Long roleId, String module);

}
