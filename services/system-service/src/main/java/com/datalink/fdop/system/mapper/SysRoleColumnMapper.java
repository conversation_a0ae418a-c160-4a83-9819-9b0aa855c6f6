package com.datalink.fdop.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datalink.fdop.system.domain.SysRoleColumn;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色权限列Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface SysRoleColumnMapper extends BaseMapper<SysRoleColumn> {

    /**
     * 查询角色权限列列表
     *
     * @param roleId 角色ID
     * @param moduleName 模块名称
     * @return 角色权限列集合
     */
    List<SysRoleColumn> selectRoleColumnList(@Param("roleId") Long roleId, @Param("moduleName") String moduleName);

    /**
     * 分页查询角色权限列列表
     *
     * @param page 分页参数
     * @param roleId 角色ID
     * @param moduleName 模块名称
     * @param queryParam 查询参数
     * @return 分页结果
     */
    IPage<SysRoleColumn> selectRoleColumnListPage(IPage<SysRoleColumn> page,
                                                  @Param("roleId") Long roleId,
                                                  @Param("moduleName") String moduleName,
                                                  @Param("queryParam") SysRoleColumn queryParam);

    /**
     * 批量插入角色权限列
     *
     * @param roleColumns 角色权限列集合
     * @return 结果
     */
    int batchInsert(@Param("list") List<SysRoleColumn> roleColumns);

    /**
     * 批量更新角色权限列显示状态
     *
     * @param roleColumns 角色权限列集合
     * @return 结果
     */
    int batchUpdateVisible(@Param("list") List<SysRoleColumn> roleColumns);

    /**
     * 删除角色权限列
     *
     * @param roleId 角色ID
     * @param moduleName 模块名称
     * @return 结果
     */
    int deleteByRoleIdAndModule(@Param("roleId") Long roleId, @Param("moduleName") String moduleName);

    /**
     * 删除指定的列
     *
     * @param roleId 角色ID
     * @param moduleName 模块名称
     * @param columnNames 要删除的列名集合
     * @return 结果
     */
    int deleteByColumns(@Param("roleId") Long roleId, @Param("moduleName") String moduleName,
                       @Param("columnNames") List<String> columnNames);
}
