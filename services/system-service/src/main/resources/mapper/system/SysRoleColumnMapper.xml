<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.fdop.system.mapper.SysRoleColumnMapper">

    <resultMap type="com.datalink.fdop.system.domain.SysRoleColumn" id="SysRoleColumnResult">
        <id property="id" column="id"/>
        <result property="roleId" column="role_id"/>
        <result property="moduleName" column="module_name"/>
        <result property="columnName" column="column_name"/>
        <result property="columnDesc" column="column_desc"/>
        <result property="isVisible" column="is_visible"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectSysRoleColumnVo">
        select id, role_id, module_name, column_name, column_desc, is_visible,
               create_by, create_time, update_by, update_time, remark
        from sys_role_column
    </sql>

    <select id="selectRoleColumnList" parameterType="map" resultMap="SysRoleColumnResult">
        <include refid="selectSysRoleColumnVo"/>
        <where>
            <if test="roleId != null">
                and role_id = #{roleId}
            </if>
            <if test="moduleName != null and moduleName != ''">
                and module_name = #{moduleName}
            </if>
        </where>
        order by column_name
    </select>

    <select id="selectRoleColumnListPage" parameterType="map" resultMap="SysRoleColumnResult">
        <include refid="selectSysRoleColumnVo"/>
        <where>
            <if test="roleId != null">
                and role_id = #{roleId}
            </if>
            <if test="moduleName != null and moduleName != ''">
                and module_name = #{moduleName}
            </if>
            <if test="queryParam != null">
                <if test="queryParam.columnName != null and queryParam.columnName != ''">
                    and column_name like concat('%', #{queryParam.columnName}, '%')
                </if>
                <if test="queryParam.columnDesc != null and queryParam.columnDesc != ''">
                    and column_desc like concat('%', #{queryParam.columnDesc}, '%')
                </if>
                <if test="queryParam.isVisible != null and queryParam.isVisible != ''">
                    and is_visible = #{queryParam.isVisible}
                </if>
            </if>
        </where>
        order by column_name
    </select>

    <insert id="batchInsert" parameterType="list">
        insert into sys_role_column(role_id, module_name, column_name, column_desc, is_visible, create_by, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.roleId}, #{item.moduleName}, #{item.columnName}, #{item.columnDesc},
             #{item.isVisible}, #{item.createBy}, #{item.createTime})
        </foreach>
    </insert>

    <update id="batchUpdateVisible" parameterType="list">
        <foreach collection="list" item="item" separator=";">
            update sys_role_column
            set is_visible = #{item.isVisible},
                update_by = #{item.updateBy},
                update_time = #{item.updateTime}
            where role_id = #{item.roleId}
              and module_name = #{item.moduleName}
              and column_name = #{item.columnName}
        </foreach>
    </update>

    <delete id="deleteByRoleIdAndModule" parameterType="map">
        delete from sys_role_column
        where role_id = #{roleId}
        <if test="moduleName != null and moduleName != ''">
            and module_name = #{moduleName}
        </if>
    </delete>

    <delete id="deleteByColumns" parameterType="map">
        delete from sys_role_column
        where role_id = #{roleId} and module_name = #{moduleName}
        and column_name in
        <foreach collection="columnNames" item="columnName" open="(" separator="," close=")">
            #{columnName}
        </foreach>
    </delete>

</mapper>
