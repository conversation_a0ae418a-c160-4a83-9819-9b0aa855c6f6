-- ----------------------------
-- PostgreSQL 数据库迁移脚本：将 sys_role_column 表的 is_visible 字段从 CHAR(1) 改为 BOOLEAN
-- 执行时间：请在业务低峰期执行
-- ----------------------------

-- 1. 备份现有数据（可选，建议在生产环境执行前先备份）
-- CREATE TABLE sys_role_column_backup AS SELECT * FROM sys_role_column;

-- 2. 添加新的临时字段
ALTER TABLE sys_role_column ADD COLUMN is_visible_new BOOLEAN DEFAULT TRUE;

-- 3. 添加字段注释
COMMENT ON COLUMN sys_role_column.is_visible_new IS '是否显示';

-- 4. 数据迁移：将字符串转换为布尔值
UPDATE sys_role_column SET is_visible_new = CASE 
    WHEN is_visible = '1' THEN TRUE 
    WHEN is_visible = '0' THEN FALSE 
    ELSE TRUE 
END;

-- 5. 删除旧字段
ALTER TABLE sys_role_column DROP COLUMN is_visible;

-- 6. 重命名新字段
ALTER TABLE sys_role_column RENAME COLUMN is_visible_new TO is_visible;

-- 7. 验证数据迁移结果
-- SELECT is_visible, COUNT(*) FROM sys_role_column GROUP BY is_visible;

-- 8. 如果需要，可以删除备份表
-- DROP TABLE IF EXISTS sys_role_column_backup;
