# PostgreSQL 数据库迁移指南

## 概述
本指南说明如何将 `sys_role_column` 表的 `is_visible` 字段从 String 类型（CHAR(1)）迁移到 Boolean 类型。

## 迁移前准备

### 1. 数据备份
```sql
-- 创建备份表
CREATE TABLE sys_role_column_backup AS SELECT * FROM sys_role_column;

-- 验证备份数据
SELECT COUNT(*) FROM sys_role_column_backup;
```

### 2. 检查现有数据
```sql
-- 检查 is_visible 字段的数据分布
SELECT is_visible, COUNT(*) 
FROM sys_role_column 
GROUP BY is_visible;
```

## 迁移步骤

### 1. 执行迁移脚本
```bash
psql -d your_database -f services/system-service/src/main/resources/sql/migration/update_sys_role_column_is_visible_postgresql.sql
```

### 2. 验证迁移结果
```sql
-- 检查字段类型
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'sys_role_column' AND column_name = 'is_visible';

-- 检查数据分布
SELECT is_visible, COUNT(*) 
FROM sys_role_column 
GROUP BY is_visible;
```

## PostgreSQL 与 MySQL 的差异

| 特性 | MySQL | PostgreSQL |
|------|-------|------------|
| 布尔类型 | TINYINT(1) | BOOLEAN |
| 自增主键 | AUTO_INCREMENT | SERIAL/BIGSERIAL |
| 反引号 | \`table\` | "table" 或 table |
| 注释语法 | COMMENT '注释' | COMMENT ON ... IS '注释' |
| 更新时间 | ON UPDATE CURRENT_TIMESTAMP | 需要触发器 |

## 应用程序配置

### 1. MyBatis 配置
PostgreSQL 的 BOOLEAN 类型会自动映射到 Java 的 Boolean 类型，无需额外配置。

### 2. 数据源配置
确保使用 PostgreSQL 驱动：
```yaml
spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    url: **********************************************
```

## 回滚方案

如果需要回滚到原来的 CHAR(1) 类型：

```sql
-- 1. 添加临时字段
ALTER TABLE sys_role_column ADD COLUMN is_visible_char CHAR(1) DEFAULT '1';

-- 2. 数据转换
UPDATE sys_role_column SET is_visible_char = CASE 
    WHEN is_visible = TRUE THEN '1' 
    WHEN is_visible = FALSE THEN '0' 
    ELSE '1' 
END;

-- 3. 删除布尔字段
ALTER TABLE sys_role_column DROP COLUMN is_visible;

-- 4. 重命名字段
ALTER TABLE sys_role_column RENAME COLUMN is_visible_char TO is_visible;
```

## 性能考虑

1. **存储空间**：BOOLEAN 类型比 CHAR(1) 更节省空间
2. **查询性能**：布尔类型的比较操作更高效
3. **索引**：如果需要按 is_visible 查询，可以创建索引：
   ```sql
   CREATE INDEX idx_sys_role_column_visible ON sys_role_column (is_visible);
   ```

## 测试建议

1. **单元测试**：确保所有涉及 is_visible 字段的测试用例通过
2. **集成测试**：验证 API 接口返回正确的布尔值
3. **性能测试**：对比迁移前后的查询性能

## 常见问题

### Q: 迁移过程中应用程序是否需要停机？
A: 建议在业务低峰期执行，短暂停机以确保数据一致性。

### Q: 如何处理迁移过程中的并发写入？
A: 使用事务确保原子性，或者在维护窗口期间执行。

### Q: 迁移失败如何处理？
A: 立即停止迁移，从备份表恢复数据，分析失败原因后重新执行。
