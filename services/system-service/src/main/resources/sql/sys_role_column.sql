-- ----------------------------
-- 角色权限列关联表 (PostgreSQL版本)
-- ----------------------------
DROP TABLE IF EXISTS sys_role_column;

CREATE TABLE sys_role_column (
    id BIGSERIAL PRIMARY KEY,
    role_id BIGINT NOT NULL,
    module_name VARCHAR(100) NOT NULL,
    column_name VARCHAR(100) NOT NULL,
    column_desc VARCHAR(200),
    is_visible BOOLEAN DEFAULT TRUE,
    create_by VARCHAR(64) DEFAULT '',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(64) DEFAULT '',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    remark VARCHAR(500)
);

-- 添加注释
COMMENT ON TABLE sys_role_column IS '角色权限列关联表';
COMMENT ON COLUMN sys_role_column.id IS '主键ID';
COMMENT ON COLUMN sys_role_column.role_id IS '角色ID';
COMMENT ON COLUMN sys_role_column.module_name IS '模块名称';
COMMENT ON COLUMN sys_role_column.column_name IS '列名';
COMMENT ON COLUMN sys_role_column.column_desc IS '列描述';
COMMENT ON COLUMN sys_role_column.is_visible IS '是否显示';
COMMENT ON COLUMN sys_role_column.create_by IS '创建者';
COMMENT ON COLUMN sys_role_column.create_time IS '创建时间';
COMMENT ON COLUMN sys_role_column.update_by IS '更新者';
COMMENT ON COLUMN sys_role_column.update_time IS '更新时间';
COMMENT ON COLUMN sys_role_column.remark IS '备注';

-- 创建唯一约束
ALTER TABLE sys_role_column ADD CONSTRAINT uk_role_column UNIQUE (role_id, module_name, column_name);

-- 创建索引
CREATE INDEX idx_role_id ON sys_role_column (role_id);
CREATE INDEX idx_module_name ON sys_role_column (module_name);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建触发器
CREATE TRIGGER update_sys_role_column_modtime
    BEFORE UPDATE ON sys_role_column
    FOR EACH ROW
    EXECUTE FUNCTION update_modified_column();
