-- ----------------------------
-- 角色权限列关联表
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_column`;
CREATE TABLE `sys_role_column` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `role_id` BIGINT(20) NOT NULL COMMENT '角色ID',
    `module_name` VARCHAR(100) NOT NULL COMMENT '模块名称',
    `column_name` VARCHAR(100) NOT NULL COMMENT '列名',
    `column_desc` VARCHAR(200) DEFAULT NULL COMMENT '列描述',
    `is_visible` CHAR(1) DEFAULT '1' COMMENT '是否显示(0隐藏 1显示)',
    `create_by` VARCHAR(64) DEFAULT '' COMMENT '创建者',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(64) DEFAULT '' COMMENT '更新者',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `remark` VARCHAR(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_column` (`role_id`, `module_name`, `column_name`),
    KEY `idx_role_id` (`role_id`),
    KEY `idx_module_name` (`module_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限列关联表';
