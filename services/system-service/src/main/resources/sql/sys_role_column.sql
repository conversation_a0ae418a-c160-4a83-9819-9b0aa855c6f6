-- zjdata.sys_role_column definition

-- Drop table

-- DROP TABLE zjdata.sys_role_column;

CREATE TABLE zjdata.sys_role_column (
                                        id bigserial NOT NULL, -- 主键ID
                                        role_id int8 NOT NULL, -- 角色ID
                                        module_name varchar(100) NOT NULL, -- 模块名称
                                        column_name varchar(100) NOT NULL, -- 列名
                                        column_desc varchar(200) NULL, -- 列描述
                                        is_visible bool DEFAULT true NULL, -- 是否显示
                                        create_by varchar(64) NULL, -- 创建者
                                        create_time timestamp NULL, -- 创建时间
                                        update_by varchar(64) NULL, -- 更新者
                                        update_time timestamp NULL, -- 更新时间
                                        CONSTRAINT sys_role_column_pkey PRIMARY KEY (id),
                                        CONSTRAINT uk_role_column UNIQUE (role_id, module_name, column_name)
);
CREATE INDEX idx_module_name ON zjdata.sys_role_column USING btree (module_name);
CREATE INDEX idx_role_id ON zjdata.sys_role_column USING btree (role_id);
COMMENT ON TABLE zjdata.sys_role_column IS '角色权限列关联表';

-- Column comments

COMMENT ON COLUMN zjdata.sys_role_column.id IS '主键ID';
COMMENT ON COLUMN zjdata.sys_role_column.role_id IS '角色ID';
COMMENT ON COLUMN zjdata.sys_role_column.module_name IS '模块名称';
COMMENT ON COLUMN zjdata.sys_role_column.column_name IS '列名';
COMMENT ON COLUMN zjdata.sys_role_column.column_desc IS '列描述';
COMMENT ON COLUMN zjdata.sys_role_column.is_visible IS '是否显示';
COMMENT ON COLUMN zjdata.sys_role_column.create_by IS '创建者';
COMMENT ON COLUMN zjdata.sys_role_column.create_time IS '创建时间';
COMMENT ON COLUMN zjdata.sys_role_column.update_by IS '更新者';
COMMENT ON COLUMN zjdata.sys_role_column.update_time IS '更新时间';