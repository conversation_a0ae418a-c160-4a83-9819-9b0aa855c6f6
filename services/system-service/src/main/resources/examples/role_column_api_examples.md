# 角色权限列API使用示例

## 1. 获取角色权限列（非分页）

```http
GET /system/role-column/1?module=ccms
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,
      "roleId": 1,
      "moduleName": "ccms",
      "columnName": "userId",
      "columnDesc": "用户ID",
      "isVisible": true,
      "createBy": "admin",
      "createTime": "2024-01-01 10:00:00"
    },
    {
      "id": 2,
      "roleId": 1,
      "moduleName": "ccms",
      "columnName": "userName",
      "columnDesc": "用户名",
      "isVisible": false,
      "createBy": "admin",
      "createTime": "2024-01-01 10:00:00"
    }
  ]
}
```

## 2. 分页获取角色权限列

```http
POST /system/role-column/1/page?module=ccms&pageNo=1&pageSize=10
Content-Type: application/json

{
  "columnName": "user",
  "isVisible": true
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalList": [
      {
        "id": 1,
        "roleId": 1,
        "moduleName": "ccms",
        "columnName": "userId",
        "columnDesc": "用户ID",
        "isVisible": true,
        "createBy": "admin",
        "createTime": "2024-01-01 10:00:00"
      }
    ],
    "total": 1,
    "totalPage": 1,
    "pageSize": 10,
    "currentPage": 1
  }
}
```

## 3. 更新角色权限列

```http
POST /system/role-column/1?module=ccms
Content-Type: application/json

[
  {
    "id": 1,
    "columnName": "userId",
    "isVisible": true
  },
  {
    "id": 2,
    "columnName": "userName", 
    "isVisible": false
  }
]
```

## 4. 批量设置显示状态

```http
PUT /system/role-column/1/batch-visible?module=ccms&visible=true
```

## 注意事项

1. **isVisible字段类型变更**：从String类型改为Boolean类型
   - 旧版本：`"isVisible": "1"` 或 `"isVisible": "0"`
   - 新版本：`"isVisible": true` 或 `"isVisible": false`

2. **数据库迁移**：需要执行迁移脚本更新现有数据

3. **向下兼容性**：原有的非分页接口保持不变，新增分页接口

4. **查询参数**：分页接口支持按列名、列描述、是否显示进行筛选
