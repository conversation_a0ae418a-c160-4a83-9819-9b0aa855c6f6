package com.datalink.fdop.system.service;

import com.datalink.fdop.system.domain.SysRoleColumn;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * 角色权限列服务测试
 *
 * <AUTHOR>
 */
@SpringBootTest
public class SysRoleColumnServiceTest {

    @Autowired
    private ISysRoleColumnService roleColumnService;

    @Test
    public void testScanEntityColumns() {
        List<SysRoleColumn> columnInfos = roleColumnService.scanEntityColumns("ccms");
        System.out.println("扫描到的列数量: " + columnInfos.size());

        columnInfos.forEach(columnInfo -> {
            System.out.println(String.format("列: %s, 描述: %s",
                columnInfo.getColumnName(),
                columnInfo.getColumnDesc()));
        });
    }

    @Test
    public void testInitRoleColumnPermissions() {
        Long roleId = 1L;
        String module = "ccms";
        
        boolean result = roleColumnService.initRoleColumnPermissions(roleId, module);
        System.out.println("初始化结果: " + result);
    }

    @Test
    public void testGetRoleColumnPermissions() {
        Long roleId = 1L;
        String module = "ccms";

        List<SysRoleColumn> permissions = roleColumnService.getRoleColumnPermissions(roleId, module);
        System.out.println("权限列数量: " + permissions.size());

        permissions.forEach(permission -> {
            System.out.println(String.format("列: %s, 描述: %s, 显示: %s",
                permission.getColumnName(),
                permission.getColumnDesc(),
                permission.getIsVisible()));
        });
    }

    @Test
    public void testUpdateRoleColumnPermissions() {
        Long roleId = 1L;
        String module = "ccms";
        
        // 获取当前权限
        List<SysRoleColumn> permissions = roleColumnService.getRoleColumnPermissions(roleId, module);

        // 修改部分权限
        if (!permissions.isEmpty()) {
            permissions.get(0).setIsVisible("0");
            if (permissions.size() > 1) {
                permissions.get(1).setIsVisible("0");
            }
        }

        // 更新权限
        boolean result = roleColumnService.updateRoleColumnPermissions(roleId, module, permissions);
        System.out.println("更新结果: " + result);
    }
}
